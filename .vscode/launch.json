{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "attach",
      "name": "Queue Service",
      "port": 5631,
      "remoteRoot": "/home/<USER>/app",
      "localRoot": "${workspaceFolder}/",
      "restart": true,
      "sourceMapPathOverrides": {
        "/home/<USER>/app/*": "${workspaceFolder}/*"
      },
    },
    {
      "type": "node",
      "request": "attach",
      "name": "Direct",
      "port": 9229,
      "restart": true,
      "sourceMapPathOverrides": {
        "/home/<USER>/app/*": "${workspaceFolder}/*"
      },
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Start Workers",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "program": "${workspaceFolder}/node_modules/.bin/nodemon",
      "args": [
        "--exec",
        "npm run babel-node",
        "--",
        "./startWorkers.js"
      ],
      "restart": true,
      "sourceMaps": true,
      // "outFiles": ["${workspaceFolder}/dist/**/*.js"],
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Start Cron",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "program": "${workspaceFolder}/node_modules/.bin/nodemon",
      "args": [
        "--exec",
        "npm run babel-node",
        "--",
        "./startCron.js"
      ],
      "restart": true,
      "sourceMaps": true,
      "outFiles": [
        "${workspaceFolder}/src/**/*.js"
      ],
    }
  ]
}
