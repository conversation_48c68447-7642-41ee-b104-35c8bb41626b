{
  "solidity.compileUsingRemoteVersion": "0.5.8",
  "material-icon-theme.showUpdateMessage": false,
  "window.zoomLevel": 0,
  "workbench.iconTheme": "material-icon-theme",
  "workbench.statusBar.visible": true,
  "workbench.sideBar.location": "left",
  "files.autoSave": "onFocusChange",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "editor.tabSize": 2,
  "editor.renderWhitespace": "all",
  "editor.minimap.enabled": true,
  "editor.renderControlCharacters": false,
  "editor.renderFinalNewline": "on",
  "editor.rulers": [
    120
  ],
  "editor.multiCursorModifier": "ctrlCmd",
  "editor.autoIndent": "full",
  "editor.detectIndentation": false,
  "javascript.implicitProjectConfig.experimentalDecorators": true,
  "javascript.updateImportsOnFileMove.enabled": "always",
  "javascript.format.insertSpaceBeforeFunctionParenthesis": true,
  "javascript.format.insertSpaceAfterConstructor": true,
  "standard.autoFixOnSave": true,
  "breadcrumbs.enabled": true,
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  },
  "cSpell.words": [
    "DAPP",
    "GRAPHQL",
    "Oracleship",
    "Toman",
    "accountsid",
    "actioncable",
    "actionee",
    "app",
    "app ezugi email",
    "app twilio accountsid",
    "app twilio authtoken",
    "app twilio serviceid",
    "appbar",
    "arial",
    "authtoken",
    "baccaart",
    "baccaart img",
    "backoff",
    "balham",
    "betcol",
    "bigint",
    "bin",
    "block",
    "btns",
    "clearfix",
    "compose",
    "contracts",
    "countup",
    "d",
    "dailycontest",
    "dailytask",
    "dataloader",
    "deployer",
    "devtools",
    "downs",
    "drop",
    "easytimer",
    "email",
    "extension",
    "ezugi",
    "fiftyx",
    "fivex",
    "formik",
    "fullhost",
    "gcloud",
    "get",
    "get drop downs navs",
    "haspopup",
    "highcharts",
    "img",
    "irant",
    "klass",
    "kubectl",
    "kubernetes",
    "latestgame",
    "latestgame block",
    "lato",
    "leaderboard",
    "logo",
    "logo irant",
    "max",
    "menu",
    "mixins",
    "modules",
    "navs",
    "node",
    "noopener",
    "noreferrer",
    "nouislider",
    "numericality",
    "prng",
    "progressbar",
    "rangeslider",
    "rc",
    "rc texty",
    "react",
    "react rangeslider",
    "redux",
    "refetch",
    "returnvalue",
    "rmgtrc",
    "roboto",
    "romdrops",
    "romtrc",
    "rotate",
    "scatterjs",
    "scrollator",
    "sendgrid",
    "serviceid",
    "sol",
    "solc",
    "solhint",
    "solium",
    "spreter",
    "table",
    "tabpanel",
    "texty",
    "todos",
    "tronbox",
    "trongrid",
    "tronlink",
    "tronpay",
    "tronweb",
    "twilio",
    "twox",
    "type",
    "unmount",
    "upsert",
    "uuidv",
    "valuenow",
    "warnings",
    "webfontloader",
    "yellow",
    "yellow rotate btns"
  ],
  "standard.validate": [
    {
      "language": "javascript",
      "autoFix": true
    },
    {
      "language": "javascriptreact",
      "autoFix": true
    }
  ],
  "standard.options": {
    "parser": "babel-eslint",
    "env": {
      "jest": true,
      "browser": true,
      "node": true
    },
    "globals": [
      "artifacts",
      "tronWeb",
      "contract",
      "before",
      "assert",
      "after"
    ]
  },
  "solidity.linter": "solhint",
  "todohighlight.defaultStyle": {
    "color": "red",
    "backgroundColor": "rgba(0,0,0,.2)",
    "overviewRulerColor": "rgba(0,0,0,.2)",
    "cursor": "pointer",
    "border": "1px solid red",
    "borderRadius": "5px",
    "isWholeLine": true,
  },
  "todohighlight.keywords": [
    "TODO",
    "FIXME"
  ],
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
    "source.organizeImports": "explicit"
  },
  "standard.enable": true,
  "cSpell.ignoreWords": [
    "betslip"
  ],
  "eslint.enable": false
}
