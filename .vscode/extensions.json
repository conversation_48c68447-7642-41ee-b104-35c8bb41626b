{
  // See http://go.microsoft.com/fwlink/?LinkId=827846 to learn about workspace recommendations.
  // Extension identifier format: ${publisher}.${name}. Example: vscode.csharp
  // List of extensions which should be recommended for users of this workspace.
  "recommendations": [
    "AravindKumar.gherkin-indent",
    "Orta.vscode-jest",
    "alexkrechik.cucumberautocomplete",
    "andys8.jest-snippets",
    "chenxsan.vscode-standardjs",
    "EditorConfig.EditorConfig",
    "eg2.vscode-npm-script",
    "eriklynd.json-tools",
    "JuanBlanco.solidity",
    "PKief.material-icon-theme",
    "ms-azuretools.vscode-docker",
    "yzhang.markdown-all-in-one",
    "aeschli.vscode-css-formatter",
    "streetsidesoftware.code-spell-checker",
    "ecmel.vscode-html-css",
    "mkaufman.htmlhint",
    "msjsdiag.debugger-for-chrome",
    "kumar-harsh.graphql-for-vscode"
  ],
  // List of extensions recommended by VS Code that should not be recommended for users of this workspace.
  "unwantedRecommendations": []
}
