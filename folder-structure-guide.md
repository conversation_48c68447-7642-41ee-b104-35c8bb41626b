# Folder Structure guide

> This doc is a guide for providing information on the folder structure.

All the application related code is present inside the `/src` folder.

Under `src` folder we have this predefined folders

- [Folder Structure guide](#folder-structure-guide)
  - [configs folder](#configs-folder)
  - [errors folder](#errors-folder)
  - [helpers folder](#helpers-folder)
  - [json-schemas folder](#json-schemas-folder)
  - [libs folder](#libs-folder)
  - [rest-resources folder](#rest-resources-folder)
    - [controllers](#controllers)
    - [middlewares](#middlewares)
    - [routes](#routes)
  - [services folder](#services-folder)
  - [socket-resources folder](#socket-resources-folder)
    - [emitters](#emitters)
    - [handlers](#handlers)
    - [middlewares](#middlewares-1)
    - [namespaces](#namespaces)
  - [utils folder](#utils-folder)
  - [templates folder](#templates-folder)
  - [queues](#queues)
  - [workers folder](#workers-folder)
  - [crons folder](#crons-folder)
  - [listeners](#listeners)

All this folder is pre created and contain code as their name suggests

## configs folder

This folder contains all the config of the projects.

Make sure file name ends with `*.config.js`

---

## errors folder

This folder contains all the error files of the application.

You can define all the error here you just need to extend your error via base error and you are good to go.
Make sure file name ends with `*.error.js` also the Error name should be ends with DemoError also in one file
there should be only one error

Please define error type in utils under `.errorTypes.js` and when use in `service` please enter proper key name like this

```
this.addError('RequestInputValidationErrorType', 'Error description')
```

---

## helpers folder

This folder contains all the helpers files of the application.

Make sure file name ends with `*.helpers.js`

---

## json-schemas folder

This folder contains all base resources of the schema.

Make sure file name ends with `*.schema.js`

---

## libs folder

This folder contains all lib files like logger, redisClient, socketClient everything which is module has come here.

after creating object gets exported from it.

---

## rest-resources folder

This folder contains files and folders related to the rest resources, we are using express js as framework here inside `index.js`.

  ### controllers

  All the controllers of the application will go here.
  File name should ends with `*.controller.js` also class name should be ends with `DemoController`
  There should be only one controller per file and it should contain static methods as a route handler
  Please refer DemoController.js inside controller

  ### middlewares

  All the rest middlewares of the application will go here.
  There are some default middlewares which is the back a bone of this structure
  File name should ends with `*.middleware.js` also middleware name should be ends with `demoMiddleware`

  i.e. ContextMiddleware, RequestValidationMiddleware, ResponseValidationMiddleware

  RequestValidationMiddleware and ResponseValidationMiddleware go just before the after the main route handler

  please see the jsdoc under the middlewares for how to use these middlewares and refer demo route.

  ### routes

  All the routes of the application will go here.

  File name should ends with `*.routes.js` also route name should be ends with `demoRoutes`

---

## services folder

All the business logic of the application will go inside the services.

Every service will extends the ServiceBase.js each service should be a class with async run() method

File name should ends with `*.service.js` also services name should be ends with `DemoService`.

Please refer HelloWorldService.js and ServiceBase.js

---

## socket-resources folder

This folder contains files and folders related to the socket resources, we are using socket.io as framework here inside `index.js`.

  ### emitters

  All the emitters of the application will go here.

  File name should ends with `*.emitter.js` also emitters name should be ends with `DemoEmitter`.
  There should be only one emitter per file and it should contain static methods as a emitter

  Please refer DemoEmitter.js

  ### handlers

  All the socket message with handlers of the application will go here.

  File name should ends with `*.handler.js` also handlers name should be ends with `DemoHandler`.
  There should be only one handler per file and it should contain static methods as a message/event handler

  Please refer DemoHandler.js

  ### middlewares

  All the socket middlewares of the application will go here.

  There are some default middlewares which is the back a bone of this structure
  File name should ends with `*.middleware.js` also middleware name should be ends with `DemoMiddleware`

  i.e. ContextMiddleware, RequestValidationSocketMiddleware, ResponseValidationSocketMiddleware

  ContextMiddleware will automatically apply ResponseValidationSocketMiddleware so you just need apply ContextMiddleware,
  RequestValidationSocketMiddleware both the middleware is socket level middleware.

  please see the jsdoc under the middlewares for how to use these middlewares and refer demo namespace.

  ### namespaces

  All the socket namespace of the application will go here.

  File name should ends with `*.namespace.js` also namespace name should be ends with `DemoNamespace`

  Please refer DemoNamespace.js.

---

## utils folder

All Application utils are written down here.

File name should ends with `*.utils.js`

---

## templates folder

All Application templates will go here.

File name should ends with `*.template.js` and it should export the function name `createDemoTemplate`


---

## queues

All Application workers are written down here.

File name should ends with `.queue.js` also worker name should be ends with demoQueue it will export `jobs` and `queue` instance from same file

Please refer Demo Queue Worker

You can enqueue the job inside queue via, rest api or webs-socket

For managing queue-worker and crons we are using [BullQueue](https://www.npmjs.com/package/bull)

---

## workers folder

All Application workers are written down here.

File name should ends with `*.worker.js` also worker name should be ends with `DemoWorker`

Every worker will load json schema and setup graceful shutdown as every worker will be run as separate process

Please refer DemoHelloWorker for example. For info refer [BullQueue](https://www.npmjs.com/package/bull)

---

## crons folder

All Application crons are written down here.

File name should ends with `*.cron.js`

All cron will go inside queue as job please refer [BullQueue](https://www.npmjs.com/package/bull) for cron jobs

---

## listeners

All Application listeners are written down here.

File name should ends with `*.listener.js` also listener name should be ends with `DemoListener`

If in app we have any third party listener we will listen to them here.
