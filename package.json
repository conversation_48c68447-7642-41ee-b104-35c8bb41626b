{"name": "queue-worker", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"prepare": "husky install", "start:workers": "node ./dist/startWorkers.js", "start:cron": "node ./dist/startCron.js", "start": "node ./dist/index.js", "babel-node": "babel-node --inspect=0.0.0.0:9229", "start:dev:workers": "nodemon --exec npm run babel-node -- ./startWorkers.js", "start:dev:cron": "nodemon --exec npm run babel-node -- ./startCron.js", "start:dev": "nodemon --exec npm run babel-node -- ./index.js", "lint": "standard --fix"}, "author": "gammastack", "license": "ISC", "engines": {"node": "20.9.0"}, "dependencies": {"@elastic/elasticsearch": "7.11.0", "@bull-board/api": "3.11.0", "@bull-board/express": "3.11.0", "@socket.io/redis-adapter": "7.2.0", "@socket.io/redis-emitter": "4.1.1", "@sendgrid/mail": "7.4.2", "@aws-sdk/client-s3": "^3.0.0", "@aws-sdk/lib-storage": "^3.839.0", "aws-sdk": "2.1327.0", "axios": "^1.7.7", "archiver": "^7.0.1", "body-parser": "1.19.1", "crypto-js": "4.1.1", "json2csv": "6.0.0-alpha.2", "sharp": "0.31.3", "bcrypt": "5.0.1", "ajv": "8.8.2", "ajv-formats": "2.1.1", "ajv-i18n": "4.2.0", "helmet": "6.0.1", "ajv-keywords": "5.1.0", "bull": "4.8.3", "convict": "6.2.1", "easytimer.js": "4.1.1", "express": "4.18.1", "express-basic-auth": "1.2.1", "flatted": "3.2.4", "http-status-codes": "2.2.0", "i18n": "0.15.1", "ioredis": "4.28.2", "lodash": "4.17.21", "morgan": "1.10.0", "node-rsa": "1.1.1", "number-precision": "1.6.0", "pg": "8.7.1", "pg-hstore": "2.3.4", "p-limit": "^3.1.0", "sequelize": "6.31.0", "socket.io": "4.4.1", "socket.io-client": "4.5.0", "winston": "3.3.3", "tesseract.js": "6.0.1", "uuid": "8.3.0", "csv-writer": "1.6.0", "moment": "2.30.1", "moment-timezone": "0.5.45", "md5": "2.3.0", "serialize-javascript": "6.0.2", "basic-ftp": "5.0.5", "jszip": "3.10.1", "canvas": "2.11.2", "nodemailer": "6.9.13", "xlsx": "0.18.5", "xml2js": "0.6.2", "stream": "0.0.2", "pg-query-stream": "^4.9.6", "ssh2-sftp-client": "12.0.0", "mailgun.js": "^12.0.3", "form-data": "^4.0.3"}, "devDependencies": {"@babel/cli": "7.16.0", "@babel/core": "7.16.5", "@babel/eslint-parser": "7.16.5", "@babel/node": "7.16.5", "@babel/preset-env": "7.16.5", "dotenv": "10.0.0", "eslint": "7.12.1", "husky": "7.0.4", "node-cron": "3.0.0", "lint-staged": "12.1.2", "nodemon": "2.0.15", "standard": "16.0.4"}, "standard": {"parser": "@babel/eslint-parser", "env": {"jest": true, "browser": true, "node": true}}, "lint-staged": {"src/**/*.js": ["npm run lint"]}}