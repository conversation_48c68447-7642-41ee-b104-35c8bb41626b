import axios from 'axios'
import { QueryTypes, Sequelize } from 'sequelize'
import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import Logger from '../libs/logger'
import { WHITECLIFF_AXIOS_DELAY, WHITECLIFF_PROVIDERS } from '../utils/constants/constant'
import { CRON_LOG_STATUS, PROD_WHITECLIFF_PROVIDER, STAGE_WHITECLIFF_PROVIDER, WHITECLIFF_CREDENTIALS, WHITECLIFF_ICON } from './constants'
import { getCasinoProvider } from './getCasinoProvider'
const { Op } = require('sequelize');


export default async (reqBody = null) => {

  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const {
      TenantCredential: TenantCredentialModel,
      CasinoGame: CasinoGameModel,
      CasinoTable: CasinoTableModel,
      CasinoItem: CasinoItemModel,
      Page: PageModel,
      CasinoMenu: CasinoMenuModel,
      PageMenu: PageMenuModel,
      MenuItem: MenuItemModel,
      MenuMaster: MenuMasterModel,
      MenuTenantSetting: MenuTenantSettingModel,
      Aggregator: AggregatorModel,
      CasinoProvider: CasinoProviderModel
    } = db
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'white_cliff_game_population',
        status: 1
      },
      attributes: ['id']
    })
    if (!queueProcessStatus) {
      return {
        success: false,
        message:"Queue Process Stopped."
      }
    }
    if (queueProcessStatus) cronLog.cronId = queueProcessStatus?.id

    const menuImages = {
      slots: 'tenants/2/menus/59f007d1-96d4-4532-86ed-301028176c87____Cas.png',
      live_casino: 'tenants/2/menus/db4f2d58-c62b-4498-b574-f3586f954568____baccarat01dec.png',
      virtual_sports: 'st8_images/casino_menu_images/virtual_sport_casino_menu.png',
      mini_game: 'tenants%2F1%2Fmenus%2…e54-84cf-4065-9bf7-2a6af5bed2de____Crash_game.png'
    }

    const casinoProviderId = config.get('env') === 'production' ? PROD_WHITECLIFF_PROVIDER : STAGE_WHITECLIFF_PROVIDER

    let tenants = await sequelize.query(`
      SELECT "tenant_id" AS "tenantId"
      FROM "public"."tenant_theme_settings" AS "TenantThemeSetting" WHERE '${casinoProviderId}' = ANY (string_to_array(assigned_providers, ','))
      ${reqBody?.tenantId ? ` AND tenant_id = '${reqBody?.tenantId}'` : ''}
      `,
      { type: QueryTypes.SELECT, useMaster: false })
      if(Array.isArray(reqBody?.tenantIds) && !reqBody?.tenantIds.includes('0')){
          tenants = tenants.filter(tenant => reqBody.tenantIds.includes(tenant.tenantId));
      }
    if (reqBody?.isSuperAdmin) {
      tenants = [{ tenantId: 0 }]
    } else{
        tenants = [{ tenantId: 0 }, ...tenants]
      }

    if(tenants.length<=0){
      return {
        success: true,
        message:"Provider not enabled for this tenant."
      }
    }
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
    let i = 0

    let messsage = ''
    for (const tenant of tenants) {
      const tenantId = tenant.tenantId

      let credentialTenantId = tenantId
      if (tenantId == 0) {
        credentialTenantId = 1
      }

      let allowedCurrencies = await sequelize.query(`
        SELECT  c.code,c.id
        FROM  tenant_configurations tc
        INNER JOIN currencies c
        ON c.id = ANY (STRING_TO_ARRAY(tc.allowed_currencies, ',')::BIGINT[])
        WHERE tc.tenant_id = '${credentialTenantId}'`,
        { type: QueryTypes.SELECT, useMaster: false })

      if (allowedCurrencies.length <= 0) {
        cronLog.status = CRON_LOG_STATUS.FAILED
        cronLog.errorMsg = 'whiteCliffWorker: No Allowed currencies for tenantId: ' + tenantId
        cronLog.endTime = new Date()
        await db.CronLog.create(cronLog)
        continue
      }
      for (const currencyObj of allowedCurrencies) {
        const currencyCode = currencyObj.code
        let keyCurrency = currencyCode == 'chips' ? 'LKR' : currencyCode
        const keys = {
          gameListUrl: 'WHITECLIFF_GAME_LIST_URL',
          agCode: WHITECLIFF_CREDENTIALS[keyCurrency]?.WHITECLIFF_AG_CODE,
          agToken: WHITECLIFF_CREDENTIALS[keyCurrency]?.WHITECLIFF_AG_TOKEN,
        };

        const credentials = await TenantCredentialModel.findAll({
          where: {
            key: Object.values(keys),
            tenantId: credentialTenantId
          },
          raw: true
        });

        const values = {};
        Object.keys(keys).forEach((creds) => {
          const val = credentials.find(obj => obj.key === keys[creds]);
          if (val) {
            values[creds] = val.value;
          }
        });

        if (!values.gameListUrl || !values.agCode || !values.agToken) {
          continue
        }
       if(i !== 0){
        /* Note : WhiteCliff API is rate limited to 1 request per minute
         Error Due to REQUEST_TOO_FREQUENT From WhiteCliff : Timeout Delay Min 1 minute Required
         so  added 1 minute delay Initially . Except for the first request all other requests
         will be delayed by 1 minute.
        */
        await delay(WHITECLIFF_AXIOS_DELAY)
       }
       i++

        const whiteCliffGameData = await axios({
          url: values.gameListUrl,
          method: 'post',
          headers: {
            'ag-code': values.agCode,
            'ag-token': values.agToken,
            'Content-Type': 'application/json'
          },
          data: {
            language: 'en'
          }
        })
        if (!(whiteCliffGameData?.data?.game_list && Object.keys(whiteCliffGameData?.data?.game_list)?.length)) {
          cronLog.status = CRON_LOG_STATUS.FAILED
          cronLog.errorMsg =(whiteCliffGameData.data?.error || 'No game list found' ) + ' ' + "TenantId: " + tenantId + ' ' + "currencyCode: " + currencyCode
          cronLog.endTime = new Date()
          await db.CronLog.create(cronLog)
          continue
        }

        const currentDate = new Date().toISOString();
        const sequelizeTransaction = await sequelize.transaction()
        try {

          // Data for a page model
          const whiteCliffGameDataObject = Object.entries(whiteCliffGameData?.data?.game_list || {}).reduce((acc, [prd_id, games]) => {
            games.forEach(cur => {
              const gameKey = prd_id
              const providerSelected = reqBody?.aggregator_providers?.length ? reqBody?.aggregator_providers.includes(gameKey) : false
              if (reqBody?.aggregator_providers) {
                if (!providerSelected) {
                  allTenantsSeeded = false
                  return acc
                }
              } else {
                if(!WHITECLIFF_PROVIDERS.includes(cur.prd_name)) {
                  return acc
                }
              }
              // Generate the unique uuid and tableId format as "prd_id____game_id"
              const uniqueId = `${gameKey}____${cur.game_id}`
              if (!acc.tableIds.includes(uniqueId)) {
                if (!acc.categories.includes(cur.prd_category)) {
                  acc.categories.push(cur.prd_category);
                  acc.categoriesData.push({
                    name: `${cur.prd_category}`,
                    casinoProviderId,
                    gameId: `${cur.prd_category}`,
                    createdAt: currentDate,
                    updatedAt: currentDate,
                  })
                }

                const pageName = cur.prd_name

                const combinationKey = `${pageName}-${cur.prd_category}`

                const gameData = {
                  name: cur.game_name,
                  gameId: `${cur.prd_category}`,
                  tableId: uniqueId,
                  providerId: casinoProviderId,
                  isEnabled: cur.is_enabled == 1 ? true : false,
                }

                const page = acc.pages.find(p => p.key === combinationKey);

                if (!page) {
                  acc.pages.push({
                    pageName: pageName,
                    category: cur.prd_category,
                    games: [gameData],
                    key: combinationKey
                  })
                }
                else {
                  page.games.push(gameData);
                }

                acc.casinoTables.push({
                  name: cur.game_name,
                  gameId: `${cur.prd_category}`,
                  createdAt: currentDate,
                  updatedAt: currentDate,
                  isLobby: true,
                  tableId: uniqueId,
                  providerId: casinoProviderId
                })

                acc.tableIds.push(uniqueId)

                acc.name.push(cur.game_name)
              }
            })

            return acc
          }, { pages: [], categories: [], categoriesData: [], casinoTables: [], tableIds: [], name: [] })
          const existingCategoriesData = await CasinoGameModel.findAll({
            attributes: ['gameId', 'id', 'name'],
            where: {
              gameId: {
                [Op.in]: whiteCliffGameDataObject.categories
              },
              casinoProviderId: casinoProviderId
            },
            raw: true
          })


          const newCategoriesData = whiteCliffGameDataObject.categoriesData.filter(i => !existingCategoriesData.map(i => i.gameId).includes(i.gameId))
          await CasinoGameModel.bulkCreate(newCategoriesData, { transaction: sequelizeTransaction })

          const existingCasinoTableData = await CasinoTableModel.findAll({
            attributes: ['gameId', 'tableId', 'name', 'id'],
            where: {
              tableId: {
                [Op.in]: whiteCliffGameDataObject.tableIds
              },
              providerId: casinoProviderId
            },
            raw: true
          })
          // check Name Change in existing categories
          for (const category of whiteCliffGameDataObject.categories) {
            const existingCategory = existingCategoriesData.find(i => i.gameId === category)
            if (existingCategory) {
              const categoryData = whiteCliffGameDataObject.categoriesData.find(i => i.gameId === category)
              if (categoryData.name !== existingCategory.name) {
                await CasinoGameModel.update(
                  { name: categoryData.name },
                  { where: { id: existingCategory.id }, transaction: sequelizeTransaction }
                )
                existingCategory.name = categoryData.name
              }
            }

          }
          for (const table of whiteCliffGameDataObject.casinoTables) {
            const existingCasinoTable = existingCasinoTableData.find(i => i.tableId === table.tableId)
            if (existingCasinoTable) {
              if (table.name !== existingCasinoTable.name) {
                await CasinoTableModel.update(
                  { name: table.name },
                  { where: { id: existingCasinoTable.id }, transaction: sequelizeTransaction }
                )
              }
            }
          }



          const newCasinoTablesData = whiteCliffGameDataObject.casinoTables.filter(i => !existingCasinoTableData.map(i => i.tableId).includes(i.tableId))
          await CasinoTableModel.bulkCreate(newCasinoTablesData, { transaction: sequelizeTransaction })

          for (const { pageName, category, games } of whiteCliffGameDataObject.pages) {
            let searchCategory = '';
            let menuImage = ''

            if (['Live Casino'].includes(category)) {
              searchCategory = 'Live Casino';
              menuImage = menuImages.live_casino
            } else if (['Sports & Esports'].includes(category)) {
              searchCategory = 'Virtual Sports';
              menuImage = menuImages.virtual_sports
            } else if (['Mini Game'].includes(category)) {
              searchCategory = 'casino'; // For Slots and Mini Game
              menuImage = menuImages.mini_game
            } else if (['Slots'].includes(category)) {
              searchCategory = 'casino'; // For Slots and Mini Game
              menuImage = menuImages.slots
            }

            if (!searchCategory) {
              continue
            }

            let menuMaster = await MenuMasterModel.findOne({
              raw: true,
              attributes: ['id'],
              where: {
                name : Sequelize.literal(`LOWER("name") = LOWER('${searchCategory.replace(/_/g, ' ')}')`)
               },
              transaction: sequelizeTransaction,
            });

            if (!menuMaster) {
              menuMaster = await MenuMasterModel.create(
                {
                  name: searchCategory,
                  path: `/${searchCategory.toLowerCase()}`,
                  active: true,
                  component: 'Casino',
                  component_name: searchCategory.toLowerCase(),
                  createdAt: currentDate,
                  updatedAt: currentDate
                },
                { transaction: sequelizeTransaction }
              );
            }

            let menuTenantSetting
            if (tenantId != 0) {
              menuTenantSetting = await MenuTenantSettingModel.findOne({
                where: {
                  tenant_id: tenantId,
                  menu_id: menuMaster.id
                },
                attributes: ['id'],
                transaction: sequelizeTransaction,
                raw: true
              });

              if (!menuTenantSetting) {
                menuTenantSetting = await MenuTenantSettingModel.create(
                  {
                    menu_id: menuMaster.id,
                    tenant_id: tenantId,
                    createdAt: currentDate,
                    updatedAt: currentDate
                  },
                  { transaction: sequelizeTransaction }
                );
              }
            } else {
              menuTenantSetting = menuMaster
            }
            let casinoPage = await getCasinoProvider(pageName, casinoProviderId, tenantId, menuTenantSetting.id, sequelizeTransaction, currentDate, currencyObj, WHITECLIFF_ICON, allTenantsSeeded)

            if (!(casinoPage.allowedCurrencies.includes(Number(currencyObj.id)))) {
              await PageModel.update(
                {
                  allowedCurrencies: Sequelize.fn('array_append', Sequelize.col('allowed_currencies'), Number(currencyObj.id))
                },
                {
                  where: {
                    id: casinoPage.id
                  },
                  transaction: sequelizeTransaction
                }
              );
            }

            let casinoMenu = await CasinoMenuModel.findOne({
              raw: true,
              attributes: ['id', 'name'],
              where: {
              name:  Sequelize.literal(`LOWER("name") = LOWER('${category.replace(/_/g, ' ')}')`), tenantId },
              transaction: sequelizeTransaction,
            });

            if (!casinoMenu) {
              casinoMenu = await CasinoMenuModel.create(
                {
                  name: category,
                  enabled: true,
                  tenantId,
                  imageUrl: menuImage,
                  createdAt: currentDate,
                  updatedAt: currentDate,
                },
                { transaction: sequelizeTransaction }
              );
            }

            let pageMenu = await PageMenuModel.findOne({
              raw: true,
              where: { pageId: casinoPage.id, casinoMenuId: casinoMenu.id },
              attributes: ['id', 'name'],
              transaction: sequelizeTransaction,
            });

            if (!pageMenu) {
              pageMenu = await PageMenuModel.create(
                {
                  name: casinoMenu.name,
                  pageId: casinoPage.id,
                  casinoMenuId: casinoMenu.id,
                  enabled: true,
                  createdAt: currentDate,
                  updatedAt: currentDate
                },
                { transaction: sequelizeTransaction }
              );
            }

            for (const game of games) {
              const { name, tableId, providerId, isEnabled } = game;

              let casinoItem = await CasinoItemModel.findOne({
                raw: true,
                attributes: ['id', 'name', 'active'],
                where: {
                  uuid: tableId,
                  tenantId,
                  provider: '' + providerId,
                },
                transaction: sequelizeTransaction,
              });

              if (!casinoItem) {
                casinoItem = await CasinoItemModel.create({
                  uuid: tableId,
                  name: name,
                  image: `provider-images/whitecliff/thumbnail/${tableId}.webp`,
                  provider: casinoProviderId,
                  active: isEnabled,
                  featured: true,
                  tenantId,
                  createdAt: currentDate,
                  updatedAt: currentDate,
                },
                  { transaction: sequelizeTransaction })
              }
              else if (casinoItem.active != isEnabled) {
                await CasinoItemModel.update(
                  { active: isEnabled },
                  { where: { id: casinoItem.id }, transaction: sequelizeTransaction }
                );
                await MenuItemModel.update(
                  { active: isEnabled },
                  { where: { casinoItemId: casinoItem.id }, transaction: sequelizeTransaction }
                );
              }
              // check name tobe updated
              if (casinoItem.name.toLowerCase() !== name.toLowerCase()) {
                await CasinoItemModel.update(
                  { name: name },
                  { where: { id: casinoItem.id }, transaction: sequelizeTransaction }
                );
              }

              let menuItemData = await MenuItemModel.findAll({
                raw: true,
                attributes: ['id', 'active', 'name', 'active', 'pageMenuId', 'casinoItemId'],
                where: { casinoItemId: casinoItem.id },
                transaction: sequelizeTransaction,
              });

              const currentPageMenuId = menuItemData.find(item => item.pageMenuId === pageMenu.id);
              if (!currentPageMenuId) {
               let menuItem = await MenuItemModel.create({
                  name: casinoItem.name,
                  pageMenuId: pageMenu.id,
                  casinoItemId: casinoItem.id,
                  active: isEnabled,
                  createdAt: currentDate,
                  updatedAt: currentDate
                }, { transaction: sequelizeTransaction });
                menuItemData.push(menuItem)
              }

              for(const menuItem of menuItemData) {
                if (menuItem.name.toLowerCase() !== name.toLowerCase()) {
                  await MenuItemModel.update(
                    { name: name,  active: isEnabled, },

                    { where: { id: menuItem.id }, transaction: sequelizeTransaction }
                  );
                  menuItem.name = name
                }
              }
              let menuItemsNotFromCurrentCasinoItem = menuItemData.filter(item => item.pageMenuId !==  pageMenu.id);

              if (menuItemsNotFromCurrentCasinoItem.length) {
                let menuListToBeDeleted = await sequelize.query(`
                SELECT "MenuItem".id
                FROM "public"."menu_items" AS "MenuItem"
                JOIN public.page_menus on "MenuItem".page_menu_id = page_menus.id
                WHERE "MenuItem"."id" IN (:menuids) and page_id in (:pageIds)`, {
                  replacements: {
                    menuids: menuItemsNotFromCurrentCasinoItem.map(item => item.id),
                    pageIds: [casinoPage.id]
                  },
                  type: QueryTypes.SELECT,
                  transaction: sequelizeTransaction
                })

                let allPageMenu = menuItemsNotFromCurrentCasinoItem.map(item => item.pageMenuId)
                let allAvailablePageMenu = await PageMenuModel.findAll({
                  attributes: ['id'],
                  where: {
                    id: {
                      [Op.in]: allPageMenu
                    }
                  },
                  raw: true
                })
                let allAvailablePageMenuIds = allAvailablePageMenu.map(item => item.id)
                let allPageMenuIds = allPageMenu.filter(item => !allAvailablePageMenuIds.includes(item))
                let allPageMenuIdsToBeDeleted = menuItemsNotFromCurrentCasinoItem.filter(item => allPageMenuIds.includes(item.pageMenuId))
                menuListToBeDeleted.push(...allPageMenuIdsToBeDeleted)

                // only delete the menu items that are belonging to the current page
                if (menuListToBeDeleted.length) {
                  await MenuItemModel.destroy({
                    where: {
                      id: {
                        [Op.in]: menuListToBeDeleted.map(item => item.id)
                      },
                      casinoItemId: casinoItem.id

                    },
                    transaction: sequelizeTransaction
                  })
                }
              }


            }

          }

          await sequelizeTransaction.commit()

        } catch (error) {
          await sequelizeTransaction.rollback()
          cronLog.status = CRON_LOG_STATUS.FAILED
          cronLog.errorMsg = (error?.message || '') + ' ' + "TenantId: " + tenantId + ' ' + "currencyCode: " + currencyCode
          cronLog.endTime = new Date()
          await db.CronLog.create(cronLog)
          Logger.info(error, '=========WhiteCliff game population error==========\n')
          continue

        }
        messsage = 'Game Seeding Completed'
      }

      // disable the category if all games in that category are inactive
      await sequelize.query(
        ` UPDATE casino_menus cm
        SET enabled = subquery.result
        FROM (
          SELECT
            ct.game_id AS game_category,
            CASE
              WHEN COUNT(*) = COUNT(CASE WHEN ci.active = false THEN 1 END) THEN false
              ELSE true
            END AS result
          FROM
            casino_tables ct
          JOIN
            casino_items ci ON ct.table_id = ci.uuid
          WHERE
            ci.provider = '${casinoProviderId}'
            AND ci.tenant_id = '${tenantId}' AND ct.provider_id = '${casinoProviderId}'
          GROUP BY
            ct.game_id
        ) AS subquery
        WHERE cm.name = subquery.game_category AND cm.tenant_id = '${tenantId}';
      `
      )
    }
    messsage = 'Game Seeding Completed'
    delete cronLog.errorMsg
    cronLog.status = CRON_LOG_STATUS.SUCCESS
    cronLog.endTime = new Date()
    await db.CronLog.create(cronLog)
    return {
      success: true,
      messsage: messsage
    }
  } catch (error) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = error.message || null
    cronLog.endTime = new Date()
    await db.CronLog.create(cronLog)
    Logger.info(error, '=========WhiteCliff game population error==========\n')
    return {
      success: false,
      Error: {
        stack: error.stack
      }
    }
  }
}
