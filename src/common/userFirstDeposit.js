import db from "../db/models"
export const userFirstDeposit = async (sequelizeTransaction,transaction) => {


  const firstDeposit = await db.UserFirstDeposit.findOne({
    where: { userId: transaction.actioneeId, tenantId: transaction.tenantId },
    raw: true,
    transaction: sequelizeTransaction
  })
  if(!firstDeposit){
    const userFirstDeposit = {
      userId: transaction.actioneeId,
      tenantId: transaction.tenantId,
      firstDepositId: transaction.id,
      firstDepositDate: transaction.createdAt,
      amount: transaction.amount
    }
    await db.UserFirstDeposit.create(userFirstDeposit, { transaction: sequelizeTransaction })
  }
  return true
}
