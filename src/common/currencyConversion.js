import { Op } from 'sequelize'
import db from '../db/models'
/**
 * This function will reduce the rollover target for the user as it places the bet.
 * @export
 * @param {object} context The argument object
 * @param {Transaction} transactionObject The argument object
 * @param {User} user contains user object
 * @return {TransactionObject}
 */
export default async (transactionObject, user, amountMain) => {


  const currencyAllowedCurrencies = await db.TenantConfiguration.findOne({
    where: { tenantId: user.tenantId },
    attributes: ['allowedCurrencies']
  })
  const currencyAllAllowed = await db.Currency.findAll({
    where: { id: { [Op.in]: currencyAllowedCurrencies?.allowedCurrencies?.split(',') } }
  })

  const exMainTransactionCurrency = user.Wallet.currencyId
  const otherCurrencyAmount = {}
  const currencyExchangeRate = await db.Currency.findOne({ where: { id: exMainTransactionCurrency } })

  for (const record of currencyAllAllowed) {
    if (exMainTransactionCurrency !== record.id) {
      const exchangeRate = await db.Currency.findOne({ where: { id: record.id }})
      const convertedAmountOther = (parseFloat(amountMain) * (exchangeRate.exchangeRate / currencyExchangeRate.exchangeRate)).toFixed(4)
      otherCurrencyAmount[record.code] = parseFloat(convertedAmountOther)
    } else {
      otherCurrencyAmount[record.code] = parseFloat(amountMain)
    }
  }

  transactionObject = { ...transactionObject, otherCurrencyAmount: JSON.stringify(otherCurrencyAmount) }
  return transactionObject
}
