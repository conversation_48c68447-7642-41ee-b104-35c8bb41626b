import moment from 'moment';
import np from 'number-precision';
import { Op, Sequelize } from 'sequelize';
import { v4 as uuidv4 } from 'uuid';
import { walletLocking } from '../common/walletLocking';
import config from '../configs/app.config';
import db, { sequelize } from '../db/models';
import { CRON_LOG_STATUS, PROD_BOT_ACTIVITY_TENANTS, STAGE_BOT_ACTIVITY_TENANTS, TRANSACTION_TYPES } from './constants';
import { numberPrecision } from './helpers';
import v3CurrencyConversion from './v3CurrencyConversion';

const gameWiseWinRatios = {
  '1000021': [0.95, 11], // 'Speed Baccarat A'
  '1000104': [1, 2, 5, 8, 11, 17, 35], // 'Speed Roulette'
  '130': [1], // 'Super 6 Baccarat'
  '221002': [1, 2, 5, 8, 11, 17, 35], //  'Speed Auto Roulette'
  '1000103': [1, 2, 5, 8, 11, 17, 35], //  'Speed Auto Roulette'
}

export default async (reqData) => {
  const cronLog = {}
  cronLog.startTime = new Date()
  let cronData = {}
  let cronCreated;
  let ggrSummary = null
  const responseObject = {
    success: false,
  }

  let initialGGR;

  let currentTotalDebitAmount = 0
  let currentTotalCreditAmount = 0
  let currentTotalRollbackAmount = 0
  let currentGGR = 0;

  try {
    const {
      TenantSetting: TenantSettingsModel,
      BotUser: BotUserModel,
      CasinoTable: CasinoTableModel,
      Wallet: WalletModel,
      Transaction: TransactionModel,
      TenantGGRSummary: TenantGGRSummaryModel,
      AuditLog: AuditLogModel,
    } = db

    const { startDate, endDate } = todaysStartAndEndDate()

    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'bot_transactions_process',
        status: 1
      },
      attributes: ['id']
    })
    if (!queueProcessStatus) {
      await TenantGGRSummaryModel.update({
        status: 4,
        message: "Main cron stopped.",
      }, {
        where: {
          status: 1,
          createdAt: {
            [Op.between]: [startDate, endDate]
          }
        },
        raw: true,
      })
      return { success: false, message: 'Main cron stopped.' }
    }

    cronLog.cronId = queueProcessStatus?.id

    let whereClause = {
      status: 1,
      createdAt: {
        [Op.between]: [startDate, endDate]
      }
    }

    // Directly Run from API for a specific summary
    if (reqData?.summaryId) {
      whereClause = {
        id: reqData?.summaryId
      }
    }

    ggrSummary = await TenantGGRSummaryModel.findOne({
      where: whereClause,
      raw: true,
      order: [['createdAt', 'desc']],
    })

    if (!ggrSummary) {
      responseObject.data = {
        message: "No GGR Summary"
      }
      return responseObject
    }
    const tenantId = parseFloat(ggrSummary.tenantId)

    const istNow = moment().tz("Asia/Kolkata");
    const startOfTodayIST = istNow.startOf("day").toDate();

    let botTransactionAuditLog = await AuditLogModel.findOne({
      where: {
        tenantId: tenantId,
        eventType: 'BotTransactionProcess',
        createdAt: { [Op.lt]: startOfTodayIST }
      },
      order: [["createdAt", "DESC"]],
      attributes: ['id', 'modifiedData', 'updatedAt'],
      raw: true
    })

    if (!botTransactionAuditLog) {
      await updateGgrSummary(ggrSummary, null, null, true, 'Tenant BOT_TRANSACTION_PROCESS status not found')
      return { success: true, message: "Tenant BOT_TRANSACTION_PROCESS status not found." }
    }

    const modifiedData = botTransactionAuditLog.modifiedData;

    const isCronLastStatusOn = modifiedData?.value == 1 ? true : false

    const isUpdatedDaysSummary = checkIsUpdatedDaysSummary(botTransactionAuditLog.updatedAt, ggrSummary.fromDate);

    // If cron is stopped and not updated Today then we have to skip ggr Change for the resepective summary.
    if ((!isCronLastStatusOn && !isUpdatedDaysSummary) || (isCronLastStatusOn && isUpdatedDaysSummary)) {
      await updateGgrSummary(ggrSummary, null, null, true, 'Tenant BOT_TRANSACTION_PROCESS Stopped');
      return { success: true, message: "Tenant BOT_TRANSACTION_PROCESS Stopped" };
    }

    const botActivityTenants = config.get('env') === 'production' ? PROD_BOT_ACTIVITY_TENANTS : STAGE_BOT_ACTIVITY_TENANTS;
    if (!botActivityTenants.includes(tenantId)) {
      responseObject.data = {
        message: "Bot activity disabled for this tenant"
      }
      return responseObject
    }

    initialGGR = ggrSummary.ggr;

    cronData.initialGGR = initialGGR

    currentGGR = initialGGR;
    currentTotalDebitAmount = ggrSummary.totalDebitAmount
    currentTotalCreditAmount = ggrSummary.totalCreditAmount
    currentTotalRollbackAmount = ggrSummary.totalRollbackAmount

    cronData.currentGGR = currentGGR
    cronData.totalDrAmount = currentTotalDebitAmount
    cronData.totalCrAmount = currentTotalCreditAmount
    cronData.totalRbAmount = currentTotalRollbackAmount

    cronData.message = "Cron Started"
    cronLog.errorMsg = JSON.stringify(cronData)
    cronCreated = await db.CronLog.create(cronLog)

    const creds = await TenantSettingsModel.findAll({
      attributes: ['key', 'value'],
      where: {
        key: ['BOT_GGR_CHANGE', 'BOT_PROVIDERS', 'BOT_PLAYERS', 'MAXIMUM_BOT_TRANSACTIONS'],
        type: 4,
        tenantId
      },
      raw: true
    })

    const allCreds = creds.reduce((creds, el) => {
      creds[`${el.key}`] = el.value
      return creds
    }, {})

    const playersCount = allCreds.BOT_PLAYERS;

    let allBotUsers = await sequelize.query(
      `SELECT bu.user_id
       FROM bot_users bu
       JOIN Users u ON bu.user_id = u.id
       JOIN wallets w ON bu.user_id = w.owner_id
       WHERE bu.tenant_id = :tenantId
       AND u.active = true AND u.tenant_id = :tenantId
       AND (w.amount + w.non_cash_amount) >= 100
       LIMIT :playersCount;`,
      {
        replacements: { tenantId, playersCount },
        type: Sequelize.QueryTypes.SELECT
      }
    );

    allBotUsers = allBotUsers.map(el => { return el.user_id })

    const pageIds = allCreds.BOT_PROVIDERS.toString().split(',');

    if (pageIds.length == 0) {
      cronData.message = 'No PageIds'
      await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)
      responseObject.data = cronData
      return responseObject;
    }

    const reqQuery = `
      SELECT DISTINCT CAST(casino_items.provider AS INT) AS provider_id
      FROM pages
      LEFT JOIN page_menus ON pages.id = page_menus.page_id
      LEFT JOIN menu_items ON page_menus.id = menu_items.page_menu_id
      LEFT JOIN casino_items ON menu_items.casino_item_id = casino_items.id
      WHERE pages.id IN (:pageIds);
    `;
    let botProviders = []

    const results = await sequelize.query(reqQuery, {
      replacements: { pageIds }, // Ensure pageIds is an array
      type: Sequelize.QueryTypes.SELECT,
    });
    if (!results) {
      cronData.message = 'No provider query results.'
      await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)
      responseObject.data = cronData
      return responseObject;
    }

    botProviders = results.map(row => row.provider_id)

    if (botProviders.length == 0) {
      cronData.message = 'No Providers Found.'
      await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)
      responseObject.data = cronData
      return responseObject;
    }

    // Static games belong to these providers only
    botProviders = [1, 2]

    let allGames = await CasinoTableModel.findAll({
      where: {
        providerId: {
          [Op.in]: botProviders
        },
        tableId: {
          [Op.in]: ['1000021', '1000104', '130', '221002', '1000103']
        }
      },
      attributes: ['gameId', 'tableId', 'providerId'],
      raw: true
    })

    if (allGames.length == 0) {
      cronData.message = 'No Games Found'
      await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)
      responseObject.data = cronData
      return responseObject;
    }

    allGames = shuffleArray(allGames)

    if (ggrSummary.totalDebitTrxns == 0) {
      cronData.message = 'No debit transactions.'
      await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)
      responseObject.data = cronData
      return responseObject;
    }

    let rollbackPercentage = ggrSummary.totalRollbackTrxns / ggrSummary.totalDebitTrxns;

    // Maximum 20%
    rollbackPercentage = Math.min(rollbackPercentage, 0.2)

    // Define min and max values for Debit and Credit range
    let minDebitAmount = ggrSummary.minDebitAmount >= 100 ? ggrSummary.minDebitAmount : 100;
    let maxDebitAmount = ggrSummary.maxDebitAmount;
    let minCreditAmount = ggrSummary.minCreditAmount;
    let maxCreditAmount = ggrSummary.maxCreditAmount;

    if (minCreditAmount == maxCreditAmount) {
      minCreditAmount = 0
    }

    if (minDebitAmount == maxDebitAmount) {
      minDebitAmount = maxDebitAmount / 2
    }

    const maxBotTransactions = parseFloat(allCreds.MAXIMUM_BOT_TRANSACTIONS) || 1
    // Maximum 20% rollback transacations allowed.
    const maxRollbackTransactions = maxBotTransactions * 0.2;

    let ggrPercentage = parseFloat(allCreds.BOT_GGR_CHANGE) || 0;

    if (ggrPercentage == 0) {
      cronData.message = 'No GGR percentage.'
      await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)
      responseObject.data = cronData
      return responseObject;
    }

    const { targetGGR, tolerance } = getTargetGGR(initialGGR, ggrPercentage)

    let noOfTransactionsAdded = 0;
    let totalRollbackTrxns = 0
    cronData.targetGGR = targetGGR
    cronData.error = ''
    cronData.message = ''

    let isTargetReached = checkIsTargetGGRAchieved(currentGGR, targetGGR, tolerance, cronData);

    let userId;
    let gameData;
    let currentUsersTransactions = 0;

    let lastUserId;
    let lastGameId;
    let lastBetAmount;
    let gameChangeGapInSeconds = 0;

    // Predefine random transaction limits
    const transactionLimits = Array.from({ length: 20 }, () => Math.floor(Math.random() * 20) + 20);

    let currentLimitIndex = 0; // Track which limit we are using

    const usersLastCreditTimes = {}

    let maxAllowedDeviation = Math.abs(initialGGR * ((ggrPercentage + 5) / 100));

    // This block to control the process if not gets complted within few minutes then reject
    const startTime = Date.now();
    const maxDuration = 4 * 60 * 1000; // Maximum time this function should run for a query

    while (!isTargetReached) {
      // Sleep here to avoid frequent sequelize transactions
      await sleep(100)

      if (allBotUsers.length <= 0) {
        cronData.message = 'Bot Users Finished'
        break
      }

      if (totalRollbackTrxns >= maxRollbackTransactions) {
        cronData.message = 'Maximum Rollback Transactions Reached.'
        break
      }

      if (Date.now() - startTime > maxDuration) {
        cronData.message = 'Maximum Iterations Reached.'
        break
      }

      const sequelizeTransaction = await sequelize.transaction()
      try {
        gameChangeGapInSeconds = 0

        if (!userId || !gameData || currentUsersTransactions >= transactionLimits[currentLimitIndex] || !allBotUsers.includes(userId)) {
          userId = allBotUsers[Math.floor(Math.random() * allBotUsers.length)];
          gameData = allGames[Math.floor(Math.random() * allGames.length)];

          // Move to the next limit in the array (loop back if at the end)
          currentLimitIndex = (currentLimitIndex + 1) % transactionLimits.length;

          currentUsersTransactions = 0; // Reset transaction count

          // If user is same and game is Changed then create a manual gap of 1 minute.
          if (lastUserId == userId && lastGameId != gameData.tableId) {
            gameChangeGapInSeconds = 60
          }

          lastUserId = userId
          lastGameId = gameData.tableId
        }

        const gameId = gameData.gameId
        const tableId = gameData.tableId
        const debitTransactionID = uuidv4().replace(/-/g, '')
        const creditTransactionID = uuidv4().replace(/-/g, '')
        const rollbackTransactionID = uuidv4().replace(/-/g, '')
        const providerId = gameData.providerId
        const roundId = `${cronCreated.id}____` + uuidv4().replace(/-/g, '')

        const query = `SELECT * FROM get_auth_user_information_by_user_id(:userId, '');`
        const [user] = await sequelize.query(query, {
          replacements: { userId },
          type: sequelize.QueryTypes.SELECT,
          useMaster: false
        })

        // Function to generate random transaction (debit, credit, or rollback)
        const transaction = generateTransaction(minDebitAmount, maxDebitAmount, minCreditAmount, maxCreditAmount, rollbackPercentage, currentGGR, targetGGR, tableId);
        // ---------------------------Debit Transacation Starts here------------------
        let betAmount = transaction.debitAmount;
        let wonAmount = transaction.creditAmount;

        // Case added by observing 1 summary on stage.
        // if (minCreditAmount == 0 && maxCreditAmount == 0 && wonAmount != 0) {
        //   wonAmount = maxDebitAmount * 0.75 + wonAmount
        // }

        if (betAmount < 100) {
          await sequelizeTransaction.rollback()
          continue;
        }

        if (betAmount == 0) {
          await sequelizeTransaction.rollback()
          continue;
        }
        const gameRatios = gameWiseWinRatios[tableId]

        if (betAmount == wonAmount && !gameRatios.includes(1)) {
          await sequelizeTransaction.rollback()
          continue;
        }

        let debitCashAmount = 0;
        let debitNonCashAmount = 0;

        const userWallet = await walletLocking(user, sequelizeTransaction)
        await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })

        let walletNonCashAmount = parseFloat(userWallet.nonCashAmount).toFixed(5)
        let walletCashAmount = parseFloat(userWallet.amount).toFixed(5)

        let totalUserWalletAmount = np.plus(walletNonCashAmount, walletCashAmount)

        if (totalUserWalletAmount < 100) {
          allBotUsers = allBotUsers.filter(el => el !== userId);
          userId = null
          gameData = null
          await sequelizeTransaction.rollback()
          continue
        }

        let totalDebitAmountWillBe = currentTotalDebitAmount + betAmount;
        let totalCreditAmountWillBe = currentTotalCreditAmount + (transaction.result != 'rollback' ? wonAmount : 0);
        let totalRollbackAmountWillBe = currentTotalRollbackAmount + (transaction.result == 'rollback' ? betAmount : 0);

        let ggrWillBe = totalDebitAmountWillBe - totalRollbackAmountWillBe - totalCreditAmountWillBe;

        // If inital GGR was positive then changed ggr can not be negative
        if (initialGGR > 0) {
          if (ggrWillBe < 0) {
            await sequelizeTransaction.rollback()
            continue
          }
          else if (ggrWillBe > initialGGR) { // Rejecting if more than current GGR
            await sequelizeTransaction.rollback()
            continue
          }
        }

        // If inital GGR was negative then changed ggr can not be positive
        if (initialGGR < 0) {
          if (ggrWillBe > 0) {
            await sequelizeTransaction.rollback()
            continue
          }
          else if (ggrWillBe < initialGGR) {  // Rejecting more  negative GGR than current
            await sequelizeTransaction.rollback()
            continue
          }
        }

        // As we are decreasing ggr so ggr shoul not be greator then initial ggr

        // Dynamically calculate max allowed deviation based on ggrPercentage

        // Check if the new GGR goes beyond acceptable range
        let ggrDifference = Math.abs(Math.abs(ggrWillBe) - Math.abs(targetGGR));

        if (ggrDifference > Math.abs(maxAllowedDeviation)) {
          await sequelizeTransaction.rollback();
          continue;
        }

        let isTargetWillBeAchieved = false

        isTargetWillBeAchieved = checkIsTargetGGRAchieved(ggrWillBe, targetGGR, tolerance, cronData);

        let isSameBetWinAmount = false

        if (betAmount == wonAmount) {
          isSameBetWinAmount = true
        }

        betAmount = betAmount < 100 ? 100 : betAmount

        let roundedBetAmount = betAmount
        if (betAmount < 2000) {
          roundedBetAmount = Math.round(betAmount / 100) * 100; // Round to nearest 100
        } else if (betAmount < 5000) {
          roundedBetAmount = Math.round(betAmount / 500) * 500; // Round to nearest 500
        } else {
          roundedBetAmount = Math.round(betAmount / 1000) * 1000; // Round to nearest 1000
        }

        if (roundedBetAmount > totalUserWalletAmount) {
          roundedBetAmount = totalUserWalletAmount - (totalUserWalletAmount % 100)
        }

        betAmount = roundedBetAmount

        if (betAmount == lastBetAmount) {
          await sequelizeTransaction.rollback();
          continue;
        }

        lastBetAmount = betAmount

        // won amount can't be nagative
        wonAmount = Math.max(0, wonAmount);

        if (isSameBetWinAmount) {
          wonAmount = betAmount
        }

        // Ensure wonAmount is rounded properly
        wonAmount = Math.round(wonAmount / 10) * 10;

        const timestamps = getTimeStamps(ggrSummary.fromDate, ggrSummary.toDate, userId, usersLastCreditTimes, gameChangeGapInSeconds);

        const betTime = timestamps.createdAt
        const crediTime = timestamps.updatedAt

        // If it is end time transaction of user then remove user from loop
        if (timestamps.isEndTimeTransaction) {
          allBotUsers = allBotUsers.filter(el => el !== userId);
        }

        let transactionObject = {
          actioneeId: userId,
          actioneeType: 'User',
          tenantId: tenantId,
          gameId: gameId,
          success: true,
          roundId: '' + roundId,
          status: 'success',
          seatId: tableId,
          tableId: tableId,
          conversionRate: +user.exchangeRate,
          providerId: providerId,
          createdAt: betTime,
          updatedAt: betTime
        }

        const trxnBetAmount = betAmount;
        const trxnWonAmount = wonAmount;

        const allTransactions = [];

        let debitNonCashTransactionObj = null
        let debitCashTransacationObj = null

        let creditCashTransactionObj = null;
        let creditNonCashTransactionObj = null;

        let rollbackNonCashTransactionObj = null;
        let rollbackCashTransactionObj = null;

        if (walletNonCashAmount > 0) {
          debitNonCashAmount = parseFloat(betAmount) > (+walletNonCashAmount) ? walletNonCashAmount : parseFloat(betAmount)

          betAmount = parseFloat(betAmount) > (+walletNonCashAmount) ? np.minus(parseFloat(betAmount), walletNonCashAmount) : 0
          debitNonCashTransactionObj = {
            ...transactionObject,
            transactionType: TRANSACTION_TYPES.DEBIT_NO_CASH,
            transactionId: debitTransactionID,
            amount: debitNonCashAmount,
            sourceWalletId: +userWallet.id,
            sourceCurrencyId: +userWallet.currencyId
          }

          walletNonCashAmount = np.minus(walletNonCashAmount, debitNonCashAmount)
        }

        if (betAmount > 0) {
          debitCashAmount = betAmount

          debitCashTransacationObj = {
            ...transactionObject,
            transactionType: TRANSACTION_TYPES.DEBIT,
            transactionId: debitTransactionID,
            sourceWalletId: +userWallet.id,
            sourceCurrencyId: +userWallet.currencyId,
            amount: debitCashAmount
          }

          walletCashAmount = np.minus(walletCashAmount, parseFloat(debitCashAmount))
        }

        if (debitNonCashTransactionObj) {

          debitNonCashTransactionObj = {
            ...debitNonCashTransactionObj,
            sourceBeforeBalance: np.plus(walletNonCashAmount, debitNonCashAmount),
            sourceAfterBalance: walletNonCashAmount,
          }
          debitNonCashTransactionObj = await v3CurrencyConversion(sequelizeTransaction, debitNonCashTransactionObj, user?.currencyId, tenantId, debitNonCashTransactionObj.amount)
        }

        if (debitCashTransacationObj) {
          debitCashTransacationObj = {
            ...debitCashTransacationObj,
            sourceBeforeBalance: np.plus(walletCashAmount, debitCashAmount),
            sourceAfterBalance: walletCashAmount,
          }
          debitCashTransacationObj = await v3CurrencyConversion(sequelizeTransaction, debitCashTransacationObj, user?.currencyId, tenantId, debitCashTransacationObj.amount)
        }
        // -----------------------------------------Debit End--------------------------------------------------------------

        transactionObject.createdAt = crediTime
        transactionObject.updatedAt = crediTime
        if (transaction.result === 'credit') {

          let creditNonCashAmount = 0;
          let creditCashAmount = 0;

          transactionObject.transactionId = creditTransactionID

          if (debitNonCashTransactionObj) {
            creditNonCashAmount = numberPrecision(wonAmount) ? Math.min(numberPrecision(wonAmount), numberPrecision(debitNonCashAmount)) : 0
            wonAmount = wonAmount ? numberPrecision(wonAmount - creditNonCashAmount) : 0
            creditNonCashTransactionObj = {
              ...transactionObject,
              transactionType: TRANSACTION_TYPES.CREDIT_NO_CASH,
              transactionId: creditTransactionID,
              debitTransactionId: debitTransactionID,
              amount: creditNonCashAmount,
              targetBeforeBalance: walletNonCashAmount,
              targetAfterBalance: walletNonCashAmount + creditNonCashAmount,
              targetWalletId: +userWallet.id,
              targetCurrencyId: +userWallet.currencyId,
            }

            walletNonCashAmount = np.plus(walletNonCashAmount, creditNonCashAmount)

            creditNonCashTransactionObj = await v3CurrencyConversion(sequelizeTransaction, creditNonCashTransactionObj, user?.currencyId, tenantId, creditNonCashTransactionObj.amount)

          }

          if (!creditNonCashTransactionObj || wonAmount > 0) {
            creditCashAmount = wonAmount
            creditCashTransactionObj = {
              ...transactionObject,
              transactionType: TRANSACTION_TYPES.CREDIT,
              transactionId: creditTransactionID,
              debitTransactionId: debitTransactionID,
              amount: creditCashAmount,
              targetBeforeBalance: walletCashAmount,
              targetAfterBalance: walletCashAmount + creditCashAmount,
              targetWalletId: +userWallet.id,
              targetCurrencyId: +userWallet.currencyId,
            }

            walletCashAmount = np.plus(walletCashAmount, creditCashAmount)

            creditCashTransactionObj = await v3CurrencyConversion(sequelizeTransaction, creditCashTransactionObj, user?.currencyId, tenantId, creditCashTransactionObj.amount)

          }
        }
        else if (transaction.result === 'rollback') {

          transactionObject.transactionId = rollbackTransactionID

          if (debitNonCashTransactionObj) {
            rollbackNonCashTransactionObj = {
              ...transactionObject,
              transactionType: TRANSACTION_TYPES.ROLLBACK_NO_CASH,
              amount: debitNonCashAmount,
              targetBeforeBalance: walletNonCashAmount,
              targetAfterBalance: walletNonCashAmount + debitNonCashAmount,
              targetWalletId: +userWallet.id,
              targetCurrencyId: +userWallet.currencyId,
            }
            walletNonCashAmount = np.plus(walletNonCashAmount, debitNonCashAmount)

            debitNonCashTransactionObj.cancelTransactionId = rollbackTransactionID
            rollbackNonCashTransactionObj = await v3CurrencyConversion(sequelizeTransaction, rollbackNonCashTransactionObj, user?.currencyId, tenantId, rollbackNonCashTransactionObj.amount)
          }

          if (debitCashTransacationObj) {
            rollbackCashTransactionObj = {
              ...transactionObject,
              transactionType: TRANSACTION_TYPES.ROLLBACK,
              amount: debitCashAmount,
              targetBeforeBalance: walletCashAmount,
              targetAfterBalance: walletCashAmount + debitCashAmount,
              targetWalletId: +userWallet.id,
              targetCurrencyId: +userWallet.currencyId,
            }
            walletCashAmount = np.plus(walletCashAmount + debitCashAmount)

            debitCashTransacationObj.cancelTransactionId = rollbackTransactionID
            rollbackCashTransactionObj = await v3CurrencyConversion(sequelizeTransaction, rollbackCashTransactionObj, user?.currencyId, tenantId, rollbackCashTransactionObj.amount)

          }
        }

        if (debitNonCashTransactionObj) {
          allTransactions.push(debitNonCashTransactionObj)
        }
        if (debitCashTransacationObj) {
          allTransactions.push(debitCashTransacationObj)
        }
        if (creditNonCashTransactionObj) {
          allTransactions.push(creditNonCashTransactionObj)
        }
        if (creditCashTransactionObj) {
          allTransactions.push(creditCashTransactionObj)
        }
        if (rollbackNonCashTransactionObj) {
          allTransactions.push(rollbackNonCashTransactionObj)
        }
        if (rollbackCashTransactionObj) {
          allTransactions.push(rollbackCashTransactionObj)
        }

        userWallet.nonCashAmount = walletNonCashAmount
        userWallet.amount = walletCashAmount

        await TransactionModel.bulkCreate(allTransactions, { transaction: sequelizeTransaction })
        await userWallet.save({ transaction: sequelizeTransaction })

        currentTotalDebitAmount += trxnBetAmount;
        currentTotalCreditAmount += (transaction.result != 'rollback' ? trxnWonAmount : 0);
        currentTotalRollbackAmount += (transaction.result == 'rollback' ? trxnBetAmount : 0);

        // Recalculate current GGR after the transaction
        currentGGR = currentTotalDebitAmount - currentTotalRollbackAmount - currentTotalCreditAmount;

        cronData.currentGGR = currentGGR
        cronData.totalDrAmount = currentTotalDebitAmount
        cronData.totalCrAmount = currentTotalCreditAmount
        cronData.totalRbAmount = currentTotalRollbackAmount

        if (transaction.result == 'rollback') {
          totalRollbackTrxns++
        }

        await sequelizeTransaction.commit()

        // If we reached the target GGR, break the loop
        if (isTargetWillBeAchieved) { // Ensure target GGR is reached with small tolerance
          isTargetReached = true
          cronData.message = 'Target GGR Achieved'
          break;
        }

        currentUsersTransactions++;
        // Stop after a set number of transactions to avoid infinite loop
        noOfTransactionsAdded++
        if (noOfTransactionsAdded >= maxBotTransactions) {
          cronData.message = 'Maximum Transaction Reached'
          break;
        }

      } catch (error) {
        await sequelizeTransaction.rollback()
        throw error
      }
    }

    cronLog.status = CRON_LOG_STATUS.SUCCESS
    await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)

    responseObject.success = true
    responseObject.message = 'Cron Completed'
    responseObject.data = cronData
    return responseObject
  } catch (error) {
    cronData.message = "Cron Failed"
    cronData.error = error.message || null
    cronLog.status = CRON_LOG_STATUS.FAILED
    await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)
    responseObject.Error = {
      stack: error.stack
    }
    return responseObject
  }
}

function generateTransaction (minDebitAmount, maxDebitAmount, minCreditAmount, maxCreditAmount, rollbackPercentage, currentGGR, targetGGR, tableId) {
  let adjustmentFactor = Math.abs(currentGGR - targetGGR) / targetGGR;
  adjustmentFactor = Math.min(adjustmentFactor, 0.5); // Limit extreme adjustments

  let betAmount = Math.round(Math.random() * (maxDebitAmount - minDebitAmount) + minDebitAmount);
  let outcome = Math.random();
  let isRollback = outcome * 3 < rollbackPercentage; // Generating Less Rollbacks
  let creditAmount = 0;

  if (isRollback) {
    return { debitAmount: betAmount, creditAmount: 0, rollbackAmount: betAmount, result: 'rollback' };
  } else {
    let winBias = Math.random();

    if (currentGGR > targetGGR) {
      if (winBias > 0.3) {
        creditAmount = betAmount + Math.round(Math.random() * (maxCreditAmount - betAmount));
      } else {
        creditAmount = Math.round(Math.random() * (maxCreditAmount - minCreditAmount) + minCreditAmount);
      }
    } else if (currentGGR < targetGGR) {
      if (winBias > 0.6) {
        creditAmount = Math.round(Math.random() * (betAmount - minCreditAmount) + minCreditAmount);
      } else {
        creditAmount = betAmount;
      }
    } else {
      creditAmount = Math.round(Math.random() * (maxCreditAmount - minCreditAmount) + minCreditAmount);
    }

    // Round Bet Amount here only
    let roundedBetAmount = betAmount
    if (betAmount < 2000) {
      roundedBetAmount = Math.round(betAmount / 100) * 100; // Round to nearest 100
    } else if (betAmount < 5000) {
      roundedBetAmount = Math.round(betAmount / 500) * 500; // Round to nearest 500
    } else {
      roundedBetAmount = Math.round(betAmount / 1000) * 1000; // Round to nearest 1000
    }

    betAmount = roundedBetAmount

    const gameRatios = gameWiseWinRatios[tableId]
    if (creditAmount > betAmount && gameRatios.length > 0) {
      const currentRatio = creditAmount / betAmount;
      // Find the largest ratio from gameRatios that is less than or equal to the current ratio
      let validRatios = gameRatios.filter(r => r >= currentRatio);

      let finalRatio = validRatios.length > 0 ? Math.min(...validRatios) : Math.max(...gameRatios);
      // Set the creditAmount to the valid ratio * betAmount
      creditAmount = betAmount * finalRatio;
    }
    else if (creditAmount > 0 && creditAmount < betAmount && gameRatios.length > 0) {

      if (tableId == '1000021') {
        creditAmount = betAmount * 0.95;
      }
      else {
        creditAmount = 0
      }
    }

    return { debitAmount: betAmount, creditAmount, rollbackAmount: 0, result: 'credit' };
  }
}

function checkIsTargetGGRAchieved (currentGGR, targetGGR, tolerance, cronData) {
  cronData.currentGGR = currentGGR
  let maxGGR = targetGGR + tolerance;
  let minGGR = targetGGR - tolerance;
  if (maxGGR > minGGR) {
    return currentGGR <= maxGGR && currentGGR >= minGGR;
  } else {
    // Handles cases where GGR is negative
    return currentGGR >= maxGGR && currentGGR <= minGGR;
  }
}

function getTargetGGR (initialGGR, ggrPercentage) {
  // In case of negative GGR we have to increase the GGR
  let targetGGR = initialGGR - (initialGGR * (ggrPercentage / 100));
  let tolerance = Math.abs(targetGGR * 0.02);
  return { targetGGR, tolerance: tolerance };
}

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))

function getTimeStamps (fromDate, toDate, userId, usersLastCreditTimes, gameChangeGapInSeconds) {
  const gapInSeconds = Math.floor(Math.random() * 6) + 15 + gameChangeGapInSeconds
  const fromTime = new Date(fromDate).getTime();
  const toTime = new Date(toDate).getTime();
  let isEndTimeTransaction = false

  let lastCreditTime = usersLastCreditTimes[userId] || fromTime;

  const randomMilliseconds = Math.floor(30 + Math.random() * 170)
  let createdAtTimestamp = lastCreditTime + gapInSeconds * 1000 + randomMilliseconds;

  if (createdAtTimestamp >= toTime) {
    isEndTimeTransaction = true
  }
  // Ensure createdAt doesn't exceed toDate
  createdAtTimestamp = Math.min(createdAtTimestamp, toTime);
  let createdAt = new Date(createdAtTimestamp);

  let updatedAt = new Date(createdAt);
  const randomSeconds = Math.floor(10 + Math.random() * 6)
  updatedAt.setSeconds(updatedAt.getSeconds() + randomSeconds);
  updatedAt.setMilliseconds(updatedAt.getMilliseconds() + Math.floor(50 + Math.random() * 201));

  // Update user's last credit time
  usersLastCreditTimes[userId] = updatedAt.getTime();

  return { createdAt, updatedAt, isEndTimeTransaction };
}

async function updateCronLog (cronCreated, cronLog, cronData, ggrSummary) {
  cronLog.errorMsg = JSON.stringify(cronData)
  cronLog.endTime = new Date()
  if (!cronCreated) {
    cronCreated = await db.CronLog.create(cronLog)
  }
  else {
    await db.CronLog.update(cronLog, {
      where: {
        id: cronCreated.id
      }
    })
  }
  await updateGgrSummary(ggrSummary, cronCreated, cronData)
}

async function updateGgrSummary (ggrSummary, cronCreated, cronData, isCronStopped = false, message = null) {

  if (!ggrSummary) {
    return;
  }

  await db.TenantGGRSummary.update({
    cronId: cronCreated?.id,
    status: isCronStopped ? 4 : cronData?.message == 'Target GGR Achieved' ? 2 : 3,
    message: message ? message : cronData?.message,
    currentGgr: cronData?.currentGGR.toFixed(3)
  }, {
    where: {
      id: ggrSummary.id
    }
  })
}

function todaysStartAndEndDate () {
  const todayStart = new Date();
  todayStart.setUTCHours(0, 0, 0, 0); // Set to 00:00:00 UTC

  // End of today in UTC
  const todayEnd = new Date(todayStart);
  todayEnd.setUTCHours(23, 59, 59, 999); // Set to 23:59:59.999 UTC
  return { startDate: todayStart, endDate: todayEnd }
}

function checkIsUpdatedDaysSummary (updatedAt, fromDate) {

  const updatedDate = new Date(updatedAt); // Parse UTC date
  const summaryFromDate = new Date(fromDate); // Parse UTC date

  // Convert both timestamps to IST (UTC+5:30)
  const IST_OFFSET = 5.5 * 60 * 60 * 1000; // 5 hours 30 minutes in milliseconds
  const updatedIST = new Date(updatedDate.getTime() + IST_OFFSET);
  const summaryIST = new Date(summaryFromDate.getTime() + IST_OFFSET);

  // Extract YYYY-MM-DD for IST date comparison
  const updatedISTDay = updatedIST.toISOString().split('T')[0];
  const summaryISTDay = summaryIST.toISOString().split('T')[0];

  // Compare dates in IST
  return updatedISTDay === summaryISTDay;
}

function shuffleArray (array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    // Swap elements at positions i and j
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}
