import db from '../db/models'
import { REFERRAL_EVENT, REFERRAL_STATUS } from '../utils/constants/constant'
import { ALLOWED_PERMISSIONS, BONUS_TYPES, DEPOSIT_REQUEST_STATUS, QUEUE_WORKER_CONSTANT } from './constants'
import Error<PERSON>ogHelper from './errorLog'

export const verifyReferralCode = async (sequelizeTransaction, transaction, status, orderId, tenantId) => {
  const {
    User: UserModel,
    QueueLog: QueueLogModel,
    TenantThemeSetting: TenantThemeSettingModel,
    ReferralSettings: ReferralSettingsModel,
    DepositRequest: DepositRequestModel,
    Referral: ReferralModel
  } = db

  try {

    const depositRequest = await DepositRequestModel.findOne({
      where: {
        id: orderId,
        tenantId: tenantId
      },
      attributes: ['id', 'userId'],
      raw: true
    })

      const tenantThemeSetting = await TenantThemeSettingModel.findOne({
        attributes: ['allowedModules'],
        where: { tenantId: tenantId },
        raw: true
      });

      if (tenantThemeSetting.allowedModules && tenantThemeSetting.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.REFERRAL_CODE)) {

        const referralSetting = await ReferralSettingsModel.findOne({
          where: { tenantId: tenantId, active: true },
          attributes: ['id', 'bonusType', 'walletType', 'event', 'minValue', 'maxValue']
        })

        if (!referralSetting) {
          return { status: false, message: 'Referral bonus is not enabled' }
        }

        let referralDetails = await ReferralModel.findOne({
          where: { tenantId: tenantId, event: REFERRAL_EVENT.FIRST_DEPOSIT, status: REFERRAL_STATUS.PENDING, refereeId: depositRequest.userId },
          attributes: ['id']
        })


        if (!referralDetails) {
          return { status: false, message: 'No pending referral bonus found' }
        }

        if (referralSetting.minValue && transaction?.amount < referralSetting.minValue && referralSetting.maxValue && transaction?.amount > referralSetting.maxValue) {
          referralDetails.status = REFERRAL_STATUS.REJECTED
          referralDetails.transactionId = transaction?.id || null
          referralDetails.transactionAmount = transaction?.amount || null
          referralDetails.comment = `Deposit amount is not in the range of Min: ${referralSetting.minValue} and Max: ${referralSetting.maxValue}`
          await referralDetails.save({ transaction: sequelizeTransaction })
          return { status: false, message: 'Deposit amount is not in the range of referral bonus' }
        }



        referralDetails.status = status === DEPOSIT_REQUEST_STATUS.COMPLETED ? REFERRAL_STATUS.IN_PROGRESS : REFERRAL_STATUS.REJECTED
        referralDetails.comment = status === DEPOSIT_REQUEST_STATUS.COMPLETED ? '' : 'Deposit request failed'
        referralDetails.transactionId = transaction.id
        referralDetails.transactionAmount = transaction.amount
        await referralDetails.save({ transaction: sequelizeTransaction })
        if (referralSetting.event == REFERRAL_EVENT.FIRST_DEPOSIT) {
          const queueLogObject = {
            type: QUEUE_WORKER_CONSTANT.BONUS,
            status: QUEUE_WORKER_CONSTANT.READY,
            ids: [{
              bonusType: BONUS_TYPES.REFERRAL_CODE,
              referralId: referralDetails.id,
              referralSettingsId: referralSetting.id
            }]
          }
          await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })
        }



        return { status: true, message: 'Referral bonus added', referral: referralDetails, referralSettingsId: referralSetting.id, event: referralSetting.event }
      } else {


        await ReferralModel.update({
          status: REFERRAL_STATUS.REJECTED,
          comment: 'Referral bonus is not enabled'
        }, {
          where: {
            event: REFERRAL_EVENT.FIRST_DEPOSIT,
            status: REFERRAL_STATUS.PENDING,
            refereeId: depositRequest.userId
          },
          transaction: sequelizeTransaction
        })
      }

  } catch (error) {
    await ErrorLogHelper.logError(error, null, '')
    return { status: false, message: 'An error occurred while adding referral bonus' }
  }


}
