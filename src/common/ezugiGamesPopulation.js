import config from '../configs/app.config'
import db from '../db/models'
import Logger from '../libs/logger'
import { EVOLUTION_SHEET, EZUGI_SHEET, NETENT_SHEET, RED_TIGER_SHEET } from './constants'
import updateGames from './updateGames'

export default async (reqBody = null) => {
  try {
    // Check if the queue process status is active
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'ezugi_evolution_game_population',
        status: 1
      },
      attributes: ['id']
    })
    if (!queueProcessStatus) {
      return {
        success: false,
      message:"Queue Process Stopped."
      }
    }

    // Extract tenantId from reqBody if available
    let tenantId = reqBody?.tenantIds
    // Create a new parameter to pass to updateGames
    let onlyMasterTenant = false
    // Get sheet IDs for different providers
    const ezugiSheetId = config.get('env') === 'production' ? EZUGI_SHEET.PROD : EZUGI_SHEET.STAGE
    const evolutionSheetId = config.get('env') === 'production' ? EVOLUTION_SHEET.PROD : EVOLUTION_SHEET.STAGE
    const redTigerSheetId = config.get('env') === 'production' ? RED_TIGER_SHEET.PROD : RED_TIGER_SHEET.STAGE
    const netentSheetId = config.get('env') === 'production' ? NETENT_SHEET.PROD : NETENT_SHEET.STAGE

    // Check if providerId is specified in reqBody
    // Run updateGames only for the specified provider
    let providerId = reqBody.providerName;
    switch (providerId) {
      case 'ezugi':
        await updateGames('', ezugiSheetId, tenantId, onlyMasterTenant, queueProcessStatus?.id,true)
        break
      case 'evolution':
        await updateGames('', evolutionSheetId, tenantId, onlyMasterTenant, queueProcessStatus?.id,true)
        break
      case 'redtiger':
        await updateGames('', redTigerSheetId, tenantId, onlyMasterTenant, queueProcessStatus?.id,true)
        break
      case 'netent':
        await updateGames('', netentSheetId, tenantId, onlyMasterTenant, queueProcessStatus?.id,true)

        break
      default:
        // If providerId doesn't match any case, run all updateGames
        await updateGames('', ezugiSheetId, tenantId, onlyMasterTenant, queueProcessStatus?.id,false)
        await updateGames('', evolutionSheetId, tenantId, onlyMasterTenant, queueProcessStatus?.id,false)
        await updateGames('', redTigerSheetId, tenantId, onlyMasterTenant, queueProcessStatus?.id,false)
        await updateGames('', netentSheetId, tenantId, onlyMasterTenant, queueProcessStatus?.id,false)
        break
    }



  } catch (error) {

    Logger.error('Error in EZUGI_EVOLUTION game population cron', error)
    return {
      success: false,
      message: 'Error in EZUGI_EVOLUTION game population cron'
    }
  }


}
