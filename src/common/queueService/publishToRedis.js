import socketServer from "../../socket-resources";
import { SUBSCRIPTION_CHANNEL } from "../constants";

export const publishToRedis = {
  publishToUserWallet: async (walletDetail) => {
    socketServer.redisClient.publish(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE, JSON.stringify(walletDetail));
  },
  publishToQueueService: async (queueDetail) => {
    socketServer.redisClient.publish(SUBSCRIPTION_CHANNEL.QUEUE_WORKER, JSON.stringify(queueDetail))
  },
  publishToPlayerNotification: async (notificationDetails) => {
    socketServer.redisClient.publish(SUBSCRIPTION_CHANNEL.PLAYER_NOTIFICATION, JSON.stringify(notificationDetails))
  }
}
