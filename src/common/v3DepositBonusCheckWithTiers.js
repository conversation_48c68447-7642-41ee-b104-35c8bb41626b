import { Op, Sequelize } from 'sequelize';
import db from '../db/models';
import { BONUS_RECURRING_STATUS, BONUS_TYPES, DEPOSIT_INSTANT_BONUS_TYPES, DEPOSIT_ROLLING_CALCULATION_METHODS, RECURRING_BONUS_TYPES, BONUS_TIER_TYPE, BONUS_TIER_CONFIG_TYPE } from './constants';
import { instantDepositBonus } from './instantDepositBonusCheck';

/**
 * Process deposit bonus with tier-based rollover system
 *
 * @param {Object} sequelizeTransaction - Sequelize transaction
 * @param {Number} amount - Deposit amount
 * @param {Object} user - User details
 * @param {Object} userWallet - User wallet
 * @param {Array} txnIds - Transaction IDs
 * @param {String} depositTxnId - Deposit transaction ID
 * @param {Object} bonus - Bonus details
 * @param {Object} userActiveDepositBonus - User's active deposit bonus
 * @returns {Promise<boolean|null>} - True if bonus applied, null otherwise
 */
export const v3DepositBonusCheckWithTiers = async (
  sequelizeTransaction,
  amount,
  user,
  userWallet,
  txnIds,
  depositTxnId,
  bonus,
  userActiveDepositBonus
) => {
  try {
    const {
      UserBonusRecurringRollover: UserBonusRecurringRolloverModel,
      DepositBonusTier: DepositBonusTierModel,
      UserDepositSequence: UserDepositSequenceModel,
    } = db;
    const isSequenceBased = bonus?.DepositBonusSetting?.tierConfigType === BONUS_TIER_CONFIG_TYPE.SEQUENCE_BASED;
    // Handle instant deposit bonus
    if (bonus.kind === BONUS_TYPES.DEPOSIT_INSTANT) {
      let result = await instantDepositBonus(
        sequelizeTransaction,
        amount,
        user,
        txnIds,
        userActiveDepositBonus,
        bonus,
        depositTxnId
      );

      if (result) {
        return true;
      }
      return null;
    }

    // Handle recurring bonus checks similar to instantDepositBonus
    if (bonus?.DepositBonusSetting?.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(bonus.kind)) {
      // Check for CUSTOM_DEPOSITS type - limited number of bonuses per day
      if (bonus?.DepositBonusSetting?.recurringBonusType === RECURRING_BONUS_TYPES.CUSTOM_DEPOSITS) {
        const today = Sequelize.literal("DATE_TRUNC('day', CURRENT_TIMESTAMP)");
        const count = await UserBonusRecurringRolloverModel.count({
          where: {
            bonusId: bonus?.id,
            userId: user?.id,
            userBonusId: userActiveDepositBonus?.id,
            createdAt: {
              [Op.gte]: today
            }
          },
          transaction: sequelizeTransaction
        });


        if (count >= bonus?.DepositBonusSetting?.customDeposits) {
          return null;
        }
      }

    }

    // Get matched tier information
    let matchedTier = null;
    let bonusToBeGiven = 0;
    
    if (isSequenceBased) {
      // SEQUENCE-BASED TIER LOGIC
      // Find or create user deposit sequence record
      let userSequence = await UserDepositSequenceModel.findOne({
        where: { 
          userId: user.id, 
          bonusId: bonus.id 
        }
      });
      
      // If no sequence record exists, create one starting at sequence 0
      if (!userSequence) {
        userSequence = await UserDepositSequenceModel.create({
          userId: user.id,
          bonusId: bonus.id,
          currentSequence: 1
        }, { transaction: sequelizeTransaction });
      } else {
        userSequence.currentSequence += 1;
        await userSequence.save({ transaction: sequelizeTransaction });
      }
     
      // Determine which sequence number to match
      let sequenceToMatch = userSequence.currentSequence;
      const applyAfterFixedNumber = bonus.DepositBonusSetting.applyToDepositSequenceGte;
      // Handle the "greater than or equal to" rule
      if (applyAfterFixedNumber && userSequence.currentSequence > applyAfterFixedNumber) {
        sequenceToMatch = applyAfterFixedNumber;
        if(amount < bonus.DepositBonusSetting.minDeposit) {
          return null; 
        }
      }
      
      // Get all tiers for this bonus and order them (since we don't have depositSequenceNumber)
      const allTiers = await DepositBonusTierModel.findAll({
        where: {
          depositBonusSettingId: bonus.DepositBonusSetting.id,
        },
        order: [['id', 'ASC']]
      });

      // Find the matching tier for this deposit sequence (1-indexed, so subtract 1 for array index)
      const tierIndex = sequenceToMatch - 1;
      if (tierIndex >= 0 && tierIndex < allTiers.length) {
        const candidateTier = allTiers[tierIndex];
        // Check if deposit amount meets the tier's minimum requirement
        if (amount >= candidateTier.minDepositAmount) {
          matchedTier = candidateTier;
          
          // Calculate bonus amount for the matched tier
          bonusToBeGiven = (amount * (matchedTier.percentage / 100)) > matchedTier.maxBonus
            ? matchedTier.maxBonus
            : +parseFloat(amount * (matchedTier.percentage / 100)).toFixed(5);
        }
      }
      if (!matchedTier) {
        return null;
      }
    } else if (bonus.DepositBonusSetting.tierType === BONUS_TIER_TYPE.TIER_2) {
      matchedTier = await DepositBonusTierModel.findOne({
        where: {
          depositBonusSettingId: bonus.DepositBonusSetting.id,
          minDepositAmount: { [Op.lte]: amount },
          maxDepositAmount: { [Op.gte]: amount }
        },
        transaction: sequelizeTransaction
      });

      // If no matching tier found, exit
      if (!matchedTier) {
        return null;
      }
    }

    // Calculate bonus amount
    if (matchedTier) {
      // Use tier percentage for tier-based bonuses
      bonusToBeGiven = (amount * (matchedTier.percentage / 100)) > matchedTier.maxBonus
        ? matchedTier.maxBonus
        : +parseFloat(amount * (matchedTier.percentage / 100)).toFixed(5);
    } else {
      // Use bonus percentage for non-tier bonuses
      bonusToBeGiven = (amount * (bonus.percentage / 100)) > bonus.DepositBonusSetting.maxBonus
        ? bonus.DepositBonusSetting.maxBonus
        : +parseFloat(amount * (bonus.percentage / 100)).toFixed(5);
    }

    // Calculate rollover target
    let rolling;
    if (bonus.DepositBonusSetting.rolloverCalculationType === DEPOSIT_ROLLING_CALCULATION_METHODS.TOTAL_BASED_ROLLING) {
      rolling = (parseFloat(bonusToBeGiven) + parseFloat(amount));
    } else if (bonus.DepositBonusSetting.rolloverCalculationType === DEPOSIT_ROLLING_CALCULATION_METHODS.DEPOSIT_BASED_ROLLING) {
      rolling = parseFloat(amount);
    } else {
      rolling = parseFloat(bonusToBeGiven);
    }

    const rolloverTarget = rolling * bonus.DepositBonusSetting.rolloverMultiplier;
    const isRecurring = bonus.DepositBonusSetting.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(bonus.kind);
    // Check if this is a tier-based bonus and tier was found
    if (isRecurring) {
      // Create recurring rollover record
      await UserBonusRecurringRolloverModel.create({
        userId: user.id,
        bonusId: bonus.id,
        userBonusId: userActiveDepositBonus.id,
        depositBonusTierId: matchedTier? matchedTier.id : null,
        bonusAmount: bonusToBeGiven,
        rolloverTarget: rolloverTarget,
        remainingRollover: rolloverTarget,
        transactionId: null,
        status: BONUS_RECURRING_STATUS.ACTIVE,
        createdAt: new Date(),
        updatedAt: new Date()
      }, { transaction: sequelizeTransaction });


      const currentRolloverBalance = userActiveDepositBonus.rolloverBalance || 0;
      const initialRolloverBalance = userActiveDepositBonus.initialRolloverBalance || 0;
      const currentBonusAmount = userActiveDepositBonus.bonusAmount || 0;
      userActiveDepositBonus.bonusAmount = currentBonusAmount + bonusToBeGiven;

      userActiveDepositBonus.rolloverBalance = currentRolloverBalance + rolloverTarget;
      userActiveDepositBonus.initialRolloverBalance = initialRolloverBalance + rolloverTarget;

      await userActiveDepositBonus.save({ transaction: sequelizeTransaction });

    }

    // For one-time bonuses, we still need to set the main bonus information
    if (bonus.DepositBonusSetting.depositBonusType !== DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(bonus.kind)) {
      userActiveDepositBonus.bonusAmount = bonusToBeGiven;
      userActiveDepositBonus.rolloverBalance = rolloverTarget;
      userActiveDepositBonus.initialRolloverBalance = rolloverTarget;
      userActiveDepositBonus.expiresAt = new Date(new Date().setDate(new Date().getDate() + bonus.DepositBonusSetting.validForDays)).toISOString();
      await userActiveDepositBonus.save({ transaction: sequelizeTransaction });
    } else {
      // For recurring bonuses, update expiry date if not already set
      if (!userActiveDepositBonus.expiresAt || new Date(userActiveDepositBonus.expiresAt) < new Date()) {
        userActiveDepositBonus.expiresAt = new Date(new Date().setDate(new Date().getDate() + bonus.DepositBonusSetting.validForDays)).toISOString();
        await userActiveDepositBonus.save({ transaction: sequelizeTransaction });
      }
    }

    return true;
  } catch (error) {
    throw error;
  }
};

export default v3DepositBonusCheckWithTiers;
