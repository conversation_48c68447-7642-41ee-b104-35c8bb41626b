import axios from 'axios'
import { Op, QueryTypes, Sequelize } from 'sequelize'
import { CRON_LOG_STATUS, PG_SOFT_GAMES_URL, PGSOFT_ICON, PROD_PGSOFT_PROVIDER, STAGE_PGSOFT_PROVIDER } from '../common/constants'
import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import Logger from '../libs/logger'
import { getCasinoProvider } from './getCasinoProvider'

export default async (reqBody = null) => {

    const cronLog = {}
    cronLog.startTime = new Date()
    try {
      const {
        TenantCredential: TenantCredentialModel,
        CasinoGame: CasinoGameModel,
        CasinoTable: CasinoTableModel,
        CasinoItem: CasinoItemModel,
        Page: PageModel,
        CasinoMenu: CasinoMenuModel,
        PageMenu: PageMenuModel,
        MenuItem: MenuItemModel
      } = db

      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'pg_soft_game_populate',
          status: 1
        },
        attributes: ['id']
      })
      if (!queueProcessStatus) {
        return {
          success: false
         }
      }
      if (queueProcessStatus) cronLog.cronId = queueProcessStatus?.id

      const casinoProviderId = config.get('env') === 'production' ? PROD_PGSOFT_PROVIDER : STAGE_PGSOFT_PROVIDER

      let tenantIds = await sequelize.query(`
      SELECT "tenant_id" AS "tenantId", ( SELECT "id" FROM "menu_tenant_setting" WHERE "menu_id" = (SELECT "id" FROM "menu_master" WHERE "name" = 'Casino')
      AND "tenant_id" = "TenantThemeSetting"."tenant_id" ) AS "casinoId"
      FROM "public"."tenant_theme_settings" AS "TenantThemeSetting" WHERE '${casinoProviderId}' = ANY (string_to_array(assigned_providers, ','))`,
      { type: QueryTypes.SELECT, useMaster: false })

      let superAdminMenuId = await sequelize.query(`SELECT "id" FROM "menu_master" WHERE "name" = 'Casino'`,
        { type: QueryTypes.SELECT, useMaster: false })

        let allTenantsSeeded = false
      if(Array.isArray(reqBody?.tenantIds) && !reqBody?.tenantIds.includes('0')){
         allTenantsSeeded = false
        tenantIds = tenantIds.filter(tenant => reqBody.tenantIds.includes(tenant.tenantId));
      } else if(Array.isArray(reqBody?.tenantIds)){
         allTenantsSeeded = true
      }
      tenantIds = [{ tenantId: 0, casinoId: superAdminMenuId[0].id } ,...tenantIds]
      for (let i = 0; i < tenantIds.length; i++) {
        const sequelizeTransaction = await sequelize.transaction()
        const tenantId = tenantIds[i].tenantId
        const topCasinoId = tenantIds[i].casinoId
        try {
        const creds = await TenantCredentialModel.findAll({
          attributes: ['key', 'value'],
          where: {
            key: ['PG_SECRET_KEY', 'PG_OPERATOR_KEY', 'PG_LAUNCH_URL'],
            tenantId
          },
          raw: true
        })

        // Getting Data from the credentials table
        const {
          PG_SECRET_KEY,
          PG_OPERATOR_KEY
        } = creds.reduce((acc, cur) => {
          return {
            ...acc,
            [cur.key]: cur.value
          }
        }, { PG_SECRET_KEY: null, PG_OPERATOR_KEY: null, PG_LAUNCH_URL: null })

        const currentDate = new Date().toISOString()
        const data = JSON.stringify({
          operator_token: PG_OPERATOR_KEY,
          secret_key: PG_SECRET_KEY
        })

        const pgsoftGameData = await axios({
          url: PG_SOFT_GAMES_URL,
          method: 'post',
          maxBodyLength: Infinity,
          headers: {
            'Content-Type': 'application/json'
          },
          data: data
        })

        if (pgsoftGameData?.data === null) {
          throw new Error('pgsoftWorker: The data to be inserted is not present')
        }

        // Data for a page model
        const pageData = {
          title: 'PGsoft',
          enabled: true,
          order: null,
          createdAt: currentDate,
          updatedAt: currentDate,
          tenantId,
          topMenuId: topCasinoId,
          enableInstantGame: null,
          image: PGSOFT_ICON
        }

        /**
       * Dataobject that returns the followin object
       *
       * @Object category @table casino_games
       * @object casinoTable @table casino_table
       * @object tableItems @table casino_item
       * */
      if(!pgsoftGameData?.data?.data?.length) {
        // return cron without any error
        cronLog.status = CRON_LOG_STATUS.FAILED
        cronLog.errorMsg = (pgsoftGameData?.data?.error?.message || 'Error: Axios response is empty') + ' TenantId: ' + tenantId
        cronLog.endTime = new Date()
        await db.CronLog.create(cronLog)
        Logger.info(cronLog.errorMsg, '========PGSOFT game population error======')
        await sequelizeTransaction.rollback()
        delete cronLog.errorMsg
        delete cronLog.status
        continue;
      }

        const pgsoftGameDataObject = pgsoftGameData?.data?.data.reduce(
          (acc, cur) => {
            if (!acc.category.length || !acc.category.includes(cur.category)) {
              acc.category.push(cur.category)
            }

            acc.casinoTable.push({
              name: cur.gameName,
              gameId: `pg_category_${cur.category}`,
              createdAt: currentDate,
              updatedAt: currentDate,
              isLobby: true,
              tableId: '' + cur.gameId,
              providerId: casinoProviderId
            })

            acc.tableIds.push('' + cur.gameId)
            acc.gameName.push(cur.gameName)
            acc.tableItems.push({
              uuid: '' + cur.gameId,
              name: cur.gameName,
              image: `provider-images/pg-soft/thumbnail/${cur.gameCode}.webp`,
              provider: casinoProviderId,
              active: (cur.status === 1),
              featured: true,
              createdAt: currentDate,
              updatedAt: currentDate,
              tenantId
            })

            return acc
          }, { category: [], casinoTable: [], tableIds: [], tableItems: [], gameName: [], menuItem: [] })

        // Object to be updated into casino games, if it doesnt alreay exists
        const dataToBeUpdatedIntoCasinoGames = pgsoftGameDataObject.category.map((item, idx) => {
          return {
            name: idx === 0 ? 'Video Slots' : 'Game Shows',
            casinoProviderId,
            createdAt: currentDate,
            updatedAt: currentDate,
            gameId: `pg_category_${item}`
          }
        })

        // Object to create or update into into casino menu specific to a tenant
        const casinoMenuDataToBeUpdated = pgsoftGameDataObject.category.map((item, idx) => {
          return {
            name: idx === 0 ? 'Video Slots' : 'Game Shows',
            menuType: null,
            menuOrder: null,
            enabled: true,
            createdAt: currentDate,
            updatedAt: currentDate,
            tenantId,
            imageUrl: PGSOFT_ICON
          }
        })

        // get existing data in CasinoGame table
        const casinoGamesData = await CasinoGameModel.findAll({
          attributes: ['gameId', 'id', 'name'],
          where: {
            gameId: {
              [Op.in]: dataToBeUpdatedIntoCasinoGames.map(item => item.gameId)
            },
            casinoProviderId: casinoProviderId
          },
          raw: true
        })

        /**
     * @object name: casinoTableData
     * @gets the already existed data in the
     * @table CasinoTableModel
     */
        const casinoTableData = await CasinoTableModel.findAll({
          attributes: ['gameId', 'tableId', 'name', 'id'],
          where: {
            gameId: {
              [Op.in]: pgsoftGameDataObject.category.map(item => `pg_category_${item}`)
            },
            providerId: casinoProviderId
            // tableId: {
            //   [Op.in]: pgsoftGameDataObject.tableIds
            // }
          },
          raw: true
        })


    let pagesData = await getCasinoProvider(pageData.title, casinoProviderId, tenantId, topCasinoId, sequelizeTransaction, currentDate, {}, pageData.image, allTenantsSeeded)
        /**
     * @object casinoItemData
     * @gets the data from table
     * @casinoItemModel
     */
        const casinoItemData = await CasinoItemModel.findAll({
          attributes: ['uuid', 'name', 'id', 'active'],
          where: {
            uuid: {
              [Op.in]: pgsoftGameDataObject.tableIds
            },
            provider: ''+casinoProviderId,
            tenantId
          },
          raw: true
        })

        /**
     * @object casinoMenuData
     * gets the data from table
     * @casinoMenu
     */
    const namesToCheck = casinoMenuDataToBeUpdated.map(i => `'${i.name.toLowerCase().replace(/'/g, "''")}'`).join(',')

        const casinoMenuData = await CasinoMenuModel.findAll({
          attributes: ['name', 'id'],
          where: {
            tenantId,
            name: Sequelize.literal(`LOWER("name") IN (${namesToCheck})`)
          },
          raw: true
        })

        // check update the casinoGamesData object responsible to maintain categorty based on new data
        const updatedDataToBeUpdatedIntoCasinoGames = dataToBeUpdatedIntoCasinoGames.filter(i => !casinoGamesData.map(i => i.gameId).includes(i.gameId))

        // update the casinoTableData object based on data from casinoTabe table
        const updatedCasinoTableData = pgsoftGameDataObject.casinoTable.filter(i => !casinoTableData.map(i => i.tableId).includes(i.tableId))

        // update casinoItemData object based on data from casinoItem table
        const updatedCasinoItemData = pgsoftGameDataObject.tableItems.filter(i => !casinoItemData.map(i => i.uuid).includes(i.uuid))

        // existing data with diffrent active status from casinoItem table
        const existingCasinoItemData = casinoItemData.filter(i => pgsoftGameDataObject.tableItems.some(item => item.uuid === i.uuid && item.active !== i.active))

        // update the name of CasinoGameModel
        await Promise.all(casinoGamesData.map(async item => {
          const fromgame = dataToBeUpdatedIntoCasinoGames.find((i) => (i.gameId === item.gameId))
          if (fromgame && fromgame.name !== item.name) {
            await CasinoGameModel.update({ name: fromgame.name }, { where: { id: item.id }, transaction: sequelizeTransaction })
            item.name = fromgame.name
          }

        }))

        // update the name of CasinoTableModel
        await Promise.all(casinoTableData.map(async item => {
          const fromgame = pgsoftGameDataObject.casinoTable.find((i) => (i.tableId === item.tableId))
          if (fromgame && fromgame.name !== item.name) {
            await CasinoTableModel.update({ name: fromgame.name }, { where: { id: item.id }, transaction: sequelizeTransaction })
            item.name = fromgame.name
          }
        }))



        const existingCasinoItemsNameToUpdate = pgsoftGameDataObject.tableItems.filter(item => {
          const existingItem = casinoItemData.find(i => i.uuid === item.uuid)
          return existingItem && existingItem.name !== item.name
        }).map(item => {
          return {
            ...item,
            name: item.name
          }
        })

        await Promise.all(existingCasinoItemData.map(async item => {
          await CasinoItemModel.update({ active: !item.active }, { where: { id: item.id }, transaction: sequelizeTransaction })
          await MenuItemModel.update({ active: !item.active }, { where: { casinoItemId: item.id }, transaction: sequelizeTransaction })
        }));

        for (const item of casinoItemData) {
          const fromgame = existingCasinoItemData.find((i) => (i.uuid === item.uuid))
          if (fromgame) {
            item.active = !fromgame.active
          }
        }

        for (const item of existingCasinoItemsNameToUpdate) {
          await CasinoItemModel.update({ name: item.name }, {
            where: { uuid: item.uuid },
            provider: casinoProviderId.toString(),
            transaction: sequelizeTransaction
          })
          await CasinoTableModel.update({ name: item.name }, {
            where: { tableId: item.uuid },
            transaction: sequelizeTransaction,
            providerId: casinoProviderId
          })
          const fromgame = casinoItemData.find((i) => (i.id === item.id))
          if (fromgame) {
            fromgame.name = item.name
          }

          const fromgameTable = casinoTableData.find((i) => (i.tableId === item.uuid))
          if (fromgameTable) {
            fromgameTable.name = item.name
          }
        }
        // if pagesData doesnt exists then create the pageData
        // const updatedPageDate = pagesData.length ? pagesData : pageData

        // const updatedCasinoMenuData = casinoMenuData.length? casinoMenuData: casinoMenuDataToBeUpdated
        const updatedCasinoMenuData = casinoMenuDataToBeUpdated.filter(i => !casinoMenuData.map(i => i.name).includes(i.name))

        // insert updated casino game data to table
        const insertedCasinoGamesData = await CasinoGameModel.bulkCreate(updatedDataToBeUpdatedIntoCasinoGames, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoGamesData: ${insertedCasinoGamesData}`)

        const insertedCasinoTableData = await CasinoTableModel.bulkCreate(updatedCasinoTableData, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoTableData: ${insertedCasinoTableData}`)

        const insertedCasinoItemData = await CasinoItemModel.bulkCreate(updatedCasinoItemData, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoItemData: ${insertedCasinoItemData}`)

        // const insertedPageData = (!updatedPageDate.length && await PageModel.create(updatedPageDate, { transaction: sequelizeTransaction })) || []

        Logger.info(`insertedPageData: ${insertedPageData}`)

        const insertedCasinoMenuData = await CasinoMenuModel.bulkCreate(updatedCasinoMenuData, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoMenuData: ${insertedCasinoMenuData}`)

        const casinoGames = [
          ...casinoGamesData,
          ...insertedCasinoGamesData.map(item => item.dataValues)
        ]
        const pageId = pagesData.id

        const casinoMenus = [
          ...casinoMenuData,
          ...insertedCasinoMenuData.map(item => item.dataValues)
        ]

        const casinoTable = [
          ...casinoTableData,
          ...insertedCasinoTableData.map(item => item.dataValues)
        ]

        const pageMenuDataToBeInserted = pgsoftGameDataObject.category.map((item, inx) => {
          return {
            pageId,
            casinoMenuId: inx === 0 ? casinoMenus.find(i => i.name.toLowerCase() === 'video slots').id : casinoMenus.find(i => i.name.toLowerCase() === 'game shows').id,
            name: `Slots ${item}`,
            menuOrder: null,
            createdAt: currentDate,
            updatedAt: currentDate
          }
        })

        const pageMenuData = await PageMenuModel.findAll({
          attributes: ['id', 'pageId', 'casinoMenuId', 'name'],
          where: {
            pageId,
            name: {
              [Op.in]: pageMenuDataToBeInserted.map(i => i.name)
            }

          },
          raw: true
        })

        const updatedCasinoPageMenuData = pageMenuDataToBeInserted.filter(i => !pageMenuData.map(i => i.name).includes(i.name))

        const insertedCasinoPageMenuData = (updatedCasinoPageMenuData.length && await PageMenuModel.bulkCreate(updatedCasinoPageMenuData, { transaction: sequelizeTransaction })) || []

        const casinoItem = [
          ...casinoItemData,
          ...insertedCasinoItemData.map(item => item.dataValues)
        ]

        const pageMenu = [
          ...pageMenuData,
          ...insertedCasinoPageMenuData.map(item => item.dataValues)
        ]





          const menuItemsDataToBeInserted = casinoItem.map(item => {
            const fromgame = pgsoftGameDataObject.casinoTable.find((i) => (i.tableId === item.uuid))
            const gameId = fromgame?.gameId
            const gameName = casinoGames.find(i => i.gameId === gameId)?.name
            const casinoMenuIds = casinoMenus.filter(i => i.name.toLowerCase() === gameName.toLowerCase())
            const mappingCasinoMenuId = casinoMenuIds.map(i => i.id)
            let pmId = mappingCasinoMenuId?.length ? pageMenu.find(i => mappingCasinoMenuId.includes(i.casinoMenuId))?.id : null
            return {
              pageMenuId: pmId,
              casinoItemId: item.id,
              name: item.name,
              order: null,
              active: item.active,
              featured: true,
              createdAt: currentDate,
              updatedAt: currentDate,
              popular: true
            }
          }
          ).filter(i => i !== null)

        Logger.info(`menuItemsDataToBeInserted: ${menuItemsDataToBeInserted}`)

        const menuItemData = await MenuItemModel.findAll({
          attributes: ['id', 'pageMenuId', 'casinoItemId', 'name', 'active'],
          where: {
            casinoItemId: {
              [Op.in]: casinoItemData.map(i => i.id)
            }
          },
          raw: true,
          transaction: sequelizeTransaction
        })

        const menuItemsNameUpdation = casinoItemData.filter(item => {
          const existingItem = menuItemData.find(i => i.casinoItemId === item.id)
          return existingItem && existingItem.name !== item.name
        }).map(item => {
          return {
            ...item,
            name: item.name
          }
        })

        for (const item of menuItemsNameUpdation) {
          await MenuItemModel.update({ name: item.name }, {
            where: { casinoItemId: item.id },
            transaction: sequelizeTransaction
          })
        }

        const updatedMenuItemsData = menuItemsDataToBeInserted.filter(i =>
          !menuItemData.some(ii => ii.casinoItemId === i.casinoItemId && ii.pageMenuId === i.pageMenuId)
        )

        const insertedMenuItemData = (updatedMenuItemsData.length && await MenuItemModel.bulkCreate(updatedMenuItemsData, { transaction: sequelizeTransaction })) || []

        Logger.info(`insertedMenuItemData: ${insertedMenuItemData}`)

        const menuItemList = [
          ...menuItemData,
          ...insertedMenuItemData.map(item => item.dataValues),
        ]

        let noLongerExists = []
        menuItemList.forEach(item => {
          const casinoItemId = casinoItem.find(i => i.id === item.casinoItemId)
          const fromgame = pgsoftGameDataObject.casinoTable.find((i) => (i.tableId === casinoItemId.uuid))
          const gameId = fromgame?.gameId
          const gameName = casinoGames.find(i => i.gameId === gameId)?.name
          const casinoMenuId = casinoMenus.find(i => i.name.toLowerCase() === gameName.toLowerCase())?.id
          const pmId = pageMenu.find(i => i.casinoMenuId === casinoMenuId)?.id

          if (item.pageMenuId !== pmId && pmId) {
            const checkMenuItem = menuItemList.find(i => i.casinoItemId === item.casinoItemId && i.pageMenuId !== pmId)
            if (checkMenuItem) {
              noLongerExists.push(item)
            }
          }
        })

          if (noLongerExists.length) {
            let menuListToBeDeleted = await sequelize.query(`
            SELECT "MenuItem".id
            FROM "public"."menu_items" AS "MenuItem"
            JOIN public.page_menus on "MenuItem".page_menu_id = page_menus.id
            WHERE "MenuItem"."id" IN (:menuids) and page_id in (:pageIds)`, {
              replacements: {
                menuids: noLongerExists.map(item => item.id),
                pageIds: [pageId]
              },
              type: QueryTypes.SELECT,
              transaction: sequelizeTransaction
            })

            let allPageMenu = noLongerExists.map(item => item.pageMenuId)
            let allAvailablePageMenu = await PageMenuModel.findAll({
              attributes: ['id'],
              where: {
                id: {
                  [Op.in]: allPageMenu
                }
              },
              raw: true
            })
            let allAvailablePageMenuIds = allAvailablePageMenu.map(item => item.id)
            let allPageMenuIds = allPageMenu.filter(item => !allAvailablePageMenuIds.includes(item))
            let allPageMenuIdsToBeDeleted = noLongerExists.filter(item => allPageMenuIds.includes(item.pageMenuId))
            menuListToBeDeleted.push(...allPageMenuIdsToBeDeleted)

            // only delete the menu items that are belonging to the current page
            if (menuListToBeDeleted.length) {
              await MenuItemModel.destroy({
                where: {
                  id: {
                    [Op.in]: menuListToBeDeleted.map(item => item.id)
                  },
                  casinoItemId: {
                    [Op.in]: casinoItem.map(item => item.id)
                  }

                },
                transaction: sequelizeTransaction
              })
            }
          }

        // disable the category if all games in that category are inactive
        const categoriesToDisable = await sequelize.query(
          ` UPDATE casino_menus cm
          SET enabled = subquery.result
          FROM (
            SELECT
              ct.game_id AS game_category,
              CASE
                WHEN COUNT(*) = COUNT(CASE WHEN ci.active = false THEN 1 END) THEN false
                ELSE true
              END AS result
            FROM
              casino_tables ct
            JOIN
              casino_items ci ON ct.table_id = ci.uuid
            WHERE
              ci.provider = '${casinoProviderId}'
              AND ci.tenant_id = '${tenantId}' AND ct.provider_id = '${casinoProviderId}'
            GROUP BY
              ct.game_id
          ) AS subquery
          WHERE cm.name = subquery.game_category AND cm.tenant_id = '${tenantId}';
        `, { transaction: sequelizeTransaction }
        )
        await sequelizeTransaction.commit()

      } catch (error) {
        cronLog.status = CRON_LOG_STATUS.FAILED
        cronLog.errorMsg = error.message + ' TenantId: ' + tenantId
        cronLog.endTime = new Date()
        await db.CronLog.create(cronLog)
        Logger.info(error, '========pg_soft_game  population error======')
        await sequelizeTransaction.rollback()
        continue
      }
      }
      cronLog.endTime = new Date()
      cronLog.errorMsg = null
      cronLog.status = CRON_LOG_STATUS.SUCCESS
      await db.CronLog.create(cronLog)
      return {
        success: true
      }
    } catch (error) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message || null
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      Logger.info(error, '========PGSOFT game population error======')
      return {
        success: false,
        Error: {
          stack: error.stack
        }
      }
    }
}
