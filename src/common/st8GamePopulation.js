import axios from 'axios'
import { ST8_GAMES_URL } from './constants'


export default async (reqBody = null) => {
  const st8GameData = await axios({
    url: ST8_GAMES_URL,
    method: 'post',
    maxBodyLength: Infinity,
    headers: {
      'Content-Type': 'application/json'
    },
    data: reqBody
  })

  if (st8GameData?.data === null) {
    throw new Error('st8Worker: The data to be inserted is not present')
  }
}
