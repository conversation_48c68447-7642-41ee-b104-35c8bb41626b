import axios from 'axios'
import { QueryTypes } from 'sequelize'
import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import { AGGREGATER_PROVIDER_LIST, ST8_PROVIDER } from '../utils/constants/constant'
import { ST8_GAMES_URL } from './constants'
import syncOrOverrideCasinoProvider from './syncOrOverrideCasinoProvider'
const { Op } = require('sequelize');


export default async (reqBody = null) => {
  const st8GameData = await axios({
    url: ST8_GAMES_URL,
    method: 'post',
    maxBodyLength: Infinity,
    headers: {
      'Content-Type': 'application/json'
    },
    data: reqBody
  })

  const queueProcessStatus = await db.QueueProcessStatus.findOne({
    where: {
      service: 'st8_game_seeding_cron',
      status: 1
    },
    attributes: ['id']
  })
  if (!queueProcessStatus) {
    return {
      success: false,
      message: "Queue Process Stopped."
    }
  }


  const casinoProviderId = config.get('env') === 'production' ? ST8_PROVIDER.PROD : ST8_PROVIDER.STAGE
  let pagesList = AGGREGATER_PROVIDER_LIST[config.get('env') === 'production' ? 'PROD_' + casinoProviderId : 'STAG_' + casinoProviderId]

  let tenants = await sequelize.query(`
      SELECT "tenant_id" AS "tenantId"
      FROM "public"."tenant_theme_settings" AS "TenantThemeSetting" WHERE '${casinoProviderId}' = ANY (string_to_array(assigned_providers, ','))
      ${reqBody?.tenantId ? ` AND tenant_id = '${reqBody?.tenantId}'` : ''}
      `,
    { type: QueryTypes.SELECT, useMaster: false })
  if (Array.isArray(reqBody?.tenantIds) && !reqBody?.tenantIds.includes('0')) {
    tenants = tenants.filter(tenant => reqBody.tenantIds.includes(tenant.tenantId));
  }
  if (reqBody?.isSuperAdmin) {
    tenants = [{ tenantId: 0 }]
  } else {
    tenants = [{ tenantId: 0 }, ...tenants]
  }

  if (tenants.length <= 0) {
    return {
      success: true,
      message: "Provider not enabled for this tenant."
    }
  }

  let pageNamesList = []
  let aggregatorProviders = reqBody?.aggregator_providers
  if (!aggregatorProviders || aggregatorProviders.length === 0) {
    pageNamesList = pagesList.map(record => record.displayName)
  } else {
    // convert strings to integers
    aggregatorProviders = aggregatorProviders.map(Number)
    pageNamesList = pagesList.filter(record => aggregatorProviders.includes(record.id)).map(record => record.displayName)
  }
  let tenantIds = tenants.map(tenant => tenant.tenantId)

  for (const pageName of pageNamesList) {
    let formattedPageName = pageName.replace(/ /g, '_')

    if (reqBody?.override_other_providers || reqBody?.sync) {
      await syncOrOverrideCasinoProvider(tenantIds, casinoProviderId, formattedPageName, reqBody?.override_other_providers, reqBody?.sync)
    }
  }

  if (st8GameData?.data === null) {
    throw new Error('st8Worker: The data to be inserted is not present')
  }
}
