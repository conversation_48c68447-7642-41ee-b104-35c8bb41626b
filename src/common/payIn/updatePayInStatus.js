import { Op } from 'sequelize'
import db, { sequelize } from '../../db/models'
import currencyConversion from '../currencyConversion'
import depositBonusCheck from '../depositBonusCheck'
import { QUEUE_WORKER_CONSTANT, CRON_LOG_STATUS } from '../constants'
import pushPayInTransaction from './pushPayInTransaction'
const axios = require('axios')

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'update_pay_in_status_cron',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      cronLog.cronId = queueProcessStatus?.id
      const payInIds = await db.TenantCredential.findOne({
        where: {
          key: 'PAYIN_IDS'
        }
      })
      const depositRequest = await db.DepositRequest.findAll({
        where: {
          status: 'opened',
          paymentProviderId: { [Op.in]: payInIds.value.split(',') }
        },
        attributes: ["id"],
        raw: true
      })
      if (depositRequest.length > 0) {
        const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
        for (const dr of depositRequest) {
          await delay(1000)
          await Promise.all([
            pushPayInTransaction(dr.id)
          ])

        }
      }
    }

  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log("==========error", e)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
