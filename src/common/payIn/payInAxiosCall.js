import axios from "axios"
import config from "../../configs/app.config"
import db from '../../db/models'
import createLog from "../createLog"
import updateLog from "../updateLog"
import { PAYIN_DEPOSIT_STATUS_URL, PAYMENT_GATEWAY_QUEUE_TYPE } from "../constants"

export default async (depositRequest) => {

      //let reqResLog = await createLog(jobId, userId)
      try{
        const paymentProviders = await db.tenantPaymentConfiguration.findOne({
          where: {
            //active: true,
            tenantId: depositRequest.tenantId,
            providerId: depositRequest.paymentProviderId
          },
          raw: true
        })
        let apiConfig = {
          method: 'post',
          maxBodyLength: Infinity,
          url: `${PAYIN_DEPOSIT_STATUS_URL}${depositRequest.orderId}`, // fetch from database
          headers: { PrivateKey: paymentProviders.providerKeyValues.privateKey }, // fetch from database
          data: {}
        }
        const apiResponse = await axios(apiConfig)
          .then(function (response) {
            return response.data
          })
          .catch(function (error, response) {
            return error;
          });

        const reqLogObject = {
          requestJson: {
            url: `${PAYIN_DEPOSIT_STATUS_URL}${depositRequest.orderId}`,
            headers: { PrivateKey: paymentProviders.providerKeyValues.privateKey },
          },
          service: PAYMENT_GATEWAY_QUEUE_TYPE.PAYIN_DEPOSIT_UPDATE_STATUS,
          url: 'queue-worker',
          responseJson: { data: apiResponse },
          tenantId: depositRequest.tenantId
        }
        await db.RequestResponseLog.create(reqLogObject)

          return apiResponse
      }catch(err){
        throw err
      }


}
