//import { DepositTransactionQueue, DepositTransaction } from '../../queues/depositTransaction.queue'
import { PaymentGateway, PaymentGatewayQueue } from "../../queues/paymentGateway.queue";
import { v4 as uuidv4 } from 'uuid'
import db, { sequelize } from '../../db/models'

export default async (id) => {

  try {
    const transactionType = "payin_deposit_update_status"
    let queue = PaymentGatewayQueue
    let queueName = PaymentGateway

    const jobTime = new Date()
    const uuid = uuidv4().replace(/-/g, '')
    const uniqueId = uuid.substr(uuid.length - 10)

    queue.add(queueName,
      {
        time: jobTime,
        transactionType: transactionType,
        transactionId: id
      },
      {
        jobId: `${id}_${queueName}_${uniqueId}`,
        removeOnComplete: true,
        delay: 10
      })
  } catch (error) {
    throw error
  }
}
