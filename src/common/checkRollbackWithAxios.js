import axios from "axios"
import { Op, Sequelize } from "sequelize"
import config from "../configs/app.config"
import db from '../db/models'
import { EZUGI_ROLLBACK_CALLBACK_URL } from '../utils/constants/constant'


export default async (txnId, tenantId) => {

  try {
    const {
      RequestResponseLog: RequestResponseLogModel,
      RequestResponseLogs: RequestResponseLogsModel,
      Transaction: TransactionModel
    } = db

    let debitTransactionExist = await TransactionModel.findOne({
      where: {
        tenantId: tenantId,
        transactionId: txnId,
        createdAt: {
          [Op.gte]: Sequelize.literal(`CURRENT_DATE - INTERVAL '7 days'`)
       }
      },
      attributes: ['id'],
      useMaster: true,
      raw: true
    })

    if(!debitTransactionExist){
      return true
    }

    let rollbackTxnExsit = await RequestResponseLogModel.findOne({
      where: {
        tenant_id: tenantId,
        request_json: {
          [Sequelize.Op.contains]: {
            body: {
              transactionId: txnId
            }
          }
        }
      },
      attributes: ['id', 'requestJson'],
      raw: true
    })
    if (!rollbackTxnExsit) {
      rollbackTxnExsit = await RequestResponseLogsModel.findOne({
        where: {
          tenant_id: tenantId,
          request_json: {
            [Sequelize.Op.contains]: {
              body: {
                transactionId: txnId
              }
            }
          }
        },
        attributes: ['id', 'requestJson'],
        raw: true
      })
    }
    if (!rollbackTxnExsit) {
      return true
    }
    const url = config.get('env') === 'production' ? EZUGI_ROLLBACK_CALLBACK_URL.PROD : EZUGI_ROLLBACK_CALLBACK_URL.STAGE
    const headers = {
      'hash': rollbackTxnExsit.requestJson.headers.hash,
      'Accept': 'application/json, text/plain, */*',
      'Content-Type': 'application/json'
    }

    let apiConfig = {
      method: 'post',
      maxBodyLength: Infinity,
      url: url,
      headers: headers,
      data: JSON.stringify(rollbackTxnExsit.requestJson.body)
    }

    const apiResponse = await axios(apiConfig)
      .then(function (response) {
        return response.data
      })


    return { success: true }

  } catch (error) {
    throw error
  }
}
