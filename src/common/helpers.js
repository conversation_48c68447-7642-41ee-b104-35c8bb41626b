import config from '../configs/app.config';
import { sequelize } from '../db/models';
import { BONUS_COMMENT_FULL_TEXT, Countries, CURRENCY_FORMAT, CURRENCY_LIST_PROD, CURRENCY_LIST_STAGE } from './constants';

export async function fetchAgentIds (currentUserId) {
  const getAgentChild = `
  WITH RECURSIVE parents AS (
    SELECT
      au.id, au.tenant_id, au.parent_id, au.parent_type
    FROM admin_users au
    WHERE au.id = :currentUserId
    UNION ALL
    SELECT
      child.id, child.tenant_id, child.parent_id, child.parent_type
    FROM admin_users child
    JOIN parents parent ON parent.id = child.parent_id AND parent.tenant_id = child.tenant_id
    WHERE child.parent_type LIKE 'AdminUser' AND parent.id != child.id
  )
  SELECT id FROM parents;
`

  const agentChildResponse = await sequelize.query(getAgentChild, {
    type: sequelize.QueryTypes.SELECT,
    replacements: { currentUserId }
  })
  return agentChildResponse.map(row => row.id)
}

export const numberPrecision = (number) => {
  return +parseFloat(number).toFixed(5)
}

export const getCurrencyByValue = (value) => {
  const currencyList = (config.get('env') === 'production') ? CURRENCY_LIST_PROD : CURRENCY_LIST_STAGE
  return currencyList.find(currency => currency.value.toLowerCase() === value.toLowerCase())
}
export const getCurrencyById = (id) => {
  const currencyList = (config.get('env') === 'production') ? CURRENCY_LIST_PROD : CURRENCY_LIST_STAGE
  return currencyList.find(currency => currency.id === id)
}

export async function getCurrentUserRole (id) {
  const data = await sequelize.query(
    `SELECT
          aro.name
      FROM admin_users au
      JOIN admin_users_admin_roles auar ON au.id = auar.admin_user_id
      JOIN admin_roles aro ON auar.admin_role_id = aro.id
      WHERE au.id = :id`,
    {
      type: sequelize.QueryTypes.SELECT,
      replacements: { id }
    }
  )
  return data ? data[0]?.name : null
}

export async function getTransactionFilters (actionType) {
  const transactionMap = {
    deposit_cash_admin: ["actionee_type = 'AdminUser'", "payment_method = 'manual'", "(comments NOT IN ('Deposit Request Approved') OR comments IS NULL )"],
    deposit_manual_user: ["comments IN ('Deposit Request Approved')", "payment_method = 'manual'"],
    deposit_gateway_user: ["comments IN ('Deposit Request')"],
    withdraw_gateway_user: ["comments IN ('Approved By Payment gateway')"],
    withdraw_cash_manual_user: ["comments IN ('approved by admin')"],
    withdraw_cash_admin: ["actionee_type = 'AdminUser'", "status = 'success'", "(comments NOT IN ('approved by admin', 'cancelled by admin', 'Approved By Payment gateway') OR comments IS NULL )"],
    withdraw: ["status = 'success'", "COALESCE(comments, '') NOT IN ('Pending confirmation from admin', 'cancelled by admin', 'cancelled by the player')"]
  }

  const transactionTypes = {
    deposit_cash_admin: 3,
    deposit_non_cash_admin: 5,
    one_time_bonus_deposit: 48,
    deposit_manual_user: 3,
    deposit_gateway_user: 3,
    withdraw_gateway_user: 4,
    withdraw_cash_manual_user: 4,
    withdraw_non_cash_admin: 6,
    one_time_bonus_withdraw: 49,
    withdraw_cash_admin: 4,
    withdraw_cancel: 14,
    sports_free_bet_deposit: 59,
    sports_free_bet_withdraw: 60,
    deposit: [3, 5, 48, 59],
    withdraw: [4, 6, 49, 60],
  }

  const defaultTransactionType = [3, 4, 6, 48, 49, 5, 14, 59, 60]


  return { filters: transactionMap[actionType] || [], transactionType: transactionTypes[actionType] || defaultTransactionType }
}

export async function getTransactionTypeByValueOrId (input) {
  const transactionTypes = {
    0: { type: 'bet', name: 'Bet' },
    1: { type: 'win', name: 'Win' },
    2: { type: 'refund', name: 'Refund' },
    3: { type: 'deposit', name: 'Deposit' },
    4: { type: 'withdraw', name: 'Withdraw' },
    5: { type: 'non_cash_granted_by_admin', name: 'Non cash granted by admin' },
    6: { type: 'non_cash_withdraw_by_admin', name: 'Non cash withdraw by admin' },
    7: { type: 'tip', name: 'Tip' },
    8: { type: 'bet_non_cash', name: 'Bet non cash' },
    9: { type: 'win_non_cash', name: 'Win non cash' },
    10: { type: 'refund_non_cash', name: 'Refund non cash' },
    11: { type: 'non_cash_bonus_claim', name: 'Non cash bonus claim' },
    12: { type: 'deposit_bonus_claim', name: 'Deposit bonus claim' },
    13: { type: 'tip_non_cash', name: 'Tip non cash' },
    14: { type: 'withdraw_cancel', name: 'Withdraw cancel' },
    15: { type: 'joining_bonus_claimed', name: 'Joining bonus claimed' },
    16: { type: 'failed', name: 'Failed' },
    17: { type: 'promo_code_bonus_claimed', name: 'Promo code bonus claimed' },
    19: { type: 'commission_received', name: 'Commission Received' },
    20: { type: 'exchange_place_bet_non_cash_debit', name: 'place bet non cash debit' },
    21: { type: 'exchange_place_bet_cash_debit', name: 'place bet cash debit' },
    22: { type: 'exchange_place_bet_cash_credit', name: 'place bet cash credit' },
    23: { type: 'exchange_refund_cancel_bet_non_cash_debit', name: 'refund cancel bet non cash debit' },
    24: { type: 'exchange_refund_cancel_bet_cash_debit', name: 'refund cancel bet cash debit' },
    25: { type: 'exchange_refund_cancel_bet_non_cash_credit', name: 'refund cancel bet non cash credit' },
    26: { type: 'exchange_refund_cancel_bet_cash_credit', name: 'refund cancel bet cash credit' },
    27: { type: 'exchange_refund_market_cancel_non_cash_debit', name: 'refund market cancel non cash debit' },
    28: { type: 'exchange_refund_market_cancel_cash_debit', name: 'refund market cancel cash debit' },
    29: { type: 'exchange_refund_market_cancel_non_cash_credit', name: 'refund market cancel non cash credit' },
    30: { type: 'exchange_refund_market_cancel_cash_credit', name: 'refund market cancel cash credit' },
    31: { type: 'exchange_settle_market_cash_credit', name: 'settle market cash credit' },
    32: { type: 'exchange_settle_market_cash_debit', name: 'settle market cash debit' },
    33: { type: 'exchange_resettle_market_cash_credit', name: 'resettle market cash credit' },
    34: { type: 'exchange_resettle_market_cash_debit', name: 'resettle market cash debit' },
    35: { type: 'exchange_cancel_settled_market_cash_credit', name: 'cancel settled market cash credit' },
    36: { type: 'exchange_cancel_settled_market_cash_debit', name: 'cancel settled market cash debit' },
    37: { type: 'exchange_deposit_bonus_claim', name: 'deposit bonus claim' },
    38: { type: 'withdraw_rejected_by_admin', name: 'Withdraw rejected' },
    39: { type: 'burning_losing_bonus', name: 'Burning cashback bonus' },
    40: { type: 'burning_joining_bonus', name: 'Burning joining bonus' },
    41: { type: 'player_bulk_categorization_bonus', name: 'Loyalty bonus claimed' },
    42: { type: 'burning_deposit_bonus', name: 'Burning deposit bonus' },
    43: { type: 'burning_non_cash_bonus_amount', name: 'Burning non cash bonus amount' },
    44: { type: 'royalty_non_cash_bonus', name: 'Loyalty Non Cash Bonus' },
    45: { type: 'royalty_cash_bonus', name: 'Loyalty Cash Bonus' },
    46: { type: 'bet_one_time_bonus', name: 'Bet Free Bets' },
    47: { type: 'refund_one_time_bonus', name: 'Refund Free Bets' },
    48: { type: 'one_time_bonus_deposit', name: 'Free Bets Deposit' },
    49: { type: 'one_time_bonus_withdraw', name: 'Free Bets Withdraw' },
    50: { type: 'burning_promo_code_bonus', name: 'Burning promo code bonus' },
    51: { type: 'burning_manual_losing_bonus', name: 'Burning manual losing bonus' },
    52: { type: 'referral_bonus_claim', name: 'Referral bonus claim' },
    53: { type: 'exchange_adjust_settled_market_cash_credit', name: 'Adjust Settled Market Cash Credit' },
    54: { type: 'exchange_adjust_settled_market_cash_debit', name: 'Adjust Settled Market Cash Debit' },
    55: { type: 'exchange_cancel_settled_bet_non_cash_debit', name: 'Cancel Settled Bet Non Cash Debit' },
    56: { type: 'exchange_cancel_settled_bet_cash_debit', name: 'Cancel Settled Bet Cash Debit' },
    57: { type: 'exchange_cancel_settled_bet_non_cash_credit', name: 'Cancel Settled Bet Non Cash Credit' },
    58: { type: 'exchange_cancel_settled_bet_cash_credit', name: 'Cancel Settled Bet Cash Credit' },
    59: { type: 'sports_free_bet_deposit', name: 'Sports Free Bet Deposit' },
    60: { type: 'sports_free_bet_withdraw', name: 'Sports Free Bet Withdraw' },
    61: { type: 'place_bet_sports_freebet_debit', name: 'Place Bet Sport Free Bet Debit' },
    62: { type: 'refund_cancel_bet_sports_freebet_debit', name: 'Refund Cancel Bet Sports Free Bet Debit' },
    63: { type: 'refund_cancel_bet_sports_freebet_credit', name: 'Refund Cancel Bet Sports Freebet Credit' },
    64: { type: 'refund_market_cancel_sports_freebet_debit', name: 'Refund Market Cancel  Sports Free Bet Debit' },
    65: { type: 'refund_market_cancel_sports_freebet_credit', name: 'Refund Market Cancel  Sports Free Bet Credit' },
    66: { type: 'cancel_settled_bet_sports_freebet_debit', name: 'Cancel Settled Bet Sports Freebet Debit' },
    67: { type: 'cancel_settled_bet_sports_freebet_credit', name: 'Cancel Settled Bet Sports Freebet Credit' },
    68: { type: 'free_bets_on_deposit_bonus_claim', name: 'Free Bets On Deposit Bonus Claim' },
    69: { type: 'sports_free_bets_deposit_bonus_claim', name: 'Sports Free Bets Deposit Bonus Claim' },
    70: { type: 'non_cash_deposit_bonus_claim', name: 'Non Cash Deposit Bonus Claim' },
    71: { type: 'non_cash_promo_code_bonus_claimed', name: 'Non Cash Promo Code Bonus Claimed' },
    72: { type: 'non_cash_referral_bonus_claim', name: 'Non Cash Referral Bonus Claim' },
    73: { type: 'non_cash_burning_deposit_bonus', name: 'Non Cash Burning Deposit Bonus' },
    74: { type: 'exchange_non_cash_deposit_bonus_claim', name: 'Exchange Non Cash Deposit Bonus Claim' },
    75: { type: 'free_bets_on_promo_code_bonus_claim', name: 'Free Bets On Promo Code Bonus Claim' },
    76: { type: 'sports_free_bets_promo_code_bonus_claim', name: 'Sports Free Bets Promo Code Bonus Claim' },
  }


  const resolveTransactionType = (item) => {
    if (typeof item === 'number') {
      return transactionTypes[item]?.name || 'na'
    } else if (typeof item === 'string') {
      const id = Object.keys(transactionTypes).find(key => transactionTypes[key]?.type === item)
      return id !== undefined ? Number(id) : 'na'
    }
    return 'na'
  }

  if (Array.isArray(input)) {
    const results = input.map(item => Promise.resolve(resolveTransactionType(item)))
    return Promise.all(results) // Waits for all promises to resolve
  } else {
    return Promise.resolve(resolveTransactionType(input))
  }
}

export function getPlayerType(payload){
  const response = payload.playerCategory || payload.player_category || payload.playerType || payload.player_type
  return response;
}

export function getDateInStringFormat(date){
  if(date){
    return '"' + date + '"'
  }else{
    return date
  }
}

export async function getSportsMarket (dabitTransactionId) {
  const query = `SELECT bets_bets.market
    FROM bets_transactions
    INNER JOIN bets_bets
      ON bets_bets.market_id::text = bets_transactions.market_id
    WHERE bets_transactions.id = :dabitTransactionId limit 1`

  const market = await sequelize.query(query,
    {
      replacements: { dabitTransactionId },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    })
  return market[0] || null
}

export async function getSportsMarkets (dabitTransactionIds) {
  const query = `
    SELECT
      jsonb_object_agg(id, markets) AS result
    FROM (
      SELECT
        bets_transactions.id, jsonb_agg(bets_bets.market) AS markets
      FROM bets_transactions
        INNER JOIN bets_bets
          ON bets_bets.bet_id::text = bets_transactions.transaction_id
      WHERE bets_transactions.id IN (:dabitTransactionIds)
      GROUP BY bets_transactions.id
    ) S1`

  let markets = await sequelize.query(query,
    {
      replacements: { dabitTransactionIds },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    })

  markets = (!markets[0] || !markets[0].result || Object.keys(markets[0].result).length <= 0) ? null : markets[0].result;
  return markets;
}

export function getMarketStatusLabel (status) {
  if (status === 'SettledMarket') {
    return 'Market Settled';
  } else if (status === 'CancelledMarket' || status === 'Cancelmarket' || status === 'CancelMarket') {
    return 'Market Cancelled';
  } else if (status === 'CancelSettledMarket') {
    return 'Market Cancelled & Settled';
  } else if (status === 'Cancelmatchedbet' || status === 'CancelMatchedBet') {
    return 'Cancel Market Bet';
  } else if (status === 'Adjustsettledmarket' || status === 'AdjustSettledMarket') {
    return 'Adjust Settled Market';
  } else {
    return status;
  }
}

export function getMatchTypeDescription (description) {
  if (description === 0) {
    return 'Odds';
  } else if (description === 6) {
    return 'Session';
  } else if (description === 7) {
    return 'Indian Odds Market';
  } else if (description === 9) {
    return 'UK Session';
  } else if (description === 10) {
    return 'Manual Odds';
  } else if (description === 14) {
    return 'VirtualSportbook Odds';
  } else if (description === 15) {
    return 'Sportbook Odds';
  }
  return 0;
}

export function isEmptyArrayOrNullOrUndefined (value) {
  if (value === null || value === undefined) {
    return true;
  }

  if (Array.isArray(value) && value.length === 0) {
    return true;
  }

  return false;
}

export const getTenantBaseCurrency = async (currentUser, tenantId) => {
  let tenantBaseCurrency = 'EUR'
  if (currentUser.parentType === 'AdminUser' && tenantId) {
    const findTenantBaseCurrency = await sequelize.query(
      'SELECT * FROM tenant_credentials WHERE key = :key AND tenant_id IN (:tenantId)',
      {
        type: sequelize.QueryTypes.SELECT,
        replacements: { key: 'TENANT_BASE_CURRENCY', tenantId }
      }
    )
    tenantBaseCurrency = ''
    if (findTenantBaseCurrency.length > 0) {
      tenantBaseCurrency = findTenantBaseCurrency[0].value
    }
  }
  return tenantBaseCurrency
}
export function getCountryNameByCode (code) {
  const country = Countries.find(c => c.value === code.toUpperCase())
  return country ? country.name.split(' - ')[1] : ''
}

export function formatAmount (amount, tenantId, currencyCode = 'USD') {
  // Set Default Currency Code
  if (!currencyCode?.trim()) currencyCode = 'USD';

  const tenantFormats = CURRENCY_FORMAT[tenantId];
  if (!tenantFormats) {
    const numericAmount = Number(amount);
    return isNaN(numericAmount) ? '0.00' : numericAmount.toFixed(2); // fallback
  }
  const displayCurrency = currencyCode; // set in constant for dynamic comma seprator
  const formattedWithSymbol= Intl.NumberFormat(tenantFormats[currencyCode], {
    style: 'currency',
    currency: displayCurrency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);

  // Remove currency symbol only, retain commas and decimals
   const currencySymbol = new Intl.NumberFormat(tenantFormats[currencyCode], {
    style: 'currency',
    currency: displayCurrency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(0).replace(/[\d\s,.]/g, '');

   const formattedWithoutSymbol = formattedWithSymbol.replace(currencySymbol, '').trim();
   return `${formattedWithoutSymbol}`;

}

export function expandBonusComment(comment) {
  if (!comment) return comment;

  // Check if the comment is an abbreviated bonus comment
  if (BONUS_COMMENT_FULL_TEXT[comment]) {
    return BONUS_COMMENT_FULL_TEXT[comment];
  }

  // Return original comment if not an abbreviation
  return comment;
}
