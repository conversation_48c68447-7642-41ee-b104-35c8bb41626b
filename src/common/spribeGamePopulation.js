import axios from 'axios'
import { QueryTypes } from 'sequelize'
import db, { sequelize } from '../db/models'
import { SPRIBE_GAMES_URL } from './constants'
import syncOrOverrideCasinoProvider from './syncOrOverrideCasinoProvider'
const { Op } = require('sequelize');


export default async (reqBody = null) => {
  const spribeGameData = await axios({
    url: SPRIBE_GAMES_URL,
    method: 'post',
    maxBodyLength: Infinity,
    headers: {
      'Content-Type': 'application/json'
    },
    data: reqBody
  })

  const queueProcessStatus = await db.QueueProcessStatus.findOne({
    where: {
      service: 'spribe_game_seeding_cron',
      status: 1
    },
    attributes: ['id']
  })
  if (!queueProcessStatus) {
    return {
      success: false,
      message: "Queue Process Stopped."
    }
  }


  const casinoProvider = await db.CasinoProvider.findOne({
    where: {
      name: '<PERSON><PERSON><PERSON><PERSON>'
    },
    attributes: ['id'],
    raw: true
  })

  let tenants = await sequelize.query(`
      SELECT "tenant_id" AS "tenantId"
      FROM "public"."tenant_theme_settings" AS "TenantThemeSetting" WHERE '${casinoProvider.id}' = ANY (string_to_array(assigned_providers, ','))
      ${reqBody?.tenantId ? ` AND tenant_id = '${reqBody?.tenantId}'` : ''}
      `,
    { type: QueryTypes.SELECT, useMaster: false })
  if (Array.isArray(reqBody?.tenantIds) && !reqBody?.tenantIds.includes('0')) {
    tenants = tenants.filter(tenant => reqBody.tenantIds.includes(tenant.tenantId));
  }
  if (reqBody?.isSuperAdmin) {
    tenants = [{ tenantId: 0 }]
  } else {
    tenants = [{ tenantId: 0 }, ...tenants]
  }

  if (tenants.length <= 0) {
    return {
      success: true,
      message: "Provider not enabled for this tenant."
    }
  }


  let tenantIds = tenants.map(tenant => tenant.tenantId)


  if (reqBody?.override_other_providers || reqBody?.sync) {
    await syncOrOverrideCasinoProvider(tenantIds, casinoProvider.id, 'Spribe', reqBody?.override_other_providers, reqBody?.sync)
  }

  if (spribeGameData?.data === null) {
    throw new Error('spribeWorker: The data to be inserted is not present')
  }
}
