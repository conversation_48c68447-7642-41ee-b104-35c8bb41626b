import axios from 'axios'
import { SPRIBE_GAMES_URL } from './constants'


export default async (reqBody = null) => {
  const spribeGameData = await axios({
    url: SPRIBE_GAMES_URL,
    method: 'post',
    maxBodyLength: Infinity,
    headers: {
      'Content-Type': 'application/json'
    },
    data: reqBody
  })

  if (spribeGameData?.data === null) {
    throw new Error('spribeWorker: The data to be inserted is not present')
  }
}
