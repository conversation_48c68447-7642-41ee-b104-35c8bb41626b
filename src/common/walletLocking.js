import { TABLES } from '../common/constants'
import db from '../db/models'

export const walletLocking = async (user, sequelizeTransaction) => {
  const walletDetails = await db.Wallet.findOne({
    where: { ownerId: user.id, ownerType: TABLES.USER },
    transaction: sequelizeTransaction,
    lock: {
      level: sequelizeTransaction.LOCK.UPDATE,
      of: db.Wallet
    },
    skipLocked: false
  })
  return walletDetails

}
