import axios from "axios"
import config from "../configs/app.config"
import { JOB_CONSTANT } from "./constants"
import db from '../db/models'
import createLog from "./createLog"
import updateLog from "./updateLog"

export default async (jobId, userId) => {
    const data = JSON.stringify({
        job_id: jobId,
        user_id: userId,
        secret_token: config.get('queue_job.secretToken')
      })

      let reqResLog = await createLog(jobId, userId)

      var apiConfig = {
        method: JOB_CONSTANT.METHOD,
        maxBodyLength: JOB_CONSTANT.MAX_BODY_LENGTH,
        url: config.get('queue_job.apiUrl'),
        headers: JOB_CONSTANT.HEADERS,
        data: data
      }
      const apiResponse = await axios(apiConfig)
        .then(function (response) {
          return response.data
        })
        .catch(function (error,response) {
          db.RequestResponseLog.update({
            responseJson: error.response.data,
            responseStatus: 'failed',
            responseCode: error.response.status,
          },
            {
              where: { id: reqResLog.id }
            }
          )
          // throw error
        })
        if(apiResponse){
          await updateLog(reqResLog.id,apiResponse)
        }
        console.log('----------------api-------------response ----', apiResponse)
        return apiResponse

}
