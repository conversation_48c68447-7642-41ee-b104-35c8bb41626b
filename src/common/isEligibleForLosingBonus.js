import { Op } from 'sequelize'
import { BONUS_TYPES } from './constants'
import { getSportNgrData } from './losingBonus/getSportNgrData'
import { getSportCasinoBothNgrData } from './losingBonus/getSportCasinoBothNgrData'
import { sequelize } from '../db/models'

export const isEligibleForLosingBonus = async ({ TransactionModel, BetsTransactionModel, userWallet, dateObj, userId, tenantId, bonusKind, dates, gameIds, casinoProviderIds }) => {
  const data = { userId: userWallet.ownerId, tenantId: tenantId, startDate: dates.startDate, endDate: dates.endDate, dateObj: dateObj, userWallet: userWallet, casinoProviderIds, gameIds }
  if (bonusKind === BONUS_TYPES.LOSING_SPORT) {
    const sportLosingAmount = await getSportNgrData(data)
    if(sportLosingAmount){
      return sportLosingAmount
    }else{
      return false
    }
  }

  if (bonusKind === BONUS_TYPES.LOSING_BOTH) {
    const sportLosingAmount = await getSportCasinoBothNgrData(data)
    if(sportLosingAmount){
      return sportLosingAmount
    }else{
      return false
    }
  }

  const query = `
    WITH game_filter AS (
    SELECT UNNEST(ARRAY[:gameIds]) AS game_ids
  ),
  filtered_transactions AS(
    SELECT
    transactions.debit_transaction_id,
    SUM(transactions.amount) AS amount
  FROM transactions
  ${gameIds.length > 0 ? `
    LEFT JOIN game_filter gf
    ON transactions.game_id = gf.game_ids
       OR transactions.table_id::TEXT = gf.game_ids
       OR transactions.seat_id = gf.game_ids
  ` : ''}
  WHERE transactions.target_wallet_id = :targetWalletId
    AND transactions.transaction_type = 1
    AND transactions.amount >= 0
    AND transactions.created_at >= :startDate
    AND transactions.created_at <= :endDate
    AND (
    -- Case 1: Both arrays are empty, include all transactions
    (${gameIds.length} = 0 AND ${casinoProviderIds.length} = 0)

    -- Case 2: gameIds is non-empty, casinoProviderIds is empty
    ${gameIds.length > 0 ? `OR (${casinoProviderIds.length} = 0 AND gf.game_ids IS NOT NULL)` : ''}

    -- Case 3: gameIds is empty, casinoProviderIds is non-empty
    OR (${gameIds.length} = 0 AND ${casinoProviderIds.length} > 0 AND transactions.provider_id = ANY(ARRAY[:casinoProviderIds]::int[]))

    -- Case 4: Both arrays are non-empty
    ${gameIds.length > 0 ? `
    OR (${casinoProviderIds.length} > 0 AND (
        gf.game_ids IS NOT NULL OR transactions.provider_id = ANY(ARRAY[:casinoProviderIds]::int[])
    ))` : ''}
    )
  GROUP BY
    transactions.debit_transaction_id
    )
  SELECT
    debit_transaction_id,
    amount
  FROM filtered_transactions;
  `

  const transactions = await sequelize.query(query, {
    replacements: {
      targetWalletId: userWallet.id,
      casinoProviderIds: casinoProviderIds.length > 0 ? casinoProviderIds : null,
      gameIds: gameIds.length > 0 ? gameIds : null,
      startDate: dateObj.createdAt[Symbol.for('gte')],
      endDate: dateObj.createdAt[Symbol.for('lte')]
    },
    type: sequelize.QueryTypes.SELECT
  })

  let { sumWins, ...transactionObj } = transactions.reduce((currentObj, element) => {
    if (element.amount === 0) {
      currentObj.loss.push(element.debit_transaction_id)
    } else if (element.amount > 0) {
      currentObj.sumWins += element.amount
      currentObj.wins.push(element.debit_transaction_id)
    }
    return currentObj
  }, { loss: [], wins: [], sumWins: 0 })

  const sumLoss = await TransactionModel.sum('amount', {
    where: {
      transactionId: { [Op.in]: transactionObj.loss },
      sourceWalletId: userWallet.id,
      transactionType: 0,
      tenantId
    }
  })

  const winDebitTransactionSum = await TransactionModel.sum('amount', {
    where: {
      transactionId: { [Op.in]: transactionObj.wins },
      sourceWalletId: userWallet.id,
      transactionType: 0,
      tenantId
    }
  })

  sumWins = sumWins - winDebitTransactionSum

  // const betTransactions = await BetsTransactionModel.findAll({
  //   where: {
  //     userId,
  //     ...dateObj
  //   },
  //   raw: true
  // })

  // const betTransactionAggregationObj = betTransactions.reduce((currentObj, element) => {
  //   (currentObj[element.betslipId] = currentObj[element.betslipId] || []).push(element)
  //   return currentObj
  // }, {})

  // for (const betSlipId in betTransactionAggregationObj) {
  //   let flag = false
  //   for (const transaction of betTransactionAggregationObj[betSlipId]) {
  //     if (transaction.journalEntry === 'cashout') {
  //       flag = true
  //     }
  //   }

  //   if (flag) {
  //     delete betTransactionAggregationObj[betSlipId]
  //   }
  // }
  // let betDebit = 0
  // let betCredit = 0

  // for (const betSlipId in betTransactionAggregationObj) {
  //   for (const transaction of betTransactionAggregationObj[betSlipId]) {
  //     if (transaction.journalEntry === 'debit') {
  //       betDebit += (+transaction.amount)
  //     } else

  //     if (transaction.journalEntry === 'credit') {
  //       betCredit += (+transaction.amount)
  //     }
  //   }
  // }

  // if ((betDebit - betCredit) <= 0) {
  //   sumWins += (betDebit - betCredit)
  // } else {
  //   sumLoss += (betDebit - betCredit)
  // }

  if (sumWins > sumLoss) {
    return false
  }

  return sumLoss - sumWins
}
