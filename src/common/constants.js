import config from '../configs/app.config'

export const TRANSACTION_TYPES = {
  DEBIT: 0,
  CREDIT: 1,
  ROLLBACK: 2,
  DEPOSIT: 3,
  WITHDRAW: 4,
  FAILED: 16,
  TIP: 7,
  DEBIT_NO_CASH: 8,
  CREDIT_NO_CASH: 9,
  R<PERSON><PERSON><PERSON><PERSON><PERSON>_NO_CASH: 10,
  NON_CASH_BONUS_CLAIM: 11,
  DEPOSIT_BONUS_CLAIM: 12,
  TIP_NON_CASH: 13,
  WITHDRAW_CANCEL: 14,
  JOINING_BONUS_CLAIMED: 15,
  PROMO_CODE_BONUS_CLAIMED: 17,
  ADMIN_ADD_NON_CASH: 5,
  BACK: 'Back',
  LAY: 'Lay',
  BURNING_LOSING_BONUS: 39,
  BURNING_JOINING_BONUS: 40,
  PLAYER_CATEGORIZATION_BONUS: 41,
  BURNING_DEPOSIT_BONUS: 42,
  ROY<PERSON><PERSON>_BONUS_NON_CASH: 44,
  ROYALTY_BONUS_CASH: 45,
  BURNING_PROMOCODE_BONUS: 50,
  DEBIT_OTB_CASH: 46,
  R<PERSON><PERSON><PERSON>CK_OTB_CASH: 47,
  BURNING_MANUAL_LOSING_BONUS: 51,
  BURNING_NON_CASH_BONUS_AMOUNT: 43,
  REFERRAL_BONUS_CLAIM: 52,
  NON_CASH_DEPOSIT_BONUS_CLAIM: 70,
  FREE_BETS_DEPOSIT_BONUS_CLAIM : 68,
  SPORTS_FREE_BETS_DEPOSIT_BONUS_CLAIM: 69,
  NON_CASH_REFERRAL_BONUS_CLAIM : 72,
  NON_CASH_BURNING_DEPOSIT_BONUS : 73,
  EXCHANGE_DEPOSIT_BONUS_CLAIM: 37,
  EXCHANGE_NON_CASH_DEPOSIT_BONUS_CLAIM: 74
}

export const BONUS_TYPES = {
  DEPOSIT: 'deposit',
  LOSING: 'losing',
  JOINING: 'joining',
  DEPOSIT_SPORTS: 'deposit_sport',
  DEPOSIT_INSTANT: 'deposit_instant',
  PROMO_CODE: 'promo_code',
  LOSING_SPORT: 'losing_sport',
  LOSING_BOTH: 'losing_both',
  DEPOSIT_BOTH: 'deposit_both',
  REFERRAL_CODE: 'referral_code',
  AUTO_BONUS_ACTIVATE: 'auto_bonus_activate'
}

export const BONUS_CANCEL_TYPE = {
  MULTIPLE_DEPOSIT: 'multiple_deposit',
  MULTIPLE_WITHDRAW: 'multiple_withdraw',
  BOTH: 'both',
  NONE: 'none'
}

export const BONUS_STATUS = {
  ACTIVE: 'active',
  CLAIMED: 'claimed',
  CANCELLED: 'cancelled',
  EXPIRED: 'expired',
  IN_PROGRESS: 'in_progress'
}

export const DEPOSIT_INSTANT_BONUS_TYPES = {
  INSTANT: 'instant',
  RECURRING: 'recurring'
}

export const RECURRING_BONUS_TYPES = {
  EVERY_TIME: 'every_time',
  ONCE_A_DAY: 'once_a_day',
  CUSTOM_DEPOSITS: 'custom_deposits'
}

export const SPORTS = {
  DEPOSIT_BONUS_PENDING: 7
}

export const DEPOSIT_BONUS_WALLET_TYPES = {
  0: 'amount',
  1: 'nonCashAmount',
  2: 'oneTimeBonusAmount',
  3: 'sportsFreebetAmount'
}

export const DEPOSIT_BONUS_ALLOWED = {
  BOTH: 0,
  MANUAL: 1,
  PAYMENT_GATEWAY: 2
}

export const DEPOSIT_ROLLING_CALCULATION_METHODS = {
  TOTAL_BASED_ROLLING: 2,
  BONUS_BASED_ROLLING: 1,
  DEPOSIT_BASED_ROLLING: 3
}

// For sport betting
export const paymentForCodes = {
  BET_PLACEMENT: 1,
  WON: 2,
  CASHOUT: 3,
  REFUND: 4,
  LOST_BY_RESETTLEMENT: 5,
  DEPOSIT_BONUS_CLAIMED: 6,
  DEPOSIT_BONUS_PENDING: 7,
  DEPOSIT_BONUS_CANCELLED: 8
}

export const JETFAIR_INTEGRATION_CONSTANT = {
  INSUFFICIENT_FUND: 'Insufficient fund',
  INSUFFICIENT_FUND_CODE: 402,
  INTERNAL_ERROR_CODE: 500,
  NEGATIVE_AMOUNT: 'Amount is negative',
  CURRENCY_ID: '2',
  CURRENCY_NAME: 'INR',
  USER_NOT_FOUND: 'User not found',
  SUCCESS: 'Success',
  FAILED: 'Failure',
  INTERNAL_SERVER_ERROR: 'Internal Server Error',
  DUPLICATE_TXN_ID: 'duplicate transaction Id',
  NULL: null,
  INVALID_USER: 'Username is invalid',
  INVALID_AGENT: 'agentUsername is invalid',
  INVALID_TENANT: 'site is invalid',
  INVALID_ACTION: 'action is not invalid',
  INVALID_SITECODE: 'siteCode is invalid',
  INVALID_SECRETKEY: 'secretKey is invalid',
  JETFAIR_SECRETKEY: 'jetfair_token',
  TXN_NOT_FOUND: 'Transaction not found',
  INVALID_BETLIMIT: 'Bet limit exceed',
  NEGATIVE_AMOUNT: 'Invalid amount',
  INSUFFICIENT_FUND: 'Insufficient fund',
  TXN_ALREADY_DONE: 'Transaction already done',
  INVALID_MARKET: 'Invalid market',
  INVALID_SETTLEd_MARKET: 'market already settled',
  INVALID_RESETTLED_MARKET: 'market already resettled',
  INVALID_CANCEL_SETTLED_MARKET: 'cancel market settled',
  CREDIT: 'CR',
  DEBIT: 'DR',
  PLACE_BET: 1,
  CANCEL_BET: 4,
  SETTLE_MARKET_WON: 2,
  SETTLE_MARKET_LOST: 5,
  MARKET_CANCEL_CREDIT: 4,
  MARKET_CANCEL_DEBIT: 5,
  CANCEL_SETTLED_MARKET: 5,
  RESETTLE: 6,
  PLACE_BET_CANCEL: 9,
  PLACE_BET_TXN_CODE: 'PlaceMatchedBet',
  CANCEL_BET_TXN_CODE: 'CancelMatchedBet',
  SETTLE_MARKET_TXN_CODE: 'SettledMarket',
  MARKET_CANCEL_TXN_CODE: 'CancelMarket',
  CANCEL_SETTLED_MARKET_TXN_CODE: 'CancelSettledMarket',
  ADJUST_SETTLED_MARKET_TXN_CODE: 'AdjustSettledMarket',
  CANCEL_SETTLED_BET_TXN_CODE: 'CancelSettledBet',
  RESETTLE_TXN_CODE: 'ResettleMarket',
  APP_JETFAIR_AGENT_USER_NAME: 'APP_JETFAIR_AGENT_USER_NAME',
  APP_JETFAIR_SECRETKEY: 'APP_JETFAIR_SECRETKEY',
  APP_JETFAIR_SITECODE: 'APP_JETFAIR_SITECODE',
  PLACE_BET_DESCRIPTION: 'jetfair place bet',
  CANCEL_PLACE_BET_DESCRIPTION: 'jetfair cancel place bet',
  SETTLE_MARKET_WON_DESCRIPTION: 'jetfair settle market won',
  SETTLE_MARKET_LOST_DESCRIPTION: 'jetfair settle market lost',
  MARKET_CANCEL_DESCRIPTION: 'jetfair market cancel',
  CANCEL_SETTLED_MARKET_DESCRIPTION: 'jetfair cancel settled market',
  PLACE_BET_REFUND_DESCRIPTION: 'jetfair place bet transaction refund',
  RESETTLE_MARKET_DESCRIPTION: 'jetfair resettle market',
  BET_SLIP_STATUS_ACCEPTED: 'accepted',
  IN_GAME: 'in_game',
  INVALID_BET: 'bet is already processed',
  DAILY_BAITING_LIMIT_REACHED: 'Daily betting limit reached',
  WEEKLY_BAITING_LIMIT_REACHED: 'Weekly betting limit reached',
  MONTHLY_BAITING_LIMIT_REACHED: 'Monthly betting limit reached',
  SITE_DOWN: 'Site is under maintenance'
}

export const SUBSCRIPTION_CHANNEL = {
  USER_WALLET_BALANCE: 'USER_WALLET_BALANCE',
  DOCUMENT_NOTIFICATION: 'ADMIN_NOTIFICATION_CHANNEL',
  PLAYER_NOTIFICATION: 'PLAYER_NOTIFICATION_CHANNEL',
  USER_WITHDRAW_NOTIFICATION: 'USER_WITHDRAW_NOTIFICATION',
  USER_DEPOSIT_NOTIFICATION: 'USER_DEPOSIT_NOTIFICATION',
  IS_MAINTENANCE_MODE: 'IS_MAINTENANCE_MODE',
  QUEUE_WORKER: 'QUEUE_WORKER',
  USER_PG_DEPOSIT_NOTIFICATION: 'USER_PG_DEPOSIT_NOTIFICATION'
}

export const REQUEST_RESPONSE_LOG = {
  SUCCESS_RESPONSE_STATUS: 'success',
  FAILED_RESPONSE_STATUS: 'failed',
  SUCCESS_RESPONSE_CODE: 200,
  FAILED_RESPONSE_CODE: 400,
  ERROR_CODE: 400
}

export const DEPOSIT_BONUS_TRANSACTION_STATUS = {
  SUCCESS: 'success',
  FAILED: 'failed',
  PENDING: 'pending'
}

export const WITHDRAW_REQUEST_STATUS = {
  PENDING: 'pending',
  CANCELLED: 'cancelled',
  REJECTED: 'rejected',
  APPROVED: 'approved',
  PENDING_BY_BANK: 'pending_by_bank',
  VERIFIED: 'verified'
}

export const FIXTURE_STATUS = {
  NOT_STARTED: 1,
  IN_PROGRESS: 2,
  FINISHED: 3,
  CANCELLED: 4
}

export const BET_TYPE = {
  BACK: 1,
  LAY: 2
}

export const RESPONSIBLE_GAMING_CONSTANT = {
  DAILY_BETTING_LIMIT: 'dailyBettingLimit',
  WEEKLY_BETTING_LIMIT: 'weeklyBettingLimit',
  MONTHLY_BETTING_LIMIT: 'monthlyBettingLimit'
}

export const JOB_CONSTANT = {
  METHOD: 'post',
  MAX_BODY_LENGTH: Infinity,
  HEADERS: { 'Content-Type': 'application/json' }
}

export const QUEUE_WORKER_CONSTANT = {
  READY: 0,
  IN_PROGRESS: 1,
  DONE: 2,
  FAILED: 3,
  BET_TRANSACTION: 'bet_transaction',
  CASINO_TRANSACTION: 'casino_transaction',
  SPORT_CASINO_TRANSACTION: 'sport_casino_transaction',
  USER_TRANSACTION: 'user_transaction',
  TYPE: 'casino_transaction',
  AFFILIATED_SYSTEM_SMARTICO: 'affiliatedSystemSmartico',
  SMARTICO_WITHDRAWAL_COMPLETED: 'smartico_withdrawal_completed',
  DEPOSIT_WAGER_TRACKING: 'deposit_wager_tracking',
  BET_PLACED: 'bet_placed',
  SMARTICO_WALLET_UPDATE: 'smartico_wallet_update',
  SMARTICO_DEPOSIT_APPROVED: 'smartico_deposit',
  DEP_WITHDRAW_SMS_OTP: 'dep_withdraw_sms_otp',
  BONUS: 'bonus',
  ELASTIC_RECORD_DELETION: 'elastic_record_deletion',
  AUDIT_LOG: 'audit_log',
  SYNC_CATEORY: 'sync_category',
  GAME_SEED_MANUAL: 'game_seed_manual',
  EXPORT_CSV: 'export_csv'
}

export const SPORT_CASINO_TXN_COMMENT = {
  EXCHANGE_PLACE_BET_NON_CASH_DEBIT: 'Exchange place bet non cash debit',
  EXCHANGE_PLACE_BET_CASH_DEBIT: 'Exchange place bet cash debit',
  EXCHANGE_PLACE_BET_CASH_CREDIT: 'Exchange place bet cash credit',
  EXCHANGE_REFUND_CANCEL_BET_CASH_DEBIT: 'Exchange refund cancel bet cash debit',
  EXCHANGE_REFUND_CANCEL_BET_NON_CASH_DEBIT: 'Exchange refund cancel bet non cash debit',
  EXCHANGE_REFUND_MARKET_CANCEL_NON_CASH_DEBIT: 'Exchange refund market cancel non cash debit',
  EXCHANGE_REFUND_CANCEL_BET_NON_CASH_CREDIT: 'Exchange refund cancel bet non cash credit',
  EXCHANGE_REFUND_MARKET_CANCEL_NON_CASH_CREDIT: 'Exchange refund market cancel non cash credit',
  EXCHANGE_REFUND_MARKET_CANCEL_CASH_DEBIT: 'Exchange refund market cancel cash debit',
  EXCHANGE_SETTLE_MARKET_CASH_DEBIT: 'Exchange settle market cash debit',
  EXCHANGE_RESETTLE_MARKET_CASH_DEBIT: 'Exchange Resettle market cash debit',
  EXCHANGE_CANCEL_SETTLED_MARKET_CASH_DEBIT: 'Exchange cancel settled market cash debit',
  EXCHANGE_REFUND_CANCEL_BET_CASH_CREDIT: 'Exchange refund cancel bet cash credit',
  EXCHANGE_REFUND_MARKET_CANCEL_CASH_CREDIT: 'Exchange refund market cancel cash credit',
  EXCHANGE_SETTLE_MARKET_CASH_CREDIT: 'Exchange settle market cash credit',
  EXCHANGE_RESETTLE_MARKET_CASH_CREDIT: 'Exchange Resettle market cash credit',
  EXCHANGE_CANCEL_SETTLED_MARKET_CASH_CREDIT: 'Exchange cancel settled market cash credit',
  EXCHANGE_DEPOSIT_BONUS_CLAIM: 'Exchange deposit bonus claim'
}

export const SPORT_CASINO_TXN_TYPE = {
  EXCHANGE_PLACE_BET_NON_CASH_DEBIT: 20,
  EXCHANGE_PLACE_BET_CASH_DEBIT: 21,
  EXCHANGE_PLACE_BET_CASH_CREDIT: 22,
  EXCHANGE_REFUND_CANCEL_BET_NON_CASH_DEBIT: 23,
  EXCHANGE_REFUND_CANCEL_BET_CASH_DEBIT: 24,
  EXCHANGE_REFUND_CANCEL_BET_NON_CASH_CREDIT: 25,
  EXCHANGE_REFUND_CANCEL_BET_CASH_CREDIT: 26,
  EXCHANGE_REFUND_MARKET_CANCEL_NON_CASH_DEBIT: 27,
  EXCHANGE_REFUND_MARKET_CANCEL_CASH_DEBIT: 28,
  EXCHANGE_REFUND_MARKET_CANCEL_NON_CASH_CREDIT: 29,
  EXCHANGE_REFUND_MARKET_CANCEL_CASH_CREDIT: 30,
  EXCHANGE_SETTLE_MARKET_CASH_CREDIT: 31,
  EXCHANGE_SETTLE_MARKET_CASH_DEBIT: 32,
  EXCHANGE_RESETTLE_MARKET_CASH_CREDIT: 33,
  EXCHANGE_RESETTLE_MARKET_CASH_DEBIT: 34,
  EXCHANGE_CANCEL_SETTLED_MARKET_CASH_CREDIT: 35,
  EXCHANGE_CANCEL_SETTLED_MARKET_CASH_DEBIT: 36,
  EXCHANGE_DEPOSIT_BONUS_CLAIM: 37,
  EXCHANGE_ADJUST_SETTLE_MARKET_CASH_CREDIT: 53,
  EXCHANGE_ADJUST_SETTLE_MARKET_CASH_DEBIT: 54,
  EXCHANGE_CANCEL_SETTLE_BET_NON_CASH_DEBIT: 55,
  EXCHANGE_CANCEL_SETTLE_BET_CASH_DEBIT: 56,
  EXCHANGE_CANCEL_SETTLE_BET_NON_CASH_CREDIT: 57,
  EXCHANGE_CANCEL_SETTLE_BET_CASH_CREDIT: 58,
}

export const DEPOSIT_REQUEST_STATUS = {
  OPEN: 'opened',
  COMPLETED: 'completed',
  CANCELED: 'canceled',
  FAILED: 'failed',
  IN_PROCESS: 'in_process',
  VERIFIED: 'verified',
  REJECTED: 'rejected',
  APPROVED: 'approved',
  PENDING: 'pending',
}

export const EXPORT_CSV_STATUS = {
  READY: 0,
  IN_PROGRESS: 1,
  DONE: 2,
  FAILED: 3
}

export const STAGE_DARWIN_PROVIDER = 5034
export const PROD_DARWIN_PROVIDER = 114
export const DARWIN_ICON = ''
export const DISABLED_DARWIN_GAMES = ['CMCRASHPT', 'CRAEPT', 'LUVASGPT', 'LUVASGSP', 'LUVASGSPPT']

export const STAGE_FUNKY_GAMES_PROVIDER = 5099
export const PROD_FUNKY_GAMES_PROVIDER = 147
export const FUNKY_ICON = ''

export const FUNKY_CERTIFIED_GAMES = [
  '602717', '602716', '60279', '602711', '602817', '60277', '60272', '60274', '60276', '602842',
  '60273', '602844', '602845', '602850', '602713', '602712', '602855', '602813', '60278', '602838',
  '602825', '602835', '602719', '602848', '60271', '60275', '602840', '602821', '602830', '602824',
  '602816', '602833', '602829', '602841', '602843', '602819', '602832', '602822', '602831', '602826',
  '602834', '602837', '880263', '190266', '190265', '880262', '190264', '190263', '190262', '190261',
  '880261', '60261', '10262', '102626', '10266', '102622', '102618', '102614', '102617', '102611',
  '10261', '102635', '102636', '102637', '102624', '102621', '102633', '102630', '102620', '8802638',
  '10264', '102619', '10269', '102613', '10263', '10265', '102632', '102623', '102615', '10267',
  '10268', '102612', '102627', '102616', '102625', '102628', '102629', '102631', '102634', '102610',
  '190267', '190268', '190269'
]

export const STAGE_WHITECLIFF_PROVIDER = 5100
export const PROD_WHITECLIFF_PROVIDER = 213
export const WHITECLIFF_ICON = ''


export const EXPORT_CSV_TYPE = {
  CASINO_TRANSACTION: 'casino_transactions',
  ACCOUNT_SUMMARY: 'account_summary',
  PLAYER_REPORT: 'player_report',
  PLAYER_REPORT_DB: 'player_report_db',
  FIRST_TIME_DEPOSIT_PLAYER_REPORT: 'first_time_deposit_player_report',
  PLAYER_FINANCIAL_REPORT: 'player_financial_report',
  FINANCIAL: 'financial',
  BET_REPORT: 'bet_report',
  BET_REPORT_DB: 'bet_report_db',
  SPORT_BETTING: 'sport_betting',
  SPORT_BETTING_DB: 'sport_betting_db',
  SPORT_TRANSACTION: 'sport_transactions',
  SPORT_TRANSACTION_DB: 'sport_transactions_db',
  UNIFIED_TRANSACTION_REPORT: 'unified_transaction_report',
  GAME_TRANSACTION_REPORT: 'game_transaction_report',
  PLAYER_NCB_REPORT: 'player_ncb_report',
  PLAYER_NCB_DB_REPORT: 'player_ncb_report_db',
  PLAYER_LIST: 'player_list',
  PROMO_CODE_USER_LIST: 'promo_code_users_list',
  SAMPLE_PLAYER: 'sample_player_list',
  SAMPLE_WITHDRAWAL: 'sample_withdrawal_list',
  SAMPLE_DEPOSIT: 'sample_deposit_list',
  DEPOSIT_UNVERIFIED_REQUEST: 'deposit_pending_request',
  DEPOSIT_VERIFIED_REQUEST: 'deposit_verified_request',
  DEPOSIT_REJECTED_REQUEST: 'deposit_rejected_request',
  WITHDRAW_VERIFIED_REQUEST: 'withdraw_verified_request',
  WITHDRAW_UNVERIFIED_REQUEST: 'withdraw_unverified_request',
  WITHDRAW_REJECTED_REQUEST: 'withdraw_rejected_request',
  SAMPLE_LANGUAGE_KEYS: 'sample_language_keys',
  MAIL_REPORT_CSV: 'mail_report_csv',
  BIGGEST_WINNER_REPORT: 'biggest_winner_report',
  PLAYER_REVENUE_REPORT: 'casino_player_revenue_report',
  AGENT_REVENUE_REPORT: 'casino_agent_revenue_report',
  AGENT_REVENUE_REPORT_V3: 'casino_agent_revenue_report_v3',
  PLAYER_FILTER_REPORT: 'player_filter_report',
  PLAYER_PERFORMANCE_AND_FINANCIAL_REPORT: 'player_performance_and_financial_report',
  PLAYER_FINANCIAL_REPORT_DB: 'player_financial_report_db',
  FINANCIAL_DB: 'financial_db',
  GAME_TRANSACTION_REPORT_DB: 'game_transaction_report_db',
  UNIFIED_TRANSACTION_REPORT_DB: 'unified_transaction_report_db',
  CASINO_TRANSACTION_DB: 'casino_transactions_db',
  ACCOUNT_SUMMARY_DB: 'account_summary_db',
  POWERPLAY_SETTLEMENT_REPORT: 'powerplay_settlement_report',
  RISK_EVALUATION_REPORT: 'risk_evaluation_report',
  CASHIER_BET_REPORT: 'cashier_bet_report',
  USER_LOGIN_HISTORY: 'user_login_history',
  SAMPLE_USER_RFID_LIST: 'sample_user_rfid_list',
}

export const TABLES = {
  TENANT: 'Tenants',
  USER: 'User',
  ADMIN_USER: 'AdminUser'
}
export const XAMAX_WITHDRAW_PROVIDER = 'Xamax Withdraw Provider';
export const SKY_WITHDRAW = 'Sky Withdraw Provider'
export const PAYWINGS_WITHDRAW = 'Paywings Withdraw Provider'
export const PAYCOOKIES_WITHDRAW = 'Paycookies Withdraw Provider'
export const ACKOPAY_WITHDRAW = 'Ackopay Withdraw Provider'
export const RPAYOUT_WITHDRAW = 'RPAYOUT'
export const INDIGRIT_WITHDRAW = 'Indigrit Withdrawal Provider'
export const PAYOUT_WITHDRAW_PROVIDER = 'Payout Withdrawal Provider'
export const PAYOUT_API_END_POINT = 'https://payout.runpay.co/api/payout/GetPayoutStatus'
export const EASY_RUPIA_END_POINT = 'https://api.easyrupia.com/api/PayoutAPI/statuscheck'
export const EASY_RUPIA_WITHDRAW = 'easyrupia'
export const UU_WALLET_WITHDRAW_PROVIDER = 'UU Wallet Withdraw Provider';

export const STAGE_TENANTS = {
  1: { domain: 'hal567.com', name: 'Samino' },
  16: { domain: 'lionplay.co', name: 'Zovi24' },
  18: { domain: 'wiin24.com', name: 'Royal24' },
  19: { domain: 'strikes247.com', name: 'Crown246' },
  20: { domain: 'spingt888.com', name: 'Spingt888	' },
  21: { domain: 'jeeto555.net', name: 'jeeto555' },
  22: { domain: 'winmarina365.com', name: 'winmarina365' },
  23: { domain: 'betmaster247.net', name: 'Bet Master 247' },
  24: { domain: 'sportway.live', name: 'Sportway' },
  53: { domain: 'cric888.net', name: 'Cric888' },
  54: { domain: 'roylfc.com', name: 'Marina888' },
  55: { domain: '11beast.com', name: '11beast' },
  90: { domain: '8dex.store', name: 'AceXBet365' },
  91: { domain: '', name: '8dex Brand 2' },
  15: { domain: "onecard.com", name: "One Card" },
  119: { domain: "www.moneywin365.in", name: "Moneywin365" },
  120: { domain: "www.superbetex.com", name: "WingtNet" },
  152: { domain: "www.8dex.xyz", name: "88Punt" },
  185: { domain: "www.ingrandstation.com", name: "GoldenIsland" },
  222: { domain: "www.8dex.cloud", name: "cric247" }
}

export const PROD_TENANTS = {
  3: { domain: 'hukum247.com', name: 'Hukum247' },
  20: { domain: 'wingt888.com', name: 'WinGt888' },
  18: { domain: 'crown246.com', name: 'Crown246' },
  1: { domain: 'roylfc567.com', name: 'Samino' },
  21: { domain: 'betmaster247.com', name: 'BetMaster247' },
  2: { domain: 'zovi24.com', name: 'Zovi24' },
  53: { domain: 'sportway.lk', name: 'Sportway' },
  54: { domain: 'jeeto555.com', name: 'jeeto555' },
  55: { domain: 'playpm365.com', name: 'playpm365' },
  56: { domain: 'cric888.com', name: 'Cric888' },
  57: { domain: 'lordbet.world', name: 'Lordbet' },
  86: { domain: 'marina888.com', name: 'Marina888' },
  119: { domain: 'acexbet365.com', name: 'AceXBet365' },
  152: { domain: 'moneywin365.com', name: 'Moneywin365' },
  153: { domain: 'betlab247.com', name: 'Betlab247' },
  185: { domain: 'wingt888.net', name: 'wingt888.NET' },
  218: { domain: 'zubabet.com.zm', name: 'ZubaBet' },
  251: { domain: '88Punt.net', name: '88Punt' },
  284: { domain: 'goldenisland.lk', name: 'GoldenIsland' },
}

export const INDIGRIT_INTEGRATION_CONSTANT = {
  PROVIDER_NAME: 'indigrit',
  FAILED_STATUS: 'failed',
  SUCCESS_STATUS: 'success',
  COMPLETED_STATUS: 'completed',
  PAYMENT_METHOD: 'UPI',
  INDIGRIT_TRANSACTION_COMMENTS: 'Deposit Request'
}

export const SASPAY_INTEGRATION_CONSTANT = {
  PROVIDER_NAME: 'saspay',
  FAILED_STATUS: 'failed',
  SUCCESS_STATUS: 'success',
  COMPLETED_STATUS: 'completed',
  PAYMENT_METHOD: 'UPI',
  TRANSACTION_COMMENTS: 'Deposit Request',
  STATUS_CHECK_API: 'https://np1.sas-pay.in/status-check',
  RESPONSE_NOT_FOUND: 'Response not found',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
  ORDER_DETAILS_NOT_FOUND: 'Order details not found',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  INVALID_ORDER_SIGNATURE: 'Invalid order signature',
  USER_NOT_FOUND: 'User not found',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully'
}

export const PAYWINGS_INTEGRATION_CONSTANT = {
  FAILED_STATUS: '1',
  SUCCESS_STATUS: '0',
  SUCCESS_RESPCODE: '0',
  REFUNDED_STATUS: '5',
  COMPLETED_STATUS: 'completed',
  PAYMENT_METHOD: 'UPI',
  TRANSACTION_COMMENTS: 'Deposit Request',
  PAYIN_STATUS_CHECK_API: 'https://status.paywings.in/api/Payin/TransactionStatusV2',
  PAYOUT_STATUS_CHECK_API: 'https://status.paywings.in/api/payout/TransactionStatusV2',
  RESPONSE_NOT_FOUND: 'Response not found',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
  ORDER_DETAILS_NOT_FOUND: 'Order details not found',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  INVALID_ORDER_SIGNATURE: 'Invalid order signature',
  USER_NOT_FOUND: 'User not found',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully',
  STATUS_NOT_UPDATED: 'Status not updated',
  TXN_NOT_FOUND: 'Transaction Not Found',
  INVALID_CLIENTID: 'Invalid ClientTID'
}

export const PAYCOOKIES_INTEGRATION_CONSTANT = {
  BASE_URL: 'https://api.paycookies.com',
  DOMAIN: 'api.paycookies.com',
  PAYOUT_STATUS_CHECK_API: 'https://api.paycookies.com/pg/v2/public/fund-transfer?refNumber=',
  FAILED_STATUS: 'FAILED',
  FAILED_STATUS_DEPO: 'FAIL',
  SUCCESS_STATUS: 'SUCCESS',
  MESSAGE: 'Fetched successfully!',
  RESPONSE_NOT_FOUND: 'Response not found',
  DEPOSIT_MESSAGE: 'Order details fetched successfully',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
  COMPLETED_STATUS: 'completed',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  TRANSACTION_COMMENTS: 'Deposit Request',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully',
  STATUS_NOT_UPDATED: 'Status not updated'
}

export const ACKOPAY_INTEGRATION_CONSTANT = {
  STATUS_CODE_SUCCESS: 1,
  STATUS_CODE_FAILED: 6,
  SUCCESS_STATUS: 1,
  FAILED_STATUS: 0,
  REVERSAL_STATUS: 4,
  RESPONSE_NOT_FOUND: 'Response not found',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
  COMPLETED_STATUS: 'completed',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  PAYMENT_METHOD: 'UPI',
  TRANSACTION_COMMENTS: 'Deposit Request',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully',
  STATUS_NOT_UPDATED: 'Status not updated',
  FAILED_TXNSTATUS: 0,
  PAID_TXNSTATUS: 1,
  CREATED_TXNSTATUS: 2,
  ATTEMPTED_TXNSTATUS: 3,
  REFUND_TXNSTATUS: 4,
  ACCEPTED_TXNSTATUS: 5,
  EXPIRED_TXNSTATUS: 6,
  PARTIALPAID_TXNSTATUS: 7,
  CANCELLED_TXNSTATUS: 8
}

export const JASPAY_INTEGRATION_CONSTANT = {
  APPROVED_STATUS: 'approved',
  REJECTED_STATUS: 'rejected',
  EXPIRED_STATUS: 'expired',
  RESPONSE_NOT_FOUND: 'Response not found',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
  COMPLETED_STATUS: 'completed',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  PAYMENT_METHOD: 'UPI',
  TRANSACTION_COMMENTS: 'Deposit Request',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully',
  STATUS_NOT_UPDATED: 'Status not updated',
  SUCCESS_STATUS_CODE: 200,
  INVALID_RESPONSE: 'Invalid response'
}

export const XAMAX_INTEGRATION_CONSTANT = {
  SUCCESS_TXNSTATUS: 'transaction_status_confirmed',
  CANCEL_TXNSTATUS: 'transaction_status_cancelled',
  FAILED_TXNSTATUS: 'transaction_status_failed',
  EXPIRED_TXNSTATUS: 'transaction_status_expired',
  REFUND_TXNSTATUS: 'transaction_status_refunded',
  TRANSACTION_COMMENTS: 'Deposit Request',
  COMPLETED_STATUS: 'completed',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  STATUS_NOT_UPDATED: 'Status not updated',
  SUCCESS_STATUS_CODE: 200,
  INVALID_RESPONSE: 'Invalid response',
  PAYMENT_METHOD: 'UPI',
  NOT_FOUND_STATUS_CODE: 404,
}

export const CLOUDCASH_PG = {
  SUCCESS_TXNSTATUS: '000',
  FAILED_TXNSTATUS: '001',
  APPROVED_TXNSTATUS: '006',
  REJECTED_TXNSTATUS: '007',
  CANCELLED_TXNSTATUS: '008',
  PENDING_TXNSTATUS: '009',

  INVALID_REQUEST: 'No deposit request found for these order id and gatewayTID',
  TRANSACTION_EXISTS: 'Transaction already exist for order ID',
  INVALID_KEY: 'Invalid Key',

  SUCCESS_STATUS_CODE: 200,
  INVALID_RESPONSE: 'Invalid response',
  COMPLETED_STATUS: 'completed',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  TRANSACTION_COMMENTS: 'Deposit Request',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully',
  STATUS_NOT_UPDATED: 'Status not updated',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
}

export const ZENXPAY_INTEGRATION_CONSTANT = {
  APPROVED_STATUS: 'approved',
  REJECTED_STATUS: 'rejected',
  EXPIRED_STATUS: 'expired',
  RESPONSE_NOT_FOUND: 'Response not found',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
  COMPLETED_STATUS: 'completed',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  PAYMENT_METHOD: 'UPI',
  TRANSACTION_COMMENTS: 'Deposit Request',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully',
  STATUS_NOT_UPDATED: 'Status not updated',
  SUCCESS_STATUS_CODE: 200,
  INVALID_RESPONSE: 'Invalid response'
}

export const SEYLAN_INTEGRATION_CONSTANT = {
  APPROVED_STATUS: 'approved',
  REJECTED_STATUS: 'rejected',
  EXPIRED_STATUS: 'expired',
  RESPONSE_NOT_FOUND: 'Response not found',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
  COMPLETED_STATUS: 'completed',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  PAYMENT_METHOD: 'CARD',
  TRANSACTION_COMMENTS: 'Deposit Request',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully',
  STATUS_NOT_UPDATED: 'Status not updated',
  SUCCESS_STATUS_CODE: 200,
  INVALID_RESPONSE: 'Invalid response'
}

export const TECHPAY_INTEGRATION_CONSTANT = {
  APPROVED_STATUS: 'approved',
  REJECTED_STATUS: 'rejected',
  EXPIRED_STATUS: 'expired',
  RESPONSE_NOT_FOUND: 'Response not found',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
  COMPLETED_STATUS: 'completed',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  PAYMENT_METHOD: 'UPI',
  TRANSACTION_COMMENTS: 'Deposit Request',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully',
  STATUS_NOT_UPDATED: 'Status not updated',
  SUCCESS_STATUS_CODE: 100,
  API_SUCCESS_CODE: 100,
  INVALID_RESPONSE: 'Invalid response'
}

export const RPAYOUT_INTEGRATION_CONSTANT = {
  FAILED_STATUS: 'FAILED',
  SUCCESS_STATUS: 'SUCCESS',
  PAYOUT_STATUS_CHECK_API_STAGE: 'https://demopayout.paymentsaga.com/payout/status',
  PAYOUT_STATUS_CHECK_API_PROD: 'https://payoutapi.runpay.co/payout/status',
  DONE_TRANSACTION_STATUS: 'DONE',
  FAIL_TRANSACTION_STATUS: 'FAIL'
}

export const EASY_RUPIA_INTEGRATION_CONSTANT = {
  FAILED_STATUS: '0',
  SUCCESS_STATUS: '1',
  PENDING_STATUS: '2',
  PROCESSING_STATUS: '3'
}

export const EASY_RUPIA_STATUS = {
  FAILED: 'Failure',
  SUCCESS: 'Success',
  PENDING: 'Pending'
}

export const PAYMENT_GATEWAY_QUEUE_TYPE = {
  PAYOUT_WITHDRAW_UPDATE_STATUS: 'payout_withdraw_update_status',
  INDIGRIT_DEPOSIT_UPDATE_STATUS: 'indigrit_deposit_update_status',
  PENDING_WITHDRAWAL_TRANSACTION: 'pending_withdrawal_transaction',
  PAYIN_DEPOSIT_UPDATE_STATUS: 'payin_deposit_update_status',
  SASPAY_DEPOSIT_UPDATE_STATUS: 'saspay_deposit_update_status',
  PENDING_DEPOSIT_REQUEST: 'pending_deposit_request',
  CREATE_WITHDRAW_REQUEST: 'create_withdraw_request',
}

export const PAYIN_DEPOSIT_STATUS_URL = 'https://runpay.co/api/Payment/Status?TransactionId='

export const SPORT_PROVIDER = {
  STAGE: 14,
  PROD: 15,
  JETFAIR: 'Jetfair',
  POWERPLAY: 'Powerplay',
  TURBOSTARS: 'TurboStars'
}

export const getPGSoftGameDataUrl = (url) => `${url}/external/Game/v2/Get`
export const STAGE_PGSOFT_PROVIDER = 5000
export const PROD_PGSOFT_PROVIDER = 49
export const STAGE_CASINO_ID = 166
export const PROD_CASINO_ID = 0
export const PGSOFT_ICON = 'https://ezugi-main-platform-prod-active-storage.s3.us-east-1.amazonaws.com/pages/135/logo/e440ffa6-24bc-4966-aba7-d1b018be24ce____pg-soft.jpg'
export const PAYMENT_GATEWAY_NAMES = {
  PAYWINGS_PAYIN: 'paywings',
  SAMBHAVPAY_PAYIN: 'sambhavPay',
  PAYCOOKIES_PAYIN: 'paycookies',
  ACKO_PAYIN: 'ackopay',
  PAYCOOKIES_PAYIN2: 'paycookies2',
  PAYCOOKIES_PAYIN3: 'paycookies3',
  PAYCOOKIES_PAYIN4: 'paycookies4',
  JASPAY_PAYIN: 'jaspay',
  XAMAX_PAYIN: 'xamax',
  CLOUDCASH_PAYIN: 'cloudcash',
  PEER_PAY_PAYIN: 'peerPay',
  PEER_PAY_PAYIN1: 'peerPay1',
  ZENXPAY_PAYIN: 'zenxpay',
  SEYLAN_PAYIN: 'seylan',
  TECHPAY_PAYIN: 'techpay'
}

export const SAMBHAVPAY_PG_INTEGRATION_CONSTANT = {
  REQUEST_SEQUENCE_STATUS: 'Mid|TotalAmount|TxnRefNo|OrderNo',
  RESPONSE_SEQUENCE_STATUS: 'Mid|OrderNo|TxnRefNo|TotalAmount|CurrencyName|MeTransReqType|AddField1|AddField2|AddField3|AddField4|AddField5|AddField6|AddField7|AddField8|AddField9|AddField10|EmailId|MobileNo|Address|City|State|Pincode|RespCode|RespMessage|PayAmount|TxnRespDate|UPIString',
  FAILED_STATUS: '01',
  SUCCESS_STATUS: '00',
  COMPLETED_STATUS: 'completed',
  PAYMENT_METHOD: 'UPI',
  TRANSACTION_COMMENTS: 'Deposit Request',
  RESPONSE_NOT_FOUND: 'Response not found',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully',
  STATUS_NOT_UPDATED: 'Status not updated',
  PENDING_FOR_AUTHORIZATION_STATUS: '03'
}

export const ALLOWED_PARALLEL_BONUS = 'parallelBonus'
export const ST8_PROVIDER_ID_STAGE = '16'
export const ST8_PROVIDER_ID_PROD = '48'
export const partnerMatrixFileName = (operator, type, date) => `${operator}_${type}_${date}.csv`
export const PM_USER_LIMIT = 2000
export const wyntaFileName = (operator, type, date) => `${operator}_${type.toUpperCase()}_${date}.tsv`

export const STAGE_PROVIDER = {
  EZUGI: '1',
  EVOLUTION: '2',
  RED_TIGER : '17',
  NET_ENT : '18'
}

export const PROD_PROVIDER = {
  EZUGI: '10',
  EVOLUTION: '1',
  RED_TIGER : '11',
  NET_ENT : '12'
}

export const EVOLUTION_PROVIDER = {
  STAGE: 2,
  PROD: 1
}

export const BONUS_TIER_TYPE = {
  TIER_1: 1,
  TIER_2: 2
}

export const PAYIN_COMMENT = 'Deposit Request'

export const ALLOWED_PERMISSIONS = {
  PLAYER_CATEGORIZATION: 'playerCategorization',
  ENABLE_FRAUD_DETECTION: 'enableFraudDetection',
  AFFILIATED_SYSTEM_SMARTICO: 'affiliatedSystemSmartico',
  AFFILIATED_SYSTEM_ALANBASE: 'alanbaseCrm',
  DYNAMIC_CATEGORY_ALLOCATION: 'dynamicCategoryAllocation',
  ENABLE_PLAYER_CATEGORY: 'enablePlayerCategory',
  CONFIGURATION_BACKUP: 'configurationBackup',
  REFERRAL_CODE: 'enableReferral',
  OFFERS: 'offerModule',
  AUTOMATIC_BONUS_ACTIVATE: 'autoActivateLosingBonus',
  MULTI_BONUS_ALLOWANCE: 'multiBonusAllowance',
  CASHBACK_BONUS_RECURRING_ACTIVATION: "cashbackBonusRecurringActivation",
}

export const DEPOSIT_WITHDRAW_JOB_STATUS = {
  NOT_STARTED: 0,
  IN_PROGRESS: 1,
  FAILED: 2,
  COMPLETED: 3,
  PARTIALLY_FAILED: 4
}

export const DEPOSIT_WITHDRAW_USER_STATUS = {
  NOT_STARTED: 0,
  FAILED: 1,
  COMPLETED: 2
}

export const MARINA888_TYPES = {
  MARINA_888_USER_PASSWORD_CHANGE: 'marina888UserPasswordChange',
  MARINA_888_AGENT: 'marina888Agent',
  MARINA_888_USER: 'marina888User',
  MARINA_888_USER_TRANSACTIONS: 'marina888UserTransaction',
  MARINA_888_AGENT_TRANSACTIONS: 'marina888AgentTransaction',
  MARINA_888_BONUS: 'marina888Bonus',
  MARINA_888_USER_BONUS: 'marina888UserBonus',
  MARINA_888_USER_BETS: 'marina888UserBets',
  MARINA_888_MIGRATION_CRON: 'marina888MigrationCron'
}

export const MARINA888_TYPES_ENDPOINT = {
  CREATE_USER: '/api/marina888/create-user',
  CREATE_AGENT: '/api/marina888/create-agent',
  TRANSACTION_DETAILS: '/api/marina888/transaction-details',
  CREATE_BONUS: '/api/marina888/create-bonus',
  CREATE_USER_BONUS: '/api/marina888/create-user-bonus',
  CREATE_USER_BETS: '/api/marina888/create-user-bet',
  PROCESS_TRANSACTION: '/api/marina888/process-user-transactions'

}

export const EVENT_TYPE = {
  player: 'Player',
  agent: 'Agent',
  SyncCategory : 'SyncCategory'
};

export const EVENT = {
  create: 'Create',
  read: 'Read',
  update: 'Update',
  toggle: 'Toggle',
  delete: 'Delete',
};

export const SMARTICO_EVENT_TYPES = {
  UPDATE_USER: 'smartico_user',
  WITHDRAWAL_REQUSTED: 'smartico_withdrawal_requested',
  WITHDRAWAL_APPROVED: 'smartico_withdrawal_approved',
  WITHDRAWAL_COMPLETED: 'smartico_withdrawal_completed',
  WITHDRAWAL_CANCELLED: 'smartico_withdrawal_cancelled',
  DEPOSIT_APPROVED: 'smartico_deposit',
  CASINO_BET: 'smartico_bet',
  CASINO_WIN: 'smartico_win',
  CASINO_REFUND: 'smartico_refund',
  BONUS: 'smartico_bonus',
  WALLET_UPDATE: 'smartico_wallet_update',
  LOGIN_STATS: 'smartico_login_stats',
  WALLET_UPDATE_BULK: 'smartico_wallet_update_bulk'

}

export const SMARTICO_EVENT_TYPES_MAPPING = {
  smartico_user: 'ezugi_player_details_update',
  smartico_withdrawal_requested: 'ezugi_online_withdrawal_pending',
  smartico_withdrawal_approved: 'ezugi_online_withdrawal_approved',
  smartico_withdrawal_cancelled: 'ezugi_online_withdrawal_rejected',
  smartico_withdrawal_completed: 'acc_withdrawal_completed',
  smartico_deposit: 'ezugi_online_deposit_approved',
  smartico_bet: 'casino_bet',
  smartico_win: 'casino_win',
  smartico_refund: 'casino_win',
  smartico_bonus: 'acc_bonus_approved',
  smartico_wallet_update: 'ezugi_wallet_update',
  smartico_login_stats: 'ezugi_player_login_stats_update',
  smartico_wallet_update_bulk: 'update_profile'
}

export const ALANBASE_EVENT_TYPES = {
  REGISTRATION: 'alanbase_registration',
  DEPOSIT: 'alanbase_deposit',
  WITHDRAWAL_COMPLETED: 'alanbase_withdrawal_completed',
  ALANBASE_WIN: 'alanbase_win',
  // Internal Sports Transactions
  ALANBASE_SB_PLACE_BET: 'alanbase_sb_place_bet', // DR // CR  // sb_bet // sb_win
  ALANBASE_SB_CANCEL_PLACE_BET: 'alanbase_sb_cancel_place_bet',
  ALANBASE_SB_MARKET_CANCEL: 'alanbase_sb_cancel_market', // CR sb_rejected_bet // DR sb_win_rollback
  ALANBASE_SB_SETTLE_MARKET: 'alanbase_sb_settle_market', // CR +  // DR - // sb_settled_bet
  ALANBASE_SB_RESETTLE_MARKET: 'alanbase_sb_resettle_market', // CR +  // DR - // sb_settled_bet
  ALANBASE_SB_CANCEL_SETTLE_MARKET: 'alanbase_sb_cancel_settle_market',
  ALANBASE_SB_CASHOUT: 'alanbase_sb_cashout', // CR sb_win // DR sb_
  ALANBASE_SB_CANCEL_CASHOUT: 'alanbase_sb_cancel_cashout',
}

export const ALANBASE_EVENT_TYPES_MAPPING = {
  alanbase_registration: 'registration',
  alanbase_deposit: 'deposit',
  alanbase_withdrawal_completed: 'withdrawal',
  alanbase_bet: 'casino_bet',
  alanbase_win: 'casino_win',
  // Alanbase Sports Transactions
  alanbase_sb_place_bet: 'sb_place_bet',
  alanbase_sb_cancel_place_bet: 'sb_cancel_place_bet',
  alanbase_sb_cancel_market: 'sb_cancel_market',
  alanbase_sb_settle_market: 'sb_settle_market',
  alanbase_sb_resettle_market: 'sb_resettle_market',
  alanbase_sb_cancel_settle_market: 'sb_cancel_settle_market',
  alanbase_sb_cashout: 'sb_cashout',
  alanbase_sb_cancel_cashout: 'sb_cancel_cashout',
}

export const STAG_ALLOWED_TENANTS_SMARTICO = [54]
export const PROD_ALLOWED_TENANTS_SMARTICO = [86]

export const STAG_ALLOWED_TENANTS_ALANBASE = [55]
export const PROD_ALLOWED_TENANTS_ALANBASE = [57]

export const CASINO_PROVIDERS_STAG = {
  6: 'Net Ent',
  2: 'Evolution',
  1: 'Ezugi',
  13: 'Spribe',
  16: 'st8',
  17: 'Red Tiger',
  18: 'NetEnt',
  5000: 'pgsoft',
  5033: '8DEX',
  5034: 'darwin',
  14: 'Jetfair',
  5035: 'Powerplay',
  5036: 'zovi'
}

export const CASINO_PROVIDERS_PROD = {
  1: 'Evolution',
  10: 'Ezugi',
  15: 'Jetfair',
  12: 'Netent',
  49: 'pgsoft',
  50: 'Powerplay',
  11: 'Red Tiger',
  13: 'Spribe',
  48: 'st8'
}

export const SPORTS_PROVIDERS_STAG = {
  5133: 'TurboStars'
}

export const SPORTS_PROVIDERS_PROD = {
  181: 'TurboStars'
}

export const NEW_NGR_FORMULA_TENANT_IDS = config.get('env') === 'production' ? ['21', '153', '54', '152', '119', '86', '56', '218', '251'] : ['23', '91', '21', '119', '90', '54', '53', '22', '152']

export const BOT_ALLOWED_TENANTS = config.get('env') === 'production' ? ['20'] : ['120']

export const CASINO_PROVIDER_IDS_ARRAY = config.get('env') === 'production' ? ['1', '10', '11', '12', '13', '48', '49', '81', '114', '147', '180', '213', '246', '279', '312'] : ['1', '2', '6', '13', '16', '17', '18', '5000', '5033', '5034', '5067', '5099', '5100', '5132', '5134', '5165', '5231', '5265']
export const SPORT_PROVIDER_IDS_ARRAY = config.get('env') === 'production' ? ['15', '50', '181'] : ['14', '5035', '5133'] // Jetfair , Powerplay , TurboStars
export const GAME_ID_PROVIDER_IDS_ARRAY = config.get('env') === 'production' ? ['13'] : ['13'] // Spribe
export const ST8_PROVIDER_ID = config.get('env') === 'production' ? '48' : '16'
export const WHITECLIFF_PROVIDER_ID= config.get('env') === 'production' ? '213' : '5100'
export const ST8_SPORTS_SEAT_IDS_ARRAY = ['bti_sportsbook', 'sap_lobby', 'sbs_sportsbook']
export const TABLE_ID_PROVIDERS_ARRAY = config.get('env') === 'production' ? ['10', '1', '81', '11', '12'] : ['1', '2', '5067', '17', '18'] // Ezugi, Evolution, Arcade, Red Tiger, NetEnt

export const TABLE_ID_PROVIDERS = config.get('env') === 'development' ? '1,2,5067,17,18' : '10,1,5067,11,12' // Ezugi , Evolution , Arcade
export const GAME_ID_PROVIDERS = config.get('env') === 'development' ? '13' : '13' // Spribe
export const NOT_SEAT_ID_PROVIDERS = Array.from(new Set([...TABLE_ID_PROVIDERS.split(','), ...GAME_ID_PROVIDERS.split(',')])).join(',')

export const ZAMBIA_TENANT_ID = config.get('env') === 'production' ? '218' : '22';

export const SPORTS_PROVIDERS_STAGE = {
  JETFAIR: 14,
  POWERPLAY: 5035,
  TURBOSTARS: 5133
}

export const SPORTS_PROVIDER_PROD = {
  JETFAIR: 15,
  POWERPLAY: 50,
  TURBOSTARS: 181
}

export const PROMO_BONUS_WALLET_TYPES = {
  0: 'amount',
  1: 'nonCashAmount'
}

export const DEPOSIT_BONUS_BURN_TYPE = {
  SINGLE_TIME_USE: 0,
  MULTIPLE_TIME: 1
}

export const IST_AUTOMATIC_LOSING_BONUS_TENANTS = {
  STAGE_TENANTS: [20],
  PROD_TENANTS: [20]
}

export const MULTIPLE_PG_MAPPING = {
  paycookies: 'PAYCOOKIES_IDS'
}

export const BONUS_ENGINE_BONUS_TYPE = {
  DEPOSIT_ROLLOVER: 'deposit_rollover',
  SINGLE_TIME_USE_BONUS: 'single_time_use_bonus'
}

export const UTR = {
  TYPE: {
    DEPOSIT: 'deposit',
    WITHDRAW: 'withdraw',
    TRANSACTION: 'transaction'
  },
  STATUS: {
    OPENED: 0,
    APPROVED: 1,
    REJECTED: 2
  }
}
export const ASSET_URL_PREFIX = {
  PROD: 'https://assets.',
  STAG: 'https://assets.roylfc.com'
}

export const OTB_BALANCE_CONFIG = {
  STAGE: {
    54: true, // true for cash , false for noncash
    1: true // enabled for testing
  },
  PROD: {
    86: true, // true for cash , false for noncash
    1: true // enabled for testing
  }
}

export const OTB_TENANTS_CONFIG = { // setting to enable/disable One Time Bonus Feature for tenants
  STAGE: {
    54: true,
    1: true // enabled for testing
  },
  PROD: {
    86: true,
    1: true // enabled for testing
  }
}

export const ERROR_LOG_TYPE = {
  ADMIN: 0,
  USER_BE: 1,
  QUEUE: 2,
  PG: 3
}

export const USER_TYPE = 'User'

export const LOGIN_TYPE = {
  MOBILE: 1,
  EMAIL: 2,
  BOTH: 0
}

export const TRANSACTION_HISTORY_LOG_TYPE = {
  'FRAUD_DETECTION': 1,
  'SMARTICO': 2
}

export const TENANT_IDS = {
  JEETO_555: {
    STAGE: 21,
    PROD: 54
  }
}

export const ENVIORNMENT = {
  PRODUCTION: 'production',
  STAGING: 'staging'
}

export const CONFIGURATION_BACKUP_MODULE = {
  REGISTRATION_FIELDS: 1,
  CONFIGURATION_VARIABLE: 2,
  CONFIGURATION_SETTINGS: 3,
  THEME_GENERAL_SETTINGS: 4,
  HELPLINE_NUMBERS: 5,
  THEME: 6,
}

export const TENANT_SETTINGS_TYPE = {
  VIP_LEVEL_WAGER_MULTIPLIER: 1,
  GLOBAL_WAGER_MULTIPLIER: 2,
  MINIMUM_WAGERING_PERCENTAGE: 3,
  BOT_CONFIGURATION: 4,
  LOYALTY_PROGRAM_SETTINGS: 5
}

export const PG_SOFT_GAMES_URL = (config.get('env') === 'production') ? 'https://pgsoft.z1k9txvm.net/api/v1/getGameData' : 'https://stgpgsoft.z1k9txvm.net/api/v1/getGameData'
export const ST8_GAMES_URL = (config.get('env') === 'production') ? 'https://adminapi.8dexsuperadmin.com/api/st8':'https://stage-adminapi.8dexsuperadmin.com/api/st8'
export const SPRIBE_GAMES_URL =  (config.get('env') === 'production') ? 'https://adminapi.8dexsuperadmin.com/api/spribe':'https://stage-adminapi.8dexsuperadmin.com/api/spribe'
export const STAGE_SPRIBE_PROVIDER = 13
export const PROD_SPRIBE_PROVIDER = 13

export const GET_SPORTS_ACTIONS = {
  'Exchange': 'Jetfair',
  'BTI SportsBook': 'bti_sportsbook',
  'Powerplay Exchange': 'Powerplay',
  'Sap Exchange': 'sap_exchange',
  'Saba SportsBook': 'sbs_sportsbook'
};

export const GET_ST8_SPORTS_ACTIONS = {
  'bti_sportsbook': 'bti_sportsbook',
  'sbs_sportsbook': 'sbs_sportsbook',
  'sap_lobby': 'sap_lobby'
};


export const PEER_PAY_INTEGRATION_CONSTANT = {
  SUCCESS_TXNSTATUS: 'SUCCESS',
  FAILED_TXNSTATUS: 'FAILED',
  PENDING_TXNSTATUS: 'PENDING',
  ONHOLD_TXNSTATUS: 'ONHOLD',
  REJECTED_TXNSTATUS: 'REJECTED',
  INCOMPLETE_TXNSTATUS: 'INCOMPLETE',
  MISMATCHED_TXNSTATUS: 'MISMATCHED',
  TRANSACTION_COMMENTS: 'Deposit Request',
  COMPLETED_STATUS: 'completed',
  COMPLETED_STATUS_UPDATED: 'Completed status updated successfully',
  STATUS_ALREADY_UPDATED: 'Status already updated',
  FAILED_STATUS_UPDATED: 'Failed status updated successfully',
  DUPLICATE_TRANSACTION: 'Duplicate transaction',
  STATUS_NOT_UPDATED: 'Status not updated',
  SUCCESS_STATUS_CODE: 0,
  INVALID_RESPONSE: 'Invalid response',
  PAYMENT_METHOD: 'UPI'
}

export const OFFER_FREQUENCY = {
  DAILY: 0,
  WEEKLY: 1,
  MONTHLY: 2,
  END_OF_CAMPAIGN_PERIOD: 3
}

export const OFFER_WINNING_TYPE = {
  ROLLOVER_WON: 0,
  GGR: 1,
  NGR: 2,
  TOTAL_WAGERED : 3
}

export const CRON_LOG_STATUS = {
  FAILED: 0,
  SUCCESS:1,
}

export const OCR_EVENT = {
  OCR_TRANSACTION : 'ocr_transaction'
}

export const CASINO_MENU_IMAGES = [
  { name: 'Baccarat', url: 'tenants%2F23%2Fmenus%2F83117e52-d0b5-43bc-bcbf-a7bcec833df5____Baccarat-Icon.png' },
  { name: 'Blackjack', url: 'tenants%2F23%2Fmenus%2F3770ed53-f902-4e19-9b0e-05049b790939____jack_21_icon.jpeg' },
  { name: 'Banca Francesa', url: 'assets/images/BancaFrancesa.png' },
  { name: 'Roulette', url: 'tenants%2F1%2Fmenus%2F6b06f3b5-ce43-4fdc-ba65-ca129b1190a1____btMmy7xTwGoEX4yJuQAejPUc.png' },
  { name: 'Bingo', url: 'assets/images/bingo.png' },
  { name: 'Slot', url: 'tenants%2F1%2Fmenus%2F462f2be5-c461-4896-acae-05eeb7d4c054____undertable.png' },
  { name: 'Steps', url: 'assets/images/Steps.png' },
  { name: 'Color', url: 'assets/images/color.png' },
  { name: 'Crash', url: 'tenants%2F1%2Fmenus%2F6f075e54-84cf-4065-9bf7-2a6af5bed2de____Crash_game.png' },
  { name: 'Dragon Tiger', url: 'tenants%2F1%2Fmenus%2Fbfa53f24-529e-42b5-bbc9-c563d6fa1562____Virtual_Sports.png' },
  { name: 'Mines', url: 'assets/images/Mines.png' },
  { name: 'Limbo', url: 'assets/images/Limbo.png' },
  { name: 'Plinko', url: 'assets/images/plinko.png' },
  { name: 'Double', url: 'assets/images/double.png' },
  { name: 'TableGame', url: 'tenants%2F1%2Fmenus%2F01685afa-a232-4c34-ae49-aa50916397c3____undertable.png' },
  { name: 'SlotGame', url: 'tenants%2F1%2Fmenus%2F0a9a6190-1667-4184-aa96-40380e993e8a____1000216216.png' },
  { name: 'Keno', url: 'tenants%2F1%2Fmenus%2F6b06f3b5-ce43-4fdc-ba65-ca129b1190a1____btMmy7xTwGoEX4yJuQAejPUc.png' },
  { name: 'CardGame', url: 'assets/images/plinko.png' },
];

export const CSV_LOG_TYPE = {
  REQUEST_RESPONSE_LOG: 1,
  CRON_LOG: 2
}

export const WHITECLIFF_CREDENTIALS = {
  WHITECLIFF_GAME_LIST_URL: 'WHITECLIFF_GAME_LIST_URL',
  WHITECLIFF_URL: 'WHITECLIFF_URL',
  INR: {
    WHITECLIFF_AG_CODE: 'WHITECLIFF_AG_CODE_INR',
    WHITECLIFF_AG_TOKEN: 'WHITECLIFF_AG_TOKEN_INR'
  },
  EUR: {
    WHITECLIFF_AG_CODE: 'WHITECLIFF_AG_CODE_EUR',
    WHITECLIFF_AG_TOKEN: 'WHITECLIFF_AG_TOKEN_EUR'
  },
  USD: {
    WHITECLIFF_AG_CODE: 'WHITECLIFF_AG_CODE_USD',
    WHITECLIFF_AG_TOKEN: 'WHITECLIFF_AG_TOKEN_USD'
  },
  LKR: {
    WHITECLIFF_AG_CODE: 'WHITECLIFF_AG_CODE_LKR',
    WHITECLIFF_AG_TOKEN: 'WHITECLIFF_AG_TOKEN_LKR'
  }
}
export const WHITECLIFF_SPORTS_PROVIDERS = [
  { name: 'Evolution Asia', prdId: 1 },
  { name: 'Illustrate Analytics', prdId: 100 },
  { name: 'BTI Sports', prdId: 105 },
]

export const WHITECLIFF_INTEGRATION_CONSTANT = {
  BET_SLIP_STATUS_ACCEPTED: 'accepted',
  IN_GAME: 'in_game',
  CREDIT: 'CR',
  DEBIT: 'DR',

  PLACE_BET: 1,
  CANCEL_BET: 4,
  SETTLE_MARKET_WON: 2,
  SETTLE_MARKET_LOST: 5,
  MARKET_CANCEL_CREDIT: 4,
  MARKET_CANCEL_DEBIT: 5,
  CANCEL_SETTLED_MARKET: 5,
  RESETTLE: 6,
  PLACE_BET_CANCEL: 9,
}

export const CODE_TO_TRANSACTION_TYPES = {
  0: 'DEBIT',
  1: 'CREDIT',
  2: 'ROLLBACK',
  3: 'DEPOSIT',
  4: 'WITHDRAW',
  7: 'TIP',
  8: 'DEBIT_NO_CASH',
  9: 'CREDIT_NO_CASH',
  10: 'ROLLBACK_NO_CASH',
  11: 'NON_CASH_BONUS_CLAIM',
  12: 'DEPOSIT_BONUS_CLAIM',
  13: 'TIP_NON_CASH',
  14: 'WITHDRAW_CANCEL',
  15: 'JOINING_BONUS_CLAIMED',
  46: 'DEBIT_OTB_CASH',
  47: 'ROLLBACK_OTB_CASH'
}
export const STAGE_BOT_ACTIVITY_TENANTS = [120]
export const PROD_BOT_ACTIVITY_TENANTS = [20]

export const TRANSACTION_PAYMENT_METHOD = {
  MANUAL: 'manual',
}

export const STAGE_LOTTERY777_GAMES_PROVIDER = 5132
export const PROD_LOTTERY777_GAMES_PROVIDER = 180
export const LOTTERY777_ICON = ''

export const CURRENCY_LIST_STAGE = [
  { id: 7, value: 'uyl' },
  { id: 5, value: 'YEN' },
  { id: 6, value: 'BRL' },
  { id: 4, value: 'USD' },
  { id: 3, value: 'GBP' },
  { id: 43, value: 'PHP' },
  { id: 10, value: 'chips' },
  { id: 11, value: 'LKR' },
  { id: 1, value: 'EUR' },
  { id: 2, value: 'INR' }
]

export const CURRENCY_LIST_PROD = [
  { id: 37, value: 'LKR' },
  { id: 4, value: 'chips' },
  { id: 3, value: 'USD' },
  { id: 2, value: 'INR' },
  { id: 1, value: 'EUR' }
]

export const AUTO_ACTIVATION_TYPE = {
  AUTO: 'auto',
  QUEUE: 'queue'
}

export const CRON_TYPE = {
  BONUS_ENGINE_DATA: 'bonusEngineData',
  MULTIPLE_BONUS_ACTIVATION_CRON: 'multipleBonusActivationCron',
  CASHBACK_BONUS_RECURRING_ACTIVATION: 'cashbackBonusRecurringActivationCron'
}

export const BONUS_CREATION_TYPE = {
  MANUAL: 0,
  AUTOMATIC: 1
}

export const RECURRING_SCHEDULE_TYPE = {
  BEGINNING_OF_MONTH: 0,
  FIRST_HALF_MONTH: 1,
  MID_LATE_MONTH: 2,
  SECOND_HALF_MONTH: 3,
}

export const RECURRING_SCHEDULE_DAYS = {
  BEGINNING_OF_MONTH: 12, // 1st of the month
  FIRST_HALF_MONTH: 14,  // 14th of the month
  MID_LATE_MONTH: 16,    // 16th of the month
  DAYS_BEFORE_MONTH_END: 2,
};

export const LOTTERY_JOB_CONSTANT = {
  METHOD: 'post',
  MAX_BODY_LENGTH: Infinity,
  HEADERS: { 'Content-Type': 'application/json', 'type': 'Queue - Lottery Callback' },
  STAGE_EZUGI_URL: 'https://stglottery777.z1k9txvm.net',
  PROD_EZUGI_URL: 'https://lottery777.z1k9txvm.net',
}

export const LOTTERY_GAMES_CREDENTIALS = {
  LOTTERY_GAMES_URL: 'LOTTERY_GAMES_URL',
  LOTTERY_GAMES_USER_TOKEN: 'lottery_games_user_token',
  INR: {
    LOTTERY_GAMES_AGENT_ID: 'LOTTERY_GAMES_AGENT_ID_INR',
    LOTTERY_GAMES_ENC_KEY: 'LOTTERY_GAMES_ENC_KEY_INR'
  },
  EUR: {
    LOTTERY_GAMES_AGENT_ID: 'LOTTERY_GAMES_AGENT_ID_EUR',
    LOTTERY_GAMES_ENC_KEY: 'LOTTERY_GAMES_ENC_KEY_EUR'
  },
  USD: {
    LOTTERY_GAMES_AGENT_ID: 'LOTTERY_GAMES_AGENT_ID_USD',
    LOTTERY_GAMES_ENC_KEY: 'LOTTERY_GAMES_ENC_KEY_USD'
  },
  LKR: {
    LOTTERY_GAMES_AGENT_ID: 'LOTTERY_GAMES_AGENT_ID_LKR',
    LOTTERY_GAMES_ENC_KEY: 'LOTTERY_GAMES_ENC_KEY_LKR'
  },

  LOTTERY_GAMES_AGENT_ID_INR: 'LOTTERY_GAMES_AGENT_ID_INR',
  LOTTERY_GAMES_AGENT_ID_EUR: 'LOTTERY_GAMES_AGENT_ID_EUR',
  LOTTERY_GAMES_AGENT_ID_USD: 'LOTTERY_GAMES_AGENT_ID_USD',
  LOTTERY_GAMES_AGENT_ID_LKR: 'LOTTERY_GAMES_AGENT_ID_LKR'
}

export const LOYALTY_PROGRAM_SETTINGS = {
  ROLLOVER: 1,
  DEPOSIT: 2
}

export const SORT_TENANT_PROD = [
  { id: '86', name: 'Marina888' },
  { id: '54', name: 'jeeto555' },
  { id: '2', name: 'Zovi24' },
  { id: '21', name: 'BetMaster247' },
  { id: '119', name: 'AceXBet365' },
  { id: '18', name: 'Crown246' },
  { id: '20', name: 'WinGt888' },
  { id: '185', name: 'wingt888.NET' },
  { id: '57', name: 'Lordbet' },
  { id: '53', name: 'Sportway' },
  { id: '153', name: 'Betlab247' },
  { id: '152', name: 'Moneywin365' }
]

export const SORT_TENANT_STAGE = [
  { id: '54', name: 'Marina888' },
  { id: '21', name: 'jeeto555' },
  { id: '16', name: 'Zovi24' },
  { id: '23', name: 'BetMaster247' },
  { id: '90', name: 'AceXBet365' },
  { id: '19', name: 'Crown246' },
  { id: '20', name: 'WinGt888' },
  { id: '120', name: 'wingt888.NET' },
  { id: '55', name: 'Lordbet' },
  { id: '24', name: 'Sportway' },
  { id: '119', name: 'Moneywin365' }
]
export const TOP_MENU = {
  CASINO: 'Casino',
  LIVE_CASINO: 'Live Casino',
  SPORTS: 'Sports',
  VIRTUAL_SPORTS: 'Virtual Sports',
  SLOTS: 'Slots',
  POKER: 'Poker',
  LOTTERY: 'Lottery',
  FISHING: 'Fishing',
  PROMOTIONS: 'Promotions',
  BETTING: 'Betting'
}

export const UUWALLET_PG_RESPONSE_CODES = {
  SUCCESS: 0
}

export const UUWALLET_PG_WITHDRAWAL_STATUS = {
  0: "Initialized",
  1: "Pending Tenant Verification",
  2: "In Progress",
  3: "Completed",
  4: "Canceled",
  5: "Manual Processing",
  6: "Failed",
}

export const EZUGI_SHEET = {
  STAGE: '********************************************',
  PROD: '1yFK-stPTef3o11nt7oKM2PphLefdzRMMsTtltUAGKwc'
}

export const EVOLUTION_SHEET = {
  STAGE: '10apUNvHCSPEXQlTGG5Xmc-idJO0oyvSnLAxVerJUGs4',
  PROD: '10apUNvHCSPEXQlTGG5Xmc-idJO0oyvSnLAxVerJUGs4'
}

export const RED_TIGER_SHEET = {
  STAGE: '1qr4O2c9AdlHOLR9Y0GcEsbXPVZug5swBqqSTFmjLRww',
  PROD: '1qr4O2c9AdlHOLR9Y0GcEsbXPVZug5swBqqSTFmjLRww'
}

export const NETENT_SHEET= {
  STAGE: '1soUyOwL14iitr1ryKtdVr5kWiYLBgnWZLOqAzSy9Z1Y',
  PROD: '1soUyOwL14iitr1ryKtdVr5kWiYLBgnWZLOqAzSy9Z1Y'
}


export const STAGE_SPINOCCHIO_GAMES_PROVIDER = 5165
export const PROD_SPINOCCHIO_GAMES_PROVIDER = 246
export const SPINOCCHIO_GAMES_URL = (config.get('env') === 'production') ? 'https://spinocchio.z1k9txvm.net/api/v1/spinocchio/games' : 'https://stgspinocchio.z1k9txvm.net/api/v1/spinocchio/games'
export const SPINOCCHIO_ICON = ''


export const CAPTCHA_VERIFICATION_STATUS = {
  UNVERIFIED: 0,
  VERIFIED: 1,
  USED: 2
}

export const BANK_TYPE_MAP = {
  1: 'Bank',
  2: 'Virtual',
  3: 'QR Code'
}
export const STAGE_TENANTS_USING_NEW_NGR_FORMULA = [23, 91, 21, 119, 90, 54, 53, 22];
export const PROD_TENANTS_USING_NEW_NGR_FORMULA = [21, 153, 54, 152, 119, 86, 56,218];

export const UU_WALLET_EXCHANGE_RATES_EUR = {
  'USDT': 0.881779,
  'PHT': 0.000013509405282158464
}

export const BONUS_RECURRING_STATUS = {
  ACTIVE: 0,
  CLAIMED: 1,
  CANCELLED: 2,
  EXPIRED: 3
};

export const BONUS_TIER_CONFIG_TYPE = {
  TIER_BASED : 1,
  SEQUENCE_BASED : 2
}

export const WYNTA_SITES =  ['88Punt','BetMaster247']

export const AFFILIATE = {
 ALANBASE : 1,
 WYNTA : 2
}

export const Countries = [
  { value: 'AD', name: 'AD - Andorra' },
  { value: 'AE', name: 'AE - United Arab Emirates' },
  { value: 'AF', name: 'AF - Afghanistan' },
  { value: 'AG', name: 'AG - Antigua and Barbuda' },
  { value: 'AI', name: 'AI - Anguilla' },
  { value: 'AL', name: 'AL - Albania' },
  { value: 'AM', name: 'AM - Armenia' },
  { value: 'AO', name: 'AO - Angola' },
  { value: 'AQ', name: 'AQ - Antarctica' },
  { value: 'AR', name: 'AR - Argentina' },
  { value: 'AS', name: 'AS - American Samoa' },
  { value: "AT", name: "AT - Austria" },
  { value: "AU", name: "AU - Australia" },
  { value: "AW", name: "AW - Aruba" },
  { value: "AX", name: "AX - Åland" },
  { value: "AZ", name: "AZ - Azerbaijan" },
  { value: "BA", name: "BA - Bosnia and Herzegovina" },
  { value: "BB", name: "BB - Barbados" },
  { value: "BD", name: "BD - Bangladesh" },
  { value: "BE", name: "BE - Belgium" },
  { value: "BF", name: "BF - Burkina Faso" },
  { value: "BG", name: "BG - Bulgaria" },
  { value: "BH", name: "BH - Bahrain" },
  { value: "BI", name: "BI - Burundi" },
  { value: "BJ", name: "BJ - Benin" },
  { value: "BL", name: "BL - Saint-Barthélemy" },
  { value: "BM", name: "BM - Bermuda" },
  { value: "BN", name: "BN - Brunei" },
  { value: "BO", name: "BO - Bolivia" },
  { value: "BQ", name: "BQ - Bonaire" },
  { value: "BR", name: "BR - Brazil" },
  { value: "BS", name: "BS - Bahamas" },
  { value: "BT", name: "BT - Bhutan" },
  { value: "BW", name: "BW - Botswana" },
  { value: "BY", name: "BY - Belarus" },
  { value: "BZ", name: "BZ - Belize" },
  { value: "CA", name: "CA - Canada" },
  { value: "CC", name: "CC - Cocos [Keeling] Islands" },
  { value: "CD", name: "CD - Congo" },
  { value: "CF", name: "CF - Central African Republic" },
  { value: "CG", name: "CG - Republic of the Congo" },
  { value: "CH", name: "CH - Switzerland" },
  { value: "CI", name: "CI - Ivory Coast" },
  { value: "CK", name: "CK - Cook Islands" },
  { value: "CL", name: "CL - Chile" },
  { value: "CM", name: "CM - Cameroon" },
  { value: "CN", name: "CN - China" },
  { value: "CO", name: "CO - Colombia" },
  { value: "COUNTRY_ISO_CODE", name:  "COUNTRY_ISO_CODE - country_name" },
  { value: "CR", name: "CR - Costa Rica" },
  { value: "CU", name: "CU - Cuba" },
  { value: "CV", name: "CV - Cape Verde" },
  { value: "CW", name: "CW - Curaçao" },
  { value: "CX", name: "CX - Christmas Island" },
  { value: "CY", name: "CY - Cyprus" },
  { value: "CZ", name: "CZ - Czech Republic" },
  { value: "DE", name: "DE - Germany" },
  { value: "DJ", name: "DJ - Djibouti" },
  { value: "DK", name: "DK - Denmark" },
  { value: "DM", name: "DM - Dominica" },
  { value: "DO", name: "DO - Dominican Republic" },
  { value: "DZ", name: "DZ - Algeria" },
  { value: "EC", name: "EC - Ecuador" },
  { value: "EE", name: "EE - Estonia" },
  { value: "EG", name: "EG - Egypt" },
  { value: "ER", name: "ER - Eritrea" },
  { value: "ES", name: "ES - Spain" },
  { value: "ET", name: "ET - Ethiopia" },
  { value: "FI", name: "FI - Finland" },
  { value: "FJ", name: "FJ - Fiji" },
  { value: "FK", name: "FK - Falkland Islands" },
  { value: "FM", name: "FM - Federated States of Micronesia" },
  { value: "FO", name: "FO - Faroe Islands" },
  { value: "FR", name: "FR - France" },
  { value: "GA", name: "GA - Gabon" },
  { value: "GB", name: "GB - United Kingdom" },
  { value: "GD", name: "GD - Grenada" },
  { value: "GE", name: "GE - Georgia" },
  { value: "GF", name: "GF - French Guiana" },
  { value: "GG", name: "GG - Guernsey" },
  { value: "GH", name: "GH - Ghana" },
  { value: "GI", name: "GI - Gibraltar" },
  { value: "GL", name: "GL - Greenland" },
  { value: "GM", name: "GM - Gambia" },
  { value: "GN", name: "GN - Guinea" },
  { value: "GP", name: "GP - Guadeloupe" },
  { value: "GQ", name: "GQ - Equatorial Guinea" },
  { value: "GR", name: "GR - Greece" },
  { value: "GS", name: "GS - South Georgia and the South Sandwich Islands" },
  { value: "GT", name: "GT - Guatemala" },
  { value: "GU", name: "GU - Guam" },
  { value: "GW", name: "GW - Guinea-Bissau" },
  { value: "GY", name: "GY - Guyana" },
  { value: "HK", name: "HK - Hong Kong" },
  { value: "HN", name: "HN - Honduras" },
  { value: "HR", name: "HR - Croatia" },
  { value: "HT", name: "HT - Haiti" },
  { value: "HU", name: "HU - Hungary" },
  { value: "ID", name: "ID - Indonesia" },
  { value: "IE", name: "IE - Ireland" },
  { value: "IL", name: "IL - Israel" },
  { value: "IM", name: "IM - Isle of Man" },
  { value: "IN", name: "IN - India" },
  { value: "IO", name: "IO - British Indian Ocean Territory" },
  { value: "IQ", name: "IQ - Iraq" },
  { value: "IR", name: "IR - Iran" },
  { value: "IS", name: "IS - Iceland" },
  { value: "IT", name: "IT - Italy" },
  { value: "JE", name: "JE - Jersey" },
  { value: "JM", name: "JM - Jamaica" },
  { value: "JO", name: "JO - Hashemite Kingdom of Jordan" },
  { value: "JP", name: "JP - Japan" },
  { value: "KE", name: "KE - Kenya" },
  { value: "KG", name: "KG - Kyrgyzstan" },
  { value: "KH", name: "KH - Cambodia" },
  { value: "KI", name: "KI - Kiribati" },
  { value: "KM", name: "KM - Comoros" },
  { value: "KN", name: "KN - Saint Kitts and Nevis" },
  { value: "KP", name: "KP - North Korea" },
  { value: "KR", name: "KR - Republic of Korea" },
  { value: "KW", name: "KW - Kuwait" },
  { value: "KY", name: "KY - Cayman Islands" },
  { value: "KZ", name: "KZ - Kazakhstan" },
  { value: "LA", name: "LA - Laos" },
  { value: "LB", name: "LB - Lebanon" },
  { value: "LC", name: "LC - Saint Lucia" },
  { value: "LI", name: "LI - Liechtenstein" },
  { value: "LK", name: "LK - Sri Lanka" },
  { value: "LR", name: "LR - Liberia" },
  { value: "LS", name: "LS - Lesotho" },
  { value: "LT", name: "LT - Republic of Lithuania" },
  { value: "LU", name: "LU - Luxembourg" },
  { value: "LV", name: "LV - Latvia" },
  { value: "LY", name: "LY - Libya" },
  { value: "MA", name: "MA - Morocco" },
  { value: "MC", name: "MC - Monaco" },
  { value: "MD", name: "MD - Republic of Moldova" },
  { value: "ME", name: "ME - Montenegro" },
  { value: "MF", name: "MF - Saint Martin" },
  { value: "MG", name: "MG - Madagascar" },
  { value: "MH", name: "MH - Marshall Islands" },
  { value: "MK", name: "MK - Macedonia" },
  { value: "ML", name: "ML - Mali" },
  { value: "MM", name: "MM - Myanmar [Burma]" },
  { value: "MN", name: "MN - Mongolia" },
  { value: "MO", name: "MO - Macao" },
  { value: "MP", name: "MP - Northern Mariana Islands" },
  { value: "MQ", name: "MQ - Martinique" },
  { value: "MR", name: "MR - Mauritania" },
  { value: "MS", name: "MS - Montserrat" },
  { value: "MT", name: "MT - Malta" },
  { value: "MU", name: "MU - Mauritius" },
  { value: "MV", name: "MV - Maldives" },
  { value: "MW", name: "MW - Malawi" },
  { value: "MX", name: "MX - Mexico" },
  { value: "MY", name: "MY - Malaysia" },
  { value: "MZ", name: "MZ - Mozambique" },
  { value: "NA", name: "NA - Namibia" },
  { value: "NC", name: "NC - New Caledonia" },
  { value: "NE", name: "NE - Niger" },
  { value: "NF", name: "NF - Norfolk Island" },
  { value: "NG", name: "NG - Nigeria" },
  { value: "NI", name: "NI - Nicaragua" },
  { value: "NL", name: "NL - Netherlands" },
  { value: "NO", name: "NO - Norway" },
  { value: "NP", name: "NP - Nepal" },
  { value: "NR", name: "NR - Nauru" },
  { value: "NU", name: "NU - Niue" },
  { value: "NZ", name: "NZ - New Zealand" },
  { value: "OM", name: "OM - Oman" },
  { value: "PA", name: "PA - Panama" },
  { value: "PE", name: "PE - Peru" },
  { value: "PF", name: "PF - French Polynesia" },
  { value: "PG", name: "PG - Papua New Guinea" },
  { value: "PH", name: "PH - Philippines" },
  { value: "PK", name: "PK - Pakistan" },
  { value: "PL", name: "PL - Poland" },
  { value: "PM", name: "PM - Saint Pierre and Miquelon" },
  { value: "PN", name: "PN - Pitcairn Islands" },
  { value: "PR", name: "PR - Puerto Rico" },
  { value: "PS", name: "PS - Palestine" },
  { value: "PT", name: "PT - Portugal" },
  { value: "PW", name: "PW - Palau" },
  { value: "PY", name: "PY - Paraguay" },
  { value: "QA", name: "QA - Qatar" },
  { value: "RE", name: "RE - Réunion" },
  { value: "RO", name: "RO - Romania" },
  { value: "RS", name: "RS - Serbia" },
  { value: "RU", name: "RU - Russia" },
  { value: "RW", name: "RW - Rwanda" },
  { value: "SA", name: "SA - Saudi Arabia" },
  { value: "SB", name: "SB - Solomon Islands" },
  { value: "SC", name: "SC - Seychelles" },
  { value: "SD", name: "SD - Sudan" },
  { value: "SE", name: "SE - Sweden" },
  { value: "SG", name: "SG - Singapore" },
  { value: "SH", name: "SH - Saint Helena" },
  { value: "SI", name: "SI - Slovenia" },
  { value: "SJ", name: "SJ - Svalbard and Jan Mayen" },
  { value: "SK", name: "SK - Slovakia" },
  { value: "SL", name: "SL - Sierra Leone" },
  { value: "SM", name: "SM - San Marino" },
  { value: "SN", name: "SN - Senegal" },
  { value: "SO", name: "SO - Somalia" },
  { value: "SR", name: "SR - Suriname" },
  { value: "SS", name: "SS - South Sudan" },
  { value: "ST", name: "ST - São Tomé and Príncipe" },
  { value: "SV", name: "SV - El Salvador" },
  { value: "SX", name: "SX - Sint Maarten" },
  { value: "SY", name: "SY - Syria" },
  { value: "SZ", name: "SZ - Swaziland" },
  { value: "TC", name: "TC - Turks and Caicos Islands" },
  { value: "TD", name: "TD - Chad" },
  { value: "TF", name: "TF - French Southern Territories" },
  { value: "TG", name: "TG - Togo" },
  { value: "TH", name: "TH - Thailand" },
  { value: "TJ", name: "TJ - Tajikistan" },
  { value: "TK", name: "TK - Tokelau" },
  { value: "TL", name: "TL - East Timor" },
  { value: "TM", name: "TM - Turkmenistan" },
  { value: "TN", name: "TN - Tunisia" },
  { value: "TO", name: "TO - Tonga" },
  { value: "TR", name: "TR - Turkey" },
  { value: "TT", name: "TT - Trinidad and Tobago" },
  { value: "TV", name: "TV - Tuvalu" },
  { value: "TW", name: "TW - Taiwan" },
  { value: "TZ", name: "TZ - Tanzania" },
  { value: "UA", name: "UA - Ukraine" },
  { value: "UG", name: "UG - Uganda" },
  { value: "UM", name: "UM - U.S. Minor Outlying Islands" },
  { value: "US", name: "US - United States" },
  { value: "UY", name: "UY - Uruguay" },
  { value: "UZ", name: "UZ - Uzbekistan" },
  { value: "VA", name: "VA - Vatican City" },
  { value: "VC", name: "VC - Saint Vincent and the Grenadines" },
  { value: "VE", name: "VE - Venezuela" },
  { value: "VG", name: "VG - British Virgin Islands" },
  { value: "VI", name: "VI - U.S. Virgin Islands" },
  { value: "VN", name: "VN - Vietnam" },
  { value: "VU", name: "VU - Vanuatu" },
  { value: "WF", name: "WF - Wallis and Futuna" },
  { value: "WS", name: "WS - Samoa" },
  { value: "XK", name: "XK - Kosovo" },
  { value: "YE", name: "YE - Yemen" },
  { value: "YT", name: "YT - Mayotte" },
  { value: "ZA", name: "ZA - South Africa" },
  { value: "ZM", name: "ZM - Zambia" },
  { value: "ZW", name: "ZW - Zimbabwe" }
]

export const CURRENCY_FORMAT = config.get('env') === 'production' // For Marina tenants, all amounts are displayed in USD  comma separators.
  ? {
    86: {
      USD: 'en-US',
      INR: 'en-US',
      EUR: 'en-US',
      LKR: 'en-US',
      BRL: 'en-US',
      GBP: 'en-US',
      uyl: 'en-US',
      YEN: 'en-US',
    }
  }
  : {
    54: {
      USD: 'en-US', //Currently, all currencies are formatted using the USD comma separator style. This may change in the future, so make sure to update the formatting logic here accordingly when needed.
      INR: 'en-US',
      EUR: 'en-US',
      LKR: 'en-US',
      BRL: 'en-US',
      GBP: 'en-US',
      uyl: 'en-US',
      YEN: 'en-US',
    }
  };
  export const STAGE_TURBO_GAMES_PROVIDER = 5231
  export const PROD_TURBO_GAMES_PROVIDER = 279
  export const TURBO_GAMES_ICON = ''
  export const DEMO_USER_ID = config.get('env') === 'production' ? 87355 : 1007

export const BONUS_COMMENT_ABBREVIATIONS = {
  TBC: 'TBC',   // Tier Bonus Claimed
  CSBC: 'CSBC', // Casino & Sports Bonus Claimed
  CBC: 'CBC',   // Casino Bonus Claimed
  SBC: 'SBC',   // Sports Bonus Claimed
  IBC: 'IBC',    // Instant Bonus Claimed
  DBC : 'DBC',   // Deposit Bonus Cancelled
  DBCFSC : 'DBCFSC', // Deposit Bonus Cancelled For Sports and Casino
  DBCDTW : 'DBCDTW', // Deposit Bonus Cancelled Due To Amount Withdrawal
  BBC : 'BBC',   // Burning Bonus Claimed : Deposit, Joining , losining, promocode, manual_losing
  RBC : 'RBC',   // Referral Bonus Claimed
  LBC : 'LBC',   // Loyalty Bonus Claimed
  CDBCDTW: 'CDBCDTW', // Casino Deposit Bonus Cancelled Due To Withdrawal
  CDBCDTD: 'CDBCDTD', // Casino Deposit Bonus Cancelled Due To Deposit
  SDBCDTW: 'SDBCDTW', // Sport Deposit Bonus Cancelled Due To Withdrawal
  SDBCDTD: 'SDBCDTD', // Sport Deposit Bonus Cancelled Due To Deposit
  BDBCDTW: 'BDBCDTW', // Both Deposit Bonus Cancelled Due To Withdrawal
  BDBCDTD: 'BDBCDTD', // Both Deposit Bonus Cancelled Due To Deposit
}

export const BONUS_COMMENT_FULL_TEXT = {
  TBC: 'Tier Bonus Claimed',
  CSBC: 'Casino & Sports Bonus Claimed',
  CBC: 'Casino Bonus Claimed',
  SBC: 'Sports Bonus Claimed',
  IBC: 'Instant Bonus Claimed',
  DBC: 'Deposit Bonus Cancelled',
  DBCFSC: 'Deposit Bonus Cancelled For Sports and Casino',
  DBCDTW: 'Deposit Bonus Cancelled Due To Amount Withdrawal',
  BBC: 'Burning Bonus Claimed',
  RBC: 'Referral Bonus Claimed',
  LBC: 'Loyalty Bonus Claimed',
  CDBCDTW: 'Casino Deposit Bonus Cancelled Due To Withdrawal',
  CDBCDTD: 'Casino Deposit Bonus Cancelled Due To Deposit',
  SDBCDTW: 'Sport Deposit Bonus Cancelled Due To Withdrawal',
  SDBCDTD: 'Sport Deposit Bonus Cancelled Due To Deposit',
  BDBCDTW: 'Both Deposit Bonus Cancelled Due To Withdrawal',
  BDBCDTD: 'Both Deposit Bonus Cancelled Due To Deposit',
  TNCBC: 'Tier bonus credit for completing rollover requirement'
}

export const BET_PAYOUT_SETTLEMENT_STATUS_CODES = {
  1: 'Settled',
  2: 'Rejected',
  3: 'Unsettled'
}
