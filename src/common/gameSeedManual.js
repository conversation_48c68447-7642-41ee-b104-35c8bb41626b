import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import {
  EVOLUTION_PROVIDER,
  PROD_DARWIN_PROVIDER,
  PROD_FUNKY_GAMES_PROVIDER,
  PROD_LOTTERY777_GAMES_PROVIDER,
  PROD_PGSOFT_PROVIDER,
  PROD_PROVIDER,
  PROD_SPINOCCHIO_GAMES_PROVIDER,
  PROD_SPRIBE_PROVIDER,
  PROD_TURBO_GAMES_PROVIDER,
  PROD_WHITECLIFF_PROVIDER,
  ST8_PROVIDER_ID_PROD, ST8_PROVIDER_ID_STAGE,
  STAGE_DARWIN_PROVIDER,
  STAGE_FUNKY_GAMES_PROVIDER,
  STAGE_LOTTERY777_GAMES_PROVIDER,
  STAGE_PGSOFT_PROVIDER,
  STAGE_PROVIDER,
  STAGE_SPINOCCHIO_GAMES_PROVIDER,
  STAGE_SPRIBE_PROVIDER,
  STAGE_TURBO_GAMES_PROVIDER,
  STAGE_WHITECLIFF_PROVIDER
} from './constants'
import darwinGamePopulation from './darwinGamePopulation'
import ezugiGamesPopulation from './ezugiGamesPopulation'
import funkyGamesGamePopulation from './funkyGamesGamePopulation'
import lotteryGamePopulation from './lotteryGamePopulation'
import pgSoftGamePolulation from './pgSoftGamePolulation'
import spinocchioGamePopulation from './spinocchioGamePopulation'
import spribeGamePopulation from './spribeGamePopulation'
import st8GamePopulation from './st8GamePopulation'
import turboGamesPopulation from './turboGamesPopulation'
import whiteCliffGamePopulation from './whiteCliffGamePopulation'

export default async (data) => {
  let transaction
  try {
    const { CasinoProvider } = db
    const { provider_id: providerId, game_provider } = data.id;
    let { tenant_ids: tenantIds } = data.id
    // tenantIds = parseInt(tenantIds)
    let aggregator_providers = []
    if(game_provider){
      aggregator_providers = game_provider || []
    }
    const isSpecificTenant = true;

    // Provider ID constants
    const funkyCasinoProviderId = config.get('env') === 'production' ? PROD_FUNKY_GAMES_PROVIDER : STAGE_FUNKY_GAMES_PROVIDER
    const darwinProviderId = config.get('env') === 'production' ? PROD_DARWIN_PROVIDER : STAGE_DARWIN_PROVIDER
    const pgSoftProviderId = config.get('env') === 'production' ? PROD_PGSOFT_PROVIDER : STAGE_PGSOFT_PROVIDER
    const spinocchioProviderId = config.get('env') === 'production' ? PROD_SPINOCCHIO_GAMES_PROVIDER : STAGE_SPINOCCHIO_GAMES_PROVIDER
    const lottery777ProviderId = config.get('env') === 'production' ? PROD_LOTTERY777_GAMES_PROVIDER : STAGE_LOTTERY777_GAMES_PROVIDER
    const whitecliffProviderId = config.get('env') === 'production' ? PROD_WHITECLIFF_PROVIDER : STAGE_WHITECLIFF_PROVIDER
    const st8ProviderId = config.get('env') === 'production' ? parseInt(ST8_PROVIDER_ID_PROD) : parseInt(ST8_PROVIDER_ID_STAGE)
    const evolutionProviderId = config.get('env') === 'production' ? EVOLUTION_PROVIDER.PROD : EVOLUTION_PROVIDER.STAGE
    const ezugiProviderId = config.get('env') === 'production' ? PROD_PROVIDER.EZUGI : STAGE_PROVIDER.EZUGI
    const redTigerProviderId = config.get('env') === 'production' ? PROD_PROVIDER.RED_TIGER : STAGE_PROVIDER.RED_TIGER
    const netEntProviderId = config.get('env') === 'production' ? PROD_PROVIDER.NET_ENT : STAGE_PROVIDER.NET_ENT
    const spribeProviderId = config.get('env') === 'production' ? PROD_SPRIBE_PROVIDER : STAGE_SPRIBE_PROVIDER
    const turboGamesProviderId = config.get('env') === 'production' ? PROD_TURBO_GAMES_PROVIDER : STAGE_TURBO_GAMES_PROVIDER
    // const jetfairProviderId = config.get('env') === 'production' ? SPORTS_PROVIDERS_PROD.JETFAIR : SPORTS_PROVIDERS_STAGE.JETFAIR
    // const powerplayProviderId = config.get('env') === 'production' ? SPORTS_PROVIDERS_PROD.POWERPLAY : SPORTS_PROVIDERS_STAGE.POWERPLAY
    // const turbostarsProviderId = config.get('env') === 'production' ? SPORTS_PROVIDERS_PROD.TURBOSTARS : SPORTS_PROVIDERS_STAGE.TURBOSTARS
    // Switch case for provider names
      switch (parseInt(providerId)) {
      case funkyCasinoProviderId:
        data.providerTitle = 'Funky'
        await funkyGamesGamePopulation({ tenantIds })
        break
      case darwinProviderId:
        data.providerTitle = 'Darwin'
        await darwinGamePopulation({ tenantIds })
        break
      case pgSoftProviderId:
        data.providerTitle = 'PGsoft'
        await pgSoftGamePolulation({ tenantIds })
        break
      case spinocchioProviderId:
        data.providerTitle = 'Spinocchio'
        await spinocchioGamePopulation({ tenantIds })
        break
      case lottery777ProviderId:
        data.providerTitle = 'Lottery777'
        await lotteryGamePopulation({ tenantIds })
        break
      case whitecliffProviderId:
        data.providerTitle = 'Whitecliff'
        await whiteCliffGamePopulation({ tenantIds , aggregator_providers})
        break
      case st8ProviderId:
         data.providerTitle = 'ST8'
        await st8GamePopulation({ tenantIds, aggregator_providers })
        break
      case spribeProviderId:
        await spribeGamePopulation({ tenantIds, aggregator_providers })
        break
      case evolutionProviderId:
        data.providerTitle = 'Evolution'
        await ezugiGamesPopulation({ tenantIds, providerName: 'evolution' })
        break
      case parseInt(ezugiProviderId):
        data.providerTitle = 'Ezugi'
        await ezugiGamesPopulation({ tenantIds, providerName: 'ezugi' })
        break
      case parseInt(redTigerProviderId):
        data.providerTitle = 'Red Tiger'
        await ezugiGamesPopulation({ tenantIds, providerName: 'redtiger' })
        break
      case parseInt(netEntProviderId):
        data.providerTitle = 'NetEnt'
        await ezugiGamesPopulation({ tenantIds, providerName: 'netent' })
        break
      case turboGamesProviderId:
        data.providerTitle = 'TurboGames'
         await turboGamesPopulation({ tenantIds })
        break

      default:
        break
    }

    transaction = await sequelize.transaction()



    await transaction.commit()
  } catch (error) {
    if (transaction) await transaction.rollback()
    throw error
  }
}
