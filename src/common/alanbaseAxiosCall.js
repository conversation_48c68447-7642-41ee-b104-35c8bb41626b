import axios from 'axios'
import config from '../configs/app.config'
import db from '../db/models'
import { getCasinoBetWinData, getDepositData, getRegistrationData, getSportsBookData, getWithdrawalData } from './alanbaseUtils'
import { ALANBASE_EVENT_TYPES, JOB_CONSTANT, PROD_ALLOWED_TENANTS_ALANBASE, STAG_ALLOWED_TENANTS_ALANBASE } from './constants'


export default async (jobData) => {

  const queueProcessStatus = await db.QueueProcessStatus.findOne({
    where: {
      service: 'alanbase_events_data_cron',
      status: 1
    },
    attributes: ['id']
  })
  if (!queueProcessStatus) {
    throw new Error('Queue Service Status Stopped  (alanbase_events_data_cron)')
  }

  const { transactionType } = jobData
  let { ids, tenantId } = jobData.data
  tenantId = parseInt(tenantId)
  const allowedTenantsAlanbase = config.get('env') === 'development' ? STAG_ALLOWED_TENANTS_ALANBASE : PROD_ALLOWED_TENANTS_ALANBASE
  if (!allowedTenantsAlanbase.includes(tenantId)) { return { success: false } }

  const eventType = transactionType

  let data
  let alanbaseType = 'event'
  switch (eventType) {
    case ALANBASE_EVENT_TYPES.REGISTRATION:
      alanbaseType = 'goal'
      data = await getRegistrationData(ids, eventType, tenantId)
      if (data.length === 0) { return { success: false } }
      break
    case ALANBASE_EVENT_TYPES.DEPOSIT:
      data = await getDepositData(ids, eventType, tenantId)
      if (data.length === 0) { return { success: false } }
      break
    case ALANBASE_EVENT_TYPES.WITHDRAWAL_COMPLETED:
      alanbaseType = 'goal'
      data = await getWithdrawalData(ids, eventType, tenantId)
      if (data.length === 0) { return { success: false } }
      break
    case ALANBASE_EVENT_TYPES.ALANBASE_WIN:
      data = await getCasinoBetWinData(ids, eventType, tenantId)
      if (data.length === 0) { return { success: false } }
      break
    case ALANBASE_EVENT_TYPES.ALANBASE_SB_PLACE_BET:
    case ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_PLACE_BET:
    case ALANBASE_EVENT_TYPES.ALANBASE_SB_MARKET_CANCEL:
    case ALANBASE_EVENT_TYPES.ALANBASE_SB_SETTLE_MARKET:
    case ALANBASE_EVENT_TYPES.ALANBASE_SB_RESETTLE_MARKET:
    case ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_SETTLE_MARKET:
    case ALANBASE_EVENT_TYPES.ALANBASE_SB_CASHOUT:
    case ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_CASHOUT:
      data = await getSportsBookData(ids, eventType, tenantId)
      if (data.length === 0) { return { success: false } }
      break
    default:
      throw new Error(`Unknown eventType: ${eventType}`)
  }
  if (!data || data?.length === 0) {
    return false
  }

  await sendDataToAlanbase(data, alanbaseType, tenantId)
}

const sendDataToAlanbase = async (data, alanbaseType, tenantId) => {

  const url = alanbaseType == 'event' ? config.get('alanbase.eventUrl') : config.get('alanbase.goalUrl')

  const axiosConfig = {
    method: JOB_CONSTANT.METHOD,
    maxBodyLength: JOB_CONSTANT.MAX_BODY_LENGTH,
    url,
    headers: {
      ...JOB_CONSTANT.HEADERS,
    },
    data: JSON.stringify(data)
  }
  try {
    const { data } = await axios(axiosConfig)

    await db.RequestResponseLog.create({
      requestJson: axiosConfig,
      responseJson: data,
      service: 'queue-alanbase-single',
      url: url,
      responseCode: data?.response?.status,
      tenantId: tenantId
    })
    return data
  } catch (e) {
    const { response } = e
    await db.RequestResponseLog.create({
      requestJson: axiosConfig,
      responseJson: response?.data,
      service: 'queue-alanbase-single',
      url: url,
      responseCode: response?.status,
      tenantId: tenantId
    })
    throw e
  }
}
