import db, { sequelize } from '../db/models';
import { CRON_LOG_STATUS } from '../common/constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'callback_transactions_cron',
        status: 1
      },
      attributes: ['id']
    })
    if(queueProcessStatus){
      cronLog.cronId = queueProcessStatus?.id
      // Call stored procedure
      const callAddCallbackTxsSP = `CALL add_callback_transactions(
        CURRENT_DATE - INTERVAL '1 DAY',
        (CURRENT_DATE + INTERVAL '1 DAY') - INTERVAL '1 microseconds'
      )`
      await sequelize.query(callAddCallbackTxsSP);
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log("=============> Error in add callback transactions cron:", e)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
