import { PutObjectCommand } from '@aws-sdk/client-s3'
import archiver from 'archiver'
import { createObjectCsvStringifier } from 'csv-writer'
import moment from 'moment'
import { Op, Sequelize } from "sequelize"
import { v4 as uuidv4 } from 'uuid'
import { CRON_LOG_STATUS, CSV_LOG_TYPE } from '../common/constants'
import config from '../configs/app.config'
import db from '../db/models'
import { s3 } from '../libs/awsS3ConfigV2'
import ErrorLogHelper from './errorLog'



export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      attributes: ['id'],
      where: {
        service: 'delete_old_error_logs_cron',
        status: 1
      }
    })
    if (queueProcessStatus) {
      cronLog.cronId = queueProcessStatus?.id
      const tenDaysAgo = moment().subtract(10, 'days').toDate()
      await db.ErrorLog.destroy({
        where: {
          createdAt: { [Op.lt]: tenDaysAgo }
        }
      })

      // for cron logs
      const params = ''
      let startDate, endDate
      if (params) {
        startDate = `${params.startDate} 00:00:00.0000`
        endDate = params.endDate
      } else {
        startDate = moment()
        endDate = moment()
        endDate = startDate.subtract(10, "days").format("YYYY-MM-DD")
        startDate = `${endDate} 00:00:00`
      }
      endDate = `${endDate} 23:59:59.9999`

      const csvStringifier = createObjectCsvStringifier({
        header: [
          { id: 'id', title: 'ID' },
          { id: 'cron_id', title: 'Cron Id' },
          { id: 'start_time', title: 'Start Time' },
          { id: 'end_time', title: 'End Time' },
          { id: 'status', title: 'Status' },
          { id: 'error_msg', title: 'Error Message' },
          { id: 'created_at', title: 'Created At' },
          { id: 'updated_at', title: 'Updated At' }
        ]
      })

      let chunkSize = 1000
      let fullCsvContent = ''
      const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
      const totalRecords = await db.CronLog.count({
        where: {
          createdAt: Sequelize.literal(`created_at < (CURRENT_DATE - INTERVAL'9 day') :: TIMESTAMPTZ`)
        }
      })
      const totalChunks = Math.ceil(totalRecords / chunkSize)
      if (totalRecords) {
        for (let i = 0; i < totalChunks; i++) {
          const offset = i * chunkSize
          const reqResData = await db.CronLog.findAll({
            where: {
              createdAt: Sequelize.literal(`created_at < (CURRENT_DATE - INTERVAL'9 day') :: TIMESTAMPTZ`)
            },
            offset: offset,
            limit: chunkSize,
          })
          let mainArr = []
          if (reqResData.length > 0) {
            for (const logs of reqResData) {
              const object = {
                id: logs.id,
                cron_id: logs.cronId,
                start_time: logs?.startTime ? logs.startTime.toISOString().replace("T", " ").substring(0, 19) : '',
                end_time: logs?.endTime ? logs.endTime.toISOString().replace("T", " ").substring(0, 19) : '',
                status: logs.status,
                error_msg: logs.errorMsg,
                created_at: logs.createdAt.toISOString().replace("T", " ").substring(0, 19),
                updated_at: logs.updatedAt.toISOString().replace("T", " ").substring(0, 19)
              }
              mainArr = [...mainArr, object]
            }
          }
          const csvData = csvStringifier.stringifyRecords(mainArr)
          if (i === 0) {
            fullCsvContent += csvStringifier.getHeaderString() // Add header once
          }

          fullCsvContent += csvData
          await delay(1000)
        }

        const uuid = uuidv4().replace(/-/g, '')
        const s3Config = config.getProperties().s3
        const key = `csv/cron_logs/cron_log_${uuid}.zip`
        const zipBuffer = await createZipBuffer('cron_logs.csv', fullCsvContent)

        await s3.send(new PutObjectCommand({
          Bucket: s3Config.bucket,
          Key: key, // Replace .csv with .zip
          Body: zipBuffer,
          ContentType: 'application/zip',
          ContentLength: zipBuffer.length
        }))
        // delete records
        await db.CronLog.destroy({
          where: {
            createdAt: Sequelize.literal(`created_at < (CURRENT_DATE - INTERVAL'9 day') :: TIMESTAMPTZ`)
          }
        })
        const reqBackup = {
          startDate: startDate,
          endDate: endDate,
          csvUrl: key,
          type: CSV_LOG_TYPE.CRON_LOG
        }
        await db.RequestResponseLogsBackup.create(reqBackup)
      }
    }

  } catch (error) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = error.message || null
    await ErrorLogHelper.logError(error, null, null)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}

function createZipBuffer (filename, content) {
  return new Promise((resolve, reject) => {
    const archive = archiver('zip', { zlib: { level: 9 } })
    const chunks = []

    archive.on('data', chunk => chunks.push(chunk))
    archive.on('end', () => resolve(Buffer.concat(chunks)))
    archive.on('error', err => reject(err))

    archive.append(content, { name: filename }) // e.g., report.csv inside zip
    archive.finalize()
  })
}
