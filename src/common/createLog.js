import config from "../configs/app.config"
import db from '../db/models'

export default async (jobId, userId) => {
    const data = {
        job_id: jobId,
        user_id: userId,
        secret_token: config.get('queue_job.secretToken')
      }
      const user = await db.User.findOne({
        where: {
          id: userId
        },
        attributes: ["tenantId"]
      })
      const reqLogObject = {
        requestJson: {
          job_id: jobId,
          user_id: userId,
          secret_token: config.get('queue_job.secretToken')
        },
        service: 'bulk_deposit_withdraw',
        url: config.get('queue_job.apiUrl'),
        tenantId: user.tenantId
      }
      const createLog = await db.RequestResponseLog.create(reqLogObject)
      return createLog

}
