import { isEmpty } from 'lodash'
import { Op } from 'sequelize'
import { BONUS_TYPES, TRANSACTION_TYPES } from '../common/constants'
import { sequelize } from '../db/models'
import { getSportCasinoBothGgrData } from './losingBonus/getSportCasinoBothGgrData'
import { getSportGgrData } from './losingBonus/getSportGgrData'

export const ggrBasedLosingBonus = async ({ TransactionModel, userWallet, dateObj, tenantId, bonusKind, dates, gameIds, casinoProviderIds }) => {
  const data = { userId: userWallet.ownerId, tenantId: tenantId, startDate: dates.startDate, endDate: dates.endDate, dateObj: dateObj, userWallet: userWallet, gameIds, casinoProviderIds }
  if (bonusKind === BONUS_TYPES.LOSING_SPORT) {
    const sportLosingAmount = await getSportGgrData(data)
    if (sportLosingAmount) {
      return sportLosingAmount
    } else {
      return false
    }
  }

  if (bonusKind === BONUS_TYPES.LOSING_BOTH) {
    const sportLosingAmount = await getSportCasinoBothGgrData(data)
    if (sportLosingAmount) {
      return sportLosingAmount
    } else {
      return false
    }
  }
  // total bet (cash and non cash and one-time-bonus) - total win (cash and non cash)
  const debitCreditIds = await TransactionModel.findAll({
    attributes: ['debitTransactionId'],
    where: {
      targetWalletId: userWallet.id,
      tenantId,
      transactionType: { [Op.in]: [TRANSACTION_TYPES.CREDIT, TRANSACTION_TYPES.CREDIT_NO_CASH] },
      ...dateObj
    },
    raw: true
  })

  const userWalletId = userWallet.id
  let totalBetSumResults
  if (isEmpty(debitCreditIds)) {
    totalBetSumResults = 0
  } else {
    const uniqueDebitTransactionIds = [...new Set(debitCreditIds.map(item => item.debitTransactionId))]
    const uniqueDebitTransactionIdsString = '(' + uniqueDebitTransactionIds.map(id => `'${id}'`).join(', ') + ')'
    const totalBetSumQuery = `
      WITH game_filter AS (
    SELECT UNNEST(ARRAY[:gameIds]) AS game_ids
  )
      SELECT SUM(amount)
      FROM transactions
      ${gameIds.length > 0 ? `
    LEFT JOIN game_filter gf
    ON transactions.game_id = gf.game_ids
       OR transactions.table_id::TEXT = gf.game_ids
       OR transactions.seat_id = gf.game_ids
  ` : ''}
      WHERE source_wallet_id = ${userWalletId}
      AND tenant_id = ${tenantId}
      AND transaction_type IN (${TRANSACTION_TYPES.DEBIT}, ${TRANSACTION_TYPES.DEBIT_NO_CASH}, ${TRANSACTION_TYPES.DEBIT_OTB_CASH})
      AND transaction_id IN ${uniqueDebitTransactionIdsString}
      AND (
    -- Case 1: Both arrays are empty, include all transactions
    (${gameIds.length} = 0 AND ${casinoProviderIds.length} = 0)

    -- Case 2: gameIds is non-empty, casinoProviderIds is empty
    ${gameIds.length > 0 ? `OR (${casinoProviderIds.length} = 0 AND gf.game_ids IS NOT NULL)` : ''}

    -- Case 3: gameIds is empty, casinoProviderIds is non-empty
    OR (${gameIds.length} = 0 AND ${casinoProviderIds.length} > 0 AND provider_id = ANY(ARRAY[:casinoProviderIds]::int[]))

    -- Case 4: Both arrays are non-empty
    ${gameIds.length > 0 ? `
    OR (${casinoProviderIds.length} > 0 AND (
        gf.game_ids IS NOT NULL OR provider_id = ANY(ARRAY[:casinoProviderIds]::int[])
    ))` : ''}
  )`
    totalBetSumResults = await sequelize.query(totalBetSumQuery, {
      replacements: {
        casinoProviderIds: casinoProviderIds.length > 0 ? casinoProviderIds : null,
        gameIds: gameIds.length > 0 ? gameIds : null
      },
      type: sequelize.QueryTypes.SELECT
    })
    totalBetSumResults = totalBetSumResults?.[0]?.sum
  }

  const symbols = Object.getOwnPropertySymbols(dateObj.createdAt)
  const startDate = dateObj.createdAt[symbols[0]]
  const endDate = dateObj.createdAt[symbols[1]]

  const totalWinSumQuery = `
    WITH game_filter AS (
    SELECT UNNEST(ARRAY[:gameIds]) AS game_ids
  )
    SELECT SUM(amount)
    FROM transactions
    ${gameIds.length > 0 ? `
    LEFT JOIN game_filter gf
    ON transactions.game_id = gf.game_ids
       OR transactions.table_id::TEXT = gf.game_ids
       OR transactions.seat_id = gf.game_ids
  ` : ''}
    WHERE target_wallet_id = ${userWalletId}
      AND amount > 0
      AND tenant_id = ${tenantId}
      AND created_at >= '${startDate}'
      AND created_at <= '${endDate}'
      AND transaction_type IN (${TRANSACTION_TYPES.CREDIT}, ${TRANSACTION_TYPES.CREDIT_NO_CASH})
       AND (
    -- Case 1: Both arrays are empty, include all transactions
    (${gameIds.length} = 0 AND ${casinoProviderIds.length} = 0)

    -- Case 2: gameIds is non-empty, casinoProviderIds is empty
    ${gameIds.length > 0 ? `OR (${casinoProviderIds.length} = 0 AND gf.game_ids IS NOT NULL)` : ''}

    -- Case 3: gameIds is empty, casinoProviderIds is non-empty
    OR (${gameIds.length} = 0 AND ${casinoProviderIds.length} > 0 AND provider_id = ANY(ARRAY[:casinoProviderIds]::int[]))

    -- Case 4: Both arrays are non-empty
    ${gameIds.length > 0 ? `
    OR (${casinoProviderIds.length} > 0 AND (
        gf.game_ids IS NOT NULL OR provider_id = ANY(ARRAY[:casinoProviderIds]::int[])
    ))` : ''}
  )`

  let totalWinSumResults = await sequelize.query(totalWinSumQuery, {
    replacements: {
      casinoProviderIds: casinoProviderIds.length > 0 ? casinoProviderIds : null,
      gameIds: gameIds.length > 0 ? gameIds : null
    },
    type: sequelize.QueryTypes.SELECT
  })
  totalWinSumResults = totalWinSumResults?.[0]?.sum ? totalWinSumResults[0].sum : 0
  if (totalWinSumResults > totalBetSumResults) {
    return false
  }

  return totalBetSumResults - totalWinSumResults
}
