import { Sequelize } from 'sequelize'
import config from '../configs/app.config'
import { sequelize } from '../db/models'
import { s3 } from '../libs/awsS3Config'
import { SUCCESS_MSG } from '../utils/constants/constant'

export default async () => {
  try {
    const query = `
      SELECT * FROM export_csv_center
      WHERE "admin_type" != 'system'
      AND "admin_id" != '-9999'
      AND updated_at < (CURRENT_DATE - INTERVAL '14 day')::TIMESTAMPTZ
     order by id asc;
    `

    const expotCsvData = await sequelize.query(query, {
      type: Sequelize.QueryTypes.SELECT,
      useMaster: false
    })

    if (!expotCsvData?.length) {
      return
    }

    const s3Config = config.getProperties().s3
    const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))
    for (const element of expotCsvData) {
      try {
        if (element.csv_url && element.csv_url.length >= 8) {
          const params = {
            Bucket: s3Config.bucket,
            Key: element.csv_url
          }

          const result = await s3.deleteObject(params).promise()

          if (result) {
            await sequelize.query(
              `DELETE FROM export_csv_center WHERE id = ${element.id};`,
              {
                type: Sequelize.QueryTypes.DELETE,
                useMaster: false
              }
            )
          }
          await sleep(300)
        } else {
          await sequelize.query(
           `DELETE FROM export_csv_center WHERE id = ${element.id};`,
           {
             type: Sequelize.QueryTypes.DELETE,
             useMaster: false
           }
          )
        }
      } catch (error) {
        console.error('Error processing element with id:', element.id, error)
      }
    }
    return { message: SUCCESS_MSG.DELETE_SUCCESS }
  } catch (e) {
    console.error('Error processing deletion of export CSV data', e)
  }
}
