import axios from 'axios'
import crypto from 'crypto'
import { MARINA888_TYPES, MARINA888_TYPES_ENDPOINT } from '../common/constants'
import config from '../configs/app.config'
import db from '../db/models'
import { JOB_CONSTANT } from './constants'

export default async (jobData) => {
  let url, signature, data, tenantId
  const sha256Hash = (data) => crypto.createHash('sha256').update(data).digest('hex')
  const marina888BaseUrl = config.get('marina888ApiUrl.phpBaseUrl')

  if (config.get('env') === 'development') {
    tenantId = 54 // marina888 tenantId  staging
  } else {
    tenantId = 86 // marina888 tenantId production
  }

  if (jobData.transactionType === MARINA888_TYPES.MARINA_888_USER_PASSWORD_CHANGE) {
    // user password change url
    url = config.get('marina888ApiUrl.userPasswordChangeUrl')
    data = {
      userId: jobData.data.userId,
      userName: jobData.data.userName,
      password: jobData.data.password
    }
  } else if (jobData.transactionType === MARINA888_TYPES.MARINA_888_USER_TRANSACTIONS || jobData.transactionType === MARINA888_TYPES.MARINA_888_AGENT_TRANSACTIONS) {
    // transaction url
    url = marina888BaseUrl + MARINA888_TYPES_ENDPOINT.TRANSACTION_DETAILS
    data = {
      id: jobData.data.id,
      action: jobData.data.action
    }
  } else if (jobData.transactionType === MARINA888_TYPES.MARINA_888_BONUS) {
    // bonus url
    url = marina888BaseUrl + MARINA888_TYPES_ENDPOINT.CREATE_BONUS
    data = {
      id: jobData.id
    }
  } else if (jobData.transactionType === MARINA888_TYPES.MARINA_888_USER_BONUS) {
    // user bonus url
    url = marina888BaseUrl + MARINA888_TYPES_ENDPOINT.CREATE_USER_BONUS
    data = {
      id: jobData.id
    }
  } else if (jobData.transactionType === MARINA888_TYPES.MARINA_888_USER_BETS) {
    // user bets url
    url = marina888BaseUrl + MARINA888_TYPES_ENDPOINT.CREATE_USER_BETS
    data = {
      id: jobData.id
    }
  } else if (jobData.transactionType === MARINA888_TYPES.MARINA_888_AGENT) {
    // Agent url
    url = marina888BaseUrl + MARINA888_TYPES_ENDPOINT.CREATE_AGENT
    data = {
      id: jobData.id

    }
  } else {
    // User url
    url = marina888BaseUrl + MARINA888_TYPES_ENDPOINT.CREATE_USER
    data = {
      id: jobData.id
    }
  }

  // let reqResLog = await createLog(jobId, userId)

  const requestBody = {
    method: JOB_CONSTANT.METHOD,
    maxBodyLength: JOB_CONSTANT.MAX_BODY_LENGTH,
    url: url,
    headers: JOB_CONSTANT.HEADERS,
    data: { ...data, secret_token: config.get('queue_job.secretToken') }
  }

  if (jobData.transactionType === MARINA888_TYPES.MARINA_888_USER_PASSWORD_CHANGE) {
    const { userId, userName, password } = jobData.data
    const originalString = `${userId}|${userName}|${password}|${config.get('marina888SecretKey.secretKey')}`
    signature = sha256Hash(originalString)
    requestBody.headers.signature = signature
    return true
  }
  try {
    const { data } = await axios(requestBody)
    await db.RequestResponseLog.create({
      requestJson: requestBody,
      responseJson: data,
      service: 'common-queue-marina888',
      url: url,
      responseCode: 200,
      responseStatus: "success",
      errorCode: 0,
      tenantId: tenantId
    })
    return data
  } catch (e) {
    const { response } = e
    await db.RequestResponseLog.create({
      requestJson: requestBody,
      responseJson: response?.data,
      service: 'common-queue-marina888',
      url: url,
      responseCode: response.status ? response.status : 400,
      responseStatus: "failed",
      errorCode: 1,
      tenantId: tenantId
    })
    throw e
  }
}
