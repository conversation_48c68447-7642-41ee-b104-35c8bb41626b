import axios from 'axios'
import { v4 as uuidv4 } from 'uuid'
import config from '../configs/app.config'
import db from '../db/models'
import { JOB_CONSTANT, OTB_TENANTS_CONFIG, PROD_ALLOWED_TENANTS_SMARTICO, PROD_TENANTS, SMARTICO_EVENT_TYPES_MAPPING, STAG_ALLOWED_TENANTS_SMARTICO, STAGE_TENANTS } from './constants'
import { getCasinoBetWinDataEzugi, getDepositDataEzugi, getEzugiWalletUpdate, getUserDataEzugi, getUserLoginStats, getWithdrawalCancelledDataEzugi, getWithdrawalDataEzugi } from './smartiCoUtils'


export default async (jobData) => {
  const { transactionType } = jobData
  let { ids, tenantId } = jobData.data
  let isMarina888Tenant = false
  let isOTBEnabled = false
  let allowedTenantSmartico, tenantName
  tenantId = parseInt(tenantId)
  const eventType = SMARTICO_EVENT_TYPES_MAPPING[transactionType]
  if (config.get('env') === 'development') {
    allowedTenantSmartico = STAG_ALLOWED_TENANTS_SMARTICO
    tenantName = STAGE_TENANTS[tenantId].name
    if (tenantId === 54) isMarina888Tenant = true // Marina888 staging tenantId
    isOTBEnabled = !!OTB_TENANTS_CONFIG.STAGE[tenantId] // check if one time bonus balance is enabled for this tenant
  } else {
    allowedTenantSmartico = PROD_ALLOWED_TENANTS_SMARTICO
    tenantName = PROD_TENANTS[tenantId].name
    if (tenantId === 86) isMarina888Tenant = true // Marina888 production tenantId
    isOTBEnabled = !!OTB_TENANTS_CONFIG.PROD[tenantId] // check if one time bonus balance is enabled for this tenant
  }
  const queueProcessStatus = await db.QueueProcessStatus.findOne({
    where: {
      service: 'smartico_events_data_cron',
      status: 1
    },
    attributes: ['id']
  })
  if (!queueProcessStatus) {
    throw new Error('Queue Service Status Stopped  (smartico_events_data_cron)')
  }
  if (!allowedTenantSmartico.includes(tenantId)) { return { success: false } }
  let data
  switch (eventType) {
    case 'ezugi_player_details_update':
      data = await getUserDataEzugi(ids, eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled)
      break
    case 'ezugi_online_withdrawal_pending':
      data = await getWithdrawalDataEzugi(ids, eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled) // requested state
      break
    case 'ezugi_online_withdrawal_approved': // completed state
      data = await getWithdrawalDataEzugi(ids, eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled) // requested state
      if (data.length === 0) { return { success: false } }
      break
    case 'ezugi_online_withdrawal_rejected': // cancelled state
      data = await getWithdrawalCancelledDataEzugi(ids, eventType, tenantId, tenantName, isMarina888Tenant)
      break
    case 'ezugi_online_deposit_approved':
      data = await getDepositDataEzugi(ids, eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled)
      if (data.length === 0) { return { success: false } }
      break
    case 'ezugi_player_login_stats_update':
      data = await getUserLoginStats([ids], eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled)
      break
    case 'ezugi_wallet_update':
      data = await getEzugiWalletUpdate([ids], eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled)
      if (data.length === 0) { return { success: false } }
      break
    case 'casino_win':
      data = await getCasinoBetWinDataEzugi([ids], eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled)
      break
    default:
      throw new Error(`Unknown eventType: ${eventType}`)
  }
  if (!data || data?.length === 0) {
    throw new Error(`No data found for event type: ${eventType}`)
  }
  if (eventType === 'ezugi_player_details_update') {
    const updateProfileData = data.map(item => ({
      ...item,
      payload: {},
      eid: uuidv4(),
      event_type: 'update_profile'
    }))
    await sendSmarticoRequest(updateProfileData, tenantId)
  }
  await sendSmarticoRequest(data, tenantId)
}

const sendSmarticoRequest = async (data, tenantId) => {
  const url = config.get('smartiCo.url')
  const token = config.get('smartiCo.token')

  const requestBody = {
    method: JOB_CONSTANT.METHOD,
    maxBodyLength: JOB_CONSTANT.MAX_BODY_LENGTH,
    url,
    headers: {
      ...JOB_CONSTANT.HEADERS,
      Authorization: token
    },
    data
  }
  try {
    const { data } = await axios(requestBody)
    await db.RequestResponseLog.create({
      requestJson: requestBody,
      responseJson: data,
      service: 'queue-smartico-single',
      url: url,
      responseCode: data?.response?.status,
      tenantId: tenantId
    })
    return data
  } catch (e) {
    const { response } = e
    await db.RequestResponseLog.create({
      requestJson: requestBody,
      responseJson: response?.data,
      service: 'queue-smartico-single',
      url: url,
      responseCode: response?.status,
      tenantId: tenantId
    })
    throw e
  }
}
