import db from '../db/models'
import { AutomaticActiveLosingBonus } from '../services/cron-services/automaticActiveLosingBonus'
export default async () => {
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'automatic_losing_bonus_cron',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      await AutomaticActiveLosingBonus.execute()
    }
  } catch (e) {
    console.log("---------error in run automatic active losing bonus----------------", e)
  }
}
