import db, { sequelize } from '../../db/models'
import { CommonJob, CommonQueue } from '../../queues/common.queue'
import { PUSH_IN_QUEUE_CRON, PUSH_IN_QUEUE_PAGE_COUNT } from '../../utils/constants/constant'

export default async () => {
  const actionTypes = ['deposit', 'withdraw', 'non_cash_deposited_by_admin', 'non_cash_withdraw_by_admin']

  if (PUSH_IN_QUEUE_CRON) {
    const { count } = await db.Marina888Agent.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        status: false
      },
      raw: true
    })

    const pages = Math.ceil(count / PUSH_IN_QUEUE_PAGE_COUNT)
    let offset = 0
    for (let i = 1; i <= pages; i++) {
      const users = await db.Marina888Agent.findAll({
        attributes: ['externalAgentId', 'id'],
        where: {
          status: false
        },
        limit: PUSH_IN_QUEUE_PAGE_COUNT,
        offset: offset,
        order: [
          ['externalAgentId', 'ASC']
        ],
        raw: true
      })
      offset = i * PUSH_IN_QUEUE_PAGE_COUNT
      if (users) {
        const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
        for (const usr of users) {
          try {
            await delay(10000)
            for (const action of actionTypes) {
              CommonQueue.add(CommonJob,
                {
                  data: {
                    id: usr.externalAgentId,
                    action
                  },
                  transactionType: 'marina888AgentTransaction'
                },
                {
                  jobId: `${usr.id}_${CommonJob}`,
                  removeOnComplete: true,
                  delay: 5
                })
            }
          } catch (error) {
            console.log('==========error=========', error)
          }
        }
      }
    }
  }
}
