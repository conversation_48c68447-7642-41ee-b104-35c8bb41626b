import db, { sequelize } from '../../db/models'
import { CommonJob, CommonQueue } from '../../queues/common.queue'
import { PUSH_IN_QUEUE_CRON, PUSH_IN_QUEUE_PAGE_COUNT } from '../../utils/constants/constant'

export default async () => {
  if (PUSH_IN_QUEUE_CRON) {
    const { count } = await db.Marina888User.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        status: false
      },
      raw: true
    })

    const pages = Math.ceil(count / PUSH_IN_QUEUE_PAGE_COUNT)
    let offset = 0
    for (let i = 1; i <= pages; i++) {
      const users = await db.Marina888User.findAll({
        attributes: ['id'],
        where: {
          status: false
        },
        limit: PUSH_IN_QUEUE_PAGE_COUNT,
        offset: offset,
        order: [
          ['id', 'ASC']
        ],
        raw: true
      })
      offset = i * PUSH_IN_QUEUE_PAGE_COUNT
      if (users) {
        const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
        for (const usr of users) {
          try {
            await delay(50)
            CommonQueue.add(CommonJob,
              {
                id: usr.id,
                transactionType: 'marina888User'
              },
              {
                jobId: `${usr.id}_${CommonJob}`,
                removeOnComplete: true,
                delay: 5
              })
          } catch (error) {
            console.log('==========error=========', error)
          }
        }
      }
    }
  }
}
