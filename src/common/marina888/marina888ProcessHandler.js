import axios from 'axios'
import config from '../../configs/app.config'
import db, { sequelize } from '../../db/models'
import { JOB_CONSTANT, MARINA888_TYPES_ENDPOINT } from '../constants'

export default async (JobData) => {
  const { id } = JobData
  const res = {}
  const url = config.get('marina888ApiUrl.phpBaseUrl') + MARINA888_TYPES_ENDPOINT.PROCESS_TRANSACTION
  const isProdEnv = config.get('env') === 'production'
  const tenantId = isProdEnv ? 86 : 54

  const requestBody = {
    method: JOB_CONSTANT.METHOD,
    maxBodyLength: JOB_CONSTANT.MAX_BODY_LENGTH,
    url,
    headers: JOB_CONSTANT.HEADERS,
    data: { id }
  }
  try {
    const { data } = await axios(requestBody)

    await db.RequestResponseLog.create({
      requestJson: requestBody,
      responseJson: data,
      service: 'marina888-queue-process',
      url: url,
      responseCode: data?.response?.status,
      tenantId
    })

    if (data && data.message === 'There are no transactions to insert.') {
      await db.Marina888UserDataProcessing.update({ status: 'completed' }, { where: { id } })
    }

    res.status = true
  } catch (e) {
    const { response } = e
    await db.RequestResponseLog.create({
      requestJson: requestBody,
      responseJson: response?.data,
      service: 'marina888-queue-process',
      url: url,
      responseCode: response?.status,
      tenantId
    })
    throw e
  }
  await updateRecordStatus(id)
  return res
}

async function updateRecordStatus (id) {
  const record = await db.Marina888UserDataProcessing.findOne({
    where: {
      id
    },
    raw: true,
    attributes: ['userActionRecordId']
  })
  if (record) {
    const userActionRecordId = parseInt(record.userActionRecordId)
    const { pending, inProgress, completed, error } = await getStatusCounts(userActionRecordId)

    if ((pending && pending > 0) || (inProgress && inProgress > 0)) {
      return true
    } else if ((completed && completed > 0) && (error && error > 0)) {
      await db.Marina888UserActionRecord.update({ status: 'partialCompleted' }, { where: { id: userActionRecordId } })
    } else if ((completed && completed > 0) && !(error && error > 0)) {
      await db.Marina888UserActionRecord.update({ status: 'completed' }, { where: { id: userActionRecordId } })
    } else if ((error && error > 0) && !(completed && completed > 0)) {
      await db.Marina888UserActionRecord.update({ status: 'error' }, { where: { id: userActionRecordId } })
    }
  }
  return true
}

async function getStatusCounts (id) {
  const counts = await db.Marina888UserDataProcessing.findAll({
    attributes: [
      'status',
      [sequelize.fn('COUNT', sequelize.col('status')), 'count']
    ],
    group: ['status'],
    where: {
      userActionRecordId: parseInt(id)
    },
    useMaster: true,
    raw: true
  })

  const statusCounts = counts.reduce((acc, count) => {
    acc[count.status] = parseInt(count.count, 10)
    return acc
  }, {})
  return statusCounts
}
