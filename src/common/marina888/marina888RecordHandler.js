import { Op } from 'sequelize'
import db, { sequelize } from '../../db/models'
import { Marina888Job, Marina888Queue } from '../../queues/marina888.queue'
import { PUSH_IN_QUEUE_CRON, PUSH_IN_QUEUE_PAGE_COUNT } from '../../utils/constants/constant'

export default async (jobData) => {
  const { id } = jobData
  if (PUSH_IN_QUEUE_CRON) {
    const { count } = await db.Marina888UserDataProcessing.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        userActionRecordId: id,
        status: {
          [Op.notIn]: ['completed','inProgress']
        }
      },
      raw: true
    })

    const pages = Math.ceil(count / PUSH_IN_QUEUE_PAGE_COUNT)
    let offset = 0
    for (let i = 1; i <= pages; i++) {
      const records = await db.Marina888UserDataProcessing.findAll({
        attributes: ['id'],
        where: {
          userActionRecordId: id,
          status: {
            [Op.notIn]: ['completed','inProgress']
          }
        },
        limit: PUSH_IN_QUEUE_PAGE_COUNT,
        offset: offset,
        order: [
          ['id', 'ASC']
        ],
        raw: true
      })
      offset = i * PUSH_IN_QUEUE_PAGE_COUNT
      if (records) {
        const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
        for (const record of records) {
          try {
            await delay(50)
            Marina888Queue.add(Marina888Job,
              {
                id: record.id,
                transactionType: 'marina888UserDataProcess'
              },
              {
                jobId: `${record.id}_${Marina888Job}`,
                removeOnComplete: true,
                delay: 0
              })
          } catch (error) {
            console.log('==========error=========', error)
          }
        }
      }
    }
  }
}
