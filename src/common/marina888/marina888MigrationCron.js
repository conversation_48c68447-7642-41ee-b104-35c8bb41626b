import db, { sequelize } from '../../db/models'
import { QUEUE_WORKER_CONSTANT } from '../constants'

export default async () => {
  const queueProcessStatus = await db.QueueProcessStatus.findOne({
    where: {
      service: 'marina888_transaction_sync',
      status: 1
    },
    attributes: ['id']
  })
  if (queueProcessStatus) {
    const { count } = await db.Marina888UserActionRecord.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        status: 'inProgress'
      },
      raw: true
    })
    if (count > 0) {
      return { status: true }
    }
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
    const records = await db.Marina888UserActionRecord.findAll({
      where: {
        status: 'pending'
      },
      raw: true,
      attributes: ['id'],
      order: [['id', 'ASC']],
      limit: 50
    })
    if (records && records.length > 0) {
      for (const record of records) {
        const queueLogObject = {
          type: 'marina888MigrationCron',
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [parseInt(record.id)]
        }

        // Insert into QueueLog for the current record
        await db.QueueLog.create(queueLogObject)

        // Update the status of the current record to 'inProgress'
        await db.Marina888UserActionRecord.update({
          status: 'inProgress'
        }, {
          where: {
            id: record.id
          }
        })
        await delay(1000)
      }
    }
  }
  return { status: true }
}
