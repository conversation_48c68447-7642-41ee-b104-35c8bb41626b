import db, { sequelize } from '../db/models'
import { CRON_LOG_STATUS } from '../common/constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'delete_queue_logs_data_cron',
        status: 1
      },
      attributes: ['id']
    })
    if(queueProcessStatus){
      cronLog.cronId = queueProcessStatus?.id
      const sql = `DELETE FROM "public"."queue_logs" WHERE ("status" = 1 AND created_at < now() - interval '1 hours') OR ("ids" = '[]');`
      const currencyAllAllowed = await sequelize.query(sql)
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log("==========error", e)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
