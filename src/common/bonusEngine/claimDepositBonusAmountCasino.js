import { Sequelize } from 'sequelize'
import { v4 as getUUIDTransactionId } from 'uuid'
import { BONUS_COMMENT_ABBREVIATIONS, BONUS_STATUS, BONUS_TYPES, DEPOSIT_BONUS_TRANSACTION_STATUS, QUEUE_WORKER_CONSTANT, TRANSACTION_TYPES } from '../../common/constants'
import db from '../../db/models'
import v3CurrencyConversion from '../v3CurrencyConversion'
import { walletLocking } from '../walletLocking'


const getTransactionType = (walletTypeValue) => {
  const transactionTypes = {
    0: TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM, // amount
    1: TRANSACTION_TYPES.NON_CASH_DEPOSIT_BONUS_CLAIM, // nonCashAmount
    2: TRANSACTION_TYPES.FREE_BETS_DEPOSIT_BONUS_CLAIM, // oneTimeBonusAmount - free_bets_on_deposit_bonus_claim
    3: TRANSACTION_TYPES.SPORTS_FREE_BETS_DEPOSIT_BONUS_CLAIM // sportsFreeBetAmount - sports_free_bets_deposit_bonus_claim
  }
  return transactionTypes[walletTypeValue] || TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM
}


/**
 * This function will credit the casino and both(sports + casino) deposit bonus amount in the user's wallet as soon as it
 * achieve the rollover target.
 * @export
 * @param {object} userActiveDepositBonus contains userBonus object
 * @param {string} userId The ID of the user to whom the bonus will be credited.
 * @param {string} walletType The type of wallet to which the bonus will be credited.
 * @param {object} sequelizeTransaction The transaction object for ensuring atomic operations in the database.
 * @return {object} Returns an object containing the userWallet and the created QueueLog record IDs.
 */
export default async (userActiveDepositBonus, userId, walletType, sequelizeTransaction) => {
  const {
    Wallet: WalletModel,
    Transaction: TransactionModel,
    QueueLog: QueueLogModel,
    AuditLog: AuditLogModel,
    User: UserModel,
  } = db

  try {
    userActiveDepositBonus.status = BONUS_STATUS.CLAIMED
    userActiveDepositBonus.rolloverBalance = 0
    userActiveDepositBonus.claimedAt = new Date().toISOString()

    const userWallet = await walletLocking({ id: userId }, sequelizeTransaction)
    await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })

    const walletTypeValue = userActiveDepositBonus?.Bonus?.walletType || 0

    // Add comments with sequelize.literal
    const transactionComment = userActiveDepositBonus?.kind === BONUS_TYPES.DEPOSIT_BOTH ? BONUS_COMMENT_ABBREVIATIONS.CSBC : BONUS_COMMENT_ABBREVIATIONS.CBC;

    userWallet[walletType] += userActiveDepositBonus.bonusAmount
    await userWallet.save({ transaction: sequelizeTransaction })

    let user = await UserModel.findOne({
      attributes: ['tenantId', 'parentId', [Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id AND wallets.owner_type = 'User')`), 'exchangeRate']
      ],
      where: {
        id: userId
      },
      raw: true
    })

    let transactionObject = {
      targetWalletId: userWallet?.id,
      targetCurrencyId: userWallet?.currencyId,
      amount: userActiveDepositBonus?.bonusAmount,
      conversionRate: user?.dataValues?.exchangeRate || user?.exchangeRate,
      targetBeforeBalance: userWallet[walletType] - userActiveDepositBonus?.bonusAmount,
      targetAfterBalance: userWallet[walletType],
      comments: transactionComment,
      actioneeId: userId,
      actioneeType: 'User',
      tenantId: user?.tenantId,
      timestamp: new Date().getTime(),
      transactionType: getTransactionType(walletTypeValue),
      transactionId: getUUIDTransactionId(),
      //errorDescription: 'Completed Successfully',
      errorCode: 0,
      status: DEPOSIT_BONUS_TRANSACTION_STATUS.SUCCESS,
      success: true,
      paymentMethod: 'manual'
    }

    transactionObject = await v3CurrencyConversion(sequelizeTransaction, transactionObject, userWallet?.currencyId, user?.tenantId, userActiveDepositBonus?.bonusAmount)

    let transactionDetail = await TransactionModel.create(transactionObject, { transaction: sequelizeTransaction })

    if (userActiveDepositBonus?.transactionId) {
      // Fetch the record to assign it to `previousData`
      const transactionRecord = await TransactionModel.findOne({
        where: {
          id: userActiveDepositBonus.transactionId,
        },
      });

      await TransactionModel.destroy({
        where: {
          id: userActiveDepositBonus.transactionId
        },
        transaction: sequelizeTransaction
      });

      let auditLogEntity = {
        tenantId: user?.tenantId,
        actioneeId: user?.parentId,
        eventType: 'DepositBonusClaim',
        event: 'Create',
        eventId: userId,
        actioneeIp: '0.0.0.0',
        action: 'Deposit Bonus Claimed',
        previousData: transactionRecord,
        modifiedData: transactionDetail,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const auditLogId = await AuditLogModel.create(auditLogEntity, { transaction: sequelizeTransaction });

      const queueLogEntries = [
        {
          type: QUEUE_WORKER_CONSTANT.AUDIT_LOG,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [auditLogId?.id],
        },
        {
          type: QUEUE_WORKER_CONSTANT.ELASTIC_RECORD_DELETION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [{ id: transactionRecord?.id, type: 'casino' }],
        },
      ];

      // Bulk insert queue log entries
      await QueueLogModel.bulkCreate(queueLogEntries, { transaction: sequelizeTransaction });
    }

    userActiveDepositBonus.transactionId = transactionDetail.id
    await userActiveDepositBonus.save({ transaction: sequelizeTransaction })

    // adding updated transaction id in queue log
    const queueLogObject = {
      type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
      status: QUEUE_WORKER_CONSTANT.READY,
      ids: [transactionDetail?.id]
    }

    const { id: casinoTransactionLogId } = await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })

    return { userWallet, queueLogIds: { casinoTransactionLogId } }
  } catch (error) {
    throw error
  }
}
