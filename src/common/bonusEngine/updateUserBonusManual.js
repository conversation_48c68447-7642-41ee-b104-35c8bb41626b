import { Op, Sequelize } from 'sequelize'
import db from '../../db/models'
import { BONUS_KIND, WEEKDAYS } from '../../utils/constants/constant'
import { bonusQueueRollover } from '../bonusQueueRollover'
import { burning<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../burningBonusCheck'
import { BONUS_COMMENT_ABBREVIATIONS, BONUS_RECURRING_STATUS, BONUS_TIER_CONFIG_TYPE, BONUS_TIER_TYPE, BONUS_TYPES, DEPOSIT_INSTANT_BONUS_TYPES, DEPOSIT_ROLLING_CALCULATION_METHODS, QUEUE_WORKER_CONSTANT, TABLES } from '../constants'
import { instantDepositBonusManual } from './instantManualDepositBonus'

const BONUS_TYPE_COMMENT_MAPPING = {
  [BONUS_KIND.DEPOSIT]: {
    withdrawal: BONUS_COMMENT_ABBREVIATIONS.CDBCDTW,
    deposit: BONUS_COMMENT_ABBREVIATIONS.CDBCDTD
  },
  [BONUS_KIND.DEPOSIT_SPORT]: {
    withdrawal: BONUS_COMMENT_ABBREVIATIONS.SDBCDTW,
    deposit: BONUS_COMMENT_ABBREVIATIONS.SDBCDTD
  },
  [BONUS_KIND.DEPOSIT_BOTH]: {
    withdrawal: BONUS_COMMENT_ABBREVIATIONS.BDBCDTW,
    deposit: BONUS_COMMENT_ABBREVIATIONS.BDBCDTD
  }
}

const getBonusComment = (bonusKind, transactionType) => {
  const transactionKey = transactionType === BONUS_KIND.WITHDRAW ? 'withdrawal' : 'deposit'
  return BONUS_TYPE_COMMENT_MAPPING[bonusKind]?.[transactionKey] || BONUS_COMMENT_ABBREVIATIONS.DBC
}


export const updateUserBonus = async (request, trans, sequelizeTransaction, depositId) => {
  const {
    UserBonus: UserBonusModel,
    User: UserModel,
    Bonus: BonusModel,
    DepositBonusSetting: DepositBonusSettingModel,
    DepositBonusTier: DepositBonusTierModel,
    Transaction: TransactionModel,
    BetsTransaction: BetsTransactionModel,
    TenantThemeSetting: TenantThemeSettingsModel,
    UserBonusRolloverTransaction: UserBonusRolloverTransactionModel,
    UserBonusRecurringRollover: UserBonusRecurringRolloverModel,
    UserDepositSequence: UserDepositSequenceModel
  } = db
  const user = await UserModel.findOne({
    where: {
      id: request.targetEmail
    },
    attributes: ['id', 'tenantId', 'firstName', 'lastName']
  })

  const { transactionAmount: amount } = request
  const weekdaysMap = WEEKDAYS
  const queueLogsInfo = []

  const userBonuses = await UserBonusModel.findAll({
    where: {
      userId: request.targetEmail,
      status: 'active',
      expiresAt: {
        [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      [Op.or]: [
        // Condition 1
        {
          kind: {
            [Op.in]: [BONUS_KIND.DEPOSIT, BONUS_KIND.DEPOSIT_SPORT, BONUS_KIND.DEPOSIT_INSTANT, BONUS_KIND.DEPOSIT_BOTH]
          },
          bonusAmount: 0,
          rolloverBalance: 0
        },

        // Condition 2
        {
          kind: {
            [Op.in]: [BONUS_KIND.DEPOSIT, BONUS_KIND.DEPOSIT_SPORT, BONUS_KIND.DEPOSIT_BOTH]
          },
          '$DepositBonusSetting.deposit_bonus_type$': DEPOSIT_INSTANT_BONUS_TYPES.RECURRING
        }
      ]
    },
    include: [{
      model: DepositBonusSettingModel,
      attributes: ['id', 'minDeposit', 'maxDeposit', 'maxBonus', 'rolloverCalculationType', 'rolloverMultiplier', 'validForDays', 'depositType', 'depositBonusType', 'recurringBonusType', 'allowedPaymentProviders', 'customDeposits', 'tierType', 'weekDay', 'burningDays', 'burnType', 'tierConfigType', 'applyToDepositSequenceGte'],
      where: {
        depositType: [0, 1],
        [Op.or]: [
          {
            tierConfigType: BONUS_TIER_CONFIG_TYPE.TIER_BASED,
            [Op.or]: [
              {
                tierType: BONUS_TIER_TYPE.TIER_1,
                minDeposit: { [Op.lte]: request.transactionAmount },
                maxDeposit: { [Op.gte]: request.transactionAmount }
              },
              {
                tierType: BONUS_TIER_TYPE.TIER_2
              }
            ]
          },
          {
            tierConfigType: BONUS_TIER_CONFIG_TYPE.SEQUENCE_BASED
          }
        ]
      },
      required: true,
      include: [{
        model: DepositBonusTierModel,
        where: {
          min_deposit_amount: { [Op.lte]: request.transactionAmount },
          max_deposit_amount: { [Op.gte]: request.transactionAmount }
        },
        required: false
      }]
    }, {
      model: BonusModel, required: true
    }]

  })

  if (userBonuses.length > 0) {
    for (const bonus of userBonuses) {
      const bonusDetails = bonus?.Bonus

      if (bonus && bonus?.Bonus && bonus?.DepositBonusSetting) {
        if (request.transactionType === BONUS_KIND.DEPOSIT) {
          if (bonus?.DepositBonusSetting?.weekDay) {
            const today = new Date()

            const specifiedWeekdayNum = weekdaysMap[bonus?.DepositBonusSetting?.weekDay?.toLowerCase()]
            if (today.getDay() !== specifiedWeekdayNum) {
              await bonusQueueRollover(amount, trans.id,user.id, null, null, sequelizeTransaction, depositId)
              return queueLogsInfo
            }
          }

          if (bonus?.DepositBonusSetting?.burningDays) {
            const giveable = await burningBonusCheck(user.id, user.tenantId)
            if (!giveable) {
              await bonusQueueRollover(amount, trans.id,user.id, null, null, sequelizeTransaction, depositId)
              return  queueLogsInfo
            }
          }

          let percentage, minDeposit, maxDeposit, maxBonusInfo, matchedTier
          const isSequenceBased = bonus.DepositBonusSetting.tierConfigType === BONUS_TIER_CONFIG_TYPE.SEQUENCE_BASED

          if (isSequenceBased) {
            // SEQUENCE-BASED TIER LOGIC
            // Find or create user deposit sequence record
            let userSequence = await UserDepositSequenceModel.findOne({
              where: {
                userId: user.id,
                bonusId: bonusDetails.id
              }
            })
            // If no sequence record exists, create one starting at sequence 0
            if (!userSequence) {
              userSequence = await UserDepositSequenceModel.create({
                userId: user.id,
                bonusId: bonusDetails.id,
                currentSequence: 1
              }, { transaction: sequelizeTransaction })
            } else {
              userSequence.currentSequence += 1
              await userSequence.save({ transaction: sequelizeTransaction })
            }

            let sequenceToMatch = userSequence.currentSequence
            const applyAfterFixedNumber = bonus.DepositBonusSetting.applyToDepositSequenceGte
            if (applyAfterFixedNumber && userSequence.currentSequence > applyAfterFixedNumber) {
              sequenceToMatch = applyAfterFixedNumber
              if (amount < bonus.DepositBonusSetting.minDeposit) {
                await bonusQueueRollover(amount, trans.id, user.id, null, null, sequelizeTransaction, depositId)
                return queueLogsInfo
              }
            }

            // Get all tiers for this bonus and order them
            const allTiers = await DepositBonusTierModel.findAll({
              where: {
                depositBonusSettingId: bonus.DepositBonusSetting.id
              },
              order: [['id', 'ASC']]
            })

            const tierIndex = sequenceToMatch - 1

            if (tierIndex >= 0 && tierIndex < allTiers.length) {
              const candidateTier = allTiers[tierIndex]

              if (amount >= candidateTier.minDepositAmount) {
                matchedTier = candidateTier

                // Set values from matched tier
                percentage = matchedTier.percentage
                minDeposit = matchedTier.minDepositAmount
                maxDeposit = matchedTier.maxDepositAmount
                maxBonusInfo = matchedTier.maxBonus
              } else {
                await bonusQueueRollover(amount, trans.id, user.id, null, null, sequelizeTransaction, depositId)
                return queueLogsInfo
              }
            } else {
              // No matching tier for this sequence
              await bonusQueueRollover(amount, trans.id, user.id, null, null, sequelizeTransaction, depositId)
              return queueLogsInfo
            }
          } else if (bonus.DepositBonusSetting.tierType === BONUS_TIER_TYPE.TIER_2 &&
                     Array.isArray(bonus.DepositBonusSetting.DepositBonusTiers) &&
                     bonus.DepositBonusSetting.DepositBonusTiers.length > 0) {
            const [{ minDepositAmount, maxDepositAmount, maxBonus, percentage: tierPercentage }] = bonus.DepositBonusSetting.DepositBonusTiers

            percentage = tierPercentage
            minDeposit = minDepositAmount
            maxDeposit = maxDepositAmount
            maxBonusInfo = maxBonus,
            matchedTier = bonus.DepositBonusSetting.DepositBonusTiers[0]
          } else {
            percentage = bonus.Bonus.percentage
            minDeposit = bonus.DepositBonusSetting.minDeposit
            maxDeposit = bonus.DepositBonusSetting.maxDeposit
            maxBonusInfo = bonus.DepositBonusSetting.maxBonus
          }

          // deposit amount less than min deposit amount and greater than max deposit amount check
          if (!isSequenceBased && (amount < minDeposit || amount > maxDeposit)) {
            await bonusQueueRollover(amount, trans.id,user.id, null, null, sequelizeTransaction, depositId)
            return queueLogsInfo
          }

          // instant deposit bonus check
          if (bonus.kind === BONUS_TYPES.DEPOSIT_INSTANT) {
            let result = await instantDepositBonusManual(bonus, request, trans, sequelizeTransaction)
            if (!result) {
              await bonusQueueRollover(amount, trans.id,user.id, null, null, sequelizeTransaction, depositId)
              return queueLogsInfo
            }
            return queueLogsInfo
          }
          let rolling

          switch (bonus?.DepositBonusSetting?.rolloverCalculationType) {
            case DEPOSIT_ROLLING_CALCULATION_METHODS.TOTAL_BASED_ROLLING:
              rolling = (parseFloat(amount) * (parseFloat(percentage) / 100) + parseFloat(amount)) * parseFloat(bonus?.DepositBonusSetting?.rolloverMultiplier)
              break
            case DEPOSIT_ROLLING_CALCULATION_METHODS.DEPOSIT_BASED_ROLLING:
              rolling = parseFloat(amount) * parseFloat(bonus?.DepositBonusSetting?.rolloverMultiplier)
              break
            default:
              rolling = (parseFloat(amount) * (parseFloat(percentage) / 100)) * parseFloat(bonus?.DepositBonusSetting?.rolloverMultiplier)
              break
          }

          const maxBonus = parseFloat(maxBonusInfo)

          let bonusAmount = (parseFloat(amount) * (parseFloat(percentage) / 100))
          if (maxBonus && bonusAmount) {
            if (maxBonus <= bonusAmount) {
              bonusAmount = maxBonus

              switch (bonus?.DepositBonusSetting?.rolloverCalculationType) {
                case DEPOSIT_ROLLING_CALCULATION_METHODS.TOTAL_BASED_ROLLING:
                  rolling = (parseFloat(bonusAmount) + parseFloat(amount)) * parseFloat(bonus?.DepositBonusSetting?.rolloverMultiplier)
                  break
                case DEPOSIT_ROLLING_CALCULATION_METHODS.DEPOSIT_BASED_ROLLING:
                  rolling = parseFloat(amount) * parseFloat(bonus?.DepositBonusSetting?.rolloverMultiplier)
                  break
                default:
                  rolling = parseFloat(bonusAmount) * parseFloat(bonus?.DepositBonusSetting?.rolloverMultiplier)
                  break
              }
            }
          }

          if (user) {
            await db.Wallet.findOne({ where: { ownerId: user.id, ownerType: TABLES.USER } });

            const finalTier = matchedTier || bonus.DepositBonusSetting.DepositBonusTiers[0];

            // Check if this is a recurring bonus
            const isRecurring = (
              bonus.DepositBonusSetting.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(bonus.kind)
            )

            if (isRecurring) {
              if (bonus.DepositBonusSetting.recurringBonusType === 'custom_deposits') {
                const today = Sequelize.literal("DATE_TRUNC('day', CURRENT_TIMESTAMP)");
                // Count how many recurring bonuses the user has received today
                const count = await UserBonusRecurringRolloverModel.count({
                  where: {
                    bonusId: bonus.bonusId,
                    userId: bonus.userId,
                    userBonusId: bonus.id,
                    createdAt: {
                      [Op.gte]: today
                    }
                  }
                });

                // If count exceeds or equals the allowed custom deposits, skip this bonus
                if (count >= bonus.DepositBonusSetting.customDeposits) {
                  return queueLogsInfo;
                }
              }

              // Create recurring rollover entry
              await UserBonusRecurringRolloverModel.create({
                userId: bonus.userId,
                bonusId: bonus.bonusId,
                userBonusId: bonus.id,
                depositBonusTierId: finalTier ? finalTier.id : null,
                bonusAmount: bonusAmount,
                rolloverTarget: rolling,
                remainingRollover: rolling,
                status: BONUS_RECURRING_STATUS.ACTIVE
              }, { transaction: sequelizeTransaction })

              // Add to existing values
              await UserBonusModel.update({
                bonusAmount: Sequelize.literal(`COALESCE(bonus_amount, 0) + ${bonusAmount}`),
                rolloverBalance: Sequelize.literal(`COALESCE(rollover_balance, 0) + ${rolling}`),
                initialRolloverBalance: Sequelize.literal(`COALESCE(initial_rollover_balance, 0) + ${rolling}`)
              }, {
                where: { id: bonus.id },
                transaction: sequelizeTransaction
              })
            } else {
              let y = 0
              // Non-recurring logic (only set once for deposit_sport kind)
              if (bonus.kind === BONUS_TYPES.DEPOSIT_SPORTS && y === 0) {
                y++
              }

              // Set exact values
              await UserBonusModel.update({
                rolloverBalance: rolling,
                initialRolloverBalance: rolling,
                bonusAmount: bonusAmount
              }, { where: { id: bonus.id }, transaction: sequelizeTransaction })
            }

            // Check if a rollover transaction record already exists for this user, bonus, and deposit transaction
            const exists = await UserBonusRolloverTransactionModel.findOne({
              where: {
                userId: user.id,
                bonusId: bonusDetails.id,
                depositTransactionId: trans.id,
              },
            }, {
              useMaster: true
            });

            // If the record does not exist, create a new rollover transaction entry within the current DB transaction
            if (!exists) {
              await UserBonusRolloverTransactionModel.create({
                userId: user.id,
                bonusId: bonusDetails.id,
                depositTransactionId: trans.id,
                createdAt: new Date()
              }, { transaction: sequelizeTransaction });
            }
          }
        } else if (request.transactionType === BONUS_KIND.WITHDRAW) {
          const mainBonus = await BonusModel.findOne({
            where: {
              id: bonusDetails.id
            }
          })

          if (mainBonus.bonusCancellationType === 'none' || mainBonus.bonusCancellationType === 'multiple_deposit' || mainBonus.kind === BONUS_KIND.DEPOSIT_INSTANT) {
            return queueLogsInfo
          }
          if (bonus.transactionId && bonus.transactionId !== '') {
            if (bonus.kind === BONUS_TYPES.DEPOSIT_SPORTS) {
              const transaction = await BetsTransactionModel.findOne({
                where: {
                  id: bonus.transactionId
                },
                transaction: sequelizeTransaction
              })
              if (transaction) {
                transaction.status = 'failed'
                transaction.paymentFor = 8
                transaction.description = BONUS_COMMENT_ABBREVIATIONS.DBCDTW
                transaction.update({ transaction: sequelizeTransaction })
                queueLogsInfo.push({
                  type: QUEUE_WORKER_CONSTANT.BET_TRANSACTION,
                  ids: [bonus.transactionId],
                  status: QUEUE_WORKER_CONSTANT.READY
                })
              }

              const casinoBonusTransaction = await TransactionModel.findOne({
                where: {
                  debitTransactionId: transaction.id, tenantId: user.tenantId
                }
              })

              if (casinoBonusTransaction) {
                casinoBonusTransaction.status = 'failed'
                casinoBonusTransaction.comments = BONUS_COMMENT_ABBREVIATIONS.DBCDTW
                casinoBonusTransaction.update({ transaction: sequelizeTransaction })
                queueLogsInfo.push({
                  type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
                  ids: [casinoBonusTransaction.id],
                  status: QUEUE_WORKER_CONSTANT.READY
                })
              }
            } else {
              const transaction = await TransactionModel.findOne({
                where: {
                  id: bonus.transactionId
                }
              })

              if (transaction) {
                transaction.status = 'failed'
                transaction.comments = BONUS_COMMENT_ABBREVIATIONS.DBCDTW
                transaction.update({ transaction: sequelizeTransaction })
                queueLogsInfo.push({
                  type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
                  ids: [bonus.transactionId],
                  status: QUEUE_WORKER_CONSTANT.READY
                })
              }
            }
          }

          await UserBonusModel.update({
            status: 'cancelled', updatedAt: new Date()
          }, { where: { id: bonus.id }, transaction: sequelizeTransaction })

          // Check if any recurring rollover entries exist for the given user_bonus_id
          const hasRecurringRollover = await UserBonusRecurringRolloverModel.count({
            where: { userBonusId: bonus.id }
          })

          if (hasRecurringRollover) {
            // Cancel all active recurring rollover entries linked to the user bonus
            await UserBonusRecurringRolloverModel.update({
              status: BONUS_RECURRING_STATUS.CANCELLED,
              updatedAt: new Date()
            }, {
              where: {
                userBonusId: bonus.id,
                status: BONUS_RECURRING_STATUS.ACTIVE
              },
              transaction: sequelizeTransaction
            })
          }
        }
      }
    }
  } else {
    const allowedModules = await TenantThemeSettingsModel.findOne({
      where: {
        tenantId: user.tenantId
      }
    })
    await bonusQueueRollover(amount, trans.id,user.id, null, null, sequelizeTransaction, depositId)

    let userBonusesInfo = await UserBonusModel.findAll({
      where: {
        userId: request.targetEmail,
        status: 'active',
        [Op.or]: [{
          kind: {
            [Op.in]: [BONUS_KIND.DEPOSIT, BONUS_KIND.DEPOSIT_SPORT, BONUS_KIND.DEPOSIT_BOTH]
          }
        }, {
          kind: {
            [Op.notIn]: [BONUS_KIND.DEPOSIT, BONUS_KIND.DEPOSIT_SPORT, BONUS_KIND.DEPOSIT_INSTANT, BONUS_KIND.DEPOSIT_BOTH]
          }
        }]
      },
      include: [{
        model: DepositBonusSettingModel,
        where: {
          depositType: {
            [Op.in]: [0, 1]
          }
        },
        required: false
      }]
    })

    if (allowedModules.allowedModules.includes('parallelBonus')) {
      userBonusesInfo = userBonusesInfo.filter(bonus => [BONUS_KIND.DEPOSIT, BONUS_KIND.DEPOSIT_SPORT, BONUS_KIND.DEPOSIT_BOTH].includes(bonus.kind))
    }

    if (userBonusesInfo.length > 0) {
      for (const userBonus of userBonusesInfo) {
        const mainBonus = await BonusModel.findOne({
          where: {
            id: userBonus.bonusId
          }
        })

        if (request.transactionType === BONUS_KIND.DEPOSIT) {
          if (mainBonus.bonusCancellationType === 'none' || mainBonus.bonusCancellationType === 'multiple_withdraw') {
            return queueLogsInfo
          }
        }

        if (request.transactionType === BONUS_KIND.WITHDRAW) {
          if (mainBonus.bonusCancellationType === 'none' || mainBonus.bonusCancellationType === 'multiple_deposit') {
            return queueLogsInfo
          }
        }

        await UserBonusModel.update({
          status: 'cancelled', updatedAt: new Date()
        }, { where: { id: userBonus.id }, transaction: sequelizeTransaction })

        // Check if any recurring rollover entries exist for the given user_bonus_id
        const hasRecurringRollover = await UserBonusRecurringRolloverModel.count({
          where: { userBonusId: userBonus.id }
        })

        if (hasRecurringRollover) {
          // Cancel all active recurring rollover entries linked to the user bonus
          await UserBonusRecurringRolloverModel.update({
            status: BONUS_RECURRING_STATUS.CANCELLED,
            updatedAt: new Date()
          }, {
            where: {
              userBonusId: userBonus.id,
              status: BONUS_RECURRING_STATUS.ACTIVE
            },
            transaction: sequelizeTransaction
          })
        }

        if (userBonus.transactionId && userBonus.transactionId !== '') {
          const transactionType = request.transactionType === BONUS_KIND.WITHDRAW ? 'withdrawal' : BONUS_KIND.DEPOSIT
          const bonusType = userBonus.kind === BONUS_KIND.DEPOSIT ? 'Casino deposit bonus' : userBonus.kind === BONUS_KIND.DEPOSIT_SPORT ? 'Sport deposit bonus' : 'Casino and Sport both deposit bonus'

          const transaction = await TransactionModel.findOne({
            where: {
              id: userBonus.transactionId
            }
          })

          if (transaction) {
            transaction.status = 'failed'
            transaction.comments = getBonusComment(userBonus.kind, request.transactionType)
            transaction.update({ transaction: sequelizeTransaction })

            queueLogsInfo.push({
              type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
              ids: [userBonus.transactionId],
              status: QUEUE_WORKER_CONSTANT.READY
            })
          }

          const betTransaction = await BetsTransactionModel.findOne({
            where: {
              id: userBonus.transactionId, tenantId: user.tenantId
            }
          })

          if (betTransaction) {
            betTransaction.status = 'failed'
            betTransaction.description = getBonusComment(userBonus.kind, request.transactionType)
            betTransaction.update({ transaction: sequelizeTransaction })
            queueLogsInfo.push({
              type: QUEUE_WORKER_CONSTANT.BET_TRANSACTION,
              ids: [userBonus.transactionId],
              status: QUEUE_WORKER_CONSTANT.READY
            })
            const casinoBonusTransaction = await TransactionModel.findOne({
              where: {
                tenantId: user.tenantId, debitTransactionId: betTransaction.id
              }
            })

            if (casinoBonusTransaction) {
              casinoBonusTransaction.status = 'failed'
              casinoBonusTransaction.comments = getBonusComment(userBonus.kind, request.transactionType)
              casinoBonusTransaction.update({ transaction: sequelizeTransaction })

              queueLogsInfo.push({
                type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
                ids: [casinoBonusTransaction.id],
                status: QUEUE_WORKER_CONSTANT.READY
              })
            }
          }
        }
      }
    }
  }

  return queueLogsInfo
}
