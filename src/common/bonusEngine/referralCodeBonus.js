import { Sequelize } from 'sequelize';
import { v4 as uuidv4 } from 'uuid';
import db, { sequelize } from '../../db/models';
import { REFERRAL_APPLY_TO, REFERRAL_BONUS_TYPE, REFERRAL_STATUS, REFERRAL_WALLET_TYPE } from '../../utils/constants/constant';
import { BONUS_COMMENT_ABBREVIATIONS, PROMO_BONUS_WALLET_TYPES, QUEUE_WORKER_CONSTANT, TRANSACTION_TYPES } from '../constants';
import ErrorLogHelper from '../errorLog';
import { publishToRedis } from '../queueService/publishToRedis';
import userCurrencyExchange from '../userCurrencyExchange';
import { walletLocking } from '../walletLocking';


export default async (bonusData) => {
  if (!bonusData || !Array.isArray(bonusData) || bonusData.length < 1) return;

  const [{  referralId, referralSettingsId }] = bonusData;

  const {
    Transaction: TransactionModel,
    Wallet: WalletModel,
    Referral: ReferralModel,
    ReferralSettings: ReferralSettingsModel,
    QueueLog: QueueLogModel,
    Currency: CurrencyModel
  } = db;

  const sequelizeTransaction = await sequelize.transaction();

  try {
    const referral = await ReferralModel.findOne({
      where: { id: parseInt(referralId), status:REFERRAL_STATUS.IN_PROGRESS },
      useMaster: true
    });
    if (!referral) return;

    // Fetch referral settings
    const referralSetting = await ReferralSettingsModel.findOne({
      where: { id: parseInt(referralSettingsId) },
      attributes: [
        'id',
        'referrerBonusAmount',
        'refereeBonusAmount',
        'bonusType',
        'walletType',
        'applyTo',
        'tenantId',
        'percentageValue',
      ],
      raw: true,
    });
    if (!referralSetting) return;


    let convertedReferrerAmount = 0;
    let convertedRefereeAmount = 0;

    const referrerBonus = parseFloat(referralSetting.referrerBonusAmount);
    const refereeBonus = parseFloat(referralSetting.refereeBonusAmount);

    if (referralSetting.bonusType === REFERRAL_BONUS_TYPE.FLAT) {
      // Flat bonus
      convertedReferrerAmount = referrerBonus;
      convertedRefereeAmount = refereeBonus;
    } else if (referralSetting.bonusType === REFERRAL_BONUS_TYPE.UPTO) {
      // Random bonus between 1 and bonusAmount
      const calculateUptoBonusAmountHandler = (bonusAmount) => {
        const randomAmount = Math.floor(Math.random() * bonusAmount) + 1;
        return randomAmount;
      };

      convertedReferrerAmount = calculateUptoBonusAmountHandler(referrerBonus);
      convertedRefereeAmount = calculateUptoBonusAmountHandler(refereeBonus);
    } else if (referralSetting.bonusType === REFERRAL_BONUS_TYPE.PERCENTAGE) {
      // Percentage bonus based on transaction amount up to bonusAmount or bonusAmount
      const calculatePercentageBonusAmountHandler = (bonusAmount, percentage) => {

        const bonusPercentage = parseFloat(percentage);
        const amount = referral.transactionAmount ? parseFloat(referral.transactionAmount) : bonusAmount;
        let calculatedAmount = (amount * bonusPercentage) / 100;
        calculatedAmount = (calculatedAmount > bonusAmount) ? bonusAmount : calculatedAmount;

        return calculatedAmount;
      }

      const percentage = referralSetting.percentageValue;
      convertedReferrerAmount = calculatePercentageBonusAmountHandler(referrerBonus, percentage);
      convertedRefereeAmount = calculatePercentageBonusAmountHandler(refereeBonus, percentage);
    }

    const walletType =
      referralSetting.walletType === REFERRAL_WALLET_TYPE.NON_CASH
        ? PROMO_BONUS_WALLET_TYPES[1]
        : PROMO_BONUS_WALLET_TYPES[0];

    let transactionInfo =[]
    let bulkData = []
    // Process referral bonuses
    for (const walletOwner of [
      referralSetting.applyTo === REFERRAL_APPLY_TO.REFERRER ||
      referralSetting.applyTo === REFERRAL_APPLY_TO.BOTH
        ? {
            walletOwnerId: referral.referrerId,
            convertedAmount: convertedReferrerAmount,
          }
        : null,
      referralSetting.applyTo === REFERRAL_APPLY_TO.REFEREE ||
      referralSetting.applyTo === REFERRAL_APPLY_TO.BOTH
        ? {
            walletOwnerId: referral.refereeId,
            convertedAmount: convertedRefereeAmount,
          }
        : null,
    ].filter(Boolean)) {
      const {walletOwnerId, convertedAmount} = walletOwner;

      const userWallet = await walletLocking({ id: walletOwnerId }, sequelizeTransaction);
      await userWallet.reload({
        lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel },
        transaction: sequelizeTransaction,
      });

      const targetBeforeBalance = (walletType === PROMO_BONUS_WALLET_TYPES[0]) ? parseFloat(userWallet.amount) : parseFloat(userWallet.nonCashAmount)
      const targetAfterBalance = targetBeforeBalance + parseFloat(convertedAmount)

      const amountInCurrency = await sequelize.query('  select v3_currency_conversion  as otherconversions  from v3_currency_conversion (:currencyId, :tenantId, :amount)', {
        replacements: {
          currencyId: userWallet.currencyId, tenantId: referralSetting.tenantId, amount: convertedAmount
        },
        type: Sequelize.QueryTypes.SELECT,
      })



      const transactionObject = {
        amount: convertedAmount,
        targetWalletId: userWallet.id,
        targetCurrencyId: userWallet?.currencyId,
        debitTransactionId:referralId,
        transactionId: uuidv4(),
        comments: BONUS_COMMENT_ABBREVIATIONS.RBC ,
        actioneeId:  walletOwnerId,
        actioneeType: 'User',
        tenantId: referralSetting.tenantId,
        timestamp: Date.now(),
        transactionType: walletType === PROMO_BONUS_WALLET_TYPES[0] ? TRANSACTION_TYPES.REFERRAL_BONUS_CLAIM : TRANSACTION_TYPES.NON_CASH_REFERRAL_BONUS_CLAIM,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        success: true,
        status: 'success',
        otherCurrencyAmount: JSON.stringify(amountInCurrency[0].otherconversions)
      };
      transactionObject.conversionRate = await userCurrencyExchange(CurrencyModel, userWallet.currencyId)

      transactionObject.targetBeforeBalance = targetBeforeBalance
      transactionObject.targetAfterBalance = targetAfterBalance

      let newTransaction = await TransactionModel.create(transactionObject, {
        transaction: sequelizeTransaction,
      });

      const newTransactionIDList = [];
      if (newTransaction) {
        newTransactionIDList.push(newTransaction.id);
      }

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: newTransactionIDList
      }
      bulkData.push(queueLogObject);

      userWallet[walletType] = targetAfterBalance

      await userWallet.save({ transaction: sequelizeTransaction });
      transactionInfo.push({
        id: newTransaction.id,
        userId: walletOwnerId,
        walletBalance: userWallet?.amount,
        nonCashAmount: userWallet?.nonCashAmount,
        bonusAmount: convertedAmount,
      });
    }

    const referralUpdateObj = {
      status: REFERRAL_STATUS.COMPLETED,
      comment: `Referral Bonus Claimed ${
        referralSetting.applyTo === REFERRAL_APPLY_TO.BOTH
          ? "by both"
          : referralSetting.applyTo === REFERRAL_APPLY_TO.REFERRER
          ? "by referrer"
          : "by referee"
      }
      `,
    };

    if (referralSetting.applyTo === REFERRAL_APPLY_TO.REFERRER) {
      referralUpdateObj.referrerBonusAmount = convertedReferrerAmount;
    }

    if (referralSetting.applyTo === REFERRAL_APPLY_TO.REFEREE) {
      referralUpdateObj.refereeBonusAmount = convertedRefereeAmount;
    }

    if (referralSetting.applyTo === REFERRAL_APPLY_TO.BOTH) {
      referralUpdateObj.referrerBonusAmount = convertedReferrerAmount;
      referralUpdateObj.refereeBonusAmount = convertedRefereeAmount;
    }

    await ReferralModel.update(
      referralUpdateObj,
      { where: { id: referralId } },
      { transaction: sequelizeTransaction }
    );

    await QueueLogModel.bulkCreate(bulkData, { transaction: sequelizeTransaction });

    await sequelizeTransaction.commit();

    try {
      // await Promise.all(
      //   transactionInfo.map(async (data) =>
      //       await publishToRedis.publishToUserWallet({
      //       UserWalletBalance: { walletBalance: data.walletBalance, userId: data.userId, nonCashAmount: data.nonCashAmount, bonusAmount: data.bonusAmount }
      //       })
      //   )
      // );
      setTimeout(() => {
        Promise.all(
          transactionInfo.map(async (data) =>
            publishToRedis.publishToUserWallet({
              UserWalletBalance: {
                walletBalance: data?.walletBalance,
                userId: data?.userId,
                nonCashAmount: data?.nonCashAmount,
                bonusAmount: data?.bonusAmount,
              },
            }).catch((err) => {
              console.error('Error_publishing_to_Redis_after_delay::', data?.userId, err);
            })
          )
        ).then(() => {
          console.log('All wallet balances published after delay.');
        });
      }, 12000); // 12 seconds

    } catch (error) {
      console.error('Failed to publish to Redis:', error);
    }
  } catch (error) {
    await ErrorLogHelper.logError(error, null, '')
    await sequelizeTransaction.rollback();
    await ReferralModel.update(
      { status: 3, comment: error.message },
      { where: { id: referralId } }
    );
    throw error;
  }
};
