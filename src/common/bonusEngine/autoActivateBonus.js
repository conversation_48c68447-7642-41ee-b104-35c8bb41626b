import { Sequelize } from 'sequelize';
import { ALLOWED_PARALLEL_BONUS, AUTO_ACTIVATION_TYPE, BONUS_TYPES } from '../../common/constants';
import db, { sequelize } from '../../db/models';
import { convertTimeToUTC } from '../../utils/common';
import { MULTI_BONUS_STATUS } from '../../utils/constants/constant';
import ErrorLogHelper from '../errorLog';
import processBonusForEligibleUsers from './processBonusForEligibleUsers';

/**
 * @returns {Promise<null>}
 */
export default async (bonusData) => {
  const [{ bonusId, userId, userIds, timezone, type, skipKycCheck, depositId }] = bonusData;
  let sequelizeTransaction;

  try {
    const {
      Bonus: BonusModel,
      BonusCategory: BonusCategoryModel,
      User: UserModel,
      TenantThemeSetting: TenantThemeSettingModel,
      UserBonusQueue: UserBonusQueueModel
    } = db;
    let bonus;
    let userDetails;
    let allowedModules;

    const getAllowedModules = async (tenantId) => {
      const allowedModulesRow = await TenantThemeSettingModel.findOne({
        attributes: ['allowedModules'],
        where: { tenantId },
        raw: true,
      });
      return allowedModulesRow ? allowedModulesRow.allowedModules : null;
    };

    if (bonusId) {
      // Fetch bonus details
      bonus = await BonusModel.findOne({
        where: {
          id: bonusId,
          enabled: true,
          ...((type === AUTO_ACTIVATION_TYPE.AUTO) ? { autoActivateBonus: true } : {}),
          validUpto: { [Sequelize.Op.gte]: timezone ? convertTimeToUTC('', timezone) :  Sequelize.literal('CURRENT_TIMESTAMP') },
        },
        attributes: ['id', 'validUpto', 'kind', 'validFrom', 'vipLevels', 'tenantId', 'promoCodes', 'currencyId', 'referType', 'referValue', 'enablePastDate', 'autoActivateBonus'],
        include: {
          model: BonusCategoryModel,
          attributes: ['category'],
          as: 'bonusCategory'
        }
      });
      if (!bonus) {
        if (type === AUTO_ACTIVATION_TYPE.QUEUE) {
          await UserBonusQueueModel.update({
            status: MULTI_BONUS_STATUS.FAILED },
            { where: {
              bonusId: bonusId,
              userId: { [Sequelize.Op.in]: userIds }
            }
          })
        }
        return null
      }

      allowedModules = await getAllowedModules(bonus.tenantId);
      sequelizeTransaction = await sequelize.transaction();
      await processBonusForEligibleUsers(bonus, sequelizeTransaction, allowedModules, userIds, timezone, type, skipKycCheck, depositId)
    } else {
      // Fetch user details
      userDetails = await UserModel.findOne({
        attributes: ['tenantId', 'parentId', [Sequelize.literal(`(SELECT currencies.id FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id AND wallets.owner_type = 'User')`), 'currencyId']
        ],
        where: {
          id: userId
        },
        raw: true
      });

      allowedModules = await getAllowedModules(userDetails.tenantId);

      let bonuses = await BonusModel.findAll({
        where: {
          currencyId: userDetails.currencyId,
          enabled: true,
          tenantId: userDetails.tenantId,
          validUpto: { [Sequelize.Op.gte]: timezone ? convertTimeToUTC('', timezone) :  Sequelize.literal('CURRENT_TIMESTAMP') },
          autoActivateBonus: true,
          kind: { [Sequelize.Op.in]: [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_BOTH, BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_BOTH, BONUS_TYPES.DEPOSIT_INSTANT, BONUS_TYPES.DEPOSIT_SPORTS] }
        },
        attributes: ['id', 'validUpto', 'kind', 'validFrom', 'vipLevels', 'tenantId', 'promoCodes', 'currencyId', 'referType', 'referValue', 'enablePastDate', 'autoActivateBonus'],
        include: {
          model: BonusCategoryModel,
          attributes: ['category'],
          as: 'bonusCategory'
        }
      });

      if (bonuses.length === 0) return;

      sequelizeTransaction = await sequelize.transaction();
      if (bonuses.length === 1) {
        await processBonusForEligibleUsers(bonuses[0], sequelizeTransaction, allowedModules, [userId], timezone, type, skipKycCheck, depositId);
      } else {

        if (allowedModules && allowedModules.includes(ALLOWED_PARALLEL_BONUS)) {
          for (let bonusInfo of bonuses) {
            await processBonusForEligibleUsers(bonusInfo, sequelizeTransaction, allowedModules, [userId], timezone, type, skipKycCheck, depositId);
          }
        } else {
          let depositBonus = bonuses.filter(bonus => [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_BOTH, BONUS_TYPES.DEPOSIT_INSTANT, BONUS_TYPES.DEPOSIT_SPORTS].includes(bonus.kind));
          await processBonusForEligibleUsers(depositBonus[0], sequelizeTransaction, allowedModules, [userId], timezone, type, skipKycCheck, depositId);
        }
      }
    }

    await sequelizeTransaction.commit();
  } catch (error) {
    if (sequelizeTransaction) {
      await sequelizeTransaction.rollback();
    }
    ErrorLogHelper.logError(error, null, null);
    throw error;
  }
};
