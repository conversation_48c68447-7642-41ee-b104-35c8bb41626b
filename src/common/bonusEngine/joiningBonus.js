import { v4 as uuidv4 } from 'uuid';
import db, { sequelize } from '../../db/models';
import { TABLES } from '../constants';
import { publishToRedis } from '../queueService/publishToRedis';

export default async (bonusData) => {
  if (!bonusData || !Array.isArray(bonusData) || bonusData.length < 1) return;

  const [{ userId, tenantId }] = bonusData;

  try {
    const { Wallet: WalletModel } = db;

    const [updatedBonusData] = await sequelize.query(
      'CALL joining_bonus(:userId, :tenantId, :transactionId, NULL, NULL, NULL, NULL, NULL)',
      {
        replacements: {
          userId,
          tenantId,
          transactionId: uuidv4(),
        }
      }
    );

    if (!updatedBonusData || !updatedBonusData.length || !updatedBonusData[0].o_transaction_id) {
      return;
    }

    const userWallet = await WalletModel.findOne({
      where: { ownerId: userId, ownerType: TABLES.USER },
      useMaster: true,
    });

    const queueId = updatedBonusData[0].queue_id;
    const convertedAmount = updatedBonusData[0].o_bonus_amount;

    if (!queueId) {
      return;
    }


    try {
      setTimeout(() => {
        publishToRedis.publishToUserWallet({
          UserWalletBalance: {
            walletBalance: userWallet.amount,
            userId,
            nonCashAmount: userWallet.nonCashAmount,
            bonusAmount: convertedAmount,
          },
        }).catch((err) => {
          console.error('Error_publishing_to_Redis_after_delay:', err);
        });
      }, 12000); // 12 seconds

      await publishToRedis.publishToQueueService({
        QueueLog: { queueLogId: queueId },
      });
    } catch (error) {
      console.error('Error in Redis publishing:', error);
    }
  } catch (error) {
    throw error;
  }
};
