import { Op, Sequelize } from 'sequelize'
import { v4 as getUUIDTransactionId } from 'uuid'
import db, { sequelize } from '../../db/models'
import { MULTI_BONUS_STATUS } from '../../utils/constants/constant'
import { BONUS_COMMENT_ABBREVIATIONS, BONUS_ENGINE_BONUS_TYPE, BONUS_RECURRING_STATUS, BONUS_STATUS, BONUS_TYPES, DEPOSIT_BONUS_BURN_TYPE, DEPOSIT_BONUS_WALLET_TYPES, DEPOSIT_INSTANT_BONUS_TYPES, QUEUE_WORKER_CONSTANT, TRANSACTION_TYPES } from '../constants'
import ErrorLogHelper from '../errorLog'
import getPageProviderId from '../getPageProviderId'
import { publishToRedis } from '../queueService/publishToRedis'
import { walletLocking } from '../walletLocking'
import claimDepositBonusAmountCasino from './claimDepositBonusAmountCasino'
import claimDepositBonusAmountSports from './claimDepositBonusAmountSports'
import v3CurrencyConversion from '../v3CurrencyConversion'

// Helper function to handle floating point precision
const roundToDecimal = (num, decimals = 2) => {
  return Math.round((num + Number.EPSILON) * Math.pow(10, decimals)) / Math.pow(10, decimals)
}

const safeSubtract = (a, b, decimals = 2) => {
  const result = Number(a) - Number(b)
  return roundToDecimal(result, decimals)
}

const safeAdd = (a, b, decimals = 2) => {
  const result = Number(a) + Number(b)
  return roundToDecimal(result, decimals)
}

// Helper function to check if a number is effectively zero (within tolerance)
const isEffectivelyZero = (num, tolerance = 0.01) => {
  return Math.abs(Number(num)) < tolerance
}

// Helper function to get wallet type field name
const getWalletTypeField = (walletTypeValue) => {
  return DEPOSIT_BONUS_WALLET_TYPES[walletTypeValue] || DEPOSIT_BONUS_WALLET_TYPES[0]
}

const getTransactionType = (walletTypeValue, bonusKind = null) => {
  // Handle exchange deposit sport bonuses
  if (bonusKind === BONUS_TYPES.DEPOSIT_SPORTS) {
    const exchangeTransactionTypes = {
      0: TRANSACTION_TYPES.EXCHANGE_DEPOSIT_BONUS_CLAIM, // amount - 37
      1: TRANSACTION_TYPES.EXCHANGE_NON_CASH_DEPOSIT_BONUS_CLAIM, // nonCashAmount - 74
      2: TRANSACTION_TYPES.FREE_BETS_DEPOSIT_BONUS_CLAIM, // oneTimeBonusAmount - free_bets_on_deposit_bonus_claim
      3: TRANSACTION_TYPES.SPORTS_FREE_BETS_DEPOSIT_BONUS_CLAIM // sportsFreeBetAmount - sports_free_bets_deposit_bonus_claim
    }
    return exchangeTransactionTypes[walletTypeValue] || TRANSACTION_TYPES.EXCHANGE_DEPOSIT_BONUS_CLAIM
  }

  // Default transaction types for other bonus kinds
  const transactionTypes = {
    0: TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM, // amount
    1: TRANSACTION_TYPES.NON_CASH_DEPOSIT_BONUS_CLAIM, // nonCashAmount
    2: TRANSACTION_TYPES.FREE_BETS_DEPOSIT_BONUS_CLAIM, // oneTimeBonusAmount - free_bets_on_deposit_bonus_claim
    3: TRANSACTION_TYPES.SPORTS_FREE_BETS_DEPOSIT_BONUS_CLAIM // sportsFreeBetAmount - sports_free_bets_deposit_bonus_claim
  }
  return transactionTypes[walletTypeValue] || TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM
}

export default async (bonusData) => {
  const {
    UserBonus: UserBonusModel,
    Bonus: BonusModel,
    DepositBonusSetting: DepositBonusSettingModel,
    BurningBonus: BurningBonusModel,
    UserBonusQueue: UserBonusQueueModel,
    QueueLog: QueueLogModel,
    UserBonusRecurringRollover: UserBonusRecurringRolloverModel,
    TenantThemeSetting: TenantThemeSettingModel,
    Transaction: TransactionModel,
    Wallet: WalletModel
  } = db

  // Return null if bonusData is invalid
  if (!bonusData || !Array.isArray(bonusData) || bonusData.length < 1) return null

  const [{ depositBonusType, userId, tenantId, providerId, seatId, isCasino, mircoService }] = bonusData
  const gameName = bonusData[0]?.gameName
  const seatInfo = seatId || gameName;

  const pageProvider = (!providerId || !isCasino) ? [] : await getPageProviderId(tenantId, providerId, seatInfo)
  if (!pageProvider) return null

  // Start a new transaction
  const sequelizeTransaction = await sequelize.transaction()
  try {
    // Find active deposit bonus for the user
    const userActiveDepositBonus = await UserBonusModel.findOne({
      attributes: ['id', 'bonusId', 'rolloverBalance', 'kind', 'transactionId', 'bonusAmount', 'status', 'claimedAt'],
      where: {
        status: BONUS_STATUS.ACTIVE,
        kind: { [Op.in]: depositBonusType },
        userId: userId,
        expiresAt: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') },
        bonusAmount: { [Op.ne]: 0 },
        rolloverBalance: { [Op.ne]: 0 }
      },
      include: {
        model: BonusModel,
        attributes: ['id', 'walletType', 'depositBonusType','kind'],
        where: {
          enabled: true,
          tenantId
        },
        required: true,
        include: {
          model: DepositBonusSettingModel,
          attributes: ['id', 'maxRolloverPerBet', 'burningDays', 'burnType', 'providerDetails', 'depositBonusType'],
          required: true
        }
      },
      transaction: sequelizeTransaction,
      lock: {
        level: sequelizeTransaction.LOCK.UPDATE,
        of: UserBonusModel
      },
      skipLocked: false
    })

    // Find active tier rollovers if BonusTierRolloverSystem is enabled
    let activeTierRollover = null;
    if (userActiveDepositBonus && userActiveDepositBonus.Bonus.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(userActiveDepositBonus.Bonus.kind)) {
      activeTierRollover = await UserBonusRecurringRolloverModel.findOne({
        attributes: ['id', 'userBonusId', 'depositBonusTierId', 'status', 'rolloverTarget', 'remainingRollover', 'bonusAmount', 'transactionId', 'createdAt'],
        where: {
          userBonusId: userActiveDepositBonus.id,
          status: BONUS_RECURRING_STATUS.ACTIVE,
          remainingRollover: { [Op.gt]: 0 }
        },
        order: [['createdAt', 'ASC']],
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: UserBonusRecurringRolloverModel
        }
      });
    }

    // Find active deposit queue bonus for the user
    const userActiveDepositQueueBonus = await UserBonusQueueModel.findOne({
      attributes: ['id', 'bonusId', 'remainingRollover', 'bonusAmount', 'status', 'rolloverTarget', 'depositId'],
      where: {
        status: MULTI_BONUS_STATUS.PENDING,
        userId: userId,
        bonusAmount: { [Op.ne]: 0 },
        remainingRollover: { [Op.ne]: 0 }
      },
      include: {
        model: BonusModel,
        as: 'Bonus',
        attributes: ['id', 'walletType', 'kind', 'depositBonusType'],
        where: {
          enabled: true,
          tenantId,
          kind: { [Op.in]: depositBonusType }
        },
        required: true,
        include: {
          model: DepositBonusSettingModel,
          attributes: ['id', 'maxRolloverPerBet', 'burningDays', 'burnType', 'providerDetails', 'depositBonusType'],
          required: true
        }
      },
      order: [['ordering', 'ASC']],
      transaction: sequelizeTransaction
    })

    // If no active deposit bonus or queue bonus, rollback transaction and return null
    if (!userActiveDepositBonus && !userActiveDepositQueueBonus) {
      await sequelizeTransaction.rollback()
      return null
    }

    const processBonus = async (activeBonus, isQueueBonus = false) => {
      if(!isQueueBonus){
        await activeBonus.reload({
          lock: {
            level: sequelizeTransaction.LOCK.UPDATE,
            of: isQueueBonus ? UserBonusQueueModel : UserBonusModel
          },
          transaction: sequelizeTransaction
        })
      }

      const depositBonusSettings = activeBonus?.Bonus?.DepositBonusSetting
      const providerDetails = depositBonusSettings ? depositBonusSettings.providerDetails : null

      // Validate provider details if applicable
      if (providerDetails && providerDetails.length > 0 && pageProvider.length > 0 && isCasino === true) {
        const uniqueProviders = new Map(providerDetails.map(provider => [`${provider.id}-${provider.top_menu_id}`, provider.game_id || []]))
        const isProviderValid = pageProvider.some(providerDetail => {
          const key = `${providerDetail.id}-${providerDetail.topMenuId}`
          if (uniqueProviders.has(key)) {
            const gameIds = uniqueProviders.get(key)
            if (gameIds.length > 0 && gameName) {
              return gameIds.includes(gameName)
            }
            return true
          }
          return false
        })
        if (!isProviderValid) {
          await sequelizeTransaction.rollback()
          return null
        }
      }

      // Determine the rollover amount to deduct
      let { rolloverAmountToDeduct } = bonusData[0]
      const maxRolloverPerBet = depositBonusSettings?.maxRolloverPerBet
      rolloverAmountToDeduct = (maxRolloverPerBet !== null && rolloverAmountToDeduct > maxRolloverPerBet) ?
        roundToDecimal(maxRolloverPerBet) : roundToDecimal(rolloverAmountToDeduct)

      let userWallet, queueLogIds = {}
      const queueLogIdsArray = []

      // Active tier rollover exists
      if (!isQueueBonus && activeTierRollover) {
        // Calculate new remaining rollover for the tier
        const currentRemainingRollover = roundToDecimal(activeTierRollover.remainingRollover)
        let newRemainingRollover = safeSubtract(currentRemainingRollover, rolloverAmountToDeduct)

        // If the result is effectively zero or negative, set to 0
        if (newRemainingRollover <= 0 || isEffectivelyZero(newRemainingRollover)) newRemainingRollover = 0;

        // Calculate how much rollover is left to deduct
        const remainingToDeduct =  safeSubtract(rolloverAmountToDeduct, currentRemainingRollover)
        const addRemainingDeductback =  remainingToDeduct > 0 ? remainingToDeduct : 0;
        activeTierRollover.remainingRollover = newRemainingRollover;
        const currentUserBonusRollover = roundToDecimal(activeBonus.rolloverBalance)
        const newUserBonusRolloverBalance = safeAdd(safeSubtract(currentUserBonusRollover, rolloverAmountToDeduct), addRemainingDeductback)

        await UserBonusModel.update(
          { rolloverBalance: newUserBonusRolloverBalance > 0 ? newUserBonusRolloverBalance : 0 },
          { where: { id: activeBonus.id }, transaction: sequelizeTransaction }
        );

        // If this tier is completed, process the bonus credit
        if (newRemainingRollover === 0) {
          activeTierRollover.status = BONUS_RECURRING_STATUS.CLAIMED;
          activeTierRollover.claimedAt = new Date().getTime();

          await activeTierRollover.save({ transaction: sequelizeTransaction });

          // Get user's wallet
          const wallet = await walletLocking({ id: userId }, sequelizeTransaction);
          await wallet.reload({
            lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel },
            transaction: sequelizeTransaction,
          });

          if (wallet) {
            const walletTypeField = getWalletTypeField(activeBonus?.Bonus?.walletType)
            const currentWalletBalance = roundToDecimal(wallet[walletTypeField])

            // Create a transaction to credit the bonus amount
            let transactionData = {
              targetWalletId: wallet.id,
              targetCurrencyId: wallet.currencyId,
              amount: roundToDecimal(activeTierRollover.bonusAmount),
              transactionType: getTransactionType(activeBonus?.Bonus?.walletType, activeBonus.kind),
              targetBeforeBalance: currentWalletBalance,
              targetAfterBalance: safeAdd(currentWalletBalance, roundToDecimal(activeTierRollover.bonusAmount)),
              comments: BONUS_COMMENT_ABBREVIATIONS.TBC,
              actioneeId: userId,
              actioneeType: 'User',
              tenantId,
              transactionId: getUUIDTransactionId(),
              // errorCode: 0,
              success: true,
              status: 'success',
              // paymentMethod: 'manual'
            };
            transactionData = await v3CurrencyConversion(sequelizeTransaction, transactionData, wallet.currencyId, tenantId, activeTierRollover.bonusAmount);
            const transaction = await TransactionModel.create(transactionData, { transaction: sequelizeTransaction });

            // Update the transactionId on the model instance
            activeTierRollover.transactionId = transaction.id;
            await activeTierRollover.save({ transaction: sequelizeTransaction });
            wallet[walletTypeField] = safeAdd(currentWalletBalance, roundToDecimal(activeTierRollover.bonusAmount))
            await wallet.save({ transaction: sequelizeTransaction });

            userWallet = {
              amount: roundToDecimal(wallet.amount),
              nonCashAmount: roundToDecimal(wallet.nonCashAmount),
              oneTimeBonusAmount: roundToDecimal(wallet.oneTimeBonusAmount),
              sportsFreeBetAmount: roundToDecimal(wallet.sportsFreeBetAmount),
              userId: userId
            };
          }


          const hasMoreActiveTiers = await UserBonusRecurringRolloverModel.count({
            where: {
              userBonusId: activeBonus.id,
              status: BONUS_RECURRING_STATUS.ACTIVE,
            },
            transaction: sequelizeTransaction
          });

          // For one-time bonuses, if no more active tiers, mark the bonus as claimed
          const isRecurringBonus = depositBonusSettings.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(activeBonus?.Bonus?.kind);

          if (!isRecurringBonus && !hasMoreActiveTiers) {
            await UserBonusModel.update({
              status: BONUS_STATUS.CLAIMED,
              claimedAt: sequelize.literal('CURRENT_TIMESTAMP'),
              rolloverBalance: 0 // Set rollover to 0 as all tiers are complete
            }, {
              where: { id: activeBonus.id },
              transaction: sequelizeTransaction
            });
          }

          // there are more tiers and remaining rollover amount to deduct
          if (hasMoreActiveTiers && remainingToDeduct > 0) {
            const nextTier = await UserBonusRecurringRolloverModel.findOne({
              where: {
                userBonusId: activeBonus.id,
                status: BONUS_RECURRING_STATUS.ACTIVE,
                remainingRollover: { [Op.gt]: 0 }
              },
              order: [['createdAt', 'ASC']],
              transaction: sequelizeTransaction
            });

            if (nextTier && remainingToDeduct > 0) {
              const depositBonusQueueLogObject = {
                type: 'bonus',
                status: QUEUE_WORKER_CONSTANT.READY,
                ids: [
                  {
                    rolloverAmountToDeduct: remainingToDeduct,
                    depositBonusType: depositBonusType,
                    userId: userId,
                    tenantId,
                    bonusType: BONUS_ENGINE_BONUS_TYPE.DEPOSIT_ROLLOVER,
                    providerId: providerId,
                    seatId: seatInfo,
                    mircoService: mircoService,
                    isCasino: isCasino,
                    gameName: gameName
                  }
                ]
              };
              const queueLog = await QueueLogModel.create(depositBonusQueueLogObject, { transaction: sequelizeTransaction });
              queueLogIdsArray.push(queueLog.id);
            }
          }

        } else {
          // Just save the remaining rollover update if tier is not completed
          await activeTierRollover.save({ transaction: sequelizeTransaction });
        }
      } else {
        // Standard rollover processing (existing code)
        if (userActiveDepositBonus.Bonus.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(userActiveDepositBonus.Bonus.kind)) {
          await sequelizeTransaction.rollback()
          return null
        }

        let rolloverBalance = isQueueBonus ? roundToDecimal(activeBonus?.remainingRollover) : roundToDecimal(activeBonus?.rolloverBalance)

        // Handle rollover balance deduction and bonus claiming
        const newRolloverBalance = safeSubtract(rolloverBalance, rolloverAmountToDeduct)

        // Check if rollover is completed (less than or equal to 0, or effectively zero)
        if (newRolloverBalance <= 0 || isEffectivelyZero(newRolloverBalance)) {
          const remainingAmount = rolloverAmountToDeduct > rolloverBalance ?
            safeSubtract(rolloverAmountToDeduct, rolloverBalance) : 0

          const walletTypeField = getWalletTypeField(activeBonus?.Bonus?.walletType)

          let rollOverSetFrom = activeBonus
          if (isQueueBonus) {

            // Create new user bonus record
            const BonusDetail = await BonusModel.findOne({
              where: {
                id: activeBonus.bonusId,
              },
              attributes: ['id', 'validUpto', 'kind', 'validFrom', 'vipLevels', 'tenantId', 'promoCodes', 'currencyId', 'referType', 'referValue', 'enablePastDate', 'autoActivateBonus']
            })
            const userBonusObject = {
              userId: userId,
              status: BONUS_STATUS.CLAIMED,
              kind: BonusDetail.kind,
              bonusId: BonusDetail.id,
              expiresAt: BonusDetail.validUpto,
              rolloverBalance: 0,
              initialRolloverBalance: roundToDecimal(activeBonus.rolloverTarget),
              bonusAmount: roundToDecimal(activeBonus.bonusAmount),
            }


            await UserBonusQueueModel.update({ status: MULTI_BONUS_STATUS.COMPLETED }, { where: { id: activeBonus.id }, transaction: sequelizeTransaction })
            let newUserBonus = await UserBonusModel.create(userBonusObject, { transaction: sequelizeTransaction })

            let rollOverSet = await UserBonusModel.findOne({
              where: {
                id: newUserBonus.id,
              },
              lock: {
                level: sequelizeTransaction.LOCK.UPDATE,
                of: UserBonusModel
              },
              transaction: sequelizeTransaction
            })

            await rollOverSet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: UserBonusModel }, transaction: sequelizeTransaction })

            rollOverSetFrom = rollOverSet
          }

          if (rollOverSetFrom?.kind === BONUS_TYPES.DEPOSIT || rollOverSetFrom?.kind === BONUS_TYPES.DEPOSIT_BOTH) {

              ({ userWallet, queueLogIds } = await claimDepositBonusAmountCasino(rollOverSetFrom, userId, walletTypeField, sequelizeTransaction))
          } else if (activeBonus?.kind === BONUS_TYPES.DEPOSIT_SPORTS) {

              ({ userWallet } = await claimDepositBonusAmountSports(rollOverSetFrom, userId, walletTypeField, sequelizeTransaction))

          }

          // Handle burning bonus history if applicable
          if (depositBonusSettings?.burningDays && depositBonusSettings?.burnType === DEPOSIT_BONUS_BURN_TYPE.SINGLE_TIME_USE) {

            const burningBonusHistory = {
              userId: userId,
              bonusId: activeBonus?.Bonus?.id,
              userBonusId: activeBonus?.id,
              bonusAmount: roundToDecimal(activeBonus?.bonusAmount)
            }
            await BurningBonusModel.create(burningBonusHistory, { transaction: sequelizeTransaction })

          }

          // Handle remaining rollover amount
          if (remainingAmount > 0) {
            const userBonusRecord = await UserBonusQueueModel.findOne({
              attributes: ['id', 'bonusAmount', 'bonusId', 'bonusAmount', 'rolloverTarget', 'depositId', 'remainingRollover'],
              where: {
                userId: userId,
                bonusAmount: { [Op.ne]: null },
                status: MULTI_BONUS_STATUS.PENDING
              },
              includes: {
                model: BonusModel,
                where: {
                  kind: { [Op.in]: depositBonusType },
                },
              },
              order: [['ordering', 'ASC']],
              transaction: sequelizeTransaction
            })

            if (userBonusRecord?.bonusId) {
              const currentRemainingRollover = roundToDecimal(userBonusRecord.remainingRollover)
              const newRemainingRollover = safeSubtract(currentRemainingRollover, remainingAmount)

              if (newRemainingRollover <= 0 || isEffectivelyZero(newRemainingRollover)) {
                const BonusDetail = await BonusModel.findOne({
                  where: {
                    id: userBonusRecord.bonusId,
                  },
                  attributes: ['id', 'validUpto', 'kind', 'validFrom', 'vipLevels', 'tenantId', 'promoCodes', 'currencyId', 'referType', 'referValue', 'enablePastDate', 'autoActivateBonus']
                })

                const userBonusObject = {
                  userId: userId,
                  status: BONUS_STATUS.ACTIVE,
                  kind: BonusDetail.kind,
                  bonusId: BonusDetail.id,
                  expiresAt: BonusDetail.validUpto,
                  rolloverBalance: currentRemainingRollover,
                  initialRolloverBalance: roundToDecimal(userBonusRecord.rolloverTarget),
                  bonusAmount: roundToDecimal(userBonusRecord.bonusAmount),
                }

                await UserBonusQueueModel.update({ status: MULTI_BONUS_STATUS.COMPLETED }, { where: { id: userBonusRecord.id }, transaction: sequelizeTransaction })

                await UserBonusModel.create(userBonusObject, { transaction: sequelizeTransaction })

                const depositBonusQueueLogObject = {
                  type: 'bonus',
                  status: QUEUE_WORKER_CONSTANT.READY,
                  ids: [
                    {
                      rolloverAmountToDeduct: remainingAmount,
                      depositBonusType: depositBonusType,
                      userId: userId,
                      tenantId,
                      bonusType: BONUS_ENGINE_BONUS_TYPE.DEPOSIT_ROLLOVER,
                      providerId: providerId,
                      seatId: seatInfo,
                      mircoService: mircoService,
                      isCasino: isCasino,
                      gameName: gameName
                    }
                  ]
                }
                const queueLog = await QueueLogModel.create(depositBonusQueueLogObject, { transaction: sequelizeTransaction })
                queueLogIdsArray.push(queueLog.id)
              } else {
                await UserBonusQueueModel.update({
                  remainingRollover: newRemainingRollover
                }, { where: { id: userBonusRecord.id }, transaction: sequelizeTransaction })
              }
            }
          }
        } else {
          // Deduct rollover amount from active bonus
          if (!isQueueBonus) {
            await UserBonusModel.update(
              { rolloverBalance: newRolloverBalance },
              { where: { id: activeBonus.id }, transaction: sequelizeTransaction }
            )
          } else {
            await UserBonusQueueModel.update(
              { remainingRollover: newRolloverBalance },
              { where: { id: activeBonus.id }, transaction: sequelizeTransaction }
            )
          }
        }
      }

      await sequelizeTransaction.commit()

      // Publish updates to Redis
      if (!userWallet) return null;

      try {
        // 1. Publish user wallet balance
        await publishToRedis.publishToUserWallet({
          UserWalletBalance: {
            walletBalance: userWallet?.amount,
            userId,
            nonCashAmount: userWallet?.nonCashAmount,
            oneTimeBonusAmount: userWallet?.oneTimeBonusAmount,
            sportsFreeBetAmount: userWallet?.sportsFreeBetAmount,
            bonusAmount: roundToDecimal(activeBonus?.bonusAmount)
          }
        });

        // 2. Combine and deduplicate queue log IDs (optional)
        const allQueueIds = [...new Set([
          ...Object.values(queueLogIds),
          ...queueLogIdsArray
        ])];

        // 3. Publish all queue logs in parallel
        await Promise.all(
          allQueueIds.map(queueId =>
            publishToRedis.publishToQueueService({
              QueueLog: { queueLogId: queueId }
            })
          )
        );

      } catch (error) {
        console.error('Redis publish error:', error);
      }
    }

    // Process active deposit bonus or queue bonus
    if (userActiveDepositBonus) {
      await processBonus(userActiveDepositBonus)
    } else {
      await processBonus(userActiveDepositQueueBonus, true)
    }
  } catch (error) {
    // Rollback transaction in case of error
    await ErrorLogHelper.logError(error, null, '')
    await sequelizeTransaction.rollback()
    throw error
  }
}
