import { Sequelize } from 'sequelize'
import { BONUS_COMMENT_ABBREVIATIONS, BONUS_STATUS, DEPOSIT_BONUS_TRANSACTION_STATUS, paymentForCodes, QUEUE_WORKER_CONSTANT } from '../../common/constants'
import db from '../../db/models'
import v3CurrencyConversion from '../v3CurrencyConversion'
import { walletLocking } from '../walletLocking'

// Helper function to get wallet type field name
const getWalletTypeField = (walletTypeValue) => {
  const walletTypeFields = {
    0: 'amount',
    1: 'nonCashAmount',
    2: 'oneTimeBonusAmount',
    3: 'sportsFreeBetAmount'
  }
  return walletTypeFields[walletTypeValue] || 'amount'
}

/**
 * This function will credit the sports deposit bonus amount in the user's wallet as soon as it
 * achieve the rollover target.
 * @export
 * @param {object} userActiveDepositBonus contains userBonus object
 * @param {string} userId The ID of the user to whom the bonus will be credited.
 * @param {string} walletType The type of wallet to which the bonus will be credited.
 * @param {object} sequelizeTransaction The transaction object for ensuring atomic operations in the database.
 * @return {object} Returns an object containing the userWallet and the created QueueLog record IDs.
 */
export default async (userActiveDepositBonus, userId, walletType, sequelizeTransaction) => {
  const {
    Wallet: WalletModel,
    BetsTransaction: BetsTransactionModel,
    Transaction: TransactionModel,
    QueueLog: QueueLogModel,
    User: UserModel,
    AuditLog: AuditLogModel
  } = db

  try {
    userActiveDepositBonus.status = BONUS_STATUS.CLAIMED
    userActiveDepositBonus.rolloverBalance = 0
    userActiveDepositBonus.claimedAt = new Date().toISOString()

    const userWallet = await walletLocking({ id: userId }, sequelizeTransaction)
    await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })
    userWallet[walletType] += userActiveDepositBonus.bonusAmount
    await userWallet.save({ transaction: sequelizeTransaction })

    let user = await UserModel.findOne({
      attributes: ['userName', 'tenantId', 'parentId',
        [Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id AND wallets.owner_type = 'User')`), 'exchangeRate']
      ],
      where: {
        id: userId
      },
      raw: true
    })


    let transactionObject = {
      isDeleted: false,
      amount: 0,
      nonCashAmount: 0,
      journalEntry: 'CR',
      merchantId: null,
      reference: `${user.userName}-${new Date().toISOString()}`,
      description: BONUS_COMMENT_ABBREVIATIONS.SBC,
      userId,
      createdAt: new Date(),
      updatedAt: new Date(),
      tenantId: user?.tenantId,
      actioneeId: userId,
      conversionRate: user?.dataValues?.exchangeRate || user?.exchangeRate,
      currentBalance: (userWallet?.amount + userWallet?.nonCashAmount),
      targetCurrencyId: userWallet?.currencyId,
      targetWalletId: userWallet?.id,
      status: DEPOSIT_BONUS_TRANSACTION_STATUS.SUCCESS,
      paymentFor: paymentForCodes.DEPOSIT_BONUS_CLAIMED
    }

    // Set the appropriate amount field and balance fields based on wallet type
    switch (walletType) {
      case 'amount':
        transactionObject.amount = userActiveDepositBonus.bonusAmount
        transactionObject.targetBeforeBalance = userWallet[walletType] - userActiveDepositBonus.bonusAmount
        transactionObject.targetAfterBalance = userWallet[walletType]
        break
      case 'nonCashAmount':
        transactionObject.nonCashAmount = userActiveDepositBonus.bonusAmount
        transactionObject.targetNonCashBeforeBalance = userWallet[walletType] - userActiveDepositBonus.bonusAmount
        transactionObject.targetNonCashAfterBalance = userWallet[walletType]
        break
      case 'sportsFreeBetAmount':
        transactionObject.sportsFreebetAmount = userActiveDepositBonus.bonusAmount
        transactionObject.targetSportsFreebetBeforeBalance = userWallet[walletType] - userActiveDepositBonus.bonusAmount
        transactionObject.targetSportsFreebetAfterBalance = userWallet[walletType]
        break
      default:
        transactionObject.amount = userActiveDepositBonus.bonusAmount
        transactionObject.targetBeforeBalance = userWallet[walletType] - userActiveDepositBonus.bonusAmount
        transactionObject.targetAfterBalance = userWallet[walletType]
    }



    transactionObject = await v3CurrencyConversion(sequelizeTransaction, transactionObject, userWallet?.currencyId, user?.tenantId, userActiveDepositBonus?.bonusAmount)

    if (walletType !== 'amount') {
      transactionObject = { ...transactionObject, otherCurrencyNonCashAmount: transactionObject.otherCurrencyAmount }
      delete transactionObject.otherCurrencyAmount
    }

    let transactionDetail = await BetsTransactionModel.create(transactionObject, { transaction: sequelizeTransaction });

    if (userActiveDepositBonus?.transactionId) {
      // Fetch the bet transaction record
      const betTransactionRecord = await BetsTransactionModel.findOne({
        where: { id: userActiveDepositBonus.transactionId },
      });

      if (!betTransactionRecord) {
        throw new Error(`Bet transaction record not found for ID: ${userActiveDepositBonus.transactionId}`);
      }

      // Delete the bet transaction
      await BetsTransactionModel.destroy({
        where: { id: userActiveDepositBonus.transactionId },
        transaction: sequelizeTransaction
      });

      // Fetch the related transaction record
      const transactionRecord = await TransactionModel.findOne({
        where: { debitTransactionId: betTransactionRecord.id, tenantId: user?.tenantId }
      });

      if (!transactionRecord) {
        throw new Error(`Transaction record not found for ID: ${transactionRecord.id}`);
      }

      // Fetch the related transaction record
      await TransactionModel.destroy({
        where: { debitTransactionId: betTransactionRecord.id, tenantId: user?.tenantId },
        transaction: sequelizeTransaction
      });

      // Common audit log fields
      const auditLogBase = {
        tenantId: user?.tenantId,
        actioneeId: user?.parentId,
        eventType: 'DepositBonusClaim',
        event: 'Create',
        actioneeIp: '0.0.0.0',
        action: 'Deposit Sports Bonus Claimed',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Create audit logs
      const auditLogs = [
        {
          ...auditLogBase,
          eventId: userId,
          previousData: betTransactionRecord,
          modifiedData: transactionObject,
        },
        {
          ...auditLogBase,
          eventId: userId,
          previousData: transactionRecord,
          modifiedData: {},
        }
      ];

      const auditLogId = await AuditLogModel.bulkCreate(auditLogs, { transaction: sequelizeTransaction });

      // Prepare queue log objects
      const queueLogs = [
        {
          type: QUEUE_WORKER_CONSTANT.ELASTIC_RECORD_DELETION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [{ id: betTransactionRecord?.id, type: 'sports' }],
        },
        {
          type: QUEUE_WORKER_CONSTANT.ELASTIC_RECORD_DELETION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [{ id: transactionRecord?.id, type: 'casino' }],
        },
        {
          type: QUEUE_WORKER_CONSTANT.AUDIT_LOG,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [auditLogId[0]?.id],
        },
        {
          type: QUEUE_WORKER_CONSTANT.AUDIT_LOG,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [auditLogId[1]?.id],
        }
      ];

      // Bulk create queue log entries
      await QueueLogModel.bulkCreate(queueLogs, { transaction: sequelizeTransaction });
    }

    userActiveDepositBonus.transactionId = transactionDetail.id
    await userActiveDepositBonus.save({ transaction: sequelizeTransaction })

    const queueLogs = [
      {
        type: QUEUE_WORKER_CONSTANT.BET_TRANSACTION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [transactionDetail.id],
      },
      {
        type: QUEUE_WORKER_CONSTANT.SPORT_CASINO_TRANSACTION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [transactionDetail.id],
      }
    ];

    // Bulk create queue log entries
    const createdRecords = await QueueLogModel.bulkCreate(queueLogs, { transaction: sequelizeTransaction });

    return { userWallet }
  } catch (error) {
    throw error
  }
}
