import { Op, Sequelize } from 'sequelize'
import { burningBonusCheck } from '../../common/burningBonusCheck'
import {
  BONUS_STATUS, BONUS_TIER_CONFIG_TYPE, BONUS_TIER_TYPE, BONUS_TYPES, DEPOSIT_BONUS_ALLOWED,
  DEPOSIT_INSTANT_BONUS_TYPES
} from '../../common/constants'
import { v3DepositBonusCheck } from '../../common/v3DepositBonusCheck'
import v3DepositBonusCheckWithTiers from '../v3DepositBonusCheckWithTiers'
import db, { sequelize } from '../../db/models'

/**
 * Process the deposit bonus for a user.
 *
 * @param {Object} bonusData - The data related to the bonus.
 * @param {Object} bonusData[0].user - The user object.
 * @param {Array} bonusData[0].transactionIds - List of transaction IDs.
 * @param {String} bonusData[0].paymentProviders - Payment providers.
 * @param {String} bonusData[0].depositTransactionId - Deposit transaction ID.
 * @param {Number} bonusData[0].amount - Deposit amount.
 * @param {Number} bonusData[0].skipManualDepositCheck - If true, skips the manual deposit type check for activating the bonus.
 *
 * @returns {Promise<null>}
 */
export default async (bonusData) => {
  const [{ user, wallet, transactionIds, paymentProviders, depositTransactionId, amount, skipManualDepositCheck = false }] = bonusData
  let sequelizeTransaction
  try {
    const {
      Bonus: BonusModel,
      UserBonus: UserBonusModel,
      DepositBonusSetting: DepositBonusSettingModel,
      DepositBonusTier: DepositBonusTierModel,
      UserBonusRolloverTransaction : UserBonusRolloverTransactionModel
    } = db
    const userActiveDepositBonus = await UserBonusModel.findOne({
      where: {
        status: BONUS_STATUS.ACTIVE,
        expiresAt: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') },
        userId: user.id,
        [Op.or]: [
          {
            kind: {
              [Op.in]: [ BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_INSTANT, BONUS_TYPES.DEPOSIT_BOTH]
            },
            bonusAmount: 0,
            rolloverBalance: 0
          },
          {
            kind: {
              [Op.in]: [ BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH]
            },
            '$DepositBonusSetting.deposit_bonus_type$': DEPOSIT_INSTANT_BONUS_TYPES.RECURRING
          }
        ]
      },
      attributes: ['id', 'status', 'bonusId', 'bonusAmount', 'rolloverBalance', 'expiresAt', 'transactionId', 'initialRolloverBalance', 'kind'],
      include: [
        {
          model: DepositBonusSettingModel,
          required: false
        }
      ]
    })
    if (!userActiveDepositBonus) {
      return null
    }

    const bonus = await BonusModel.findOne({
      where: {
        id: userActiveDepositBonus.bonusId,
        enabled: true,
        [Op.or]: [
          {
            '$DepositBonusSetting.tier_config_type$': BONUS_TIER_CONFIG_TYPE.TIER_BASED,
            [Op.or]: [
              {
                '$DepositBonusSetting.tier_type$': 1,
                '$DepositBonusSetting.min_deposit$': { [Op.lte]: amount },
                '$DepositBonusSetting.max_deposit$': { [Op.gte]: amount }
              },
              {
                '$DepositBonusSetting.tier_type$': 2,
                '$DepositBonusSetting.DepositBonusTiers.min_deposit_amount$': { [Op.lte]: amount },
                '$DepositBonusSetting.DepositBonusTiers.max_deposit_amount$': { [Op.gte]: amount }
              }
            ]
          },
          {
            '$DepositBonusSetting.tier_config_type$': BONUS_TIER_CONFIG_TYPE.SEQUENCE_BASED
          }
        ]
      },
      include: {
        model: DepositBonusSettingModel,
        attributes: ['id', 'minDeposit', 'maxDeposit', 'maxBonus', 'rolloverCalculationType', 'rolloverMultiplier', 'validForDays', 'depositType', 'depositBonusType', 'recurringBonusType', 'allowedPaymentProviders', 'customDeposits', 'tierType', 'weekDay', 'burningDays', 'burnType', 'tierConfigType', 'applyToDepositSequenceGte'],
        required: true,
        include: {
          model: DepositBonusTierModel,
          required: false
        }
      },
      attributes: ['id', 'percentage', 'kind', 'walletType', 'depositBonusType']
    })

    if (!bonus) {
      // userActiveDepositBonus.status = BONUS_STATUS.EXPIRED
      // await userActiveDepositBonus.update({ status: BONUS_STATUS.EXPIRED },
      //   { where: { id: userActiveDepositBonus.id } }
      // )
      return null
    }
    // If bonus have payment gateway and both option in deposit type then only active it
    if (!skipManualDepositCheck && bonus?.DepositBonusSetting?.depositType === DEPOSIT_BONUS_ALLOWED.MANUAL) {
      return null
    }

    // if the deposit uses the specified payment gateway
    if (paymentProviders && bonus?.DepositBonusSetting?.allowedPaymentProviders && !bonus?.DepositBonusSetting?.allowedPaymentProviders?.includes(paymentProviders)) {
      return null
    }

    // checking week day for bonus if there
    if (bonus?.DepositBonusSetting?.weekDay) {
      const today = new Date()
      const weekdaysMap = { sunday: 0, monday: 1, tuesday: 2, wednesday: 3, thursday: 4, friday: 5, saturday: 6 }
      const specifiedWeekdayNum = weekdaysMap[bonus?.DepositBonusSetting?.weekDay?.toLowerCase()]
      if (today.getDay() !== specifiedWeekdayNum) return null
    }
    //checking if they have any active bonuses with burning days remaining that they haven't utilized yet. (only if this bonus has burning days)
    if (bonus?.DepositBonusSetting?.burningDays) {
      const giveable = await burningBonusCheck(user.id, user.tenantId)
      if (!giveable) return null
    }

    if (bonus.DepositBonusSetting.tierType === BONUS_TIER_TYPE.TIER_2 && Array.isArray(bonus.DepositBonusSetting.DepositBonusTiers) && bonus.DepositBonusSetting.DepositBonusTiers.length > 0) {
      const { percentage, minDepositAmount, maxDepositAmount, maxBonus } = bonus.DepositBonusSetting.DepositBonusTiers[0];
      bonus.percentage = percentage;
      bonus.DepositBonusSetting.minDeposit = minDepositAmount;
      bonus.DepositBonusSetting.maxDeposit = maxDepositAmount;
      bonus.DepositBonusSetting.maxBonus = maxBonus;
    }
    const isSequenceBased = bonus.DepositBonusSetting.tierConfigType === BONUS_TIER_CONFIG_TYPE.SEQUENCE_BASED
    // deposit amount less than min deposit amount and greater than max deposit amount check
    if (!isSequenceBased && (amount < bonus?.DepositBonusSetting?.minDeposit || amount > bonus?.DepositBonusSetting?.maxDeposit)) {
      return null
    }
     sequelizeTransaction = await sequelize.transaction()
    // const v3DepositBonus = await v3DepositBonusCheck(sequelizeTransaction, amount, user, wallet, transactionIds, depositTransactionId, bonus, userActiveDepositBonus)
    let v3DepositBonus = null;

    if (bonus?.DepositBonusSetting.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(bonus.kind)) {
      // Call v3DepositBonusCheckWithTiers if tier rollover system is enabled and bonus type is recurring
      v3DepositBonus = await v3DepositBonusCheckWithTiers(
        sequelizeTransaction,
        amount,
        user,
        wallet,
        transactionIds,
        depositTransactionId,
        bonus,
        userActiveDepositBonus
      );
    } else {
      // Use standard v3DepositBonusCheck otherwise
      v3DepositBonus = await v3DepositBonusCheck(
        sequelizeTransaction,
        amount,
        user,
        wallet,
        transactionIds,
        depositTransactionId,
        bonus,
        userActiveDepositBonus
      );
    }

    if (v3DepositBonus) {
      await UserBonusRolloverTransactionModel.create({
        bonusId: userActiveDepositBonus.bonusId,
        userId: user.id,
        depositTransactionId: depositTransactionId
      }, { transaction: sequelizeTransaction });
    }

    await sequelizeTransaction.commit()
    return v3DepositBonus
  } catch (error) {
    if (sequelizeTransaction) {
      await sequelizeTransaction.rollback()
    }
    console.log("======Deposit Bonus worker error", error)
    throw error
  }
}
