import { DEPOSIT_BONUS_BURN_TYPE, TRANSACTION_TYPES } from '../../common/constants'
import { sequelize } from '../../db/models'
import ErrorLogHelper from '../errorLog'
import { publishToRedis } from '../queueService/publishToRedis'

export default async (bonusData) => {
  if (!bonusData || !Array.isArray(bonusData) || bonusData.length < 1) return

  const [{ userId, tenantId, walletId, walletCurrencyId, walletOwnerType, nonCashCreditAmount, debitNonCashAmount }] = bonusData
  const user = { id: userId, tenantId } // for error log function
  try {
    const updatedBonusData = await sequelize.query(
      'CALL check_burning_bonus(:userId, :tenantId, :walletId, :walletCurrencyId, :walletOwnerType, :depositBonusBurnType, :nonCashCreditAmount, :debitNonCashAmount, :transactionTypes)',
      {
        replacements: {
          userId,
          tenantId,
          walletId,
          walletCurrencyId,
          walletOwnerType,
          depositBonusBurnType: JSON.stringify(DEPOSIT_BONUS_BURN_TYPE),
          nonCashCreditAmount: String(nonCashCreditAmount),
          debitNonCashAmount: String(debitNonCashAmount),
          transactionTypes: JSON.stringify(TRANSACTION_TYPES)
        }
      }
    )

    if (!updatedBonusData || !updatedBonusData.length || !updatedBonusData[0][0].o_new_transaction_id) {
      return
    }

    try {
      await publishToRedis.publishToUserWallet({
        UserWalletBalance: { walletBalance: +updatedBonusData[0][0].o_updated_wallet_data.amount, userId, nonCashAmount: +updatedBonusData[0][0].o_updated_wallet_data.non_cash_amount, bonusAmount: +updatedBonusData[0][0].o_burning_bonus_amount }
      })
      await publishToRedis.publishToQueueService({
        QueueLog: { queueLogId: +updatedBonusData[0][0].o_queue_log_id }
      })
    } catch (error) {
      await ErrorLogHelper.logError(error, null, user)
    }
  } catch (error) {
    await ErrorLogHelper.logError(error, null, user)
    throw error
  }
}
