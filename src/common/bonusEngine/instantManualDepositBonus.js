import moment from 'moment'
import { Op, Sequelize } from 'sequelize'
import { v4 as getUUIDTransactionId } from 'uuid'
import db, { sequelize } from '../../db/models'
import { BONUS_COMMENT_ABBREVIATIONS, BONUS_STATUS, DEPOSIT_BONUS_BURN_TYPE, DEPOSIT_BONUS_WALLET_TYPES, DEPOSIT_INSTANT_BONUS_TYPES, QUEUE_WORKER_CONSTANT, RECURRING_BONUS_TYPES, TRANSACTION_TYPES } from '../constants'

const getWalletTypeField = (walletTypeValue) => {
  return DEPOSIT_BONUS_WALLET_TYPES[walletTypeValue] || DEPOSIT_BONUS_WALLET_TYPES[0]
}

// Helper function to get transaction type based on wallet type
const getTransactionType = (walletTypeValue) => {
  const transactionTypes = {
    0: TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM, // amount
    1: TRANSACTION_TYPES.NON_CASH_DEPOSIT_BONUS_CLAIM, // nonCashAmount
    2: TRANSACTION_TYPES.FREE_BETS_DEPOSIT_BONUS_CLAIM, // oneTimeBonusAmount - free_bets_on_deposit_bonus_claim
    3: TRANSACTION_TYPES.SPORTS_FREE_BETS_DEPOSIT_BONUS_CLAIM // sportsFreeBetAmount - sports_free_bets_deposit_bonus_claim
  }
  return transactionTypes[walletTypeValue] || TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM
}

export const instantDepositBonusManual = async (userBonus, request, trans, sequelizeTransaction) => {
  const {
    QueueLog: QueueLogModel,
    User: UserModel,
    BurningBonus: BurningBonusModel,
    Transaction: TransactionModel,
    Wallet: WalletModel,
    InstantDepositBonusHistory: InstantDepositBonusHistoryModel
  } = db
  const today = moment().startOf('day').toDate()

  const user = await UserModel.findOne({
    attributes: ['id', 'tenantId'], where: { id: request.targetEmail }
  })

  if (userBonus.DepositBonusSetting.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING) {
    if (userBonus.DepositBonusSetting.recurringBonusType === RECURRING_BONUS_TYPES.ONCE_A_DAY) {
      const existingDepositBonus = await InstantDepositBonusHistoryModel.findOne({
        where: {
          bonusId: userBonus.Bonus.id,
          userId: user.id,
          userBonusId: userBonus.id,
          createdAt: { [Op.gte]: today },
          bonusType: DEPOSIT_INSTANT_BONUS_TYPES.RECURRING,
          recurringBonusType: RECURRING_BONUS_TYPES.ONCE_A_DAY
        }
      })
      if (existingDepositBonus) {
        return null
      }
    }

    if (userBonus.DepositBonusSetting.recurringBonusType === RECURRING_BONUS_TYPES.CUSTOM_DEPOSITS) {
      const existingDepositBonuses = await InstantDepositBonusHistoryModel.findAll({
        where: {
          bonusId: userBonus.Bonus.id,
          userId: user.id,
          userBonusId: userBonus.id,
          createdAt: { [Op.gte]: today },
          bonusType: DEPOSIT_INSTANT_BONUS_TYPES.RECURRING,
          recurringBonusType: RECURRING_BONUS_TYPES.CUSTOM_DEPOSITS
        }
      })

      if (existingDepositBonuses.length >= userBonus.DepositBonusSetting.customDeposits) {
        return null
      }
    }
  }

  const maxBonus = parseFloat(userBonus.DepositBonusSetting.maxBonus)
  let bonusAmount = (parseFloat(request.transactionAmount) * parseFloat(userBonus.Bonus.percentage)) / 100
  bonusAmount = bonusAmount <= maxBonus ? bonusAmount : maxBonus

  const userWallet = await WalletModel.findOne({
    where: { ownerType: 'User', ownerId: user.id },
    transaction: sequelizeTransaction,
    lock: {
      level: sequelizeTransaction.LOCK.UPDATE,
      of: WalletModel
    },
    skipLocked: false
  })

  await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })

  const walletTypeField = getWalletTypeField(userBonus.Bonus.walletType)
  const targetBeforeBalance = parseFloat(userWallet[walletTypeField])
  const targetAfterBalance = targetBeforeBalance + bonusAmount

  // Update wallet
  userWallet[walletTypeField] = targetAfterBalance

  await userWallet.save({ transaction: sequelizeTransaction })

  const amountInCurrency = await sequelize.query('SELECT v3_currency_conversion AS otherconversions FROM v3_currency_conversion(:currencyId, :tenantId, :amount)', {
    replacements: {
      currencyId: userWallet.currencyId, tenantId: user.tenantId, amount: bonusAmount
    }, type: Sequelize.QueryTypes.SELECT
  })

  const transactionData = {
    actioneeType: 'User',
    actioneeId: user.id,
    targetCurrencyId: userWallet.currencyId,
    conversionRate: trans.conversionRate,
    targetWalletId: userWallet.id,
    targetBeforeBalance: targetBeforeBalance,
    targetAfterBalance: targetAfterBalance,
    amount: bonusAmount,
    status: 'success',
    tenantId: user.tenantId,
    createdAt: new Date(),
    comments: BONUS_COMMENT_ABBREVIATIONS.IBC,
    transactionType: getTransactionType(userBonus.Bonus.walletType),
    paymentMethod: 'manual',
    transactionId: getUUIDTransactionId(),
    otherCurrencyAmount: JSON.stringify(amountInCurrency[0].otherconversions)
  }

  const createdTransaction = await TransactionModel.create(transactionData, { transaction: sequelizeTransaction })
  const transactionId = createdTransaction.id

  await QueueLogModel.create({
    type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION, ids: [transactionId], status: QUEUE_WORKER_CONSTANT.READY
  }, { transaction: sequelizeTransaction })

  const bonusHistory = await InstantDepositBonusHistoryModel.create({
    bonusId: userBonus.Bonus.id,
    userId: user.id,
    userBonusId: userBonus.id,
    bonusTransactionId: transactionId,
    depositTransactionId: trans.id,
    depositAmount: request.transactionAmount,
    bonusAmount: bonusAmount,
    bonusType: userBonus.DepositBonusSetting.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.INSTANT ? DEPOSIT_INSTANT_BONUS_TYPES.INSTANT : DEPOSIT_INSTANT_BONUS_TYPES.RECURRING,
    recurringBonusType: userBonus.DepositBonusSetting.recurringBonusType
  }, { transaction: sequelizeTransaction })

  if (userBonus.DepositBonusSetting.burningDays && userBonus.DepositBonusSetting.burnType === DEPOSIT_BONUS_BURN_TYPE.SINGLE_TIME_USE) {
    await BurningBonusModel.create({
      userId: user.id,
      bonusId: userBonus.Bonus.id,
      userBonusId: userBonus.id,
      instantBonusId: bonusHistory.id,
      bonusAmount: bonusAmount
    }, { transaction: sequelizeTransaction })
  }

  userBonus.transactionId = transactionId
  userBonus.claimedAt = moment().toDate()

  if (userBonus.DepositBonusSetting.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.INSTANT) {
    userBonus.status = BONUS_STATUS.CLAIMED
  }

  await userBonus.save({ transaction: sequelizeTransaction })

  return true
}
