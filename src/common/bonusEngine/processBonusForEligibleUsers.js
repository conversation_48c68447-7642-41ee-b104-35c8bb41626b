import { Sequelize } from 'sequelize';
import { ALLOWED_PARALLEL_BONUS, ALLOWED_PERMISSIONS, AUTO_ACTIVATION_TYPE, BONUS_TYPES, DEPOSIT_INSTANT_BONUS_TYPES, QUEUE_WORKER_CONSTANT } from '../../common/constants';
import db, { sequelize } from '../../db/models';
import { convertTimeToUTC } from '../../utils/common';
import { MULTI_BONUS_STATUS } from '../../utils/constants/constant';

export default async (bonus, sequelizeTransaction, allowedModules, userId = [], timezone, type, skipKycCheck = false, depositTransactionId = null) => {
  const {
    User: UserModel,
    UserBonus: UserBonusModel,
    Wallet: WalletModel,
    UserPromoCodeBonus: UserPromoCodeBonusModel,
    UserBonusQueue: UserBonusQueueModel,
    BotUser: BotUserModel,
    Transaction : TransactionModel,
    QueueLog : QueueLogModel
  } = db;

  // Check if the bonus type is 'queue' and if the user has the required permissions
  if(type == AUTO_ACTIVATION_TYPE.QUEUE && !allowedModules?.includes(ALLOWED_PERMISSIONS.MULTI_BONUS_ALLOWANCE) ){
    await UserBonusQueueModel.update(
      { status: MULTI_BONUS_STATUS.FAILED },
      {
        where: {
          userId: { [Sequelize.Op.in]: userId },
          bonusId: bonus.id,
          status: MULTI_BONUS_STATUS.PENDING
        },
        transaction: sequelizeTransaction
      }
    );
    return sequelizeTransaction
  }

  const today = bonus.enablePastDate ? bonus.validFrom : convertTimeToUTC('', timezone);
  const vipLevelArray = bonus.vipLevels;
  const bonusCategories = bonus?.bonusCategory?.map(c => c.category) ?? [];

  const subQueryConditions = {
    status: 'active',
    expiresAt: { [Sequelize.Op.gte]: today },
  };

  // Check if the tenant has the playerCategory module enabled
  const hasPlayerCategory = allowedModules
    ? allowedModules.split(',').map(module => module.trim()).includes(ALLOWED_PERMISSIONS.ENABLE_PLAYER_CATEGORY)
    : false;

  // Adjust subQueryConditions based on allowedModules
  if (allowedModules && allowedModules.includes(ALLOWED_PARALLEL_BONUS)) {
    subQueryConditions.kind = [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_BOTH, BONUS_TYPES.LOSING_SPORT].includes(bonus.kind)
      ? { [Sequelize.Op.in]: [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_BOTH, BONUS_TYPES.LOSING_SPORT] }
      : { [Sequelize.Op.in]: [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_BOTH, BONUS_TYPES.DEPOSIT_INSTANT, BONUS_TYPES.DEPOSIT_SPORTS] };
  }

  const promoCodeJoinCondition = bonus.promoCodes ? { promoCodeId: { [Sequelize.Op.in]: bonus.promoCodes } } : null;

  // Promo or referral condition
  const promoOrReferralConditions = [];

  if (bonus.promoCodes) {
    promoOrReferralConditions.push({
      '$UserPromoCodeBonus.promo_code_id$': { [Sequelize.Op.in]: bonus.promoCodes }
    });
  }

  if (bonus.referType === 'affiliate_code') {
    promoOrReferralConditions.push({
      affiliatedData: bonus.referValue || { [Sequelize.Op.ne]: null }
    });
  } else if (bonus.referType === 'referral_link' && bonus.referValue) {
    promoOrReferralConditions.push(
      Sequelize.literal(`
      EXISTS (
        SELECT 1 FROM admin_users
        WHERE affiliate_token = regexp_replace('${bonus.referValue}', '^.*/', '')
        AND admin_users.id = "User"."parent_id"
      )
    `)
    );
  }

  const searchWhereCondition = {
    active: true,
    ...((!skipKycCheck && type !== AUTO_ACTIVATION_TYPE.QUEUE) ? { kycDone: true } : {}),
    tenantId: bonus.tenantId,
    vipLevel: { [Sequelize.Op.in]: vipLevelArray },
    // Conditionally add categoryType filter only if bonusCategories has values
    ...(hasPlayerCategory  && bonusCategories.length > 0 ? { categoryType: { [Sequelize.Op.in]: bonusCategories } } : {}),
    [Sequelize.Op.and]: [
      Sequelize.literal(`"BotUser"."id" IS NULL`),
      {
        [Sequelize.Op.or]: [
          { '$UserBonus.bonus_id$': { [Sequelize.Op.is]: null } },
          { '$UserBonus.bonus_id$': { [Sequelize.Op.ne]: bonus.id } }
        ]
      },
      ...(promoOrReferralConditions.length > 0 ? [{
        [Sequelize.Op.or]: promoOrReferralConditions
      }] : [])
    ]
  };

  if (userId.length > 0) {
    searchWhereCondition.id = { [Sequelize.Op.in]: userId };
  }

  const usersQuery = {
    attributes: [
      ['id', 'userId'],
      'categoryType',
      'tenantId',
      Sequelize.literal(`'active' as status`),
      Sequelize.literal(`'${bonus.kind}' as kind`),
      Sequelize.literal(`${bonus.id} as bonus_id`),
      Sequelize.literal(`'${bonus.validUpto}' as expires_at`),
      Sequelize.literal(`'${today}' as created_at`),
      Sequelize.literal(`'${today}' as updated_at`),
    ],
    include: [
      {
        model: WalletModel,
        attributes: ['id', 'currencyId'],
        where: { currencyId: bonus.currencyId },
      },
      {
        model: UserBonusModel,
        required: false,
        attributes: ['bonus_id'],
        where: { [Sequelize.Op.and]: subQueryConditions },
      },
      {
        model: BotUserModel,
        required: false,
        attributes: ['id']
      },
      // Include the UserPromoCodeBonusModel to filter by promo code
      ...(promoCodeJoinCondition ? [{
        model: UserPromoCodeBonusModel,
        as: 'UserPromoCodeBonus',
        required: false,
        attributes: [],
      }] : [])
    ],
    where: searchWhereCondition
  };

  let eligibleUsers = await UserModel.findAll(usersQuery);
  if (!eligibleUsers || eligibleUsers.length === 0) {
    return sequelizeTransaction;
  }

  const noUserIds = type === AUTO_ACTIVATION_TYPE.QUEUE ? userId.filter(id => !eligibleUsers.some(user => user.dataValues.userId == id)) : [];

  const userBonusQueueData = await UserBonusQueueModel.findAll({
    where: {
      bonusId: bonus.id,
      status: MULTI_BONUS_STATUS.PENDING,
      userId: { [Sequelize.Op.in]: userId }
    },
    attributes: ['userId', 'bonusAmount', 'rolloverTarget', 'remainingRollover']
  });

  const userBonusQueueMap = new Map(userBonusQueueData.map(queue => [queue.userId, queue]));

  const { userBonusData, queueBonus } = eligibleUsers.reduce((acc, user) => {
    const userData = {
      userId: user?.dataValues?.userId,
      status: 'active',
      kind: bonus.kind,
      bonusId: bonus.id,
      expiresAt: bonus.validUpto,
      createdAt: today,
      updatedAt: today,
    };

    const userBonusQueue = userBonusQueueMap.get(user.dataValues.userId);
    if (userBonusQueue) {
      userData.bonusAmount = userBonusQueue.bonusAmount || 0;
      userData.initialRolloverBalance = userBonusQueue.rolloverTarget || 0;
      userData.rolloverBalance = userBonusQueue.remainingRollover || 0;
    }

    if (user.dataValues.UserBonus.length === 0) {
      acc.userBonusData.push(userData);
    } else {
      acc.queueBonus.push({
        userId: user?.dataValues?.userId,
        bonusId: bonus.id,
        status: MULTI_BONUS_STATUS.PENDING,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    return acc;
  }, { userBonusData: [], queueBonus: [] });

  // Update UserBonusQueueModel for users who are not eligible
  if (noUserIds.length && type === AUTO_ACTIVATION_TYPE.QUEUE) {
    await UserBonusQueueModel.update(
      { status: MULTI_BONUS_STATUS.FAILED },
      {
        where: {
          userId: { [Sequelize.Op.in]: noUserIds },
          bonusId: bonus.id,
          status: MULTI_BONUS_STATUS.PENDING
        },
        transaction: sequelizeTransaction
      }
    );
  }
  const existingRecords = userBonusData?.map(user => user.userId)

  // Update UserBonusQueueModel for users who are eligible
  if (existingRecords?.length && type === AUTO_ACTIVATION_TYPE.QUEUE) {
    await UserBonusQueueModel.update(
      { status:  MULTI_BONUS_STATUS.COMPLETED },
      {
        where: {
          userId: { [Sequelize.Op.in]: existingRecords },
          bonusId: bonus.id,
          status: MULTI_BONUS_STATUS.PENDING
        },
        transaction: sequelizeTransaction
      }
    );
  }

  // Insert eligible users into UserBonusModel
  if (userBonusData.length > 0) {
    await UserBonusModel.bulkCreate(userBonusData, {
      transaction: sequelizeTransaction,
      ignoreDuplicates: true,
    });
  }


  // This indicates that the Admin assigned a bonus to a specific user and selected one of the deposit requests to set the rollover.
  if (depositTransactionId) {
    const transaction = await TransactionModel.findOne({
      where: { id: depositTransactionId },
      attributes: ['amount', 'paymentProviderId'],
      raw: true
    });

    if (transaction && eligibleUsers.length > 0) {
      const userInfo = eligibleUsers[0];

      const userDetails = {
        id: userInfo?.dataValues?.userId,
        tenantId: userInfo?.dataValues?.tenantId,
        Wallet: {
          id: userInfo?.Wallet?.id,
          currencyId: userInfo?.Wallet?.currencyId
        }
      };

      const depositBonusQueueLogObject = {
        type: 'bonus',
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [
          {
            amount: transaction.amount,
            user: userDetails,
            wallet: null,
            transactionIds: [],
            depositTransactionId,
            paymentProviders: transaction?.paymentProviderId,
            bonusType: BONUS_TYPES.DEPOSIT,
            skipManualDepositCheck: true
          }
        ]
      };

      await QueueLogModel.create(depositBonusQueueLogObject, { transaction: sequelizeTransaction });
    }
  }

  // Insert queue bonus data if applicable
  if (queueBonus.length > 0 && type !== AUTO_ACTIVATION_TYPE.QUEUE && (allowedModules && allowedModules.includes(ALLOWED_PERMISSIONS.MULTI_BONUS_ALLOWANCE) )) {

    // Check for instant bonuses in the queue
    const instantBonusQueueUsers = await sequelize.query(`
      SELECT "UserBonusQueue"."user_id" AS "userId"
      FROM "user_bonus_queue" AS "UserBonusQueue"
      JOIN "bonus" AS "Bonus" ON "UserBonusQueue"."bonus_id" = "Bonus"."id"
      WHERE "UserBonusQueue"."status" = 0
        AND "UserBonusQueue"."user_id" IN (:userId)
        AND "UserBonusQueue"."bonus_amount" IS NULL
        AND "Bonus"."deposit_bonus_type" = :depositBonusType;
    `, {
      replacements: {
        userId: queueBonus.map(user => user.userId),
        depositBonusType: DEPOSIT_INSTANT_BONUS_TYPES.RECURRING
       },
      type: Sequelize.QueryTypes.SELECT,
    });



    const instantBonusQueueUserIds = instantBonusQueueUsers.map(user => user.userId);

    // Filter out users with active or queued instant bonuses
    const filteredQueueBonus = queueBonus.filter(user => !instantBonusQueueUserIds.includes(user.userId));

    const existingQueueUsers = await UserBonusQueueModel.findAll({
      attributes: ['userId'],
      where: {
        bonusId: bonus.id,
        status:  MULTI_BONUS_STATUS.PENDING,
        userId: { [Sequelize.Op.in]: filteredQueueBonus.map(user => user.userId) }
      }
    });

    const newQueueBonus = filteredQueueBonus.filter(user => !existingQueueUsers.some(queueUser => queueUser.userId === user.userId));

    await UserBonusQueueModel.bulkCreate(newQueueBonus, { transaction: sequelizeTransaction });
  }

  return sequelizeTransaction
};
