import { Op } from 'sequelize';
import { DEPOSIT_REQUEST_STATUS, TENANT_SETTINGS_TYPE } from '../../common/constants';
import db, { sequelize } from '../../db/models';
import { filterValidMultipliers } from '../../utils/common';
import ErrorLogHelper from '../../common/errorLog';

export default async (payload) => {

  const {
    DepositRequest: DepositRequestModel,
    DepositWager: DepositWagerModel,
    DepositWagerLinkage: DepositWagerLinkageModel,
    TenantSetting: TenantSettingModel,
    User: UserModel
  } = db;

  let sequelizeTransaction = null, userEntity = null;

  try {
    const depositRequestId = payload.transactionId;

    const depositRequest = await DepositRequestModel.findOne({
      attributes: ['id', 'userId', 'tenantId', 'amount'],
      where: {
        id: depositRequestId,
        status: DEPOSIT_REQUEST_STATUS.COMPLETED
      },
      raw: true,
    });

    if (!depositRequest) {
      throw Object.assign(new Error('DepositRequest not found for Id'), { notFound: true });
    }

    userEntity = await UserModel.findOne({
      attributes: ['vipLevel', 'wagerMultiplier', 'parentType', 'tenantId'],
      where: {
        id: depositRequest.userId,
      },
      raw: true,
    });

    // Fetch global and VIP level wager multipliers
    const tenantSettings = await TenantSettingModel.findAll({
      where: {
        tenantId: depositRequest.tenantId,
        [Op.or]: [
          { type: TENANT_SETTINGS_TYPE.GLOBAL_WAGER_MULTIPLIER },
          { type: TENANT_SETTINGS_TYPE.VIP_LEVEL_WAGER_MULTIPLIER, key: userEntity?.vipLevel?.toString() },
        ],
      },
      attributes: ['key', 'value', 'type'],
      raw: true,
    });

    // Map the settings for easier access
    const settingsMap = tenantSettings.reduce((map, setting) => {
      map[setting.type] = setting.value;
      return map;
    }, {});

    let wagerMultiplier = 1;
    const multipliers = filterValidMultipliers([
      userEntity.wagerMultiplier,
      settingsMap[TENANT_SETTINGS_TYPE.VIP_LEVEL_WAGER_MULTIPLIER],
      settingsMap[TENANT_SETTINGS_TYPE.GLOBAL_WAGER_MULTIPLIER],
    ]);

    // Step 1: Check if any multiplier (User, VIP, global) is zero
    if (multipliers.includes(0)) {
      wagerMultiplier = 0;
    } else {
      // Step 2: Determine the wager multiplier priority
      if (userEntity.wagerMultiplier) {
        wagerMultiplier = Number(userEntity.wagerMultiplier);
      }
      else if (settingsMap[TENANT_SETTINGS_TYPE.VIP_LEVEL_WAGER_MULTIPLIER]) {
        wagerMultiplier = Number(settingsMap[TENANT_SETTINGS_TYPE.VIP_LEVEL_WAGER_MULTIPLIER]);
      }
      else if (settingsMap[TENANT_SETTINGS_TYPE.GLOBAL_WAGER_MULTIPLIER]) {
        wagerMultiplier = Number(settingsMap[TENANT_SETTINGS_TYPE.GLOBAL_WAGER_MULTIPLIER]);
      }
    }

    sequelizeTransaction = await sequelize.transaction();

    if (wagerMultiplier) {
      const depositWagerTracking = await DepositWagerModel.findOne({
        where: {
          userId: depositRequest.userId,
          tenantId: depositRequest.tenantId,
          wageringCompleted: false,
        },
      });

      // Handle existing `depositWagerTracking`
      let depositWagerId = null;
      if (depositWagerTracking) {
        depositWagerTracking.wagerAmount += depositRequest.amount * wagerMultiplier;
        await depositWagerTracking.save({ transaction: sequelizeTransaction });
        depositWagerId = depositWagerTracking.id;
      } else {
        const newDepositWager = await DepositWagerModel.create({
          userId: depositRequest.userId,
          tenantId: depositRequest.tenantId,
          wagerAmount: depositRequest.amount * wagerMultiplier,
          wageringCompleted: false,
          wagerAchieved: 0,
        }, { transaction: sequelizeTransaction });

        depositWagerId = newDepositWager.id;
      }

      // Create the linkage
      await DepositWagerLinkageModel.create({ depositWagerId, depositId: depositRequest.id }, { transaction: sequelizeTransaction });
    } else {
      await DepositWagerModel.update({ wageringCompleted: true },
        {
          where: { userId: depositRequest.userId, tenantId: depositRequest.tenantId, wageringCompleted: false }
        },
        { transaction: sequelizeTransaction }
      );
    }

    // Commit the transaction
    await sequelizeTransaction.commit();

    return { message: 'Success' };

  } catch (error) {

    await ErrorLogHelper.logError(error, null, userEntity)

    if (sequelizeTransaction) {
      await sequelizeTransaction.rollback();
    }

    // If the error is not due to a "not found" condition, rethrow the error
    if (!error.notFound) {
      throw error;
    }
  }
};
