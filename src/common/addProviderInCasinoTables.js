import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import db, { sequelize } from '../db/models';

export default async () => {
  try {
    // Update casino tables records which does not have casino items records.
    const updateCasinoTablesWithoutChildRecordQuery = `
      update casino_tables as ct
      set provider_id = q2.casino_provider_id
      from (
        select
          q1.id as id, min(cg.casino_provider_id) as casino_provider_id
        from (
          select ct.id as id, ct.game_id as game_id
          from casino_tables ct
            left join casino_items ci on ct.table_id = ci.uuid
          where ci.id is null
        ) q1 left join casino_games cg on q1.game_id = cg.game_id
        group by q1.id
      ) q2
      where ct.id = q2.id
    `;
    await sequelize.query(updateCasinoTablesWithoutChildRecordQuery);

    await new Promise(resolve => setTimeout(resolve, 250));

    // Update casino tables records.
    const selectCasinoTablesRecordsToUpdateProvider = `
      select
        ct.id as ct_id, ct.game_id as game_id, ci.uuid as uuid, ct.name as table_name, cg.casino_provider_id as provider_id
      from casino_tables ct
        join casino_items ci on ct.table_id = ci.uuid
        join casino_games cg on ct.game_id = cg.game_id
      where
        cg.casino_provider_id = ci.provider::bigint
    `;
    const result = await sequelize.query(selectCasinoTablesRecordsToUpdateProvider, {
      type: sequelize.QueryTypes.SELECT
    });

    let i = 0;
    for (const record of result) {
      i++;

      // Find casino table record for game_id, uuid and provider.
      let gameTableExists = await db.CasinoTable.findOne({
        raw: true,
        where: { gameId: record.game_id, tableId: record.uuid, providerId: record.provider_id },
        attributes: ['id', 'name', ['table_id', 'tableId'], ['provider_id', 'providerId']],
      });

      if (!gameTableExists) {
        let gameTable = await db.CasinoTable.findOne({
          where: { gameId: record.game_id, tableId: record.uuid, providerId: { [Op.is]: null } },
          attributes: ['id', 'name', ['table_id', 'tableId'], ['provider_id', 'providerId']],
        });

        if (!gameTable) {
          gameTable = await db.CasinoTable.create({
            name: record.table_name,
            gameId: record.game_id,
            tableId: record.uuid,
            providerId: record.provider_id,
            isLobby: true
          });
        } else {
          gameTable.providerId = record.provider_id;
          gameTable = await gameTable.save();
        }
      }

      if (i % 25 === 0) await new Promise(resolve => setTimeout(resolve, 100));
    }

    return { message: 'Data updated successfully!', status: StatusCodes.OK }
  } catch (error) {
    return { message: error.message, status: StatusCodes.INTERNAL_SERVER_ERROR }
  }
}
