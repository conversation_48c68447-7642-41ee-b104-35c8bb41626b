import moment from 'moment'
import db, { sequelize } from '../db/models'
import <PERSON><PERSON>rLogHelper from './errorLog'
import { CRON_LOG_STATUS } from '../common/constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      raw: true,
      attributes: ['id'],
      where: {
        service: 'update_latest_winners_cron',
        status: 1
      }
    })
    if (queueProcessStatus) {
      cronLog.cronId = queueProcessStatus?.id
      const startDate = moment().subtract(2, 'hours').toISOString(); // Last 3 hours data
      const endDate = moment().toISOString();

      /*
        Flow:
          If there are new entries available to enter for the tenant in latest_winners table then remove old entries for the tenant and insert new entries.
          If there is no new entry available to enter in latest_winners table then don't remove old entries.
      */

      const updateLatestWinnersQuery = `
        -- Step-1: Insert the new records in latest_winners.
        INSERT INTO latest_winners (
          game_id, table_id, table_name, image, bet_count,
          user_id, username, email, winning_amount, other_currency_amount,
          tenant_id, provider_id, provider_name,
          created_at, updated_at
        )

        WITH most_played_games AS (
          -- Query to get top 20 games based on bet count rank tenant wise.
          SELECT
            game_id,
            table_id,
            tenant_id,
            provider_id,
            bet_count
          FROM (
            -- Query to assign the rank to the game based on bet count tenant wise.
            SELECT
              game_id,
              table_id,
              tenant_id,
              provider_id,
              bet_count,
              row_number() OVER (PARTITION BY tenant_id ORDER BY bet_count DESC) AS game_bet_rank
            FROM
            (
              -- Query to find the bet count of games tenant wise.
              -- Query-1: Provider not in ('Spribe', 'st8', 'Darwin')
              SELECT
                ts.game_id AS game_id,
                ts.table_id::varchar AS table_id,
                ts.tenant_id AS tenant_id,
                ts.provider_id AS provider_id,
                count(ts.id) AS bet_count
              FROM
                transactions ts
              WHERE
                ts.status = 'success'
                AND ts.actionee_type = 'User'
                AND ts.transaction_type IN (0,8)
                AND ts.created_at BETWEEN :start_date AND :end_date
                AND (ts.seat_id Not IN ('sap_lobby','sbs_sportsbook','bti_sportsbook') OR ts.seat_id is null)
                AND ts.provider_id IN (SELECT id FROM casino_providers WHERE name NOT IN ('Spribe', 'st8', 'Darwin'))
                AND ts.game_id IS NOT NULL
                AND ts.table_id IS NOT NULL
              GROUP BY
                ts.tenant_id, ts.provider_id, ts.game_id, ts.table_id
              HAVING count(ts.id) > 0

              UNION ALL

              -- Query-2: Provider = 'Spribe'
              SELECT
                ts.game_id AS game_id,
                ts.game_id AS table_id,
                ts.tenant_id AS tenant_id,
                (SELECT id FROM casino_providers WHERE name = 'Spribe') AS provider_id,
                count(ts.id) AS bet_count
              FROM
                transactions ts
              WHERE
                ts.status = 'success'
                AND ts.actionee_type = 'User'
                AND ts.transaction_type IN (0,8)
                AND ts.created_at BETWEEN :start_date AND :end_date
                AND (ts.seat_id Not IN ('sap_lobby','sbs_sportsbook','bti_sportsbook') OR ts.seat_id is null)
                AND ts.provider_id IN (SELECT id FROM casino_providers WHERE name = 'Spribe')
                AND ts.game_id IS NOT NULL
              GROUP BY
                ts.tenant_id, ts.game_id
              HAVING count(ts.id) > 0

              UNION ALL

              -- Query-3: Provider in ('st8', 'Darwin')
              SELECT
                ts.game_id AS game_id,
                ts.seat_id AS table_id,
                ts.tenant_id AS tenant_id,
                ts.provider_id AS provider_id,
                count(ts.id) AS bet_count
              FROM
                transactions ts
              WHERE
                ts.status = 'success'
                AND ts.actionee_type = 'User'
                AND ts.transaction_type IN (0,8)
                AND ts.created_at BETWEEN :start_date AND :end_date
                AND (ts.seat_id Not IN ('sap_lobby','sbs_sportsbook','bti_sportsbook') OR ts.seat_id is null)
                AND ts.provider_id IN (SELECT id FROM casino_providers WHERE name IN ('st8', 'Darwin'))
                AND ts.game_id IS NOT NULL
                AND ts.seat_id IS NOT NULL
              GROUP BY
                ts.tenant_id, ts.provider_id, ts.game_id, ts.seat_id
              HAVING count(ts.id) > 0

            ) tenant_game_bet_count
          ) tenant_game_bet_count_number
          WHERE
            game_bet_rank <= 20
        )
        , top_games_winner_rank AS (
          -- Query to assign winner rank to top games.
          SELECT
            ts.game_id AS game_id,
            mpg.table_id AS table_id,
            ci.name AS table_name,
            ci.image AS image,
            mpg.bet_count AS bet_count,
            u.id AS user_id,
            u.user_name AS username,
            u.email AS email,
            ts.amount AS winning_amount,
            ts.other_currency_amount AS other_currency_amount,
            ts.tenant_id AS tenant_id,
            ts.provider_id AS provider_id,
            cp.name AS provider_name,
            row_number() OVER (PARTITION BY ts.tenant_id, ts.provider_id, ts.game_id, mpg.table_id ORDER BY ts.amount DESC) AS winner_rank
          FROM
            transactions ts
            JOIN most_played_games mpg ON (
              ts.game_id = mpg.game_id
              AND ts.tenant_id = mpg.tenant_id
              AND ts.provider_id = mpg.provider_id
              AND (
                (ts.provider_id IN (SELECT id FROM casino_providers WHERE name = 'Spribe') AND ts.game_id = mpg.table_id)
                OR (ts.provider_id IN (SELECT id FROM casino_providers WHERE name = 'st8') AND ts.seat_id = mpg.table_id)
                OR (ts.table_id::varchar = mpg.table_id)
              )
            )
            JOIN casino_items ci ON (
              ts.tenant_id = ci.tenant_id AND ci.uuid = mpg.table_id AND ci.provider = ts.provider_id::varchar
            )
            JOIN users u ON (ts.actionee_id = u.id)
            JOIN casino_providers cp ON (ts.provider_id = cp.id)

          WHERE
            ts.status = 'success'
            AND ts.actionee_type = 'User'
            AND ts.transaction_type IN (1,9)
            AND ts.created_at BETWEEN :start_date AND :end_date
            AND (ts.seat_id Not IN ('sap_lobby','sbs_sportsbook','bti_sportsbook') OR ts.seat_id is null)
            AND ts.amount > 0
        )
        -- Query to get players with games data whose winner rank is 1.
        SELECT
          game_id,
          table_id,
          table_name,
          image,
          bet_count,
          user_id,
          username,
          email,
          winning_amount,
          other_currency_amount,
          tenant_id,
          provider_id,
          provider_name,
          now() as created_at,
          now() as updated_at
        FROM top_games_winner_rank
        WHERE winner_rank = 1
        ORDER BY tenant_id, bet_count DESC

        ON CONFLICT (tenant_id, provider_id, game_id, table_id) DO UPDATE
        SET
          table_name = EXCLUDED.table_name,
          image = EXCLUDED.image,
          bet_count = EXCLUDED.bet_count,
          user_id = EXCLUDED.user_id,
          username = EXCLUDED.username,
          email = EXCLUDED.email,
          winning_amount = EXCLUDED.winning_amount,
          other_currency_amount = EXCLUDED.other_currency_amount,
          provider_name = EXCLUDED.provider_name,
          updated_at = now();

        -- Step-2: Delete records which are having position greater than 20.
        WITH games_rank AS (
          SELECT
            id,
            ROW_NUMBER() OVER (PARTITION BY tenant_id ORDER BY updated_at DESC, bet_count DESC) AS game_rank
          FROM
            latest_winners
        )
        DELETE FROM latest_winners WHERE id IN (
          SELECT id FROM games_rank WHERE game_rank > 20
        );
      `;

      const transaction = await sequelize.transaction();

      try {
        await sequelize.query(updateLatestWinnersQuery, {
          replacements: { start_date: startDate, end_date: endDate },
          transaction
        });

        await transaction.commit();

      } catch (error) {
        await transaction.rollback();
        throw error;
      }

    }
  } catch (error) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = error.message || null
    await ErrorLogHelper.logError(error, null, null)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
