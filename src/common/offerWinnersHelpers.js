
import momentTimezone from 'moment-timezone';
import { OFFER_FREQUENCY } from './constants';

export const calculateStartDate = (offer, timezone, dateFormat) => {
  const { frequency, validFrom, dayOfWeek } = offer;
  const targetDayOfWeek = dayOfWeek || 1; // Default to Monday (1)
  const utcTime = momentTimezone.utc(validFrom).tz(timezone).format('YYYY-MM-DD HH:mm:ss');;
  const validFromMoment = momentTimezone.utc(utcTime).tz(timezone); // Convert validFrom to the target timezone
  const yesterdayStart = momentTimezone().tz(timezone).subtract(1, 'day').startOf('day') // Yesterday's start in the target timezone
  const lastWeek = momentTimezone().tz(timezone).subtract(1, 'week').startOf('day').isoWeekday(targetDayOfWeek); // Start of last week
  const lastMonth = momentTimezone().tz(timezone).subtract(1, 'month').subtract(1, 'day').startOf('day'); // Start of last month

  switch (frequency) {
    case OFFER_FREQUENCY.DAILY: {
      // If `validFrom` matches the calculated date (ignoring time), use `validFrom` time
      return yesterdayStart.format(dateFormat);
    }

    case OFFER_FREQUENCY.WEEKLY: {
      // Check if `validFrom` is within the last 7 days or the same day as `lastWeek`
      return momentTimezone()
        .tz(timezone)
        .startOf('day')
        .diff(validFromMoment, 'days') < 7 || validFromMoment.isSame(lastWeek, 'day')
        ? validFromMoment.format(dateFormat) // Use validAt if it's within the last week
        : lastWeek.add(1, 'day').format(dateFormat); // Otherwise, adjust by 1 day
    }

    case OFFER_FREQUENCY.MONTHLY: {
      const validFromDate = validFromMoment.format(dateFormat); // Format validFrom for comparison
      const lastMonthDate = lastMonth.format(dateFormat); // Format last month's date for comparison

      // Check if the calculated date matches `validFrom` (ignoring time)
      return validFromDate === lastMonthDate
        ? validFromMoment.format(dateFormat)
        : lastMonth.add(1, 'day').format(dateFormat); // Adjust by 1 day if necessary
    }

    case OFFER_FREQUENCY.END_OF_CAMPAIGN_PERIOD: {
      return validFromMoment.format(dateFormat);
    }

    default:
      throw new Error('Invalid frequency specified');
  }
};

export const jumbleFakeWinners = (fetchedResults, offer) => {
  let fakeWinners = offer?.OfferFakeWinners || [];

  // Create a map of fake winners grouped by position
  const resultsByPosition = fakeWinners.reduce((acc, result) => {
    if (!acc[result.position]) acc[result.position] = [];
    acc[result.position].push(result);
    return acc;
  }, {});

  // Track the final results
  const finalResults = [...fetchedResults]; // Clone fetchedResults to avoid mutation

  // Process each position in the map
  Object.entries(resultsByPosition).forEach(([position, positionResults]) => {
    const pos = parseInt(position, 10); // Convert position to a number

    // Ensure there's a real winner at the specified position
    if (pos <= finalResults.length) {
      let selectedFakeWinner;

      // Pick a fake winner for this position
      if (positionResults.length === 1) {
        selectedFakeWinner = positionResults[0].dataValues;
      } else {
        const randomIndex = Math.floor(Math.random() * positionResults.length);
        selectedFakeWinner = positionResults[randomIndex].dataValues;
      }

      // Get the real winner at the current position
      const realWinner = finalResults[pos - 1];

      // Calculate the difference value based on the fake winner's position
      const diffValue =
        pos === 1
          ? parseFloat(realWinner.winner_value)
          : parseFloat(finalResults[pos - 2].winner_value) - parseFloat(realWinner.winner_value);

      // Update the fake winner's value and mark it as fake
      selectedFakeWinner.winner_value = parseFloat(
        (Math.abs(diffValue) * (selectedFakeWinner.percentage / 100) + parseFloat(realWinner.winner_value)).toFixed(2)
      );
      selectedFakeWinner.is_fake_user = true;

      // Replace the real winner with the fake winner, and step down the real winner
      finalResults.splice(pos - 1, 0, selectedFakeWinner);
    }
  });

  // Trim the results to match total fake + real winners that can be placed
  return finalResults
};

export const assignPrizes = (finalWinners, offer) => {
  // Ensure prizes are sorted by their id in ascending order
  const sortedPrizes = [...offer.prizes].sort((a, b) => a.id - b.id);

  let remainingWinners = 0; // Tracks the index of the next winner to assign a prize

  // Iterate through each prize in the sorted prizes
  sortedPrizes.forEach(prize => {
    const { title, prizesCount } = prize;

    // Assign the current prize title to the specified number of winners
    for (let i = 0; i < prizesCount; i++) {
      if (remainingWinners < finalWinners.length) {
        finalWinners[remainingWinners].prize_title = title; // Assign prize title
        remainingWinners++;
      }
    }
  });

  // Filter out winners without a prize_title
  const filteredWinners = finalWinners.filter(winner => winner.prize_title !== undefined);

  return filteredWinners;
};
