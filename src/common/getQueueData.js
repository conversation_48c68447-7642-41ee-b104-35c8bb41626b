import { Op } from 'sequelize'
import { SMARTICO_EVENT_TYPES } from '../common/constants'
import db, { sequelize } from '../db/models'
import { PUSH_IN_QUEUE_CRON, PUSH_IN_QUEUE_PAGE_COUNT, QUEUE_TYPE, TRANSACTION_TYPE } from '../utils/constants/constant'
import pushInQueue from './pushInQueue'

export default async () => {

  if (PUSH_IN_QUEUE_CRON) {
    const {count} = await db.QueueLog.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      ],
      where: {
        [Op.or]: [
          { status: 0 },
          { status: 3 }
        ],
        ids: {
          [Op.ne]: []
        },
        type: {
          [Op.notIn]: [TRANSACTION_TYPE.CREATE_CSV, TRANSACTION_TYPE.USER_TRANSACTION, TRANSACTION_TYPE.CASINO_TRANSACTION, TRANSACTION_TYPE.BET_TRANSACTION, TRANSACTION_TYPE.SPORT_CASINO_TRANSACTION, TRANSACTION_TYPE.AUDIT_LOG, TRANSACTION_TYPE.BULK_DEPOSIT_WITHDRAW, TRANSACTION_TYPE.PLAYER_COMMISION, TRANSACTION_TYPE.BONUS,
            QUEUE_TYPE.BET_PLACED,
            SMARTICO_EVENT_TYPES.UPDATE_USER,
            SMARTICO_EVENT_TYPES.WITHDRAWAL_REQUSTED,
            SMARTICO_EVENT_TYPES.WITHDRAWAL_APPROVED,
            SMARTICO_EVENT_TYPES.WITHDRAWAL_CANCELLED,
            SMARTICO_EVENT_TYPES.DEPOSIT_APPROVED,
            SMARTICO_EVENT_TYPES.CASINO_WIN,
            SMARTICO_EVENT_TYPES.LOGIN_STATS,
            SMARTICO_EVENT_TYPES.WALLET_UPDATE,
            SMARTICO_EVENT_TYPES.WALLET_UPDATE_BULK,
            TRANSACTION_TYPE.ROLLBACK_TRANSACTION,
            TRANSACTION_TYPE.EXPORT_CSV
          ]
        }
      },
      raw: true
    })

    const pages = Math.ceil(count / PUSH_IN_QUEUE_PAGE_COUNT)

    for (let offset = 0; offset< pages; offset++){
        console.log('==========start=========')
        console.log('==========count=========',count)
        console.log('==========pages=========', pages)
        console.log('==========offset=========', offset)
        console.log('==========end=========')
      const queueLog = await db.QueueLog.findAll({
        attributes: ['id'],
        where: {
          [Op.or]: [
            { status: 0 },
            { status: 3 }
          ],
          ids: {
            [Op.ne]: []
          },
          type: {
            [Op.notIn]: [TRANSACTION_TYPE.CREATE_CSV, TRANSACTION_TYPE.USER_TRANSACTION, TRANSACTION_TYPE.CASINO_TRANSACTION, TRANSACTION_TYPE.BET_TRANSACTION, TRANSACTION_TYPE.SPORT_CASINO_TRANSACTION, TRANSACTION_TYPE.AUDIT_LOG, TRANSACTION_TYPE.BULK_DEPOSIT_WITHDRAW, TRANSACTION_TYPE.PLAYER_COMMISION, TRANSACTION_TYPE.BONUS,
              QUEUE_TYPE.BET_PLACED,
              SMARTICO_EVENT_TYPES.UPDATE_USER,
              SMARTICO_EVENT_TYPES.WITHDRAWAL_REQUSTED,
              SMARTICO_EVENT_TYPES.WITHDRAWAL_APPROVED,
              SMARTICO_EVENT_TYPES.WITHDRAWAL_CANCELLED,
              SMARTICO_EVENT_TYPES.DEPOSIT_APPROVED,
              SMARTICO_EVENT_TYPES.CASINO_WIN,
              SMARTICO_EVENT_TYPES.LOGIN_STATS,
              SMARTICO_EVENT_TYPES.WALLET_UPDATE,
              SMARTICO_EVENT_TYPES.WALLET_UPDATE_BULK
            ]
          }
        },
        limit: PUSH_IN_QUEUE_PAGE_COUNT,
        offset:offset,
        order: [
          [ 'id', 'ASC' ]
        ],
        raw: true,
        useMaster: true
      })
      if (queueLog) {
        const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
        for (const queue of queueLog) {
          try {
            await delay(50)
            await pushInQueue(queue.id)
          } catch (error) {
            console.log('==========error=========', error)
            await db.QueueLog.update(
              { status: 3 },
              { where: { id: queue.id } }
            )
          }
        }
      }
    }
  }
}
