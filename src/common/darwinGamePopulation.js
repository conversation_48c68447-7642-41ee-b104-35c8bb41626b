import axios from 'axios'
import { Op, QueryTypes, Sequelize } from 'sequelize'
import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import Logger from '../libs/logger'
import { CASINO_MENU_IMAGES, CRON_LOG_STATUS, DARWIN_ICON, DISABLED_DARWIN_GAMES, PROD_DARWIN_PROVIDER, STAGE_DARWIN_PROVIDER } from './constants'
import { getCasinoProvider } from './getCasinoProvider'
import syncOrOverrideCasinoProvider from './syncOrOverrideCasinoProvider'

export default async (reqBody = null) => {

    const cronLog = {}
    cronLog.startTime = new Date()
    try {
      const {
        TenantCredential: TenantCredentialModel,
        CasinoGame: CasinoGameModel,
        CasinoTable: CasinoTableModel,
        CasinoItem: CasinoItemModel,
        Page: PageModel,
        CasinoMenu: CasinoMenuModel,
        PageMenu: PageMenuModel,
        MenuItem: MenuItemModel
      } = db

      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'darwin_game_populate',
          status: 1
        },
        attributes: ['id']
      })
      if (!queueProcessStatus) {
        return {
          success: false
        }
      }

      cronLog.cronId = queueProcessStatus?.id

      const casinoProviderId = config.get('env') === 'production' ? PROD_DARWIN_PROVIDER : STAGE_DARWIN_PROVIDER

      let tenantIds = await sequelize.query(`
          SELECT "tenant_id" AS "tenantId", ( SELECT "id" FROM "menu_tenant_setting" WHERE "menu_id" = (SELECT "id" FROM "menu_master" WHERE "name" = 'Casino')
      AND "tenant_id" = "TenantThemeSetting"."tenant_id" ) AS "casinoId",
        ( SELECT "id" FROM "menu_tenant_setting" WHERE "menu_id" = (SELECT "id" FROM "menu_master" WHERE "name" = 'Crash')
      AND "tenant_id" = "TenantThemeSetting"."tenant_id" ) AS "crashId"
      FROM "public"."tenant_theme_settings" AS "TenantThemeSetting"
      WHERE '${casinoProviderId}' = ANY (string_to_array("TenantThemeSetting"."assigned_providers", ','))`,
      { type: QueryTypes.SELECT, useMaster: false })

      const superAdminMenuId = await sequelize.query(`
          SELECT "id" FROM "menu_master" WHERE "name" = 'Casino'`,
      { type: QueryTypes.SELECT, useMaster: false })

      const superAdminMenuIdCrash = await sequelize.query(`
          SELECT "id" FROM "menu_master" WHERE "name" = 'Crash'`,
      { type: QueryTypes.SELECT, useMaster: false })
      let override_other_providers = reqBody?.override_other_providers
      if(Array.isArray(reqBody?.tenantIds) && !reqBody?.tenantIds.includes('0')){
        tenantIds = tenantIds.filter(tenant => reqBody.tenantIds.includes(tenant.tenantId));
      }
      tenantIds = [{ tenantId: 0, casinoId: superAdminMenuId[0].id, crashId: superAdminMenuIdCrash[0].id }, ...tenantIds]
      for (let i = 0; i < tenantIds.length; i++) {
        const sequelizeTransaction = await sequelize.transaction()


        const tenantId = tenantIds[i].tenantId
        const topCasinoId = tenantIds[i].casinoId
        const topCrashId = tenantIds[i]?.crashId
        try {
        const creds = await TenantCredentialModel.findAll({
          attributes: ['key', 'value'],
          where: {
            key: ['DARWIN_GAME_LIST_URL'],
            tenantId
          },
          raw: true
        })

        // Getting data from the credentials table
        const {
          DARWIN_GAME_LIST_URL
        } = creds.reduce((acc, cur) => {
          return {
            ...acc,
            [cur.key]: cur.value
          }
        }, { DARWIN_GAME_LIST_URL: null })

        const currentDate = new Date().toISOString()
        let darwinGameData = await axios({
            url: `${DARWIN_GAME_LIST_URL}`,
            method: 'get',
            maxBodyLength: Infinity,
            headers: {
              'Content-Type': 'application/json'
            }
          })

        if (darwinGameData?.data === null) {
          cronLog.status = CRON_LOG_STATUS.FAILED
          cronLog.errorMsg =(darwinGameData?.error || 'No game list found' ) + ' ' + "TenantId: " + tenantId
          cronLog.endTime = new Date()
          await db.CronLog.create(cronLog)
          continue
        }

        // Data for a page model
        const pageData = {
          title: 'Darwin',
          enabled: true,
          order: null,
          createdAt: currentDate,
          updatedAt: currentDate,
          tenantId,
          topMenuId: topCasinoId,
          enableInstantGame: null,
          image: DARWIN_ICON
        }


        /**
        * Dataobject that returns the followin object
        *
        * @Object category @table casino_games
        * @object casinoTable @table casino_table
        * @object tableItems @table casino_item
        * */

        const darwinGameDataObject = darwinGameData?.data?.reduce(
          (acc, cur) => {
            if (!acc.category.length || !acc.category.includes(cur.type)) {
              acc.category.push(cur.type)
            }

            acc.casinoTable.push({
              name: cur.name,
              gameId: `${cur.type}`,
              createdAt: currentDate,
              updatedAt: currentDate,
              isLobby: true,
              tableId: '' + cur.identifier,
              providerId: casinoProviderId
            })

            acc.tableIds.push('' + cur.identifier)
            acc.name.push(cur.name)
            acc.tableItems.push({
              uuid: '' + cur.identifier,
              name: cur.name,
              image: `provider-images/darwin/thumbnail/${cur.identifier}.webp`,
              provider: casinoProviderId,
              active: DISABLED_DARWIN_GAMES.includes(cur.identifier) ? false : cur.available,
              featured: true,
              createdAt: currentDate,
              updatedAt: currentDate,
              tenantId
            })

            return acc
          }, { category: [], casinoTable: [], tableIds: [], tableItems: [], name: [], menuItem: [] })

          let pageDataCrash = null
          if (darwinGameDataObject.category.includes('Crash') && topCrashId) {
            pageDataCrash = {
              title: 'Darwin',
              enabled: true,
              order: null,
              createdAt: currentDate,
              updatedAt: currentDate,
              tenantId,
              topMenuId: topCrashId,
              enableInstantGame: null,
              image: DARWIN_ICON
            }
          }

          const crashGamesDataObject = darwinGameData?.data?.reduce(
            (acc, cur) => {
            if (cur.type === 'Crash') {
              if (!acc.category.length || !acc.category.includes(cur.type)) {
                acc.category.push(cur.type)
              }
              acc.casinoTable.push({
                name: cur.name,
                gameId: `${cur.type}`,
                createdAt: currentDate,
                updatedAt: currentDate,
                isLobby: true,
                tableId: '' + cur.identifier,
                providerId: casinoProviderId
              })
              acc.tableIds.push('' + cur.identifier)
              acc.name.push(cur.name)
              acc.tableItems.push({
                uuid: '' + cur.identifier,
                name: cur.name,
                image: `provider-images/darwin/thumbnail/${cur.identifier}.webp`,
                provider: casinoProviderId,
                active: DISABLED_DARWIN_GAMES.includes(cur.identifier) ? false : cur.available,
                featured: true,
                createdAt: currentDate,
                updatedAt: currentDate,
                tenantId
              })
            }
            return acc
          }, { category: [], casinoTable: [], tableIds: [], tableItems: [], name: [], menuItem: [] })

        // Object to be updated in casino games, if it does'nt already exists
        const dataToBeUpdatedIntoCasinoGames = darwinGameDataObject.category.map((item, idx) => {
          return {
            name: `${item}`,
            casinoProviderId,
            createdAt: currentDate,
            updatedAt: currentDate,
            gameId: `${item}`
          }
        })

        // Object to create or update into casino menu specific to a tenant
        const casinoMenuDataToBeUpdated = darwinGameDataObject.category.map((item, idx) => {
          return {
            name: `${item}`,
            menucategory: null,
            menuOrder: null,
            enabled: true,
            createdAt: currentDate,
            updatedAt: currentDate,
            tenantId,
            imageUrl: (CASINO_MENU_IMAGES.find((iurl) => iurl.name === item)) ? CASINO_MENU_IMAGES.find((iurl) => iurl.name === item).url : DARWIN_ICON
          }
        })

        // get existing data in CasinoGame table
        const casinoGamesData = await CasinoGameModel.findAll({
          attributes: ['gameId', 'id', 'name'],
          where: {
            gameId: {
              [Op.in]: dataToBeUpdatedIntoCasinoGames.map(item => item.gameId)
            },
            casinoProviderId: casinoProviderId
          },
          raw: true,
        })

        /**
         * @object name: casinoTableData
         * @gets the already existed data in the
         * @table CasinoTableModel
         */

        const casinoTableData = await CasinoTableModel.findAll({
          attributes: ['gameId', 'tableId', 'name', 'id'],
          where: {
            gameId: {
              [Op.in]: darwinGameDataObject.category.map(item => `${item}`)
            },
            providerId: casinoProviderId
          },
          raw: true,

        })


          let pagesData = await getCasinoProvider(pageData.title, casinoProviderId, tenantId, topCasinoId, sequelizeTransaction, currentDate, {}, DARWIN_ICON, override_other_providers);
          let pagesDataCrash = await getCasinoProvider(pageData.title, casinoProviderId, tenantId, topCrashId, sequelizeTransaction, currentDate, {}, DARWIN_ICON, override_other_providers);

        /**
       * @object casinoItemData
       * @gets the data from table
       * @casinoItemModel
       */
        const casinoItemData = await CasinoItemModel.findAll({
          attributes: ['uuid', 'name', 'id', 'active'],
          where: {
            uuid: {
              [Op.in]: darwinGameDataObject.tableIds
            },
            tenantId,
            provider: casinoProviderId.toString()
          },
          raw: true,
        })

        /**
         * @object casinoMenuData
         * gets the data from table
         * @casinoMenu
         */

        const namesToCheck = casinoMenuDataToBeUpdated.map(i => `'${i.name.toLowerCase()}'`).join(',')

        const casinoMenuData = await CasinoMenuModel.findAll({
          attributes: ['name', 'id'],
          where: {
            tenantId,
            name: Sequelize.literal(`LOWER("name") IN (${namesToCheck})`)
          },
          raw: true,
        })

        // check update the casinoGamesData object responsible to maintain category based on new data
        const updatedDataToBeUpdatedIntoCasinoGames = dataToBeUpdatedIntoCasinoGames.filter(i => !casinoGamesData.map(i => i.gameId).includes(i.gameId))

        // update the casinoTableData object based on data from casinoTable table
        const updatedCasinoTableData = darwinGameDataObject.casinoTable.filter(i => !casinoTableData.map(i => i.tableId).includes(i.tableId))

        // update casinoItemData object based on data from casinoItem table
        const updatedCasinoItemData = darwinGameDataObject.tableItems.filter(i => !casinoItemData.map(i => i.uuid).includes(i.uuid))

        // existing data with diffrent active status from casinoItem table
        const existingCasinoItemData = casinoItemData.filter(i => darwinGameDataObject.tableItems.some(item => item.uuid === i.uuid && item.active !== i.active))

        const existingCasinoItemsNameToUpdate = darwinGameDataObject.tableItems.filter(item => {
          const existingItem = casinoItemData.find(i => i.uuid === item.uuid)
          return existingItem && existingItem.name !== item.name
        }).map(item => {
          return {
            ...item,
            name: item.name
          }
        })
        // update the name of CasinoGameModel
        await Promise.all(casinoGamesData.map(async item => {
          const fromgame = dataToBeUpdatedIntoCasinoGames.find((i) => (i.gameId === item.gameId))
          if (fromgame && fromgame.name !== item.name) {
            await CasinoGameModel.update({ name: fromgame.name }, { where: { id: item.id }, transaction: sequelizeTransaction })
            item.name = fromgame.name
          }

        }))

        // update the name of CasinoTableModel
        await Promise.all(casinoTableData.map(async item => {
          const fromgame = darwinGameDataObject.casinoTable.find((i) => (i.tableId === item.tableId))
          if (fromgame && fromgame.name !== item.name) {
            await CasinoTableModel.update({ name: fromgame.name }, { where: { id: item.id }, transaction: sequelizeTransaction })
            item.name = fromgame.name
          }
        }))

        await Promise.all(existingCasinoItemData.map(async item => {
          await CasinoItemModel.update({ active: !item.active }, { where: { id: item.id }, transaction: sequelizeTransaction })
          await MenuItemModel.update({ active: !item.active }, { where: { casinoItemId: item.id }, transaction: sequelizeTransaction })
        }));

        for (const item of casinoItemData) {
          const fromgame = existingCasinoItemData.find((i) => (i.uuid === item.uuid))
          if (fromgame) {
            item.active = !fromgame.active
          }
        }

        for (const item of existingCasinoItemsNameToUpdate) {
          await CasinoItemModel.update({ name: item.name }, {
            where: { uuid: item.uuid },
            provider: casinoProviderId.toString(),
            transaction: sequelizeTransaction
          })
          await CasinoTableModel.update({ name: item.name }, {
            where: { tableId: item.uuid },
            transaction: sequelizeTransaction,
            providerId: casinoProviderId
          })
          const fromgame = casinoItemData.find((i) => (i.id === item.id))
          if (fromgame) {
            fromgame.name = item.name
          }

          const fromgameTable = casinoTableData.find((i) => (i.tableId === item.uuid))
          if (fromgameTable) {
            fromgameTable.name = item.name
          }
        }
        // if pagesData doesnt exists then create the pageData
        // const updatedPageData = pagesData.length ? pagesData : pageData
        // const updatedPageDataCrash = pagesDataCrash.length ? pagesDataCrash : pageDataCrash

        // const updatedCasinoMenuData = casinoMenuData.length? casinoMenuData: casinoMenuDataToBeUpdated
        const updatedCasinoMenuData = casinoMenuDataToBeUpdated.filter(i => !casinoMenuData.map(i => i.name.toLowerCase()).includes(i.name.toLowerCase()))

        // insert updated casino game data to table
        const insertedCasinoGamesData = await CasinoGameModel.bulkCreate(updatedDataToBeUpdatedIntoCasinoGames, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoGamesData: ${insertedCasinoGamesData}`)

        const insertedCasinoTableData = await CasinoTableModel.bulkCreate(updatedCasinoTableData, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoTableData: ${insertedCasinoTableData}`)

        const insertedCasinoItemData = await CasinoItemModel.bulkCreate(updatedCasinoItemData, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoItemData: ${insertedCasinoItemData}`)

        // const insertedPageData = !updatedPageData.length && await PageModel.create(updatedPageData, { transaction: sequelizeTransaction })
        // const insertedPageDataCrash = !updatedPageDataCrash.length && await PageModel.create(updatedPageDataCrash, { transaction: sequelizeTransaction })

        // Logger.info(`insertedPageData: ${insertedPageData}`)

        const insertedCasinoMenuData = await CasinoMenuModel.bulkCreate(updatedCasinoMenuData, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoMenuData: ${insertedCasinoMenuData}`)

        const casinoGames = [
          ...casinoGamesData,
          ...insertedCasinoGamesData.map(item => item.dataValues)
        ]
        const pageId = pagesData.id
        const pageIdCrash = pagesDataCrash.id

        const casinoMenus = [
          ...casinoMenuData,
          ...insertedCasinoMenuData.map(item => item.dataValues)
        ]

        const casinoTable = [
          ...casinoTableData,
          ...insertedCasinoTableData.map(item => item.dataValues)
        ]

        const pageMenuDataToBeInserted = darwinGameDataObject.category.map((item, idx) => {
          return {
            pageId,
            casinoMenuId: casinoMenus.find(itemm => itemm.name.toLowerCase() === item.toLowerCase()).id,
            name: `${item}`,
            menuOrder: null,
            createdAt: currentDate,
            updatedAt: currentDate
          }
        })

        const pageMenuDataToBeInsertedCrash = crashGamesDataObject.category.map((item, idx) => {
          return {
            pageId: pageIdCrash,
            casinoMenuId: casinoMenus.find(item => item.name === 'Crash').id,
            name: `${item}`,
            menuOrder: null,
            createdAt: currentDate,
            updatedAt: currentDate
          }
        })

        const pageMenuData = await PageMenuModel.findAll({
          attributes: ['id', 'pageId', 'casinoMenuId', 'name'],
          where: {
            pageId,
            name: {
              [Op.in]: pageMenuDataToBeInserted.map(i => i.name)
            }

          },
          raw: true,
        })
        const pageMenuDataCrash = await PageMenuModel.findAll({
          attributes: ['id', 'pageId', 'casinoMenuId', 'name'],
          where: {
            pageId: pageIdCrash,
            name: {
              [Op.in]: pageMenuDataToBeInsertedCrash.map(i => i.name)
            }
          },
          raw: true,
        })

        const updatedCasinoPageMenuData = pageMenuDataToBeInserted.filter(i => !pageMenuData.map(i => i.name).includes(i.name)).filter(i => i.name !== 'Crash')

        const updatedCasinoPageMenuDataCrash = pageMenuDataToBeInsertedCrash.filter(i => !pageMenuDataCrash.map(i => i.name).includes(i.name))

        const insertedCasinoPageMenuData = (updatedCasinoPageMenuData.length && await PageMenuModel.bulkCreate(updatedCasinoPageMenuData, { transaction: sequelizeTransaction })) || []
        const insertedCasinoPageMenuDataCrash = (updatedCasinoPageMenuDataCrash.length && await PageMenuModel.bulkCreate(updatedCasinoPageMenuDataCrash, { transaction: sequelizeTransaction })) || []

        const casinoItem = [
          ...casinoItemData,
          ...insertedCasinoItemData.map(item => item.dataValues)
        ]

        const pageMenu = [
          ...pageMenuData,
          ...insertedCasinoPageMenuData.map(item => item.dataValues)
        ]

        const pageMenuCrash = [
          ...pageMenuDataCrash,
          ...insertedCasinoPageMenuDataCrash.map(item => item.dataValues)
        ]

        const menuItemsDataToBeInserted = casinoTable.reduce((acc, table) => {
          const matchingCasinoItem = casinoItem.find(item =>
            item.uuid === table.tableId && table.gameId !== 'Crash'
          )
          if (matchingCasinoItem) {
            const gameId = table.gameId
            const gameName = casinoGames.find(i => i.gameId === gameId).name
            const casinoMenuIds = casinoMenus.filter(i => i.name.toLowerCase() === gameName.toLowerCase())
            const mappingCasinoMenuId = casinoMenuIds.map(i => i.id)
            const pmId = casinoMenuIds.length ? pageMenu.find(i => mappingCasinoMenuId.includes(i.casinoMenuId))?.id : null
            if(!pmId) {
              return acc
            }
            acc.push({
              pageMenuId: pmId,
              casinoItemId: matchingCasinoItem.id,
              name: table.name,
              order: null,
              active: matchingCasinoItem.active,
              featured: true,
              createdAt: currentDate,
              updatedAt: currentDate,
              popular: true
            })
          }
          return acc
        }, [])

        const menuItemsDataToBeInsertedCrash = casinoTable.reduce((acc, table) => {
          const matchingCasinoItem = casinoItem.find(item =>
            item.uuid === table.tableId && table.gameId === 'Crash'
          )

          if (matchingCasinoItem) {
            const casinoMenuId = casinoMenus.find(i => i.name === 'Crash').id
            const pmId = pageMenuCrash.find(i => i.casinoMenuId === casinoMenuId).id

            acc.push({
              pageMenuId: pmId,
              casinoItemId: matchingCasinoItem.id,
              name: table.name,
              order: null,
              active: matchingCasinoItem.active,
              featured: true,
              createdAt: currentDate,
              updatedAt: currentDate,
              popular: true
            })
          }

          return acc
        }, [])

        Logger.info(`menuItemsDataToBeInserted: ${menuItemsDataToBeInserted}`)
        Logger.info(`menuItemsDataToBeInsertedCrash: ${menuItemsDataToBeInsertedCrash}`)



        const menuItemData = await MenuItemModel.findAll({
          attributes: ['id', 'pageMenuId', 'casinoItemId', 'name', 'active'],
          where: {
            casinoItemId: {
              [Op.in]: casinoItemData.map(i => i.id)
            }
          },
          raw: true,
          transaction: sequelizeTransaction
        })

        const menuItemsNameUpdation = casinoItemData.filter(item => {
          const existingItem = menuItemData.find(i => i.casinoItemId === item.id)
          return existingItem && (existingItem.name !== item.name)
        }).map(item => {
          return {
            ...item,
            name: item.name,
          }
        })

        for (const item of menuItemsNameUpdation) {
          await MenuItemModel.update({ name: item.name }, {
            where: { casinoItemId: item.id },
            transaction: sequelizeTransaction
          })
        }

        const updatedMenuItemsData = menuItemsDataToBeInserted.filter(i =>
          !menuItemData.some(ii => ii.casinoItemId === i.casinoItemId && ii.pageMenuId === i.pageMenuId)
        )

        const updatedMenuItemsDataCrash = menuItemsDataToBeInsertedCrash.filter(i =>
          !menuItemData.some(ii => ii.casinoItemId === i.casinoItemId && ii.pageMenuId === i.pageMenuId)
        )

        const insertedMenuItemData = (updatedMenuItemsData.length && await MenuItemModel.bulkCreate(updatedMenuItemsData, { transaction: sequelizeTransaction })) || []
        const insertedMenuItemDataCrash = (updatedMenuItemsDataCrash.length && await MenuItemModel.bulkCreate(updatedMenuItemsDataCrash, { transaction: sequelizeTransaction })) || []

        Logger.info(`insertedMenuItemData: ${insertedMenuItemData}`)
        Logger.info(`insertedMenuItemDataCrash: ${insertedMenuItemDataCrash}`)

        const menuItemList = [
          ...menuItemData,
          ...insertedMenuItemData.map(item => item.dataValues),
          ...insertedMenuItemDataCrash.map(item => item.dataValues)
        ]

        let noLongerExists = []
        menuItemList.forEach(item => {
          const fromgame = casinoItem.find((i) => (i.id === item.casinoItemId))
          const findObj = darwinGameDataObject.casinoTable.find((i) => (i.name === fromgame.name || i.tableId === fromgame.uuid))

          const gameId = findObj?.gameId
          const gameName = casinoGames.find(i => i.gameId === gameId)?.name
          const casinoMenuIds = casinoMenus.filter(i => i.name.toLowerCase() === gameName.toLowerCase())
          const mappingCasinoMenuId = casinoMenuIds.map(i => i.id)
          let pmId
          if (findObj?.gameId !== 'Crash') {
            pmId = mappingCasinoMenuId?.length ? pageMenu.find(i => mappingCasinoMenuId.includes(i.casinoMenuId))?.id : null
          } else {
            pmId = mappingCasinoMenuId?.length ? pageMenuCrash.find(i => mappingCasinoMenuId.includes(i.casinoMenuId))?.id : null
          }
          if (item.pageMenuId !== pmId && pmId) {
            const checkMenuItem = menuItemList.find(i => i.casinoItemId === item.casinoItemId && i.pageMenuId !== pmId)
            if (checkMenuItem) {
              noLongerExists.push(item)
            }
          }
        })

          if (noLongerExists.length) {
            let menuListToBeDeleted = await sequelize.query(`
            SELECT "MenuItem".id
            FROM "public"."menu_items" AS "MenuItem"
            JOIN public.page_menus on "MenuItem".page_menu_id = page_menus.id
            WHERE "MenuItem"."id" IN (:menuids) and page_id in (:pageIds)`, {
              replacements: {
                menuids: noLongerExists.map(item => item.id),
                pageIds: [pageId, pageIdCrash]
              },
              type: QueryTypes.SELECT,
              transaction: sequelizeTransaction
            })

            let allPageMenu = noLongerExists.map(item => item.pageMenuId)
            let allAvailablePageMenu = await PageMenuModel.findAll({
              attributes: ['id'],
              where: {
                id: {
                  [Op.in]: allPageMenu
                }
              },
              raw: true
            })
            let allAvailablePageMenuIds = allAvailablePageMenu.map(item => item.id)
            let allPageMenuIds = allPageMenu.filter(item => !allAvailablePageMenuIds.includes(item))
            let allPageMenuIdsToBeDeleted = noLongerExists.filter(item => allPageMenuIds.includes(item.pageMenuId))
            menuListToBeDeleted.push(...allPageMenuIdsToBeDeleted)

            // only delete the menu items that are belonging to the current page
            if (menuListToBeDeleted.length) {
              await MenuItemModel.destroy({
                where: {
                  id: {
                    [Op.in]: menuListToBeDeleted.map(item => item.id)
                  },
                  casinoItemId: {
                    [Op.in]: casinoItem.map(item => item.id)
                  }

                },
                transaction: sequelizeTransaction
              })
            }
          }

        // disable the category if all games in that category are inactive
        // const categoriesToDisable = await sequelize.query(
        //   ` UPDATE casino_menus cm
        //   SET enabled = subquery.result
        //   FROM (
        //     SELECT
        //       ct.game_id AS game_category,
        //       CASE
        //         WHEN COUNT(*) = COUNT(CASE WHEN ci.active = false THEN 1 END) THEN false
        //         ELSE true
        //       END AS result
        //     FROM
        //       casino_tables ct
        //     JOIN
        //       casino_items ci ON ct.table_id = ci.uuid
        //     WHERE
        //       ci.provider = '${casinoProviderId}'
        //       AND ci.tenant_id = '${tenantId}' AND ct.provider_id = '${casinoProviderId}'
        //     GROUP BY
        //       ct.game_id
        //   ) AS subquery
        //   WHERE cm.name = subquery.game_category AND cm.tenant_id = '${tenantId}';
        // `, { transaction: sequelizeTransaction }
        // )
        await sequelizeTransaction.commit()

      } catch (error) {
        cronLog.status = CRON_LOG_STATUS.FAILED
        cronLog.errorMsg = error.message + ' TenantId: ' + tenantId
        cronLog.endTime = new Date()
        await db.CronLog.create(cronLog)
        Logger.info(error, '========DARWIN game population error======')
        await sequelizeTransaction.rollback()
        continue
      }
      }

      if (reqBody?.override_other_providers || reqBody?.sync) {
        await syncOrOverrideCasinoProvider(tenantIds, casinoProviderId, 'Darwin', reqBody?.override_other_providers, reqBody?.sync)
      }

      cronLog.endTime = new Date()
      cronLog.errorMsg = null
      cronLog.status = CRON_LOG_STATUS.SUCCESS
      await db.CronLog.create(cronLog)
      return {
        success: true
      }
    } catch (error) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message || null
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      Logger.info(error, '========DARWIN game population error======')
      return {
        success: false,
        Error: {
          stack: error.stack
        }
      }
    }
}
