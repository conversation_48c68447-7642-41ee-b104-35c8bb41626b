import { Sequelize } from 'sequelize'
import db from '../db/models'
import { v4 as uuidv4 } from 'uuid'
import config from '../configs/app.config'
import { s3 } from '../libs/awsS3Config'
import moment from 'moment'
import serialize from 'serialize-javascript'
const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')
const JSZip = require('jszip')

export default async (params = '') => {
  try {

    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'delete_request_response_logs_cron',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      let startDate, endDate
      if (params) {
        startDate = `${params.startDate} 00:00:00.0000`
        endDate = params.endDate
      } else {
        startDate = moment()
        endDate = moment()
        endDate = startDate.subtract(15, "days").format("YYYY-MM-DD")
        startDate = `${endDate} 00:00:00`
      }
      endDate = `${endDate} 23:59:59.9999`
      const tenantDetails = await db.RequestResponseLogs.findAll({
        where: {
          createdAt: Sequelize.literal(`created_at < (CURRENT_DATE - INTERVAL'14 day') :: TIMESTAMPTZ`)
        },
        attributes: [[Sequelize.fn('DISTINCT', Sequelize.col('tenant_id')) ,'tenantId']],
      })
      if (tenantDetails.length > 0) {

        for (const tenant of tenantDetails) {
          let limit = 1000
          let offset = 0
          const { count, rows } = await db.RequestResponseLogs.findAndCountAll({
            where: {
              tenantId: tenant.tenantId,
              createdAt: Sequelize.literal(`created_at < (CURRENT_DATE - INTERVAL'14 day') :: TIMESTAMPTZ`)
            },
            offset: offset,
            limit: limit,
          })

          const uuid = uuidv4().replace(/-/g, '')
          const filePath = `/tmp/request_response_log_${uuid}.csv`
          const csvWriter = createCsvWriter({
            path: filePath,
            header: [
              { id: 'id', title: 'ID' },
              { id: 'request_json', title: 'Request Json' },
              { id: 'response_json', title: 'Response Json' },
              { id: 'service', title: 'Service' },
              { id: 'url', title: 'Url' },
              { id: 'tenant_id', title: 'Tenant Id' },
              { id: 'response_code', title: 'Response Code' },
              { id: 'response_status', title: 'Response Status' },
              { id: 'error_code', title: 'Error Code' },
              { id: 'created_at', title: 'Created At' },
              { id: 'updated_at', title: 'Updated At' }
            ]
          });
          const csvData = rows

          let mainArr = []
          if (csvData.length > 0) {
            for (const logs of csvData) {
              const object = {
                id: logs.id,
                request_json: serialize(logs.requestJson),
                response_json: serialize(logs.responseJson),
                service: logs.service,
                url: logs.url,
                tenant_id: logs.tenantId,
                response_code: logs.responseCode,
                response_status: logs.responseStatus,
                error_code: logs.errorCode,
                created_at: logs.createdAt.toISOString().replace("T", " ").substring(0, 19),
                updated_at: logs.updatedAt.toISOString().replace("T", " ").substring(0, 19)
              }
              mainArr = [...mainArr, object]
            }
          }
          await Promise.all([csvWriter.writeRecords(mainArr)])

          let totalRecords = count / limit
          const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

          for (let i = 1; i < totalRecords; i++) {
            await delay(1000)
            offset = i * limit
            const reqResData = await db.RequestResponseLogs.findAll({
              where: {
                tenantId: tenant.tenantId,
                createdAt: Sequelize.literal(`created_at < (CURRENT_DATE - INTERVAL'14 day') :: TIMESTAMPTZ`)
              },
              offset: offset,
              limit: limit,
            })

            const csvData = reqResData

            let mainArr = []
            if (csvData.length > 0) {
              for (const logs of csvData) {
                const object = {
                  id: logs.id,
                  request_json: serialize(logs.requestJson),
                  response_json: serialize(logs.responseJson),
                  service: logs.service,
                  url: logs.url,
                  tenant_id: logs.tenantId,
                  response_code: logs.responseCode,
                  response_status: logs.responseStatus,
                  error_code: logs.errorCode,
                  created_at: logs.createdAt.toISOString().replace("T", " ").substring(0, 19),
                  updated_at: logs.updatedAt.toISOString().replace("T", " ").substring(0, 19)
                }
                mainArr = [...mainArr, object]
              }
            }
            await Promise.all([csvWriter.writeRecords(mainArr)])

          }
          const zipFilePath = `/tmp/request_response_log_${uuid}.zip`
          const zip = new JSZip()
          const zipData = fs.readFileSync(filePath)
          zip.file(`request_response_log_${uuid}.csv`, zipData)

          await (() =>
          new Promise((resolve, reject) => {
            zip
              .generateNodeStream({ type: 'nodebuffer', streamFiles: true, compression: "DEFLATE", compressionOptions: {level: 9} })
              .pipe(fs.createWriteStream(zipFilePath))
              .on("finish", function () {
                resolve()
              });
          }))();

          //upload file to s3
          const s3Config = config.getProperties().s3
          const fileContent = await fs.promises.readFile(zipFilePath);
          const key = `csv/request_response_logs/request_response_log_${uuid}.zip`

          const s3Params = {
            ACL: 'public-read',
            Bucket: s3Config.bucket,
            Key: key,
            Body: fileContent
          }
          const uploadedFile = await s3.upload(s3Params).promise()
          // delete file from tmp directory
          fs.unlink(filePath, (err) => {
            if (err) {
              throw err
            }
          })
          fs.unlink(zipFilePath, (err) => {
            if (err) {
              throw err
            }
          })
          // delete records
          await db.RequestResponseLogs.destroy({
            where: {
              tenantId: tenant.tenantId,
              createdAt: Sequelize.literal(`created_at < (CURRENT_DATE - INTERVAL'14 day') :: TIMESTAMPTZ`)
            }
          })
          const reqBackup = {
            startDate: startDate,
            endDate: endDate,
            tenantId: tenant.tenantId,
            csvUrl: uploadedFile.Location
          }
          await db.RequestResponseLogsBackup.create(reqBackup)
        }
      }

    }
  } catch (e) {
    console.log("==========error", e)
  }

}
