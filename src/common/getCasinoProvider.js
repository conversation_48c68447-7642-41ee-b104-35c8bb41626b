import db, { sequelize } from '../db/models';
const { Op, fn, col, where, literal } = require('sequelize');


export async function getCasinoProvider(pageName, casinoProviderId, tenantId, topMenuId, sequelizeTransaction, currentDate, currencyObj, icon, override_other_providers = false) {

  const {
    Page: PageModel,
    Aggregator: AggregatorModel,
  } = db
  const casinoPageResults = await sequelize.query(`
    SELECT
      "page"."id"                     AS "id",
      "page"."title"                  AS "title",
      "page"."allowed_currencies"     AS "allowedCurrencies",
      "page"."top_menu_id"            AS "topMenuId",
      "Aggregator"."id"               AS "aggregatorId",
      "Aggregator"."status"           AS "status",
      "page"."enabled"                AS "enabled",
      "Aggregator"."aggregator_id"    AS "casinoAggregatorId"
    FROM "aggregator" AS "Aggregator"
    INNER JOIN "public"."pages" AS "page"
      ON "Aggregator"."page_id" = "page"."id"
      AND LOWER("page"."title") = LOWER(:pageName)
      AND "page"."tenant_id" = :tenantId
      AND "page"."top_menu_id" = :topMenuId
    INNER JOIN "public"."casino_providers" AS "casinoProvider"
      ON "Aggregator"."aggregator_id" = "casinoProvider"."id"
  `, {
    replacements: {
      pageName: pageName.replace(/_/g, ' '),
      tenantId: tenantId,
      topMenuId: topMenuId,
    },
    type: sequelize.QueryTypes.SELECT,
    transaction: sequelizeTransaction,
    raw: true,
  })
  // Process the results to separate current page from other pages
  const pageData = casinoPageResults?.reduce((acc, cur) => {
    if (cur.casinoAggregatorId == casinoProviderId) {
      acc.currentPage = {
      id: cur.id,
      title: cur.title,
      allowedCurrencies: cur.allowedCurrencies,
      topMenuId: cur.topMenuId,
      aggregatorId: cur.aggregatorId,
      status: cur.status,
      enabled: cur.enabled
    }
    }
    else {
      acc.otherPages.push({
        id: cur.id,
        title: cur.title,
        allowedCurrencies: cur.allowedCurrencies,
        topMenuId: cur.topMenuId,
        aggregatorId: cur.aggregatorId,
        status: cur.status
      })
    }
    if (cur.casinoAggregatorId != casinoProviderId && cur.enabled === true) {
      acc.otherProviderSelected = true
      acc.oldPageStatus = cur.enabled

    }



    return acc;


  }, { currentPage: {}, otherPages: [], otherProviderSelected: true, oldPageStatus: false })

  let casinoPage = pageData.currentPage

  // If no other pages exist, ensure otherProviderSelected is false
  if (pageData.otherPages.length === 0) {
    pageData.otherProviderSelected = false
  }


  if (!pageData.currentPage.id) {
    casinoPage = await PageModel.create(
      {
        title: pageName,
        tenantId,
        topMenuId: topMenuId,
        enabled: pageData.otherPages.length == 0 ? true : (tenantId == 0 ? true : pageData.oldPageStatus ),
        image: icon,
        createdAt: currentDate,
        updatedAt: currentDate,
        allowedCurrencies: currencyObj?.id ? [Number(currencyObj.id)] : []
      },

      { transaction: sequelizeTransaction,
        raw: true
       }
    )
    // when ever page is created, we need to create aggregator
    await AggregatorModel.create(
      {
        aggregatorId: casinoProviderId,
        pageId: casinoPage.id,
        status: tenantId == 0 ? true : (pageData.otherPages.length > 0 && !override_other_providers ? false : true),
      },
      { transaction: sequelizeTransaction }
    )


  }

  if (override_other_providers) {
    await AggregatorModel.update(
      { status: true },
      {
        where: {
          aggregatorId: casinoProviderId,
          pageId: casinoPage.id,
        },
        transaction: sequelizeTransaction
      }
    )

    await AggregatorModel.update(
      { status: tenantId == 0 ? true : false },
      {
        where: {
          pageId: pageData.otherPages.map(page => page.id),
        },
        transaction: sequelizeTransaction
      }
    )

  }

    await PageModel.update(
      { enabled:  pageData.otherPages.length == 0 ? true : (tenantId == 0 ? true : pageData.oldPageStatus) },
      {
        where: {
          id: [...pageData.otherPages.map(page => page.id), casinoPage.id],
        },
        transaction: sequelizeTransaction
      }
    )




  return casinoPage
}
