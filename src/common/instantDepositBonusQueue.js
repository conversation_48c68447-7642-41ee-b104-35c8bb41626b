import { Sequelize } from 'sequelize'
import db from '../db/models'
import { BONUS_COMMENT_ABBREVIATIONS, BONUS_STATUS, DEPOSIT_BONUS_BURN_TYPE, DEPOSIT_BONUS_WALLET_TYPES, DEPOSIT_INSTANT_BONUS_TYPES, QUEUE_WORKER_CONSTANT, TRANSACTION_TYPES } from './constants'
import userCurrencyExchange from './userCurrencyExchange'
import v3CurrencyConversion from './v3CurrencyConversion'
import { walletLocking } from './walletLocking'


const getWalletTypeField = (walletTypeValue) => {
  return DEPOSIT_BONUS_WALLET_TYPES[walletTypeValue] || DEPOSIT_BONUS_WALLET_TYPES[0]
}

// Helper function to get transaction type based on wallet type
const getTransactionType = (walletTypeValue) => {
  const transactionTypes = {
    0: TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM, // amount
    1: TRANSACTION_TYPES.NON_CASH_DEPOSIT_BONUS_CLAIM, // nonCashAmount
    2: TRANSACTION_TYPES.FREE_BETS_DEPOSIT_BONUS_CLAIM, // oneTimeBonusAmount - free_bets_on_deposit_bonus_claim
    3: TRANSACTION_TYPES.SPORTS_FREE_BETS_DEPOSIT_BONUS_CLAIM // sportsFreeBetAmount - sports_free_bets_deposit_bonus_claim
  }
  return transactionTypes[walletTypeValue] || TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM
}

export const instantDepositBonusQueue = async (sequelizeTransaction, amount, user, bonus, depositTransactionId) => {
  const {
    Transaction: TransactionModel,
    Currency: CurrencyModel,
    QueueLog: QueueLogModel,
    Bonus: BonusModel,
    UserBonus: UserBonusModel,
    InstantDepositBonusHistory: InstantDepositBonusHistoryModel,
    BurningBonus: BurningBonusModel,
    Wallet: WalletModel
  } = db

  if (bonus?.DepositBonusSetting?.depositBonusType !== DEPOSIT_INSTANT_BONUS_TYPES.INSTANT) {
    return false
  }

  const bonusToBeGiven = (amount * (bonus?.percentage / 100)) > bonus?.DepositBonusSetting?.maxBonus ? bonus?.DepositBonusSetting?.maxBonus : +parseFloat(amount * (bonus?.percentage / 100)).toFixed(5)

  const walletTypeField = getWalletTypeField(bonus?.walletType)

  const userInfo = await db.User.findOne({
    attributes: ['id', 'tenantId', 'userName', 'withdrawWagerAllowed', [Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id AND wallets.owner_type = 'User')`), 'exchangeRate']
    ],
    where: {
      id: user?.id,
    },
    include: {
      model: db.Wallet,
      attributes: ['id', 'currencyId', 'amount', 'nonCashAmount', 'oneTimeBonusAmount', 'sportsFreeBetAmount'],
      where: {
        ownerType: 'User'
      },
      required: true
    }
  })

  const currencyExchange = await userCurrencyExchange(CurrencyModel, userInfo?.Wallet?.currencyId)

  let transactionObject = {
    targetWalletId: userInfo?.Wallet?.id,
    targetCurrencyId: userInfo?.Wallet?.currencyId,
    amount: bonusToBeGiven,
    conversionRate: currencyExchange,
    comments: BONUS_COMMENT_ABBREVIATIONS.IBC,
    actioneeId: userInfo?.id,
    actioneeType: 'User',
    tenantId: userInfo?.tenantId,
    transactionType: getTransactionType(bonus?.walletType, bonus?.kind),
    errorCode: 0,
    status: 'success',
    success: true
  }

  const userWallet = await walletLocking(userInfo, sequelizeTransaction)
  await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })

  userWallet[walletTypeField] += parseFloat(bonusToBeGiven)
  await userWallet.save({ transaction: sequelizeTransaction })

  transactionObject.targetAfterBalance = userWallet[walletTypeField]
  transactionObject.targetBeforeBalance = userWallet[walletTypeField] - parseFloat(bonusToBeGiven)
  transactionObject = await v3CurrencyConversion(sequelizeTransaction, transactionObject, userInfo?.Wallet?.currencyId, userInfo?.tenantId, bonusToBeGiven)

  const { id } = await TransactionModel.create(transactionObject, { transaction: sequelizeTransaction })

  if (id) {
    const queueLogObject = {
      type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
      status: QUEUE_WORKER_CONSTANT.READY,
      ids: [id]
    }
    await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })
  }
  const BonusDetail = await BonusModel.findOne({
    where: {
      id: bonus.id,
      validUpto: { [Sequelize.Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') },
    },
    attributes: ['id', 'validUpto', 'kind', 'validFrom', 'vipLevels', 'tenantId', 'promoCodes', 'currencyId', 'referType', 'referValue', 'enablePastDate', 'autoActivateBonus']
  });
  const userBonusObject = {
    userId: user.id,
    status: BONUS_STATUS.CLAIMED,
    kind: BonusDetail.kind,
    bonusId: BonusDetail.id,
    expiresAt: BonusDetail.validUpto,
    transactionId: id,
    claimedAt: new Date().toISOString()

  }
  let userActiveDepositBonus = await UserBonusModel.create(userBonusObject, { transaction: sequelizeTransaction });


  if (userActiveDepositBonus?.id) {
    const instantDepositBonusHistory = {
      bonusId: bonus?.id,
      userId: user?.id,
      userBonusId: userActiveDepositBonus?.id,
      bonusTransactionId: id,
      depositTransactionId: depositTransactionId,
      depositAmount: amount,
      bonusAmount: bonusToBeGiven,
      bonusType: bonus?.DepositBonusSetting?.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.INSTANT ? DEPOSIT_INSTANT_BONUS_TYPES.INSTANT : DEPOSIT_INSTANT_BONUS_TYPES.RECURRING,
      recurringBonusType: bonus?.DepositBonusSetting?.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING ? bonus?.DepositBonusSetting?.recurringBonusType : null
    }
    const { id: instantBonusId } = await InstantDepositBonusHistoryModel.create(instantDepositBonusHistory, { transaction: sequelizeTransaction })

    if (bonus?.DepositBonusSetting?.burningDays && bonus?.DepositBonusSetting?.burnType === DEPOSIT_BONUS_BURN_TYPE.SINGLE_TIME_USE) {
      const burningBonusHistory = {
        userId: user?.id,
        bonusId: bonus?.id,
        userBonusId: userActiveDepositBonus?.id,
        instantBonusId,
        bonusAmount: bonusToBeGiven
      }
      await BurningBonusModel.create(burningBonusHistory, { transaction: sequelizeTransaction })
    }
  }



  return true
}
