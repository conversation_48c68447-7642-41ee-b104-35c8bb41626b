import db, { sequelize } from '../db/models'
import config from '../configs/app.config'
import { STAGE_TENANTS, PROD_TENANTS, EXPORT_CSV_STATUS, CRON_LOG_STATUS } from './constants'
import * as moment from 'moment-timezone';
import momentJs from 'moment'

const CSV_TYPES = {
  SALES: 'sales',
  REG: 'reg'
}

const convertToIST = (inputDate, inputTimezone) => {
  const date = moment.tz(inputDate, inputTimezone);
  const istDate = date.tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss');
  return istDate;
}
const convertToUTC = (inputDate, inputTimezone) => {
  const date = moment.tz(inputDate, inputTimezone);
  const utcDate = date.utc().format('YYYY-MM-DD HH:mm:ss');
  return utcDate;
}
export default async (param = false) => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {

    const {
      ExportCsvCenter: ExportCsvCenterModel,
      QueueLog: QueueLogModel
    } = db

    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'partner-matrix-queue-cron',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      cronLog.cronId = queueProcessStatus?.id
      const sites = ['jeeto555', 'AceXBet365']
      const tenants = config.get('env') === 'development' ? STAGE_TENANTS : PROD_TENANTS
      const siteIds = Object.entries(tenants).filter(tenant => sites.includes(tenant[1].name)).map(item => item[0])
      const sitePayload = []
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');

        const todayDate = `${year}-${month}-${day} 00:00:00`;
        let utcStartTime =null;
        let utcEndTime =null;
        let startTime =null;
        let endTime =null;
        if(param){
            const yesterdayMoment = momentJs(today).add(-1, 'days');
            const yesterdayMomentFormatted = yesterdayMoment.format('YYYY-MM-DD 00:00:00');
            utcStartTime= convertToUTC(yesterdayMomentFormatted, 'Asia/Kolkata');
            utcEndTime= convertToUTC(todayDate , 'Asia/Kolkata');
            startTime= yesterdayMomentFormatted;
            endTime= todayDate;
        }else{
            const tomorrowMoment = momentJs(today).add(1, 'days');
            const tomorrowFormattedMoment = tomorrowMoment.format('YYYY-MM-DD 00:00:00');
            utcStartTime= convertToUTC(todayDate, 'Asia/Kolkata');
            utcEndTime= convertToUTC(tomorrowFormattedMoment , 'Asia/Kolkata');
            startTime= todayDate;
            endTime= tomorrowFormattedMoment;
        }
      const { exportCsvCenterTypes, exportCsvCenterData } = siteIds.reduce((acc, cur) => {
        acc.exportCsvCenterTypes.push(...Object.values(CSV_TYPES).map(i => `pm_${cur}_${i}_report`))
        acc.exportCsvCenterData.push(
          ...Object.values(CSV_TYPES).map(i => ({
            payload: {
              tenantId: cur,
              type: i,
              utcStartTime: utcStartTime,
              utcEndTime: utcEndTime,
              startTime: startTime,
              endTime: endTime,
            },
            adminType: 'system',
            adminId: '-9999',
            status: EXPORT_CSV_STATUS.READY,
            type: `pm_${cur}_${i}_report`
          }))
        )
        return acc
      }, { exportCsvCenterTypes: [], exportCsvCenterData: [] })


      const data = await ExportCsvCenterModel.bulkCreate(exportCsvCenterData, { raw: true })
      const ids = data.map(i => i.id)

      const { id } = await QueueLogModel.create({
        status: 0,
        type: 'create_csv',
        ids
      })

      return {
        queueIds: id,
        ids
      }
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    cronLog.endTime = new Date()
    await db.CronLog.create(cronLog)
    throw new Error(e)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
