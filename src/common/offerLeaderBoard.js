import momentTimezone from 'moment-timezone';
import { col, fn, literal, Op } from 'sequelize';
import generateOfferLeaderboard from '../common/generateOfferLeaderboard';
import getTopUsersBasedOnWinningType from '../common/getTopUsersBasedOnWinningType';
import db, { sequelize } from '../db/models';
import ErrorLogHelper from './errorLog';

export default async () => {
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'offer_leaderboard_cron',
        status: 1
      },
      attributes: ['id']
    });
    if (queueProcessStatus) {
      const { Offer: OfferModel, Prize: PrizeModel, OfferFakeWinner: OfferFakeWinnerModel, OfferLeaderboard: OfferLeaderboardModel, TenantThemeSetting: TenantThemeSettingModel, OfferProvider: OfferProviderModel  } = db;

      const timezone = 'Asia/kolkata';
      const dateFormat = 'YYYY-MM-DD';
      const currentDateTimeIST = momentTimezone.tz(timezone).toISOString();
      const yesterdayDateIST = momentTimezone.tz(timezone).subtract(1, 'day').format(dateFormat);

      // ################################# Truncate all leaderboard data #################################
      const dltQuery = `
        truncate table offer_leaderboards;
        alter sequence offer_leaderboards_id_seq restart with 1;
      `;

      await sequelize.query(dltQuery);

      // ################################# Fetch all offers and prizes data #################################
      const offerPrizeData = await OfferModel.findAll({
        raw: true,
        where: {
          status: true,
          validFrom: { [Op.lte]: currentDateTimeIST },
          validTo: { [Op.gte]: currentDateTimeIST },
          tenantId: { [Op.in]: literal(`(select tenant_id from tenant_theme_settings where 'offerModule' = ANY(string_to_array(allowed_modules, ',')))`) },
        },
        attributes: [
          ['id', 'offer_id'],
          [fn('sum', col('prizes.prizes_count')), 'total_prizes'],
          [fn('min', col('Offer.winning_type')), 'winning_type'],
          [fn('min', literal('"Offer"."valid_from"::text')), 'valid_from'],
          [fn('min', literal('"Offer"."valid_to"::text')), 'valid_to'],
          [fn('min', col('Offer.tenant_id')), 'tenant_id'],
          [fn('min', col('Offer.min_fake_users_percentage')), 'min_fake_users_percentage']
        ],
        include: [
          {
            model: PrizeModel,
            as: 'prizes',
            attributes: [],
            required: true
          }
        ],
        group: ['Offer.id'],
        order: [['id', 'asc']]
      });

      const providerData = await OfferProviderModel.findAll({
        raw: true,
        attributes: [
          'offer_id',
          [fn('array_agg', col('provider_id')), 'provider_ids']
        ],
        where: {
          offer_id: { [Op.in]: offerPrizeData.map(offer => offer.offer_id) }
        },
        group: ['offer_id']
      });

      const providerMap = providerData.reduce((acc, item) => {
        acc[item.offer_id] = item.provider_ids;
        return acc;
      }, {});

      const offersData = offerPrizeData.map(offer => ({
        ...offer,
        provider_ids: providerMap[offer.offer_id] || [null]
      }));

      // ############################# Loop through offers data and create leaderboard ############################
      for (const offerData of offersData) {
        const { offer_id: offerId, winning_type: offerWinningType, provider_ids: providerIds, valid_from: offerValidFrom, valid_to: offerValidTo, min_fake_users_percentage: minFakeUsersPercentage, tenant_id: tenantId } = offerData;
        const noOfPrizes = parseInt(offerData.total_prizes) || 0;

        // If prizes count is 0 then skip
        if (!noOfPrizes) continue;

        // Get provider IDs for this offer
        const cleanProviderIds = providerIds ? providerIds.filter(id => id !== null) : [];

        // Fetch fake users by offer id
        let fakeUsers = await OfferFakeWinnerModel.findAll({
          raw: true,
          attributes: ['id', 'userName', 'winningAmount', [literal('true'), 'isFake']],
          where: { offerId },
          order: [['id', 'asc']]
        });

        // Calculate minimum no of fake users that must be added in leaderboard
        const minNoOfFakeUsers = Math.ceil(noOfPrizes * minFakeUsersPercentage / 100);

        // Calculate maximum no of real users that can be added in leaderboard
        const maxNoOfRealUsers = noOfPrizes - minNoOfFakeUsers;

        // If total no of fake users in database for this offer is less than 'minNoOfFakeUsers' then skip
        if (fakeUsers.length < minNoOfFakeUsers) continue;

        // Calculate start and end date to fetch data for leaderboard
        let leaderboardDataStartDate = momentTimezone.utc(offerValidFrom).tz(timezone).format(dateFormat);
        let leaderboardDataEndDate = yesterdayDateIST;

        // If offer starts from today then set it to fetch leaderboard data data of yesterday
        if (leaderboardDataStartDate === momentTimezone.tz(timezone).format(dateFormat)) {
          leaderboardDataStartDate = yesterdayDateIST;
        }

        // Fetch real winners from database based on winning type
        const offerWinningTypeText = { 0: 'rollover', 1: 'ggr', 2: 'ngr', 3: 'total_wagered' };
        const realUsers = await getTopUsersBasedOnWinningType(tenantId, offerWinningTypeText[offerWinningType], leaderboardDataStartDate, leaderboardDataEndDate, noOfPrizes, cleanProviderIds);

        // Generate leaderboard
        const finalLeaderboard = generateOfferLeaderboard(realUsers, fakeUsers, minNoOfFakeUsers, noOfPrizes);

        // Update fake users winning amount only if offer winning type is rollover.
        // GGR and NGR can be less also than the previous value.
        if (offerWinningType === 0 || offerWinningType === 3) {
          await Promise.all(finalLeaderboard.map(u => {
            if (u.isFake) {
              return OfferFakeWinnerModel.update({ winningAmount: u.winningAmount }, { where: { id: u.id } });
            }
          }));
        }

        // Store leaderboard in database
        await OfferLeaderboardModel.bulkCreate(finalLeaderboard.map(u => ({
          offerId: offerId,
          userName: u.userName,
          winningAmount: u.winningAmount,
          tenantId
        })));
      }
    }
  } catch (error) {
    await ErrorLogHelper.logError(error, null, null)
  }
}
