import axios from 'axios'
import { Op, QueryTypes, Sequelize } from 'sequelize'
import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import Logger from '../libs/logger'
import { CRON_LOG_STATUS, PROD_SPINOCCHIO_GAMES_PROVIDER, SPINOCCHIO_GAMES_URL, SPINOCCHIO_ICON, STAGE_SPINOCCHIO_GAMES_PROVIDER } from './constants'
import { getCasinoProvider } from './getCasinoProvider'
import syncOrOverrideCasinoProvider from './syncOrOverrideCasinoProvider'

export default async (reqBody= null) => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const {
      CasinoGame: CasinoGameModel,
      CasinoTable: CasinoTableModel,
      CasinoItem: CasinoItemModel,
      Page: PageModel,
      CasinoMenu: CasinoMenuModel,
      PageMenu: PageMenuModel,
      MenuItem: MenuItemModel
    } = db
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'spinocchio_game_population',
        status: 1
      },
      attributes: ['id']
    })
    if (!queueProcessStatus) {
      return {
        success: false
      }
    }
    if (!queueProcessStatus) return { success: false }

    cronLog.cronId = queueProcessStatus?.id

    const casinoProviderId = config.get('env') === 'production' ? PROD_SPINOCCHIO_GAMES_PROVIDER : STAGE_SPINOCCHIO_GAMES_PROVIDER

    let tenants = await sequelize.query(`
      SELECT "tenant_id" AS "tenantId", ( SELECT "id" FROM "menu_tenant_setting" WHERE "menu_id" = (SELECT "id" FROM "menu_master" WHERE "name" = 'Casino')
      AND "tenant_id" = "TenantThemeSetting"."tenant_id" ) AS "casinoId",
        ( SELECT "id" FROM "menu_tenant_setting" WHERE "menu_id" = (SELECT "id" FROM "menu_master" WHERE "name" = 'Crash')
      AND "tenant_id" = "TenantThemeSetting"."tenant_id" ) AS "crashId"
      FROM "public"."tenant_theme_settings" AS "TenantThemeSetting" WHERE '${casinoProviderId}' = ANY (string_to_array(assigned_providers, ','))
      ${reqBody?.tenantId ? ` AND "tenant_id" = '${reqBody?.tenantId}'` : ''}`,
      { type: QueryTypes.SELECT, useMaster: false })

    if (!tenants) {
      return {
        success: false
      }
    }

    const superAdminMenuId = await sequelize.query('SELECT "id" FROM "menu_master" WHERE "name" = \'Casino\';',
      { type: QueryTypes.SELECT, useMaster: false })

    const superAdminMenuIdCrash = await sequelize.query('SELECT "id" FROM "menu_master" WHERE "name" = \'Crash\'',
      { type: QueryTypes.SELECT, useMaster: false })

     let allTenantsSeeded = false
      if(Array.isArray(reqBody?.tenantIds) && !reqBody?.tenantIds.includes('0')){
         allTenantsSeeded = false
        tenants = tenants.filter(tenant => reqBody.tenantIds.includes(tenant.tenantId));
      } else if(Array.isArray(reqBody?.tenantIds)){
         allTenantsSeeded = true
      }
    tenants.push({ tenantId: 0, casinoId: superAdminMenuId[0].id, crashId: superAdminMenuIdCrash[0].id })
  for (const tenant of tenants) {
    const sequelizeTransaction = await sequelize.transaction()

    const tenantId = tenant.tenantId
    const casinoId = tenant.casinoId
    const crashId = tenant.crashId

    try {
    const currentDate = new Date().toISOString()

    // Getting Data from the credentials table

    const data = JSON.stringify({
      tenantId: (tenantId === 0) ? '1' : tenantId
    })

    const spinocchioGameData = await axios({
      url: SPINOCCHIO_GAMES_URL,
      method: 'post',
      maxBodyLength: Infinity,
      headers: {
        'Content-Type': 'application/json'
      },
      data: data
    })

    if (spinocchioGameData?.data === null) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = (spinocchioGameData?.error || 'No game list found') + ' ' + "TenantId: " + tenantId
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      continue
    }
    const allGames = spinocchioGameData?.data.games

    const categoryImages = {
      SLOT: 'assets/images/slot.png',
      CRASH: 'assets/images/crash.png'
    }

    const providerGameDataObject = allGames.reduce((acc, cur) => {
      const uniqueId = cur.game_id
      const category = cur.game_type
      const gameName = cur.game_title
      if (!acc.tableIds.includes(uniqueId)) {
        if (!acc.categories.includes(category)) {
          acc.categories.push(category)
          acc.categoriesData.push({
            name: `${category}`,
            casinoProviderId,
            gameId: `${category}`,
            createdAt: currentDate,
            updatedAt: currentDate
          })
        }

        const pageName = 'Spinocchio'

        // Combination key to create data page and category wise.
        const combinationKey = `${pageName}-${category}`

        const gameData = {
          name: gameName,
          gameId: category,
          tableId: uniqueId,
          providerId: casinoProviderId,
          isEnabled: true
        }

        const page = acc.pages.find(p => p.key === combinationKey)

        if (!page) {
          acc.pages.push({
            pageName: pageName,
            category: category,
            games: [gameData],
            key: combinationKey
          })
        } else {
          page.games.push(gameData)
        }

        acc.casinoTables.push({
          name: gameName,
          gameId: category,
          createdAt: currentDate,
          updatedAt: currentDate,
          isLobby: true,
          tableId: uniqueId,
          providerId: casinoProviderId
        })

        acc.tableIds.push(uniqueId)

        acc.name.push(gameName)
      }

      return acc
    }, { pages: [], categories: [], categoriesData: [], casinoTables: [], tableIds: [], name: [] })

    const existingCategoriesData = await CasinoGameModel.findAll({
      attributes: ['gameId', 'id', 'name'],
      where: {
        gameId: {
          [Op.in]: providerGameDataObject.categories
        },
        casinoProviderId: casinoProviderId
      },
      raw: true
    })
    const newCategoriesData = providerGameDataObject.categoriesData.filter(i => !existingCategoriesData.map(i => i.gameId).includes(i.gameId))
    await CasinoGameModel.bulkCreate(newCategoriesData, { transaction: sequelizeTransaction })

    for (const category of existingCategoriesData) {
      const categoryData = providerGameDataObject.categoriesData.find(i => i.gameId === category.gameId)
      if (categoryData && categoryData.name !== category.name) {
        await CasinoGameModel.update(
          { name: categoryData.name },
          { where: { id: category.id }, transaction: sequelizeTransaction }
        )
        category.name = categoryData.name
      }

    }

    const existingCasinoTableData = await CasinoTableModel.findAll({
      attributes: ['gameId', 'tableId', 'name', 'id'],
      where: {
        tableId: {
          [Op.in]: providerGameDataObject.tableIds
        },
        providerId: casinoProviderId
      },
      raw: true
    })
    const newCasinoTablesData = providerGameDataObject.casinoTables.filter(i => !existingCasinoTableData.map(i => i.tableId).includes(i.tableId))
    await CasinoTableModel.bulkCreate(newCasinoTablesData, { transaction: sequelizeTransaction })

    for (const table of existingCasinoTableData) {
      const tableData = providerGameDataObject.casinoTables.find(i => i.tableId === table.tableId)
      if (tableData && tableData.name !== table.name) {
        await CasinoTableModel.update(
          { name: tableData.name },
          { where: { id: table.id }, transaction: sequelizeTransaction }
        )
        table.name = tableData.name
      }
    }

    for (const { pageName, category, games } of providerGameDataObject.pages) {
      const topMenuId = (category === 'CRASH') ? crashId : casinoId
      let casinoPage = await getCasinoProvider(pageName, casinoProviderId, tenantId, topMenuId, sequelizeTransaction, currentDate, {}, SPINOCCHIO_ICON, reqBody?.override_other_providers)
      let casinoMenu = await CasinoMenuModel.findOne({
        raw: true,
        attributes: ['id', 'name'],
        where: {
          name: Sequelize.literal(`LOWER("name") = LOWER('${category.replace(/'/g, "''")}')`), // escape single quotes
          tenantId
        },
        transaction: sequelizeTransaction
      })

      if (!casinoMenu) {
        casinoMenu = await CasinoMenuModel.create(
          {
            name: category,
            enabled: true,
            tenantId,
            imageUrl: categoryImages[category],
            createdAt: currentDate,
            updatedAt: currentDate
          },
          { transaction: sequelizeTransaction }
        )
      }

      let pageMenu = await PageMenuModel.findOne({
        raw: true,
        where: { pageId: casinoPage.id, casinoMenuId: casinoMenu.id },
        attributes: ['id', 'name'],
        transaction: sequelizeTransaction
      })

      if (!pageMenu) {
        pageMenu = await PageMenuModel.create(
          {
            name: casinoMenu.name,
            pageId: casinoPage.id,
            casinoMenuId: casinoMenu.id,
            enabled: true,
            createdAt: currentDate,
            updatedAt: currentDate
          },
          { transaction: sequelizeTransaction }
        )
      }

      for (const game of games) {
        const { name, tableId, providerId, isEnabled } = game

        let casinoItem = await CasinoItemModel.findOne({
          raw: true,
          attributes: ['id', 'name', 'active'],
          where: {
            uuid: tableId,
            tenantId,
            provider: '' + providerId
          },
          transaction: sequelizeTransaction
        })

        // check name changes
        if (casinoItem && casinoItem.name !== name) {
          await CasinoItemModel.update(
            { name: name },
            { where: { id: casinoItem.id }, transaction: sequelizeTransaction }
          );
        }

        if (!casinoItem) {
          casinoItem = await CasinoItemModel.create({
            uuid: tableId,
            name: name,
            image: `provider-images/spinocchio/thumbnail/${tableId}.webp`,
            provider: casinoProviderId,
            active: isEnabled,
            featured: true,
            tenantId,
            createdAt: currentDate,
            updatedAt: currentDate
          },
          { transaction: sequelizeTransaction })
        } else if (casinoItem.active !== isEnabled) {
          await CasinoItemModel.update(
            { active: isEnabled },
            { where: { id: casinoItem.id }, transaction: sequelizeTransaction }
          )
          await MenuItemModel.update(
            { active: isEnabled },
            { where: { casinoItemId: casinoItem.id }, transaction: sequelizeTransaction }
          );
        }

        if (casinoItem) {
          // Find Casino Menu Item Exists, If not then create else update.
          let menuItems = await MenuItemModel.findAll({
            attributes: ['id', 'active', 'name', 'casinoItemId', 'pageMenuId'],
            where: { casinoItemId: casinoItem.id },
            transaction: sequelizeTransaction
          })

          const currentPageMenuId = menuItems.find(item => item.pageMenuId === pageMenu.id);
          if (!currentPageMenuId) {
            let menuItem = await MenuItemModel.create({
              name: casinoItem.name,
              pageMenuId: pageMenu.id,
              casinoItemId: casinoItem.id,
              active: isEnabled
            }, { transaction: sequelizeTransaction });
            menuItems.push(menuItem);
          }

          for (const menuItem of menuItems) {
            if (menuItem.name.toLowerCase() !== name.toLowerCase()) {
              menuItem.name = name
            }
            await menuItem.save({ transaction: sequelizeTransaction });
          }

          let menuItemsNotFromCurrentCasinoItem = menuItems.filter(item => item.pageMenuId !== pageMenu.id);
          if (menuItemsNotFromCurrentCasinoItem.length) {
            let menuListToBeDeleted = await sequelize.query(`
          SELECT "MenuItem".id
          FROM "public"."menu_items" AS "MenuItem"
          JOIN public.page_menus on "MenuItem".page_menu_id = page_menus.id
          WHERE "MenuItem"."id" IN (:menuids) and page_id in (:pageIds)`, {
              replacements: {
                menuids: menuItemsNotFromCurrentCasinoItem.map(item => item.id),
                pageIds: [casinoPage.id]
              },
              type: QueryTypes.SELECT,
              transaction: sequelizeTransaction
            })


            let allPageMenu = menuItemsNotFromCurrentCasinoItem.map(item => item.pageMenuId)
            let allAvailablePageMenu = await PageMenuModel.findAll({
              attributes: ['id'],
              where: {
                id: {
                  [Op.in]: allPageMenu
                }
              },
              raw: true
            })
            let allAvailablePageMenuIds = allAvailablePageMenu.map(item => item.id)
            let allPageMenuIds = allPageMenu.filter(item => !allAvailablePageMenuIds.includes(item))
            let allPageMenuIdsToBeDeleted = menuItemsNotFromCurrentCasinoItem.filter(item => allPageMenuIds.includes(item.pageMenuId))
            menuListToBeDeleted.push(...allPageMenuIdsToBeDeleted)

            // only delete the menu items that are belonging to the current page
            if (menuListToBeDeleted.length) {
              await MenuItemModel.destroy({
                where: {
                  id: {
                    [Op.in]: menuListToBeDeleted.map(item => item.id)
                  },
                  casinoItemId: casinoItem.id

                },
                transaction: sequelizeTransaction
              })
            }
          }
        }
      }

    }

    await sequelizeTransaction.commit()

    // disable the category if all games in that category are inactive
    await sequelize.query(
      ` UPDATE casino_menus cm
      SET enabled = subquery.result
      FROM (
        SELECT
          ct.game_id AS game_category,
          CASE
            WHEN COUNT(*) = COUNT(CASE WHEN ci.active = false THEN 1 END) THEN false
            ELSE true
          END AS result
        FROM
          casino_tables ct
        JOIN
          casino_items ci ON ct.table_id = ci.uuid
        WHERE
          ci.provider = '${casinoProviderId}'
          AND ci.tenant_id = '${tenantId}' AND ct.provider_id = '${casinoProviderId}'
        GROUP BY
          ct.game_id
      ) AS subquery
      WHERE cm.name = subquery.game_category AND cm.tenant_id = '${tenantId}';
    `
    )

  } catch (error) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message + ' TenantId: ' + tenantId
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      Logger.info(error, '=========SPINOCCHIO game population error==========\n')
      await sequelizeTransaction.rollback()
      continue
    }
  }
  let tenantIds = tenants.map(tenant => tenant.tenantId)
  if (reqBody?.override_other_providers || reqBody?.sync) {
    await syncOrOverrideCasinoProvider(tenantIds,  casinoProviderId, 'Spinocchio', reqBody?.override_other_providers, reqBody?.sync )
  }


    cronLog.status = CRON_LOG_STATUS.SUCCESS
    cronLog.errorMsg = null
    cronLog.endTime = new Date()
    await db.CronLog.create(cronLog)
    return {
      success: true
    }
  } catch (error) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = error.message || null
    cronLog.endTime = new Date()
    await db.CronLog.create(cronLog)
    Logger.info(error, '=========SPINOCCHIO game population error==========\n')
    return {
      success: false,
      Error: {
        stack: error.stack
      }
    }
  }
}
