import config from "../configs/app.config"
import { STAGE_PROVIDER, PROD_PROVIDER } from "./constants"

export default async (providerId) => {
  if(config.get('env') === 'production'){
    if(providerId==PROD_PROVIDER.EZUGI){
      return "Ezugi"
    }
    if(providerId==PROD_PROVIDER.EVOLUTION){
      return "Evolution"
    }
  }else{
    if(providerId==STAGE_PROVIDER.EZUGI){
      return "Ezugi"
    }
    if(providerId==STAGE_PROVIDER.EVOLUTION){
      return "Evolution"
    }
  }


}
