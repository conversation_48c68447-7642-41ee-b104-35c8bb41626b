import { Op, Sequelize } from 'sequelize';
import config from '../configs/app.config';
import db from '../db/models';
import { PROD_BOT_ACTIVITY_TENANTS, SPORT_CASINO_TXN_TYPE, STAGE_BOT_ACTIVITY_TENANTS, TRANSACTION_TYPES } from './constants';

export default async (reqData) => {
  const cronLog = {};
  cronLog.startTime = new Date();
  try {
    const {
      Transaction: TransactionModel,
      TenantGGRSummary: TenantGGRSummaryModel
    } = db;


    // Every 3 hours as Per IST 00:00, 03:00,....
    const ggrCronTimings = ["18:30", "21:30", "00:30", "03:30", "06:30", "09:30", "12:30", "15:30"];

    const currentTime = new Date().toTimeString().slice(0, 5);

    if (!ggrCronTimings.includes(currentTime) && !reqData?.tenantId) {
      return { success: false }
    }

    const timeRange = await getTimeRange();
    const startDate = timeRange.startDate
    const endDate = timeRange.endDate

    let botActivityTenants = []

    if (reqData?.tenantId) {
      botActivityTenants = [reqData?.tenantId]
    }
    else {
      botActivityTenants = config.get('env') === 'production' ? PROD_BOT_ACTIVITY_TENANTS : STAGE_BOT_ACTIVITY_TENANTS;
    }

    for (const tenantId of botActivityTenants) {
      // Only Real Players Transactions
      const transactions = await TransactionModel.findAll({
        where: {
          tenantId,
          createdAt: {
            [Op.gte]: startDate,
            [Op.lt]: endDate,
          },
          [Op.and]: Sequelize.literal(`
            NOT EXISTS (
              SELECT 1 FROM bot_users
              WHERE bot_users.user_id = "Transaction".actionee_id
            )
          `),
        },
      });

      const casinoDebitTransacations = [TRANSACTION_TYPES.DEBIT, TRANSACTION_TYPES.DEBIT_NO_CASH, TRANSACTION_TYPES.DEBIT_OTB_CASH]
      const casinoCredittransactions = [TRANSACTION_TYPES.CREDIT, TRANSACTION_TYPES.CREDIT_NO_CASH]
      const casinoRollbackTransactions = [TRANSACTION_TYPES.ROLLBACK, TRANSACTION_TYPES.ROLLBACK_NO_CASH, TRANSACTION_TYPES.ROLLBACK_OTB_CASH]

      const sportsDebitTransactions = [
        SPORT_CASINO_TXN_TYPE.EXCHANGE_PLACE_BET_NON_CASH_DEBIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_PLACE_BET_CASH_DEBIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_REFUND_CANCEL_BET_NON_CASH_DEBIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_REFUND_CANCEL_BET_CASH_DEBIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_REFUND_MARKET_CANCEL_NON_CASH_DEBIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_REFUND_MARKET_CANCEL_CASH_DEBIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_SETTLE_MARKET_CASH_DEBIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_RESETTLE_MARKET_CASH_DEBIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_CANCEL_SETTLED_MARKET_CASH_DEBIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_ADJUST_SETTLE_MARKET_CASH_DEBIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_CANCEL_SETTLE_BET_NON_CASH_DEBIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_CANCEL_SETTLE_BET_CASH_DEBIT,

      ]
      const sportsCreditTransactions = [
        SPORT_CASINO_TXN_TYPE.EXCHANGE_PLACE_BET_CASH_CREDIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_SETTLE_MARKET_CASH_CREDIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_RESETTLE_MARKET_CASH_CREDIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_CANCEL_SETTLED_MARKET_CASH_CREDIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_ADJUST_SETTLE_MARKET_CASH_CREDIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_CANCEL_SETTLE_BET_NON_CASH_CREDIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_CANCEL_SETTLE_BET_CASH_CREDIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_REFUND_CANCEL_BET_NON_CASH_CREDIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_REFUND_CANCEL_BET_CASH_CREDIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_REFUND_MARKET_CANCEL_NON_CASH_CREDIT,
        SPORT_CASINO_TXN_TYPE.EXCHANGE_REFUND_MARKET_CANCEL_CASH_CREDIT
      ]

      const debitTransactions = transactions.filter(t => casinoDebitTransacations.includes(t.transactionType) || sportsDebitTransactions.includes(t.transactionType));
      const creditTransactions = transactions.filter(t => casinoCredittransactions.includes(t.transactionType) || sportsCreditTransactions.includes(t.transactionType));
      const rollbackTransactions = transactions.filter(t => casinoRollbackTransactions.includes(t.transactionType));

      const totalDebitAmount = debitTransactions.reduce((sum, t) => sum + t.amount, 0);
      const totalCreditAmount = creditTransactions.reduce((sum, t) => sum + t.amount, 0);
      const totalRollbackAmount = rollbackTransactions.reduce((sum, t) => sum + t.amount, 0);

      const totalDebitTrxns = debitTransactions.length;
      const totalCreditTrxns = creditTransactions.length;
      const totalRollbackTrxns = rollbackTransactions.length;

      const minDebitAmount = debitTransactions.length > 0 ? Math.min(...debitTransactions.map(t => t.amount)) : null;
      const maxDebitAmount = debitTransactions.length > 0 ? Math.max(...debitTransactions.map(t => t.amount)) : null;
      const minCreditAmount = creditTransactions.length > 0 ? Math.min(...creditTransactions.map(t => t.amount)) : null;
      const maxCreditAmount = creditTransactions.length > 0 ? Math.max(...creditTransactions.map(t => t.amount)) : null;

      const actualDebitAmount = totalDebitAmount - totalRollbackAmount;
      const ggr = actualDebitAmount - totalCreditAmount;

      await TenantGGRSummaryModel.create({
        tenantId,
        totalDebitAmount,
        totalCreditAmount,
        totalRollbackAmount,
        totalDebitTrxns,
        totalCreditTrxns,
        totalRollbackTrxns,
        minDebitAmount,
        maxDebitAmount,
        minCreditAmount,
        maxCreditAmount,
        ggr,
        fromDate: startDate,
        toDate: endDate,
        summaryDate: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    return { success: true }

  } catch (error) {
    throw error
  }
};


async function getTimeRange () {
  const now = new Date();

  let startDate, endDate;

  startDate = new Date(now);
  startDate.setHours(startDate.getHours() - 3);
  startDate.setSeconds(0, 0)

  endDate = new Date(now);
  endDate.setSeconds(0, 0);

  return { startDate, endDate };
}
