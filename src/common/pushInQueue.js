import { Op } from 'sequelize'
import { v4 as uuidv4 } from 'uuid'
import { ALANBASE_EVENT_TYPES, DEPOSIT_REQUEST_STATUS, EXPORT_CSV_TYPE, MARINA888_TYPES, OCR_EVENT, SMARTICO_EVENT_TYPES } from '../common/constants'
import db from '../db/models'
import { BonusEngine, BonusEngineQueue } from '../queues/bonusEngine.queue'
import { CommonJob, CommonQueue } from '../queues/common.queue'
import { CreateCsv, CreateCsvQueue } from '../queues/createCsv.queue'
import { ExportCsv, ExportCsvQueue } from '../queues/exportCsv.queue'
import { PaymentGateway, PaymentGatewayQueue } from '../queues/paymentGateway.queue'
import { SmartiCoJob, SmartiCoQueue } from '../queues/smartiCo.queue'
import { TransactionJob, TransactionQueue } from '../queues/transaction.queue'

export default async (queueLogId) => {
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      raw: true,
      where: {
        service: 'push_in_queue',
        status: 1
      },
      attributes: ['id']
    })
    if (!queueProcessStatus) {
      return
    }
    const queueLog = await db.QueueLog.findOne({
      raw: true,
      attributes: ['id', 'ids', 'type', 'tenantId'],
      where: {
        id: queueLogId
      },
      useMaster: true
    })
    if (!queueLog || queueLog.ids.length < 1) {
      return
    }
    const transactionId = queueLog.ids
    const transactionType = queueLog.type
    const tenantId = queueLog.tenantId
        let queue
    let queueName
    if (transactionType == 'payin_deposit_update_status' || transactionType === 'saspay_deposit_update_status' || transactionType === 'pending_deposit_request') {
      const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
      const findOpenedDepositReq = await db.DepositRequest.findOne({
        raw: true,
        where: {
          id: transactionId[0],
          status: {
            [Op.or]: [DEPOSIT_REQUEST_STATUS.OPEN, DEPOSIT_REQUEST_STATUS.FAILED]
          }
        },
        attributes: ['id']
      })
      if (!findOpenedDepositReq) {
        return
      }
      await delay(20000)
    }
    switch (transactionType) {
      case 'casino_transaction':
      case 'bet_transaction':
      case 'user_transaction':
      case 'sport_casino_transaction':
      case 'rollback_transaction':
        queue = TransactionQueue
        queueName = TransactionJob
        break
      case 'bulk_deposit_withdraw':
      case 'bulk_request_deposit_withdraw':
      case 'bulk_deposit_request':
      case 'burning_losing_bonus':
      case 'player_bulk_categorization_bonus':
      case 'player_commission':
      case 'audit_log':
      case 'dep_withdraw_sms_otp':
      case 'deposit_wager_tracking':
      case 'elastic_record_deletion':
      case 'game_seed_manual':
      case 'sync_category':
      case 'wynta_new_user_resync':
      case 'create_thumbnail':
        queue = CommonQueue
        queueName = CommonJob
        break
      case 'create_csv':
        queue = CreateCsvQueue
        queueName = CreateCsv
        break
      case 'export_csv':
        queue = ExportCsvQueue
        queueName = ExportCsv
        break
      case 'payin_deposit_update_status':
        queue = PaymentGatewayQueue
        queueName = PaymentGateway
        break
      case 'saspay_deposit_update_status':
        queue = PaymentGatewayQueue
        queueName = PaymentGateway
        break
      case 'payout_withdraw_update_status':
        queue = PaymentGatewayQueue
        queueName = PaymentGateway
        break
      case 'pending_deposit_request':
        queue = PaymentGatewayQueue
        queueName = PaymentGateway
        break
      case 'pending_withdrawal_transaction':
        queue = PaymentGatewayQueue
        queueName = PaymentGateway
        break
      case 'create_withdraw_request':
        queue = PaymentGatewayQueue
        queueName = PaymentGateway
        break
      case MARINA888_TYPES.MARINA_888_USER_PASSWORD_CHANGE:
        queue = CommonQueue
        queueName = CommonJob
        break
      case 'bonus':
        queue = BonusEngineQueue
        queueName = BonusEngine
        break
      case SMARTICO_EVENT_TYPES.UPDATE_USER:
      case SMARTICO_EVENT_TYPES.WITHDRAWAL_REQUSTED:
      case SMARTICO_EVENT_TYPES.WITHDRAWAL_APPROVED:
      case SMARTICO_EVENT_TYPES.WITHDRAWAL_CANCELLED:
      case SMARTICO_EVENT_TYPES.DEPOSIT_APPROVED:
      case SMARTICO_EVENT_TYPES.CASINO_WIN:
      case SMARTICO_EVENT_TYPES.LOGIN_STATS:
      case SMARTICO_EVENT_TYPES.WALLET_UPDATE:
        queue = SmartiCoQueue
        queueName = SmartiCoJob
        break
      case MARINA888_TYPES.MARINA_888_MIGRATION_CRON:
        queue = CommonQueue
        queueName = CommonJob
        break
      case EXPORT_CSV_TYPE.MAIL_REPORT_CSV:
        queue = CommonQueue
        queueName = CommonJob
        break
      case ALANBASE_EVENT_TYPES.REGISTRATION:
      case ALANBASE_EVENT_TYPES.DEPOSIT:
      case ALANBASE_EVENT_TYPES.WITHDRAWAL_COMPLETED:
      case ALANBASE_EVENT_TYPES.ALANBASE_WIN:
      case ALANBASE_EVENT_TYPES.ALANBASE_SB_PLACE_BET:
      case ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_PLACE_BET:
      case ALANBASE_EVENT_TYPES.ALANBASE_SB_MARKET_CANCEL:
      case ALANBASE_EVENT_TYPES.ALANBASE_SB_SETTLE_MARKET:
      case ALANBASE_EVENT_TYPES.ALANBASE_SB_RESETTLE_MARKET:
      case ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_SETTLE_MARKET:
      case ALANBASE_EVENT_TYPES.ALANBASE_SB_CASHOUT:
      case ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_CASHOUT:
        queue = CommonQueue
        queueName = CommonJob
        break
      case OCR_EVENT.OCR_TRANSACTION:
        queue = CommonQueue
        queueName = CommonJob

      // To do implement later
      // case "deposit_transaction":
      //   queue = DepositTransactionQueue
      //   queueName = DepositTransaction
      //   break

      // case "withdraw_transaction":
      //   queue = WithdrawTransactionQueue
      //   queueName = WithdrawTransaction
      //   break
    }
    // console.log(queue, "-----------------------queue------------------------\n")
    const jobTime = new Date()
    const uuid = uuidv4().replace(/-/g, '')
    const uniqueId = uuid.substr(uuid.length - 10)

    if (transactionType === 'bonus') {
      queue.add(queueName,
        {
          time: jobTime,
          transactionType: transactionType,
          transactionId: queueLog.id,
          bonusData: queueLog.ids
        },
        {
          jobId: `${queueLog.id}_${queueName}`,
          removeOnComplete: true,
          delay: 2
        })

      await db.QueueLog.update(
        { status: 1 },
        { where: { id: queueLogId } }
      )
      return
    }

    if (transactionType === 'create_withdraw_request') {
      queue.add(queueName,
        {
          time: jobTime,
          transactionType: transactionType,
          transactionId: queueLog.id,
          withdrawalData: queueLog.ids
        },
        {
          jobId: `${queueLog.id}_${queueName}`,
          removeOnComplete: true,
          delay: 2,
        })

      await db.QueueLog.update(
        { status: 1 },
        { where: { id: queueLogId } }
      )
      return
    }
    if ([
      SMARTICO_EVENT_TYPES.UPDATE_USER,
      SMARTICO_EVENT_TYPES.WITHDRAWAL_REQUSTED,
      SMARTICO_EVENT_TYPES.WITHDRAWAL_APPROVED,
      SMARTICO_EVENT_TYPES.WITHDRAWAL_CANCELLED,
      SMARTICO_EVENT_TYPES.DEPOSIT_APPROVED,
      SMARTICO_EVENT_TYPES.CASINO_WIN,
      SMARTICO_EVENT_TYPES.LOGIN_STATS,
      SMARTICO_EVENT_TYPES.WALLET_UPDATE,
    ].includes(transactionType)) {
      queue.add(queueName,
        {
          time: jobTime,
          transactionType: transactionType,
          data: {
            ids: transactionId,
            tenantId
          }
        },
        {
          jobId: `${queueLogId}_${queueName}`,
          removeOnComplete: true,
          delay: 0
        })

      await db.QueueLog.update(
        { status: 1 },
        { where: { id: queueLogId } }
      )
      return
    }

    if ([
      ALANBASE_EVENT_TYPES.REGISTRATION,
      ALANBASE_EVENT_TYPES.DEPOSIT,
      ALANBASE_EVENT_TYPES.WITHDRAWAL_COMPLETED,
      ALANBASE_EVENT_TYPES.ALANBASE_WIN,
      ALANBASE_EVENT_TYPES.ALANBASE_SB_PLACE_BET,
      ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_PLACE_BET,
      ALANBASE_EVENT_TYPES.ALANBASE_SB_MARKET_CANCEL,
      ALANBASE_EVENT_TYPES.ALANBASE_SB_SETTLE_MARKET,
      ALANBASE_EVENT_TYPES.ALANBASE_SB_RESETTLE_MARKET,
      ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_SETTLE_MARKET,
      ALANBASE_EVENT_TYPES.ALANBASE_SB_CASHOUT,
      ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_CASHOUT
    ].includes(transactionType)) {
      queue.add(queueName,
        {
          time: jobTime,
          transactionType: transactionType,
          data: {
            ids: transactionId,
            tenantId
          }
        },
        {
          jobId: `${queueLogId}_${queueName}`,
          removeOnComplete: true,
          delay: 0
        })

      await db.QueueLog.update(
        { status: 1 },
        { where: { id: queueLogId } }
      )
      return
    }

    if (transactionId.length > 0) {
      for (const txnId of transactionId) {
        if (transactionType === 'bulk_deposit_withdraw' || transactionType === 'player_commission' || transactionType === 'player_bulk_categorization_bonus' || transactionType === 'bulk_request_deposit_withdraw' ||
          transactionType === 'bulk_deposit_request'
        ) {
          const depositWithdrawUser = await db.DepositWithdrawUser.findAll({
            raw: true,
            where: {
              jobId: txnId
            },
            useMaster: true
          })
          const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
          if (depositWithdrawUser.length > 0) {
            for (const depositWithdraw of depositWithdrawUser) {
              await delay(1000)
              await Promise.all([
                queue.add(queueName,
                  {
                    time: jobTime,
                    transactionType: transactionType,
                    transactionId: depositWithdraw.id
                  },
                  {
                    jobId: `${depositWithdraw.id}_${txnId}_${queueName}`,
                    removeOnComplete: true,
                    delay: 2
                  })
              ])
            }
          }
        } else {
          if (transactionType === 'user_transaction') {
            queue.add(queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                transactionId: txnId
              },
              {
                jobId: `${txnId}_${queueName}`,
                removeOnComplete: true,
                delay: 10
              })
          } else if (transactionType === 'burning_losing_bonus') {
            queue.add(queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                transactionId: txnId
              },
              {
                jobId: `${queueLogId}_${queueName}`,
                removeOnComplete: true,
                delay: 10
              })
          } else if (transactionType === MARINA888_TYPES.MARINA_888_USER_PASSWORD_CHANGE) {
            queue.add(queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                data: txnId
              },
              {
                jobId: `${queueLogId}_${queueName}`,
                removeOnComplete: true,
                delay: 10
              })
          } else if (transactionType === MARINA888_TYPES.MARINA_888_MIGRATION_CRON || transactionType === EXPORT_CSV_TYPE.MAIL_REPORT_CSV) {
            queue.add(queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                id: txnId
              },
              {
                jobId: `${queueLogId}_${queueName}`,
                removeOnComplete: true,
                delay: 10
              })
          } else if (transactionType === 'dep_withdraw_sms_otp') {
            queue.add(queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                id: txnId,
                tenantId
              },
              {
                jobId: `${queueLogId}_${queueName}`,
                removeOnComplete: true,
                delay: 2
              })
          } else if (transactionType === 'sync_category') {
            queue.add(queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                id: txnId,
                tenantId
              },
              {
                jobId: `${queueLogId}_${queueName}`,
                removeOnComplete: true,
                delay: 10
              })
          }else if (transactionType === 'game_seed_manual') {
            queue.add(queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                id: txnId,
                tenantId
              },
              {
                jobId: `${queueLogId}_${queueName}`,
                removeOnComplete: true,
                delay: 10
              })
          }else if (transactionType === 'rollback_transaction') {
            queue.add(queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                transactionId: txnId,
                tenantId
              },
              {
                jobId: `${queueLogId}_${queueName}`,
                removeOnComplete: true,
                delay: 10
              })
          } else if (transactionType === 'export_csv'){
            queue.add(queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                transactionId: txnId,
                queueLogId: queueLogId
              },
              {
                jobId: `${queueLogId}_${txnId}_${queueName}`,
                removeOnComplete: true
              })
         }
          else {
            queue.add(queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                transactionId: txnId
              },
              {
                jobId: `${transactionType === 'elastic_record_deletion' ? txnId.id : txnId}_${queueName}`,
                removeOnComplete: true,
                delay: 2
              })
          }
        }
      }
    }
    if (transactionType === 'export_csv'){
      await db.QueueLog.update(
        { status: 2 },
        { where: { id: queueLogId } }
      )
    }else{
      await db.QueueLog.update(
        { status: 1 },
        { where: { id: queueLogId } }
      )
    }

  } catch (error) {
    throw error
  }
}
