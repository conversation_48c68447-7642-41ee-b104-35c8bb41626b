import db from '../db/models'
import { AutomaticLosingBonus } from '../services/cron-services/automaticLosingBonus'
export default async () => {
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'automatic-losing-bonus-cron',
        status: 1
      },
      attributes: ['id']
    })
    if(queueProcessStatus){
      await AutomaticLosingBonus.execute()
    }
  } catch (e) {
    console.log("==========error", e)
  }

}
