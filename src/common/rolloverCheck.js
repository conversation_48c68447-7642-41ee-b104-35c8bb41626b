import { Op } from 'sequelize'
import { BONUS_TIER_TYPE, BONUS_TYPES, DEPOSIT_BONUS_ALLOWED, DEPOSIT_ROLLING_CALCULATION_METHODS } from '../common/constants'
import db from '../db/models'
import { burningBonusCheck } from './burningBonusCheck'
import { instantDepositBonusQueue } from './instantDepositBonusQueue'

export const rolloverCheck = async (bonusId, user, amount, paymentProviders, sequelizeTransaction, depositTxnId, isManualDeposit) => {
  const {
    Bonus: BonusModel,
    DepositBonusSetting: DepositBonusSettingModel,
    DepositBonusTier: DepositBonusTierModel
  } = db

  let result = {}

  const bonus = await BonusModel.findOne({
    where: {
      id: bonusId,
      enabled: true,
      [Op.or]: [
        {
          '$DepositBonusSetting.tier_type$': 1,
          '$DepositBonusSetting.min_deposit$': { [Op.lte]: amount },
          '$DepositBonusSetting.max_deposit$': { [Op.gte]: amount }
        },
        {
          '$DepositBonusSetting.tier_type$': 2,
          '$DepositBonusSetting.DepositBonusTiers.min_deposit_amount$': { [Op.lte]: amount },
          '$DepositBonusSetting.DepositBonusTiers.max_deposit_amount$': { [Op.gte]: amount }
        }
      ]
    },
    include: {
      model: DepositBonusSettingModel,
      attributes: ['minDeposit', 'maxDeposit', 'maxBonus', 'rolloverCalculationType', 'rolloverMultiplier', 'validForDays', 'depositType', 'depositBonusType', 'recurringBonusType', 'allowedPaymentProviders', 'customDeposits', 'tierType', 'weekDay', 'burningDays', 'burnType'],
      required: true,
      include: {
        model: DepositBonusTierModel,
        required: false
      }
    },
    attributes: ['id', 'percentage', 'kind', 'walletType', 'validUpto']
  })

  // bonus not found check
  if (!bonus) {
    return null
  }

  if ((bonus?.DepositBonusSetting?.depositType === DEPOSIT_BONUS_ALLOWED.MANUAL && !isManualDeposit)
    || (bonus?.DepositBonusSetting?.depositType === DEPOSIT_BONUS_ALLOWED.PAYMENT_GATEWAY && isManualDeposit)) {
    return null
  }

  // if the deposit uses the specified payment gateway
  if (paymentProviders && bonus?.DepositBonusSetting?.allowedPaymentProviders && !bonus?.DepositBonusSetting?.allowedPaymentProviders?.includes(paymentProviders)) {
    return null
  }


  // checking week day for bonus if there
  if (bonus?.DepositBonusSetting?.weekDay) {
    const today = new Date()
    const weekdaysMap = { sunday: 0, monday: 1, tuesday: 2, wednesday: 3, thursday: 4, friday: 5, saturday: 6 }
    const specifiedWeekdayNum = weekdaysMap[bonus?.DepositBonusSetting?.weekDay?.toLowerCase()]
    if (today.getDay() !== specifiedWeekdayNum) return null
  }

  //checking if they have any active bonuses with burning days remaining that they haven't utilized yet. (only if this bonus has burning days)
  if (bonus?.DepositBonusSetting?.burningDays) {
    const givable = await burningBonusCheck(user.id, user.tenantId)
    if (!givable) return null
  }

  if (bonus.DepositBonusSetting.tierType === BONUS_TIER_TYPE.TIER_2 && Array.isArray(bonus.DepositBonusSetting.DepositBonusTiers) && bonus.DepositBonusSetting.DepositBonusTiers.length > 0) {
    const { percentage, minDepositAmount, maxDepositAmount, maxBonus } = bonus.DepositBonusSetting.DepositBonusTiers[0];
    bonus.percentage = percentage;
    bonus.DepositBonusSetting.minDeposit = minDepositAmount;
    bonus.DepositBonusSetting.maxDeposit = maxDepositAmount;
    bonus.DepositBonusSetting.maxBonus = maxBonus;
  }

  // deposit amount less than min deposit amount and greater than max deposit amount check
  if (amount < bonus?.DepositBonusSetting?.minDeposit || amount > bonus?.DepositBonusSetting?.maxDeposit) {
    return null
  }

  // instant deposit bonus check
  if (bonus.kind === BONUS_TYPES.DEPOSIT_INSTANT) {
    const instantDeposit = await instantDepositBonusQueue(sequelizeTransaction, amount, user, bonus, depositTxnId)
    return { instantDeposit: instantDeposit }
  }

  const bonusToBeGiven = (amount * (bonus?.percentage / 100)) > bonus?.DepositBonusSetting?.maxBonus ? bonus?.DepositBonusSetting?.maxBonus : +parseFloat(amount * (bonus?.percentage / 100)).toFixed(5)
  result.bonusAmount = bonusToBeGiven

  let rolling
  if (bonus?.DepositBonusSetting?.rolloverCalculationType === DEPOSIT_ROLLING_CALCULATION_METHODS.TOTAL_BASED_ROLLING) {
    rolling = (parseFloat(bonusToBeGiven) + parseFloat(amount))
  } else if (bonus?.DepositBonusSetting?.rolloverCalculationType === DEPOSIT_ROLLING_CALCULATION_METHODS.DEPOSIT_BASED_ROLLING) {
    rolling = parseFloat(amount)
  } else {
    rolling = parseFloat(bonusToBeGiven)
  }
  result.bonusId = bonus.id
  result.rolloverBalance = rolling * bonus?.DepositBonusSetting?.rolloverMultiplier
  result.initialRolloverBalance = rolling * bonus?.DepositBonusSetting?.rolloverMultiplier
  result.expiresAt = new Date(new Date().setDate(new Date().getDate() + bonus.DepositBonusSetting.validForDays)).toISOString()
  result.bonusKind = bonus.kind
  result.expiresAt = bonus.validUpto
  return result

}
