
import sharp from 'sharp';
import config from '../configs/app.config';
import db from '../db/models';
import { s3 } from '../libs/awsS3Config';
const path = require('path');
const s3Config = config.getProperties().s3
export default async (jobData) => {
  try {
    const casinoItemId = jobData?.transactionId
    const casinoItem = await db.CasinoItem.findOne({
      where: {
        id: casinoItemId
      },
      attributes: ['image'],
      raw: true
    })
   const s3Key = casinoItem.image

   // Get the object from S3
    const { Body } = await s3.getObject({
      Bucket: s3Config.bucket,
      Key: s3Key,
    }).promise();


    const ext = path.extname(s3Key).toLowerCase();
    const fileName = path.basename(s3Key, ext);
    const dir = path.dirname(s3Key);

    const webpKey = `${dir}/${fileName}.webp`;
    const thumbnailKey = `${dir}/thumbnail/${fileName}.webp`;


    // Convert to thumbnail (70%)
    const thumbnailBuffer = await sharp(Body)
      .resize({ width: 350 }) // optional: set width/height for thumbnail 350
      .toFormat("webp", { quality: 70 })
      .toBuffer();

     if (ext !== '.webp') {
      const webpBuffer = await sharp(Body).webp({ quality: 90 }).toBuffer();

      await s3.putObject({
        Bucket: s3Config.bucket,
        Key: webpKey,
        Body: webpBuffer,
        ContentType: 'image/webp',
      }).promise();
    }

    // Upload thumbnail
    await s3.putObject({
      Bucket: s3Config.bucket,
      Key: thumbnailKey,
      Body: thumbnailBuffer,
      ContentType: 'image/webp',
    }).promise();

   await db.CasinoItem.update(
      { image: thumbnailKey },
      { where: { id: casinoItemId } }
    );

    return true
  } catch (e) {
    throw e
  }

}
