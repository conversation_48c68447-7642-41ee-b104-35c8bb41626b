export default async (TransactionModel, tenantId) => {
  let transactionId = [...Array(36)].map(i => (~~(Math.random() * 36)).toString(36)).join('')
  let transaction = await TransactionModel.findOne({ where: { transactionId, tenantId } })
  while (transaction) {
    transactionId = [...Array(36)].map(i => (~~(Math.random() * 36)).toString(36)).join('')
    transaction = await TransactionModel.findOne({ where: { transactionId, tenantId } })
  }

  return transactionId
}
