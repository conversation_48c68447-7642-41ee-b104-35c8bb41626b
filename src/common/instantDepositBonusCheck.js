import { Op, Sequelize } from 'sequelize'
import db from '../db/models'
import { BONUS_COMMENT_ABBREVIATIONS, BONUS_STATUS, DEPOSIT_BONUS_BURN_TYPE, DEPOSIT_BONUS_WALLET_TYPES, DEPOSIT_INSTANT_BONUS_TYPES, QUEUE_WORKER_CONSTANT, RECURRING_BONUS_TYPES, TRANSACTION_TYPES } from './constants'
import userCurrencyExchange from './userCurrencyExchange'
import v3CurrencyConversion from './v3CurrencyConversion'
import { walletLocking } from './walletLocking'

const getWalletTypeField = (walletTypeValue) => {
  return DEPOSIT_BONUS_WALLET_TYPES[walletTypeValue] || DEPOSIT_BONUS_WALLET_TYPES[0]
}

// Helper function to get transaction type based on wallet type
const getTransactionType = (walletTypeValue) => {
  const transactionTypes = {
    0: TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM, // amount
    1: TRANSACTION_TYPES.NON_CASH_DEPOSIT_BONUS_CLAIM, // nonCashAmount
    2: TRANSACTION_TYPES.FREE_BETS_DEPOSIT_BONUS_CLAIM, // oneTimeBonusAmount - free_bets_on_deposit_bonus_claim
    3: TRANSACTION_TYPES.SPORTS_FREE_BETS_DEPOSIT_BONUS_CLAIM // sportsFreeBetAmount - sports_free_bets_deposit_bonus_claim
  }
  return transactionTypes[walletTypeValue] || TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM
}

export const instantDepositBonus = async (sequelizeTransaction, amount, user, txnIds, userActiveDepositBonus, bonus, depositTxnId) => {
  const {
      Transaction: TransactionModel,
      InstantDepositBonusHistory: InstantDepositBonusHistoryModel,
      Wallet: WalletModel,
      Currency: CurrencyModel,
      BurningBonus: BurningBonusModel,
      QueueLog: QueueLogModel
    } = db

  if (bonus?.DepositBonusSetting?.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && bonus?.DepositBonusSetting?.recurringBonusType === RECURRING_BONUS_TYPES.ONCE_A_DAY) {
    const today = Sequelize.literal("DATE_TRUNC('day', CURRENT_TIMESTAMP)")
    const existingDepositBonus = await InstantDepositBonusHistoryModel.findOne({
      where: {
        bonusId: bonus?.id,
        userId: user?.id,
        userBonusId: userActiveDepositBonus?.id,
        createdAt: {
          [Op.gte]: today
        },
        bonusType: DEPOSIT_INSTANT_BONUS_TYPES.RECURRING,
        recurringBonusType: RECURRING_BONUS_TYPES.ONCE_A_DAY
      }
    })
    if (existingDepositBonus) return null
  }

  if (bonus?.DepositBonusSetting?.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && bonus?.DepositBonusSetting?.recurringBonusType === RECURRING_BONUS_TYPES.CUSTOM_DEPOSITS) {
    const today = Sequelize.literal("DATE_TRUNC('day', CURRENT_TIMESTAMP)")
    const count = await InstantDepositBonusHistoryModel.count({
      where: {
        bonusId: bonus?.id,
        userId: user?.id,
        userBonusId: userActiveDepositBonus?.id,
        createdAt: {
          [Op.gte]: today
        },
        bonusType: DEPOSIT_INSTANT_BONUS_TYPES.RECURRING,
        recurringBonusType: RECURRING_BONUS_TYPES.CUSTOM_DEPOSITS
      }
    })
    if (count >= bonus?.DepositBonusSetting?.customDeposits) return null
  }
  const bonusToBeGiven = (amount * (bonus?.percentage / 100)) > bonus?.DepositBonusSetting?.maxBonus ? bonus?.DepositBonusSetting?.maxBonus : +parseFloat(amount * (bonus?.percentage / 100)).toFixed(5)

  const walletTypeField = getWalletTypeField(bonus?.walletType)

  const currencyExchange = await userCurrencyExchange(CurrencyModel, user?.Wallet?.currencyId)

  let transactionObject = {
    targetWalletId: user?.Wallet?.id,
    targetCurrencyId: user?.Wallet?.currencyId,
    amount: bonusToBeGiven,
    conversionRate: currencyExchange,
    comments: BONUS_COMMENT_ABBREVIATIONS.IBC,
    actioneeId: user?.id,
    actioneeType: 'User',
    tenantId: user?.tenantId,
    timestamp: new Date().getTime(),
    transactionType: getTransactionType(bonus?.walletType),
    //errorDescription: 'Completed Successfully',
    errorCode: 0,
    status: 'success',
    success: true
  }

  const userWallet = await walletLocking(user, sequelizeTransaction)
  await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })
  userWallet[walletTypeField] += parseFloat(bonusToBeGiven)
  await userWallet.save({ transaction: sequelizeTransaction })

  transactionObject.targetAfterBalance = userWallet[walletTypeField]
  transactionObject.targetBeforeBalance = userWallet[walletTypeField] - parseFloat(bonusToBeGiven)
  transactionObject = await v3CurrencyConversion(sequelizeTransaction, transactionObject, user?.Wallet?.currencyId, user?.tenantId, bonusToBeGiven)

  const { id } = await TransactionModel.create(transactionObject, { transaction: sequelizeTransaction })
  if (id) txnIds.push(id)

  if (id) {
    const queueLogObject = {
      type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
      status: QUEUE_WORKER_CONSTANT.READY,
      ids: [id]
    }
    await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })
  }

  userActiveDepositBonus.claimedAt = new Date().toISOString()
  userActiveDepositBonus.transactionId = id
  if (bonus?.DepositBonusSetting?.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.INSTANT) userActiveDepositBonus.status = BONUS_STATUS.CLAIMED

  await userActiveDepositBonus.save({ transaction: sequelizeTransaction })

  const instantDepositBonusHistory = {
    bonusId: bonus?.id,
    userId: user?.id,
    userBonusId: userActiveDepositBonus?.id,
    bonusTransactionId: id,
    depositTransactionId: depositTxnId,
    depositAmount: amount,
    bonusAmount: bonusToBeGiven,
    bonusType: bonus?.DepositBonusSetting?.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.INSTANT ? DEPOSIT_INSTANT_BONUS_TYPES.INSTANT : DEPOSIT_INSTANT_BONUS_TYPES.RECURRING,
    recurringBonusType: bonus?.DepositBonusSetting?.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING ? bonus?.DepositBonusSetting?.recurringBonusType : null
  }
  const { id: instantBonusId } = await InstantDepositBonusHistoryModel.create(instantDepositBonusHistory, { transaction: sequelizeTransaction })

  if (bonus?.DepositBonusSetting?.burningDays && bonus?.DepositBonusSetting?.burnType === DEPOSIT_BONUS_BURN_TYPE.SINGLE_TIME_USE) {
    const burningBonusHistory = {
      userId: user?.id,
      bonusId: bonus?.id,
      userBonusId: userActiveDepositBonus?.id,
      instantBonusId,
      bonusAmount: bonusToBeGiven
    }
    await BurningBonusModel.create(burningBonusHistory, { transaction: sequelizeTransaction })
  }
  return true
}
