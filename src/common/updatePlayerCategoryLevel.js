import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import { ALLOWED_PERMISSIONS, LOYALTY_PROGRAM_SETTINGS, PROD_TENANTS, STAGE_TENANTS, CRON_LOG_STATUS, TENANT_SETTINGS_TYPE } from './constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {

    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'update_player_category_cron',
        status: 1
      },
      attributes: ['id']
    })

    if(!queueProcessStatus) return
    cronLog.cronId = queueProcessStatus?.id
    const tenants = config.get('env') === 'development' ? STAGE_TENANTS : PROD_TENANTS

    for (const [tenantId, tenant] of Object.entries(tenants)) {

      const tenantThemeSetting = await db.TenantThemeSetting.findOne({
        attributes: ['tenantId', 'allowedModules'],
        where: { tenantId },
        raw: true
      });

      if (!tenantThemeSetting) continue;

      const { allowedModules } = tenantThemeSetting;

      // Check if the tenant has the playerCategory module enabled
      const hasPlayerCategory = allowedModules
        ? allowedModules.split(',').map(module => module.trim()).includes(ALLOWED_PERMISSIONS.PLAYER_CATEGORIZATION)
        : false;

      if (!hasPlayerCategory) continue;

      // Check if the tenant has any categories in the player_categories table
      const playerCategoriesCount = await db.PlayerCategory.count({
        where: {
          tenantId,
          status: true
        }
      });

      if (playerCategoriesCount === 0) continue;

      const tenantSetting = await db.TenantSetting.findOne({
        attributes: ['key', 'value', 'type'],
        where: { tenantId, type: TENANT_SETTINGS_TYPE.LOYALTY_PROGRAM_SETTINGS },
        raw: true,
      });

      const bonusCalculationProcedures = {
        [LOYALTY_PROGRAM_SETTINGS.ROLLOVER]: 'CALL player_bonus_calculation(:tenantId);',
        [LOYALTY_PROGRAM_SETTINGS.DEPOSIT]: 'CALL deposit_based_loyalty_bonus_calculation(:tenantId);',
      };

      // Default to ROLLOVER procedure if no valid setting is found
      const procedure = bonusCalculationProcedures[tenantSetting?.value] || bonusCalculationProcedures[LOYALTY_PROGRAM_SETTINGS.ROLLOVER];

      await sequelize.query(procedure, { replacements: { tenantId } });
    }

  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.error(`Error processing player bet summary and bonus`, e);
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
