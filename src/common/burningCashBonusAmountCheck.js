import db, { sequelize } from '../db/models'
import { TRANSACTION_TYPES, SPORT_CASINO_TXN_TYPE } from '../common/constants'
const { Op, literal } = require('sequelize')

export const burningCashBonusAmountCheck = async (userLosingBonusClaimHistoryData) => {

  const totalCashSportsBetAmount = await sequelize.query(
    'SELECT * FROM get_total_cash_sports_bet_amount(:user_id, :tenant_id, :start_date, :end_date);',
    {
      replacements: {
        user_id: userLosingBonusClaimHistoryData.userId,
        tenant_id: userLosingBonusClaimHistoryData.tenantId,
        start_date: userLosingBonusClaimHistoryData.createdAt,
        end_date: new Date().toISOString()
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  const totalCashCasinoBetAmount = await sequelize.query(
    'SELECT * FROM get_total_cash_casino_bet_amount(:tenant_id, :actionee_id, :start_date, :end_date);',
    {
      replacements: {
        tenant_id: userLosingBonusClaimHistoryData.tenantId,
        actionee_id: userLosingBonusClaimHistoryData.userId,
        start_date: userLosingBonusClaimHistoryData.createdAt,
        end_date: new Date().toISOString()
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  const sumAmount = (totalCashSportsBetAmount[0]['get_total_cash_sports_bet_amount'] + totalCashCasinoBetAmount[0]['get_total_cash_casino_bet_amount']).toFixed(4)

  return (userLosingBonusClaimHistoryData.bonusAmount - sumAmount).toFixed(4)
}
