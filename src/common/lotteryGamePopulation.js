import { Op, QueryTypes, Sequelize } from 'sequelize'
import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import Logger from '../libs/logger'
import { CRON_LOG_STATUS, LOTTERY777_ICON, PROD_LOTTERY777_GAMES_PROVIDER, STAGE_LOTTERY777_GAMES_PROVIDER } from './constants'
import { getCasinoProvider } from './getCasinoProvider'
import syncOrOverrideCasinoProvider from './syncOrOverrideCasinoProvider'
export default async (reqBody = null) => {

  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const {
      CasinoGame: CasinoGameModel,
      CasinoTable: CasinoTableModel,
      CasinoItem: CasinoItemModel,
      Page: PageModel,
      CasinoMenu: CasinoMenuModel,
      PageMenu: PageMenuModel,
      MenuItem: MenuItemModel,
      MenuMaster: MenuMasterModel,
      MenuTenantSetting: MenuTenantSettingModel,
    } = db
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'lottery777_game_population',
        status: 1
      },
      attributes: ['id']
    })
    if (!queueProcessStatus) {
      return {
        success: false
       }
    }

    cronLog.cronId = queueProcessStatus?.id

    const casinoProviderId = config.get('env') === 'production' ? PROD_LOTTERY777_GAMES_PROVIDER : STAGE_LOTTERY777_GAMES_PROVIDER

    let tenants = await sequelize.query(`
      SELECT "tenant_id" AS "tenantId"
      FROM "public"."tenant_theme_settings" AS "TenantThemeSetting" WHERE '${casinoProviderId}' = ANY (string_to_array(assigned_providers, ','))
        ${ reqBody?.tenantId != null ? `AND "tenant_id" = ${reqBody?.tenantId}` : `` }
      `,
      { type: QueryTypes.SELECT, useMaster: false })
     let allTenantsSeeded = false
      if(Array.isArray(reqBody?.tenantIds) && !reqBody?.tenantIds.includes('0')){
         allTenantsSeeded = false
        tenants = tenants.filter(tenant => reqBody.tenantIds.includes(tenant.tenantId));
      } else if(Array.isArray(reqBody?.tenantIds)){
         allTenantsSeeded = true
      }
      tenants.push({tenantId:'0'})
    for (const tenant of tenants) {
      const sequelizeTransaction = await sequelize.transaction()

      const tenantId = tenant.tenantId
      try {

      const currentDate = new Date().toISOString();

      const allGames = [
        { gameId: "1", name: "Win Go 1 Min", category: "Bingo" },
        { gameId: "2", name: "Win Go 3 Min", category: "Bingo" },
        { gameId: "3", name: "Win Go 5 Min", category: "Bingo" },
        { gameId: "4", name: "Win Go 10 Min", category: "Bingo" },
        { gameId: "21", name: "Five Digit 1 Min", category: "Lottery" },
        { gameId: "22", name: "Five Digit 3 Min", category: "Lottery" },
        { gameId: "23", name: "Five Digit 5 Min", category: "Lottery" },
        { gameId: "24", name: "Five Digit 10 Min", category: "Lottery" },
        { gameId: "31", name: "Fast Three 1 Min", category: "Lottery" },
        { gameId: "32", name: "Fast Three 3 Min", category: "Lottery" },
        { gameId: "33", name: "Fast Three 5 Min", category: "Lottery" },
        { gameId: "34", name: "Fast Three 10 Min", category: "Lottery" },
        { gameId: "41", name: "Trx Win Go 1 Min", category: "Bingo" },
        { gameId: "42", name: "Trx Win Go 3 Min", category: "Bingo" },
        { gameId: "43", name: "Trx Win Go 5 Min", category: "Bingo" },
        { gameId: "44", name: "Trx Win Go 10 Min", category: "Bingo" }
      ];

      const categoryImages = {
        Bingo: 'assets/images/bingo.png',
        Lottery: 'assets/images/lottery.png'
      }

      const providerGameDataObject = allGames.reduce((acc, cur) => {
        const uniqueId = cur.gameId
        const category = cur.category
        const gameName = cur.name
        if (!acc.tableIds.includes(uniqueId)) {

          if (!acc.categories.includes(category)) {
            acc.categories.push(category);
            acc.categoriesData.push({
              name: `${category}`,
              casinoProviderId,
              gameId: `${category}`,
              createdAt: currentDate,
              updatedAt: currentDate,
            })
          }

          const pageName = 'Lottery777'

          // Combination key to create data page and category wise.
          const combinationKey = `${pageName}-${category}`

          const gameData = {
            name: gameName,
            gameId: category,
            tableId: uniqueId,
            providerId: casinoProviderId,
            isEnabled: true,
          }

          const page = acc.pages.find(p => p.key === combinationKey);

          if (!page) {
            acc.pages.push({
              pageName: pageName,
              category: category,
              games: [gameData],
              key: combinationKey
            })
          }
          else {
            page.games.push(gameData);
          }

          acc.casinoTables.push({
            name: gameName,
            gameId: category,
            createdAt: currentDate,
            updatedAt: currentDate,
            isLobby: true,
            tableId: uniqueId,
            providerId: casinoProviderId
          })

          acc.tableIds.push(uniqueId)

          acc.name.push(gameName)
        }

        return acc
      }, { pages: [], categories: [], categoriesData: [], casinoTables: [], tableIds: [], name: [] })

      const existingCategoriesData = await CasinoGameModel.findAll({
        attributes: ['gameId', 'id', 'name'],
        where: {
          gameId: {
            [Op.in]: providerGameDataObject.categories
          },
          casinoProviderId: casinoProviderId
        },
        raw: true
      })
      const newCategoriesData = providerGameDataObject.categoriesData.filter(i => !existingCategoriesData.map(i => i.gameId).includes(i.gameId))
      await CasinoGameModel.bulkCreate(newCategoriesData, { transaction: sequelizeTransaction })

    for (const category of existingCategoriesData) {
      const categoryData = providerGameDataObject.categoriesData.find(i => i.gameId === category.gameId)
      if (categoryData && categoryData.name !== category.name) {
        await CasinoGameModel.update(
          { name: categoryData.name },
          { where: { id: category.id }, transaction: sequelizeTransaction }
        )
        category.name = categoryData.name
      }

    }

      const existingCasinoTableData = await CasinoTableModel.findAll({
        attributes: ['gameId', 'tableId', 'name', 'id'],
        where: {
          tableId: {
            [Op.in]: providerGameDataObject.tableIds
          },
          providerId: casinoProviderId
        },
        raw: true
      })
      const newCasinoTablesData = providerGameDataObject.casinoTables.filter(i => !existingCasinoTableData.map(i => i.tableId).includes(i.tableId))
      await CasinoTableModel.bulkCreate(newCasinoTablesData, { transaction: sequelizeTransaction })

      for (const table of existingCasinoTableData) {
        const tableData = providerGameDataObject.casinoTables.find(i => i.tableId === table.tableId)
        if (tableData && tableData.name !== table.name) {
          await CasinoTableModel.update(
            { name: tableData.name },
            { where: { id: table.id }, transaction: sequelizeTransaction }
          )
          table.name = tableData.name
        }
      }

      for (const { pageName, category, games } of providerGameDataObject.pages) {
        let searchCategory = 'Lottery';

        let menuMaster = await MenuMasterModel.findOne({
          raw: true,
          attributes: ['id'],
          where: {
            name: Sequelize.literal(`LOWER("name") = LOWER('${searchCategory}')`)
          },
          transaction: sequelizeTransaction,
        });

        if (!menuMaster) {
          menuMaster = await MenuMasterModel.create(
            {
              name: searchCategory,
              path: `/${searchCategory.toLowerCase()}`,
              active: true,
              component: 'Casino',
              componentName: searchCategory.toLowerCase(),
              createdAt: currentDate,
              updatedAt: currentDate
            },
            { transaction: sequelizeTransaction }
          );
        }
        let menuTenantSetting
        if (tenantId != 0) {
           menuTenantSetting = await MenuTenantSettingModel.findOne({
            where: { tenant_id: tenantId, menu_id: menuMaster.id },
            attributes: ['id'],
            transaction: sequelizeTransaction,
            raw: true
          });

          if (!menuTenantSetting) {
            menuTenantSetting = await MenuTenantSettingModel.create(
              {
                menu_id: menuMaster.id,
                tenant_id: tenantId,
                createdAt: currentDate,
                updatedAt: currentDate
              },
              { transaction: sequelizeTransaction }
            );
          }
        } else {
          menuTenantSetting = menuMaster
        }

        let casinoPage = await getCasinoProvider(pageName, casinoProviderId, tenantId, menuTenantSetting.id, sequelizeTransaction, currentDate, {}, LOTTERY777_ICON, reqBody?.override_other_providers)


        let casinoMenu = await CasinoMenuModel.findOne({
          raw: true,
          attributes: ['id', 'name'],
          where: {
            name: Sequelize.literal(`LOWER("name") = LOWER('${category.replace(/'/g, "''")}')`), // escape single quotes
            tenantId
          },
          transaction: sequelizeTransaction,
        });

        if (!casinoMenu) {
          casinoMenu = await CasinoMenuModel.create(
            {
              name: category,
              enabled: true,
              tenantId,
              imageUrl: categoryImages[category],
              createdAt: currentDate,
              updatedAt: currentDate,
            },
            { transaction: sequelizeTransaction }
          );
        }

        let pageMenu = await PageMenuModel.findOne({
          raw: true,
          where: { pageId: casinoPage.id, casinoMenuId: casinoMenu.id },
          attributes: ['id', 'name'],
          transaction: sequelizeTransaction,
        });

        if (!pageMenu) {
          pageMenu = await PageMenuModel.create(
            {
              name: casinoMenu.name,
              pageId: casinoPage.id,
              casinoMenuId: casinoMenu.id,
              enabled: true,
              createdAt: currentDate,
              updatedAt: currentDate
            },
            { transaction: sequelizeTransaction }
          );
        }

        for (const game of games) {
          const { name, tableId, providerId, isEnabled } = game;

          let casinoItem = await CasinoItemModel.findOne({
            raw: true,
            attributes: ['id', 'name', 'active'],
            where: {
              uuid: tableId,
              tenantId,
              provider: '' + providerId,
            },
            transaction: sequelizeTransaction,
          });
          // check name changes
          if (casinoItem && casinoItem.name !== name) {
            await CasinoItemModel.update(
              { name: name },
              { where: { id: casinoItem.id }, transaction: sequelizeTransaction }
            );
          }

          if (!casinoItem) {
            casinoItem = await CasinoItemModel.create({
              uuid: tableId,
              name: name,
              image: `provider-images/lottery777/thumbnail/${tableId}.webp`,
              provider: casinoProviderId,
              active: isEnabled,
              featured: true,
              tenantId,
              createdAt: currentDate,
              updatedAt: currentDate,
            },
              { transaction: sequelizeTransaction })
          }
          else if (casinoItem.active != isEnabled) {
            await CasinoItemModel.update(
              { active: isEnabled },
              { where: { id: casinoItem.id }, transaction: sequelizeTransaction }
            );
          }

          if (casinoItem) {
            // Find Casino Menu Item Exists, If not then create else update.
            let menuItems = await MenuItemModel.findAll({
              attributes: ['id', 'active', 'name', 'casinoItemId', 'pageMenuId'],
              where: { casinoItemId: casinoItem.id },
              transaction: sequelizeTransaction
            });

            const currentPageMenuId = menuItems.find(item => item.pageMenuId === pageMenu.id);
            if (!currentPageMenuId) {
              let menuItem = await MenuItemModel.create({
                name: casinoItem.name,
                pageMenuId: pageMenu.id,
                casinoItemId: casinoItem.id,
                active: isEnabled
              }, { transaction: sequelizeTransaction });
              menuItems.push(menuItem);
            }

            for (const menuItem of menuItems) {
              if (menuItem.name.toLowerCase() !== name.toLowerCase()) {
                menuItem.name = name
              }
              if(menuItem.active != isEnabled){
                menuItem.active = isEnabled;

              }
              await menuItem.save({ transaction: sequelizeTransaction });
            }

            let menuItemsNotFromCurrentCasinoItem = menuItems.filter(item => item.pageMenuId !== pageMenu.id);
            if (menuItemsNotFromCurrentCasinoItem.length) {
              let menuListToBeDeleted = await sequelize.query(`
              SELECT "MenuItem".id
              FROM "public"."menu_items" AS "MenuItem"
              JOIN public.page_menus on "MenuItem".page_menu_id = page_menus.id
              WHERE "MenuItem"."id" IN (:menuids) and page_id in (:pageIds)`, {
                replacements: {
                  menuids: menuItemsNotFromCurrentCasinoItem.map(item => item.id),
                  pageIds: [casinoPage.id]
                },
                type: QueryTypes.SELECT,
                transaction: sequelizeTransaction
              })


              let allPageMenu = menuItemsNotFromCurrentCasinoItem.map(item => item.pageMenuId)
              let allAvailablePageMenu = await PageMenuModel.findAll({
                attributes: ['id'],
                where: {
                  id: {
                    [Op.in]: allPageMenu
                  }
                },
                raw: true
              })
              let allAvailablePageMenuIds = allAvailablePageMenu.map(item => item.id)
              let allPageMenuIds = allPageMenu.filter(item => !allAvailablePageMenuIds.includes(item))
              let allPageMenuIdsToBeDeleted = menuItemsNotFromCurrentCasinoItem.filter(item => allPageMenuIds.includes(item.pageMenuId))
              menuListToBeDeleted.push(...allPageMenuIdsToBeDeleted)

              // only delete the menu items that are belonging to the current page
              if (menuListToBeDeleted.length) {
                await MenuItemModel.destroy({
                  where: {
                    id: {
                      [Op.in]: menuListToBeDeleted.map(item => item.id)
                    },
                    casinoItemId: casinoItem.id

                  },
                  transaction: sequelizeTransaction
                })
              }
            }
          }
        }

      }

      await sequelizeTransaction.commit()

      // disable the category if all games in that category are inactive
      await sequelize.query(
        ` UPDATE casino_menus cm
        SET enabled = subquery.result
        FROM (
          SELECT
            ct.game_id AS game_category,
            CASE
              WHEN COUNT(*) = COUNT(CASE WHEN ci.active = false THEN 1 END) THEN false
              ELSE true
            END AS result
          FROM
            casino_tables ct
          JOIN
            casino_items ci ON ct.table_id = ci.uuid
          WHERE
            ci.provider = '${casinoProviderId}'
            AND ci.tenant_id = '${tenantId}' AND ct.provider_id = '${casinoProviderId}'
          GROUP BY
            ct.game_id
        ) AS subquery
        WHERE cm.name = subquery.game_category AND cm.tenant_id = '${tenantId}';
      `
      )

    } catch (error) {
        cronLog.status = CRON_LOG_STATUS.FAILED
        cronLog.errorMsg = error.message + ' TenantId: ' + tenantId
        cronLog.endTime = new Date()
        await db.CronLog.create(cronLog)
        Logger.info(error, '=========Lottery777 game population error==========\n')
        await sequelizeTransaction.rollback()
        continue
      }
    }

    let tenantIds = tenants.map(tenant => tenant.tenantId)
  if (reqBody?.override_other_providers || reqBody?.sync) {
      await syncOrOverrideCasinoProvider(tenantIds,  casinoProviderId, 'Lottery777', reqBody?.override_other_providers, reqBody?.sync )
  }

    cronLog.endTime = new Date()
    await db.CronLog.create(cronLog)
    return {
      success: true
    }
  } catch (error) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = error.message || null
    cronLog.endTime = new Date()
    await db.CronLog.create(cronLog)
    Logger.info(error, '=========Lottery777 game population error==========\n')
    return {
      success: false,
      Error: {
        stack: error.stack
      }
    }
  }
}
