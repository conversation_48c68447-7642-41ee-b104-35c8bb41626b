import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import { ALLOWED_PERMISSIONS, PROD_TENANTS, STAGE_TENANTS, CRON_LOG_STATUS } from './constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {

    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'update_player_category_score_wise_cron',
        status: 1
      },
      attributes: ['id']
    })

    if(!queueProcessStatus) return
    cronLog.cronId = queueProcessStatus?.id
    const tenants = config.get('env') === 'development' ? STAGE_TENANTS : PROD_TENANTS

    for (const [tenantId, tenant] of Object.entries(tenants)) {

      const tenantThemeSetting = await db.TenantThemeSetting.findOne({
        attributes: ['tenantId', 'allowedModules'],
        where: {
          tenantId: tenantId
        },
        raw: true
      });

      if (!tenantThemeSetting) continue

      const { allowedModules } = tenantThemeSetting;

      // Check if the tenant has the playerCategory module enabled
      const hasPlayerCategory = allowedModules
        ? allowedModules.split(',').map(module => module.trim()).includes(ALLOWED_PERMISSIONS.ENABLE_PLAYER_CATEGORY)
        : false;

      if (!hasPlayerCategory) continue

      // Proceed with calling the stored procedure
      const response = await sequelize.query('CALL update_user_category(:tenantId);', {
        replacements: { tenantId: tenantId },
      });
      console.log(`[Success]_update_user_category:_${tenantId}`,response);
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    cronLog.endTime = new Date()
    await db.CronLog.create(cronLog)
      console.log(`[Error]_update_user_category`,e);
    throw e
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
