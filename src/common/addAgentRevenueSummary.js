import { StatusCodes } from "http-status-codes";
import moment from 'moment';
import config from '../configs/app.config';
import { sequelize } from '../db/models';
import { AGENT_PLAYER_REVENUE_TRACKER_SP_TYPE, PROGRESS_TRACKER_STATUS } from "../utils/constants/constant";
import ErrorLogHelper from './errorLog';
moment.tz.setDefault("UTC");

export default async (body) => {
  try {
    const isProdEnv = config.get('env') === 'production';
    const startDate = '2023-09-01 18:30:00.000000'
    const endDate = '2025-06-01 18:29:59.999999'
    const beginningDate = moment(startDate).toISOString();
    const lastDate = moment(endDate).toISOString();
    let dateRanges;
    const chunkSizeInDays = 1;  // If the interval is 0, the date will be formatted as '2024-10-11 18:30:00.000000' to '2024-10-11 18:29:59.999999.'
    dateRanges = getDateRange(beginningDate, lastDate, chunkSizeInDays);
    let API_BASED_DATA_SEEDING = true

    // If API_BASED_DATA_SEEDING is false then it will only creates jobs
    if(body.process == false){
      API_BASED_DATA_SEEDING = false
    }

		await sequelize.query(`
			TRUNCATE TABLE agent_player_revenue_summary_progress_tracker;
			ALTER SEQUENCE agent_player_revenue_summary_progress_tracker_id_seq RESTART WITH 1;
	  `);

		// dateRanges.pop() if last_day is not mentioned
		const batchSize = 10; // Define the batch size
    const dateRangeBatches = splitIntoBatches(dateRanges, batchSize);

    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    // Mark all ranges as "Not Started" in the progress tracker table
		// Process batches sequentially
    for (const batch of dateRangeBatches) {
      await Promise.all(
        batch.map((range, index) =>
          updateProgressTracker(range.id, range, AGENT_PLAYER_REVENUE_TRACKER_SP_TYPE.UPDATE_PLAYER_SUMMARY_PROVIDER_WISE, PROGRESS_TRACKER_STATUS.NOT_STARTED, dateRanges.length, 0)
        )
      );
    }

    // If API_BASED_DATA_SEEDING is true then data seeding will takes place otherwise it will only creates pending jobs
    if(API_BASED_DATA_SEEDING){
      // Process each date range sequentially to avoid async issues
      let i = 0;
      while (i < dateRanges.length) {
        let query = `SELECT count(id) FROM public.agent_player_revenue_summary_progress_tracker WHERE status = 0;`
        const progressTrackerEntry = await sequelize.query(query, { type: sequelize.QueryTypes.SELECT, useMaster: true });

        // If user want to break the process then user can truncate the table agent_player_revenue_summary_progress_tracker
        if(progressTrackerEntry[0].count === '0'){
          break;
        }
        const transaction = await sequelize.transaction();
        let range, current_range_index, range_count;
        try {
          range = dateRanges[i];
          current_range_index = i+1;
          range_count = dateRanges.length;

          await delay(1000)

          await updateProgressTracker(range.id, range, AGENT_PLAYER_REVENUE_TRACKER_SP_TYPE.UPDATE_PLAYER_SUMMARY_PROVIDER_WISE, PROGRESS_TRACKER_STATUS.IN_PROGRESS, dateRanges.length, 0, transaction)

          await processRange(range, isProdEnv, range_count, current_range_index, transaction);

          await transaction.commit();

        } catch (error) {
          // Update progress to "Failed" in case of errors
          await transaction.rollback();
          await updateProgressTracker(range.id, range, 1, PROGRESS_TRACKER_STATUS.FAILED, range_count, current_range_index);
          await ErrorLogHelper.logError(error, null, null)
          throw error; // Re-throw the error to stop the process
        }
        i++;
      }
    }

    console.log(`[SUCCESS]_DATA_UPDATED_SUCCESSFULLY`);
    return { message: 'Data updated successfully!', status: StatusCodes.OK };
  } catch (error) {
    await ErrorLogHelper.logError(error, null, null)
    return { message: error.message, status: StatusCodes.INTERNAL_SERVER_ERROR };
  }
}

async function processRange(range, isProdEnv, range_count, current_range_index, transaction) {
  try {
    // Delete all records from player revenue tables for date
    let deleteQuery = `
      delete from player_provider_other_currency where player_summary_provider_id in (
        select id from player_summary_provider_wise where date = date('${range.to}'::timestamp)
      );
      delete from player_summary_provider_wise where date = date('${range.to}'::timestamp);
    `;

    await sequelize.query(deleteQuery, { transaction });

    const callPlayerSummaryProvWiseSp = `CALL update_player_summary_provider_wise_sp(
      '${range.from}'::TIMESTAMP,
      '${range.to}'::TIMESTAMP,
       ${isProdEnv}
    )`

    await sequelize.query(callPlayerSummaryProvWiseSp, { transaction });

    // Mark the range as "In Progress" in the progress tracker table
    await updateProgressTracker(range.id, range, AGENT_PLAYER_REVENUE_TRACKER_SP_TYPE.UPDATE_PLAYER_SUMMARY_PROVIDER_WISE, PROGRESS_TRACKER_STATUS.IN_PROGRESS, range_count, current_range_index, transaction);

    // Adding delay of 1 sec (1000 ms) between stored procedure calls
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
    await delay(1000)

    const callPlayerSummaryProvGameWiseTransSp = `CALL update_player_summary_provider_game_wise_transaction_sp(
      '${range.from}'::TIMESTAMP,
      '${range.to}'::TIMESTAMP,
       ${isProdEnv}
    )`

    await sequelize.query(callPlayerSummaryProvGameWiseTransSp, { transaction });

    // Update progress to "Completed" after successful processing
    await updateProgressTracker(range.id, range, AGENT_PLAYER_REVENUE_TRACKER_SP_TYPE.UPDATE_PLAYER_SUMMARY_PROVIDER_GAME_WISE_TRANS, PROGRESS_TRACKER_STATUS.IN_PROGRESS, range_count, current_range_index, transaction);

    await delay(1000)

    // Delete all records from agent revenue tables for date
    deleteQuery = `
      delete from agent_revenue_report_currency where agent_revenue_report_id in (
        select id from agent_revenue_report where date = date('${range.to}'::timestamp)
      );
      delete from agent_revenue_report where date = date('${range.to}'::timestamp);
    `;

    await sequelize.query(deleteQuery, { transaction });

    const callAgentRevenueReportSp = `CALL update_agent_revenue_report(
      '${range.from}'::TIMESTAMP,
      '${range.to}'::TIMESTAMP,
      ${isProdEnv},
      '${range.from}'::TIMESTAMP,
      '${range.to}'::TIMESTAMP
    )`

    await sequelize.query(callAgentRevenueReportSp, { transaction });

    // Update progress to "Completed" after successful processing
    await updateProgressTracker(range.id, range, AGENT_PLAYER_REVENUE_TRACKER_SP_TYPE.UPDATE_AGENT_REVENUE_REPORT, PROGRESS_TRACKER_STATUS.COMPLETED, range_count, current_range_index, transaction);

  } catch (error) {
    console.error(`[ERROR]_Error_processing_range: ${range.from} - ${range.to}`, error);
    await updateProgressTracker(range.id, range, 1, PROGRESS_TRACKER_STATUS.FAILED, range_count, current_range_index);
    await ErrorLogHelper.logError(error, null, null)
    throw error;
  }
}

async function updateProgressTracker(id, range, type, status, range_count, current_range_index, transaction) {
  const query = `
    INSERT INTO public.agent_player_revenue_summary_progress_tracker (id, range_from, range_to, type, status, range_count, current_range_index, created_at, updated_at)
    VALUES (:id, :rangeFrom, :rangeTo, :type, :status, :range_count, :current_range_index, NOW(), NOW())
    ON CONFLICT (range_from, range_to)
    DO UPDATE SET type = :type, status = :status, current_range_index = :current_range_index, updated_at = NOW()
  `;

  await sequelize.query(query, {
    replacements: {
      id: id,
      rangeFrom: range.from,
      rangeTo: range.to,
      type: type,
      status: status,
      range_count:range_count,
      current_range_index: current_range_index
    },
    ...(transaction && { transaction })
  });

  // console.log(`[Progress_Tracker] Range: ${range.from} - ${range.to}, Status: ${status}`);
}

function getDateRange(beginningDate, lastDate, chunkSizeInDays) {
  let start = moment(beginningDate).startOf('day').add(18, 'hours').add(30, 'minutes'); // Set the start time to 18:30:00
  const end = moment(lastDate).startOf('day').add(18, 'hours').add(30, 'minutes'); // Ensure the end time matches the format
  // const end = moment(lastDate).startOf('day').add(18, 'hours').add(30, 'minutes'); // Ensure the end time matches the format
  const ranges = [];

  let id = 0;
  while (start.isBefore(end)) {
    let chunkEnd = moment(start).add(chunkSizeInDays, 'days').subtract(1, 'seconds'); // Adjust the end time to 18:29:59
    if (chunkEnd.isAfter(end)) {
      chunkEnd = end.subtract(1, 'seconds');
    }

    ranges.push({
      id: ++id,
      from: start.format('YYYY-MM-DD 18:30:00.000000'),
      to: chunkEnd.format('YYYY-MM-DD 18:29:59.999999')
    });

    start = moment(chunkEnd).add(1, 'seconds'); // Start from the next interval
  }
  return ranges;
}

// Helper function to split an array into smaller batches
function splitIntoBatches(data, batchSize) {
  const batches = [];
  for (let i = 0; i < data.length; i += batchSize) {
    batches.push(data.slice(i, i + batchSize));
  }
  return batches;
}
