import { CRON_LOG_STATUS } from '../common/constants'
import db, { sequelize } from '../db/models'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try{
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'archive_transaction_cron',
        status: 1
      },
      attributes: ['id']
    })
    if(queueProcessStatus){
      cronLog.cronId = queueProcessStatus?.id
      await sequelize.query(`call archive_transactions();` )
    }
  }catch(e){
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
