import db from '../db/models';
import { ERROR_LOG_TYPE } from '../common/constants'
export default class ErrorLogHelper {
  static async logError (error, context, user) {
    const {
      ErrorLog: ErrorLogModel
    } = db

    const statusToLog = this.getHttpStatus(error);
    const descriptionToLog = error?.message;
    const hostUrlToLog = context?.req?.url || 'Unknown';
    const userId = user ? user?.id : null;
    const userParentType = user ? user?.parentType : null;
    const tenantId = user ? user?.tenantId : null;

    const { fileName, lineNumber } = this.getFileAndLineNumber(error);

    const errorLog = {
      hostUrl: hostUrlToLog,
      errorMessage: descriptionToLog,
      fileName: fileName,
      errorLineNo: lineNumber,
      errorStatus: statusToLog,
      stackTrace: error?.stack ? JSON.stringify(error?.stack) : null,
      adminId: userId,
      adminType: userParentType,
      exceptionCode: error?.code && typeof error?.code === 'number' ? error.code : null,
      functionName: this.getFunctionName(error),
      tenantId,
      type: ERROR_LOG_TYPE.QUEUE
    };

    try {
      await ErrorLogModel.create(errorLog);
    } catch (err) {
      console.error('FailedToLogError:', err);
    }
  }

  static getHttpStatus (error) {
    if (error?.status) {
      return error?.status;
    }

    return 417; // HTTP_EXPECTATION_FAILED equivalent
  }

  static getFunctionName (error) {
    if (error?.stack) {
      const stackLines = error?.stack.split('\n');
      for (const line of stackLines) {
        if (line.includes('at ')) {
          const match = line.match(/at (\S+)/);
          if (match) {
            return match[1];
          }
        }
      }
    }

    return 'Unknown';
  }

  static getFileAndLineNumber (error) {
    if (error?.stack) {
      const stackLines = error?.stack.split('\n');
      console.log('stackLines=', stackLines);

      const line = stackLines[1]; // The second line usually contains the file and line number info
      const match = line.match(/(?:\()([^:]+):(\d+):\d+(?:\))/);

      if (match) {
        return {
          fileName: match[1],
          lineNumber: match[2]
        };
      }
    }

    return { fileName: 'Unknown', lineNumber: 'Unknown' };
  }

}
