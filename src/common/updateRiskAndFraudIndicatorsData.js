import { QueryTypes } from 'sequelize';
import db, { sequelize } from '../db/models';
import { CRON_LOG_STATUS } from './constants';
import ErrorLogHelper from './errorLog';

export default async () => {
  // Start transaction
  const transaction = await sequelize.transaction();

  const cronLog = {};
  cronLog.startTime = new Date();
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'financial_overview_tracker_cron',
        status: 1
      },
      attributes: ['id']
    })

    if (queueProcessStatus){
      cronLog.cronId = queueProcessStatus.id;

      // Get updated fraud and risk data
      const getQuery = getRiskAndFraudDataQuery();
      const riskAndFraudData = await sequelize.query(getQuery, { type: QueryTypes.SELECT, useMaster: false });

      // Truncate table and reset sequence in a transaction
      let deleteQuery = `
        TRUNCATE TABLE risk_fraud_indicators;
        ALTER SEQUENCE risk_fraud_indicators_id_seq RESTART WITH 1;
      `;
      await sequelize.query(deleteQuery, { transaction });

      // Insert data in a transaction
      await db.RiskFraudIndicators.bulkCreate(riskAndFraudData, { transaction, returning: false });

      // Commit transaction
      await transaction.commit();
    }
  } catch (e) {
    console.log("Upadte_risk_and_fraud_indicators_cron_error:", e)
    // Rollback transaction
    await transaction.rollback();

    cronLog.status = CRON_LOG_STATUS.FAILED;
    cronLog.errorMsg = e.message || null;
    await ErrorLogHelper.logError(e, null, null)
  }
  cronLog.endTime = new Date();
  await db.CronLog.create(cronLog);
}

function getRiskAndFraudDataQuery() {
  return `
  SET enable_seqscan = OFF;

  WITH unusual_bets_wins AS (
    WITH agg_data AS (
      SELECT
        actionee_id AS user_id,
        MAX(amount) FILTER (WHERE created_at >= NOW() - INTERVAL '90 days' AND transaction_type IN (0, 8, 20, 21, 23, 24, 27, 28, 32, 34, 36, 46, 54, 55, 56, 61, 62, 64, 66)) AS max_latest_bet,
        AVG(amount) FILTER (WHERE created_at >= NOW() - INTERVAL '90 days' AND transaction_type IN (0, 8, 20, 21, 23, 24, 27, 28, 32, 34, 36, 46, 54, 55, 56, 61, 62, 64, 66)) AS avg_latest_bet,
        MAX(amount) FILTER (WHERE transaction_type IN (0, 8, 20, 21, 23, 24, 27, 28, 32, 34, 36, 46, 54, 55, 56, 61, 62, 64, 66)) AS max_lifetime_bet,
        AVG(amount) FILTER (WHERE transaction_type IN (0, 8, 20, 21, 23, 24, 27, 28, 32, 34, 36, 46, 54, 55, 56, 61, 62, 64, 66)) AS avg_lifetime_bet,
        MAX(amount) FILTER (WHERE created_at >= NOW() - INTERVAL '90 days' AND transaction_type IN (1, 9, 22, 25, 26, 29, 30, 31, 33, 35, 53, 57, 58, 63, 65, 67)) AS max_latest_win,
        AVG(amount) FILTER (WHERE created_at >= NOW() - INTERVAL '90 days' AND transaction_type IN (1, 9, 22, 25, 26, 29, 30, 31, 33, 35, 53, 57, 58, 63, 65, 67)) AS avg_latest_win,
        MAX(amount) FILTER (WHERE transaction_type IN (1, 9, 22, 25, 26, 29, 30, 31, 33, 35, 53, 57, 58, 63, 65, 67)) AS max_lifetime_win,
        AVG(amount) FILTER (WHERE transaction_type IN (1, 9, 22, 25, 26, 29, 30, 31, 33, 35, 53, 57, 58, 63, 65, 67)) AS avg_lifetime_win
      FROM transactions
      WHERE transaction_type IN (
        0, 8, 20, 21, 23, 24, 27, 28, 32, 34, 36, 46, 54, 55, 56, 61, 62, 64, 66,
        1, 9, 22, 25, 26, 29, 30, 31, 33, 35, 53, 57, 58, 63, 65, 67
      )
      GROUP BY user_id
    )
    SELECT
      user_id,
      CASE
        WHEN max_latest_bet > (2 * avg_latest_bet) THEN true
        ELSE false
      END AS unusual_bet_size,
      CASE
        WHEN max_lifetime_bet > (2 * avg_lifetime_bet) THEN true
        ELSE false
      END AS lifetime_unusual_bet_size_medium,
      CASE
        WHEN max_lifetime_bet > (3 * avg_lifetime_bet) THEN true
        ELSE false
      END AS lifetime_unusual_bet_size_high,
      CASE
        WHEN max_latest_win > (2 * avg_latest_win) THEN true
        ELSE false
      END AS unusual_win_size_medium,
      CASE
        WHEN max_latest_win > (3 * avg_latest_win) THEN true
        ELSE false
      END AS unusual_win_size_high,
      CASE
        WHEN max_lifetime_win > (2 * avg_lifetime_win) THEN true
        ELSE false
      END AS lifetime_unusual_win_size_medium,
      CASE
        WHEN max_lifetime_win > (3 * avg_lifetime_win) THEN true
        ELSE false
      END AS lifetime_unusual_win_size_high
    FROM agg_data
  ),
  deposit_spikes_data AS (
    SELECT
      wallets.owner_id AS user_id,
      true AS deposit_spikes
    FROM transactions
      JOIN wallets ON (wallets.id = transactions.target_wallet_id AND wallets.owner_type = 'User')
    WHERE transactions.transaction_type = 3
      AND transactions.created_at BETWEEN NOW() - INTERVAL '90 days' AND NOW()
    GROUP BY wallets.owner_id
    HAVING MAX(transactions.amount) > (2 * AVG(transactions.amount))
  ),
  withdraw_spikes AS (
    WITH agg_data AS (
      SELECT
        wallets.owner_id AS user_id,
        MAX(transactions.amount) FILTER (WHERE transactions.created_at >= NOW() - INTERVAL '90 days') AS max_latest_withdrawal,
        AVG(transactions.amount) FILTER (WHERE transactions.created_at >= NOW() - INTERVAL '90 days') AS avg_latest_withdrawal,
        MAX(transactions.amount) AS max_lifetime_withdrawal,
        AVG(transactions.amount) AS avg_lifetime_withdrawal
      FROM transactions
        JOIN wallets ON (wallets.id = transactions.source_wallet_id AND wallets.owner_type = 'User')
      WHERE transactions.transaction_type = 4
        AND status = 'success'
        AND (comments NOT IN ('cancelled by admin', 'cancelled by the player', 'Pending confirmation from admin')
          OR comments IS NULL
        )
        AND created_at <= NOW()
      GROUP BY wallets.owner_id
    )
    SELECT
      user_id,
      CASE
        WHEN max_latest_withdrawal > (2 * avg_latest_withdrawal) THEN true
        ELSE false
      END AS withdrawal_spikes_medium,
      CASE
        WHEN max_latest_withdrawal > (3 * avg_latest_withdrawal) THEN true
        ELSE false
      END AS withdrawal_spikes_high,
      CASE
        WHEN max_lifetime_withdrawal > (2 * avg_lifetime_withdrawal) THEN true
        ELSE false
      END AS lifetime_withdrawal_spikes_medium,
      CASE
        WHEN max_lifetime_withdrawal > (3 * avg_lifetime_withdrawal) THEN true
        ELSE false
      END AS lifetime_withdrawal_spikes_high
    FROM agg_data
  ),
  multiple_payment_methods AS (
    SELECT
      actionee_id AS user_id,
      TRUE AS multiple_payment_methods_used
    FROM transactions
    WHERE
      actionee_type = 'User'
      AND transaction_type IN (3, 4, 16)
      AND payment_provider_id IS NOT NULL
      AND created_at BETWEEN NOW() - INTERVAL '90 days' AND NOW()
    GROUP BY user_id
    HAVING COUNT(DISTINCT payment_provider_id) > 1
  ),
  different_ip_logins AS (
    SELECT
      user_id,
      TRUE AS frequent_logins_different_ips
    FROM user_login_history
    WHERE
      created_at >= NOW() - INTERVAL '90 days'
    GROUP BY user_id
    HAVING COUNT(DISTINCT ip) > 1
  ),
  cal_suspicious_risk_score AS (
    SELECT
      u.id AS user_id,
      u.tenant_id,
      dsd.deposit_spikes,
      ws.withdrawal_spikes_medium AS withdrawal_spikes,
      ubw.unusual_bet_size,
      mpm.multiple_payment_methods_used,
      dil.frequent_logins_different_ips,
      (
        (CASE WHEN dsd.deposit_spikes IS TRUE THEN 1 ELSE 0 END) +
        (CASE WHEN ws.withdrawal_spikes_medium IS TRUE THEN 1 ELSE 0 END) +
        (CASE WHEN ubw.unusual_bet_size IS TRUE THEN 1 ELSE 0 END) +
        (CASE WHEN mpm.multiple_payment_methods_used IS TRUE THEN 1 ELSE 0 END) +
        (CASE WHEN dil.frequent_logins_different_ips IS TRUE THEN 1 ELSE 0 END)
      ) >= 3 AS suspicious_account_behavior,
      CASE
        WHEN
          ws.withdrawal_spikes_high IS TRUE AND
          ubw.unusual_bet_size IS TRUE AND
          ubw.unusual_win_size_high IS TRUE AND
          mpm.multiple_payment_methods_used IS TRUE AND
          dil.frequent_logins_different_ips IS TRUE
        THEN 3

        WHEN
          ws.withdrawal_spikes_medium IS TRUE AND
          ubw.unusual_bet_size IS TRUE AND
          ubw.unusual_win_size_medium IS TRUE AND
          (mpm.multiple_payment_methods_used IS TRUE OR dil.frequent_logins_different_ips IS TRUE)
        THEN 2

        ELSE 1
      END AS risk_score,
      ubw.lifetime_unusual_bet_size_high,
      ubw.lifetime_unusual_win_size_high,
      ws.lifetime_withdrawal_spikes_high,
      ubw.lifetime_unusual_bet_size_medium,
      ubw.lifetime_unusual_win_size_medium,
      ws.lifetime_withdrawal_spikes_medium
    FROM
      users u
      LEFT JOIN unusual_bets_wins ubw ON (u.id = ubw.user_id)
      LEFT JOIN deposit_spikes_data dsd ON (u.id = dsd.user_id)
      LEFT JOIN withdraw_spikes ws ON (u.id = ws.user_id)
      LEFT JOIN multiple_payment_methods mpm ON (u.id = mpm.user_id)
      LEFT JOIN different_ip_logins dil ON (u.id = dil.user_id)
  )
  SELECT
    user_id AS "userId",
    tenant_id AS "tenantId",
    COALESCE(deposit_spikes, FALSE) AS "depositSpikes",
    COALESCE(withdrawal_spikes, FALSE) AS "withdrawalSpikes",
    COALESCE(unusual_bet_size, FALSE) AS "unusualBetSize",
    COALESCE(multiple_payment_methods_used, FALSE) AS "multiplePaymentMethodsUsed",
    COALESCE(frequent_logins_different_ips, FALSE) AS "frequentLoginsDifferentIps",
    suspicious_account_behavior AS "suspiciousAccountBehavior",
    risk_score AS "riskScore",
    CASE
      WHEN
        risk_score = 3
        AND lifetime_unusual_bet_size_high IS TRUE
        AND lifetime_unusual_win_size_high IS TRUE
        AND lifetime_withdrawal_spikes_high IS TRUE
      THEN 3

      WHEN
        risk_score = 2
        AND lifetime_unusual_bet_size_medium IS TRUE
        AND lifetime_unusual_win_size_medium IS TRUE
        AND lifetime_withdrawal_spikes_medium IS TRUE
      THEN 2

      ELSE 1
    END AS "potentialFraudRisk"
  FROM
    cal_suspicious_risk_score;

  SET enable_seqscan = ON;
  `;
}
