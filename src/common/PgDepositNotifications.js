import db from '../db/models'
/**
 * @export
 * @param {*} depositId
 * @param {*} context
 * @param {*} userId
 */
export default async function PgDepositNotification (depositId, sequelizeTransaction, userId, amount) {
  const {
    Notification: NotificationModel,
    NotificationReceiver: NotificationReceiverModel,
    User: UserModel
  } = db

  const userInfo = (await UserModel.findOne({ attributes: ['parentId', 'userName'], where: { id: userId }, raw: true }))
  const userOwnerId = userInfo.parentId

  const notificationSender = { senderId: userId, senderType: 'User', referenceType: 'Deposit' }
  const notificationReference = {
    referenceId: depositId,
    message: `${userInfo.userName}, deposit of amount ${amount} was successful.`
  }

  const notifications = {
    ...notificationReference,
    ...notificationSender
  }
  try {
    const notificationInserted = await NotificationModel.create(notifications, { transaction: sequelizeTransaction })

    const notificationReceiver = {
      receiverId: userOwnerId,
      receiverType: 'AdminUser',
      isRead: false
    }
    const notificationReference = { ...notificationReceiver, notificationId: notificationInserted.id }
    await NotificationReceiverModel.create(notificationReference, { transaction: sequelizeTransaction })

  } catch (e) {
    console.log("-----------error in Pg Deposit notifications--------",e)
    throw new Error(e)
  }
}
