import { Sequelize } from 'sequelize'
import db from '../db/models'
import { CRON_LOG_STATUS } from '../common/constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'delete_spribe_token_cron',
        status: 1
      },
      attributes: ['id']
    })
    if(queueProcessStatus){
      cronLog.cronId = queueProcessStatus?.id
      await db.SpribeAuthToken.destroy(
        {
          where: {
            createdAt: Sequelize.literal("created_at < now() - interval '24 hours'")
          }
        }
      )
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log("==========error", e)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
