import { Op, Sequelize } from 'sequelize'
import db, { sequelize } from '../db/models'
import { BONUS_STATUS, BONUS_TYPES, CRON_LOG_STATUS } from '../common/constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      attributes: ['id'],
      where: {
        service: 'update_losing_bonus_status_cron',
        status: 1
      }
    })
    if (queueProcessStatus) {
      cronLog.cronId = queueProcessStatus?.id
      const currentDate = new Date()
      const previousDate = new Date(currentDate)
      previousDate.setDate(currentDate.getDate() - 1)
      const losingBonus = await db.Bonus.findAll({
        attributes: ['id'],
        where: {
          kind: BONUS_TYPES.LOSING,
          valid_upto: {
            [Op.between]: [previousDate, currentDate]
          }
        },
        include: {
          model: db.LosingBonusSetting,
          attributes: ['bonus_claim_type'],
          where: {
            bonus_claim_type: 'manual'
          }
        },
        raw: true
      })
      const losingbonusIds = losingBonus.map(bonus => bonus.id)

      await db.UserBonus.update(
        { status: BONUS_STATUS.CLAIMED },
        {
          where: {
            bonus_id: {
              [Op.in]: losingbonusIds
            },
            claimed_at: {
              [Op.ne]: null
            },
            status: BONUS_STATUS.ACTIVE
          }
        })
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log('==========error=========', e)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
