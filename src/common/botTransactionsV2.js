import np from 'number-precision';
import { Op, Sequelize } from 'sequelize';
import { v4 as uuidv4 } from 'uuid';
import config from '../configs/app.config';
import db, { sequelize } from '../db/models';
import { CRON_LOG_STATUS, EVOLUTION_PROVIDER, PROD_BOT_ACTIVITY_TENANTS, STAGE_BOT_ACTIVITY_TENANTS, TRANSACTION_TYPES } from './constants';
import { numberPrecision } from './helpers';
import v3CurrencyConversion from './v3CurrencyConversion';
import { walletLocking } from './walletLocking';

// Global check to trace activity status
let isCronRunning = false

const gameWiseWinRatios = {
  // '130': [0, 2, 2, 2, 2], // 'Super 6 Baccarat'         // Ezugi
  // '221002': [0, 2, 2, 2, 2], //  'Speed Auto Roulette'  // Ezugi
  // '1000103': [0, 2, 2, 2, 2], //  'Speed Auto Roulette' // Ezugi

  '1000104': [0, 2, 0, 2, 0], // 'Speed Roulette'       // Evolution
  '1000103': [0, 2, 0, 2, 0], //  'Speed Auto Roulette' // Evolution
  '1000417': [0, 2, 0, 2, 0], //  'Hindi Speed Baccarat A' // Evolution
  '1000021': [0, 2, 0, 2, 0], //  'Speed Baccarat A'  // Evolution
  '1000129': [0, 2, 0, 2, 0], //  'Speed Baccarat J'  // Evolution
  '1000271': [0, 2, 0, 2, 0], //  'Speed Baccarat S'  // Evolution
  '1000429': [0, 2, 0, 2, 0], //  'Speed Baccarat U'  // Evolution
  '1000432': [0, 2, 0, 2, 0], //  'Speed Baccarat X'  // Evolution
}

// Working 5,8 Ratios
// '1000104': [0, 2, 5, 2, 5, 0, 8, 2, 5, 2], // 'Speed Roulette'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  let cronData = {}
  let cronCreated;
  let ggrSummary = null
  const responseObject = {
    success: false,
  }

  try {
    const {
      CasinoTable: CasinoTableModel,
      Wallet: WalletModel,
      Transaction: TransactionModel,
      TenantGGRSummary: TenantGGRSummaryModel,
    } = db

    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'bot_transactions_process',
        status: 1
      },
      attributes: ['id']
    })
    if (!queueProcessStatus) {
      await TenantGGRSummaryModel.update({
        status: 3,
        message: "Main cron stopped.",
      }, {
        where: {
          status: 1
        },
        raw: true,
      })
      return { success: false, message: 'Main cron stopped.' }
    }

    let isInProgress = await TenantGGRSummaryModel.findOne({
      where: {
        status: 2
      },
      raw: true,
      order: [['createdAt', 'desc']],
    })

    let whereClause = {
      status: 1
    }

    if (isInProgress) {
      if (isCronRunning) {
        return { success: false, message: 'Activity in progress.' }
      } else {
        whereClause = {
          id: isInProgress.id
        }
      }
    }

    cronLog.cronId = queueProcessStatus?.id

    ggrSummary = await TenantGGRSummaryModel.findOne({
      where: whereClause,
      raw: true,
      order: [['createdAt', 'desc']],
    })

    if (!ggrSummary) {
      responseObject.data = {
        message: "No GGR Summary"
      }
      return responseObject
    }

    // Mark In Progress
    await TenantGGRSummaryModel.update({
      status: 2,
    }, {
      where: {
        id: ggrSummary.id
      },
      raw: true,
    })

    isCronRunning = true;

    const tenantId = parseFloat(ggrSummary.tenantId)

    const botActivityTenants = config.get('env') === 'production' ? PROD_BOT_ACTIVITY_TENANTS : STAGE_BOT_ACTIVITY_TENANTS;
    if (!botActivityTenants.includes(tenantId)) {
      responseObject.data = {
        message: "Bot activity disabled for this tenant"
      }
      return responseObject
    }

    let currentGgr = ggrSummary.currentGgr || 0;
    let currentTotalDebitAmount = ggrSummary.totalDebitAmount || 0
    let currentTotalCreditAmount = ggrSummary.totalCreditAmount || 0
    let currentTotalRollbackAmount = ggrSummary.totalRollbackAmount || 0

    cronData.currentGgr = currentGgr
    cronData.totalDrAmount = currentTotalDebitAmount
    cronData.totalCrAmount = currentTotalCreditAmount
    cronData.totalRbAmount = currentTotalRollbackAmount

    cronData.message = "Cron Started"
    cronLog.errorMsg = JSON.stringify(cronData)
    cronCreated = isInProgress ? { id: isInProgress.cronId } : await db.CronLog.create(cronLog)

    let allBotUsers = await sequelize.query(
      `SELECT bu.user_id
       FROM bot_users bu
       JOIN Users u ON bu.user_id = u.id
       JOIN wallets w ON bu.user_id = w.owner_id
       WHERE bu.tenant_id = :tenantId
       AND u.active = true AND u.tenant_id = :tenantId
       AND (w.amount + w.non_cash_amount) >= 100`,
      {
        replacements: { tenantId },
        type: Sequelize.QueryTypes.SELECT
      }
    );

    allBotUsers = allBotUsers.map(el => { return el.user_id })

    allBotUsers = shuffleArray(allBotUsers)

    const providerId = config.get('env') === 'production' ? EVOLUTION_PROVIDER.PROD : EVOLUTION_PROVIDER.STAGE

    let allGames = await CasinoTableModel.findAll({
      where: {
        providerId: {
          [Op.in]: [providerId]
        },
        tableId: {
          [Op.in]: ['1000104', '1000103', '1000417', '1000021', '1000129', '1000271', '1000429', '1000432']
        }
      },
      attributes: ['gameId', 'tableId', 'providerId'],
      raw: true
    })

    if (allGames.length == 0) {
      cronData.message = 'No Games Found'
      await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)
      responseObject.data = cronData
      return responseObject;
    }

    allGames = shuffleArray(allGames)

    const rollbackPercentage = 0

    let minDebitAmount = ggrSummary.minDebitAmount;
    let maxDebitAmount = ggrSummary.maxDebitAmount;

    const targetGGR = ggrSummary.ggr

    const tolerance = getTolerance(targetGGR, 10)

    let noOfTransactionsAdded = ggrSummary.totalDebitTrxns || 0;
    let totalRollbackTrxns = ggrSummary.totalRollbackTrxns || 0

    cronData.targetGGR = targetGGR
    cronData.error = ''
    cronData.message = ''

    let isTargetReached = checkIsTargetGGRAchieved(currentGgr, targetGGR);

    if (isTargetReached) {
      await TenantGGRSummaryModel.update({
        status: 4,
        message: 'Target GGR Achieved'
      }, {
        where: {
          id: ggrSummary.id
        },
        raw: true,
      })
      cronLog.status = CRON_LOG_STATUS.SUCCESS
      cronData.message = 'Target GGR Achieved'
      await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)
    }

    let userId;
    let gameData;
    let currentUsersTransactions = 0;

    let lastUserId;
    let lastTableId;
    let lastBetAmount;

    // Predefine random transaction limits
    const transactionLimits = Array.from({ length: 20 }, () => Math.floor(Math.random() * 10) + 20);

    let currentLimitIndex = 0; // Track which limit we are using
    const accepted = []
    let isCancelled = false
    while (!isTargetReached) {
      // Add delay here to avoid frequent sequelize transactions
      await sleep(100)

      if (allBotUsers.length == 0) {
        cronData.message = 'Bot Users Finished'
        break
      }

      isCancelled = await checkIsCancelled(ggrSummary.id)
      if (isCancelled) {
        cronData.message = 'Cancelled.'
        break
      }

      const sequelizeTransaction = await sequelize.transaction()
      try {

        if (!userId || !gameData || currentUsersTransactions >= transactionLimits[currentLimitIndex] || !allBotUsers.includes(userId)) {
          userId = allBotUsers[Math.floor(Math.random() * allBotUsers.length)];
          gameData = allGames[Math.floor(Math.random() * allGames.length)];

          // Move to the next limit in the array (loop back if at the end)
          currentLimitIndex = (currentLimitIndex + 1) % transactionLimits.length;

          currentUsersTransactions = 0; // Reset transaction count

          // If user is same and game is Changed then create a manual delay of 1 minute.
          if (lastUserId == userId && lastTableId != gameData.tableId) {
            await sleep(60 * 1000)
          }

          lastUserId = userId
          lastTableId = gameData.tableId
        }

        const gameId = gameData.gameId
        const tableId = gameData.tableId
        const debitTransactionID = uuidv4().replace(/-/g, '')
        const creditTransactionID = uuidv4().replace(/-/g, '')
        const rollbackTransactionID = uuidv4().replace(/-/g, '')
        const providerId = gameData.providerId
        const roundId = `${cronCreated.id}____` + uuidv4().replace(/-/g, '')

        const query = `SELECT * FROM get_auth_user_information_by_user_id(:userId, '');`
        const [user] = await sequelize.query(query, {
          replacements: { userId },
          type: sequelize.QueryTypes.SELECT,
          useMaster: false
        })

        let debitCashAmount = 0;
        let debitNonCashAmount = 0;

        const userWallet = await walletLocking(user, sequelizeTransaction)
        await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })

        let walletNonCashAmount = parseFloat(userWallet.nonCashAmount).toFixed(5)
        let walletCashAmount = parseFloat(userWallet.amount).toFixed(5)

        let totalUserWalletAmount = np.plus(walletNonCashAmount, walletCashAmount)

        if (totalUserWalletAmount < 100) {
          allBotUsers = allBotUsers.filter(el => el !== userId);
          userId = null
          gameData = null
          await sequelizeTransaction.rollback()
          continue
        }

        // Function to generate random transaction (debit, credit, or rollback)
        const transaction = generateTransaction(minDebitAmount, maxDebitAmount, rollbackPercentage, tableId, totalUserWalletAmount);
        // ---------------------------Debit Transacation Starts here------------------
        let betAmount = transaction.debitAmount;
        let wonAmount = transaction.creditAmount;

        if (betAmount < 100) {
          await sequelizeTransaction.rollback()
          continue;
        }

        if (betAmount == 0) {
          await sequelizeTransaction.rollback()
          continue;
        }

        if (betAmount == lastBetAmount) {
          await sequelizeTransaction.rollback();
          continue;
        }

        let acceptedPercentageChange;

        if (targetGGR < 100000) {
          acceptedPercentageChange = 0.3
        }
        else if (targetGGR < 300000) {
          acceptedPercentageChange = 0.25
        }
        else if (targetGGR < 500000) {
          acceptedPercentageChange = 0.25
        }
        else {
          acceptedPercentageChange = 0.2
        }

        const thisGGR = wonAmount - betAmount
        if (thisGGR > targetGGR * acceptedPercentageChange) {
          await sequelizeTransaction.rollback();
          continue;
        }

        let totalDebitAmountWillBe = currentTotalDebitAmount + betAmount;
        let totalCreditAmountWillBe = currentTotalCreditAmount + (transaction.result != 'rollback' ? wonAmount : 0);
        let totalRollbackAmountWillBe = currentTotalRollbackAmount + (transaction.result == 'rollback' ? betAmount : 0);

        let ggrWillBe = totalCreditAmountWillBe - (totalDebitAmountWillBe - totalRollbackAmountWillBe);

        if (ggrWillBe < 0) {
          await sequelizeTransaction.rollback()
          continue
        }

        let isTargetWillBeAchieved = checkIsTargetGGRAchieved(ggrWillBe, targetGGR)

        if (isTargetWillBeAchieved && ggrWillBe > targetGGR + tolerance) {
          await sequelizeTransaction.rollback()
          continue
        }

        if (isTargetWillBeAchieved && ggrWillBe != targetGGR && ggrWillBe > targetGGR) {
          let ggrDifference = ggrWillBe - targetGGR
          const currentTargetGgr = thisGGR - ggrDifference;

          const isAdjusted = await adjustBetAndWin(betAmount, wonAmount, currentTargetGgr);
          if (isAdjusted) {
            betAmount = isAdjusted.bet
            wonAmount = isAdjusted.win
          }
        }

        // Next GGR Change should not be less than 70 % of achieved ggr
        if (ggrWillBe < currentGgr * 0.7) {
          await sequelizeTransaction.rollback()
          continue
        }

        lastBetAmount = betAmount

        // Add delay in every transaction
        const trxnGapInSeconds = Math.floor(Math.random() * 10) + 20; //(20-30)

        if (noOfTransactionsAdded > 0) { // Skipping first transaction
          await sleep(trxnGapInSeconds * 1000)
        }

        const betTime = new Date();

        // Add delay in win
        const gapInSeconds = Math.floor(Math.random() * 10) + 25  // (25-35)
        await sleep(gapInSeconds * 1000)

        const crediTime = new Date();

        isCancelled = await checkIsCancelled(ggrSummary.id)
        if (isCancelled) {
          await sequelizeTransaction.rollback()
          cronData.message = 'Cancelled.'
          break
        }

        accepted.push({
          betAmount: betAmount,
          wonAmount: wonAmount,
        })

        let transactionObject = {
          actioneeId: userId,
          actioneeType: 'User',
          tenantId: tenantId,
          gameId: gameId,
          success: true,
          roundId: '' + roundId,
          status: 'success',
          seatId: tableId,
          tableId: tableId,
          conversionRate: +user.exchangeRate,
          providerId: providerId,
          createdAt: betTime,
          updatedAt: betTime
        }

        const trxnBetAmount = betAmount;
        const trxnWonAmount = wonAmount;

        const allTransactions = [];

        let debitNonCashTransactionObj = null
        let debitCashTransacationObj = null

        let creditCashTransactionObj = null;
        let creditNonCashTransactionObj = null;

        let rollbackNonCashTransactionObj = null;
        let rollbackCashTransactionObj = null;

        if (walletNonCashAmount > 0) {
          debitNonCashAmount = parseFloat(betAmount) > (+walletNonCashAmount) ? walletNonCashAmount : parseFloat(betAmount)

          betAmount = parseFloat(betAmount) > (+walletNonCashAmount) ? np.minus(parseFloat(betAmount), walletNonCashAmount) : 0
          debitNonCashTransactionObj = {
            ...transactionObject,
            transactionType: TRANSACTION_TYPES.DEBIT_NO_CASH,
            transactionId: debitTransactionID,
            amount: debitNonCashAmount,
            sourceWalletId: +userWallet.id,
            sourceCurrencyId: +userWallet.currencyId
          }

          walletNonCashAmount = np.minus(walletNonCashAmount, debitNonCashAmount)
        }

        if (betAmount > 0) {
          debitCashAmount = betAmount

          debitCashTransacationObj = {
            ...transactionObject,
            transactionType: TRANSACTION_TYPES.DEBIT,
            transactionId: debitTransactionID,
            sourceWalletId: +userWallet.id,
            sourceCurrencyId: +userWallet.currencyId,
            amount: debitCashAmount
          }

          walletCashAmount = np.minus(walletCashAmount, parseFloat(debitCashAmount))
        }

        if (debitNonCashTransactionObj) {

          debitNonCashTransactionObj = {
            ...debitNonCashTransactionObj,
            sourceBeforeBalance: np.plus(walletNonCashAmount, debitNonCashAmount),
            sourceAfterBalance: walletNonCashAmount,
          }
          debitNonCashTransactionObj = await v3CurrencyConversion(sequelizeTransaction, debitNonCashTransactionObj, user?.currencyId, tenantId, debitNonCashTransactionObj.amount)
        }

        if (debitCashTransacationObj) {
          debitCashTransacationObj = {
            ...debitCashTransacationObj,
            sourceBeforeBalance: np.plus(walletCashAmount, debitCashAmount),
            sourceAfterBalance: walletCashAmount,
          }
          debitCashTransacationObj = await v3CurrencyConversion(sequelizeTransaction, debitCashTransacationObj, user?.currencyId, tenantId, debitCashTransacationObj.amount)
        }
        // -----------------------------------------Debit End--------------------------------------------------------------

        transactionObject.createdAt = crediTime
        transactionObject.updatedAt = crediTime
        if (transaction.result === 'credit') {

          let creditNonCashAmount = 0;
          let creditCashAmount = 0;

          transactionObject.transactionId = creditTransactionID

          if (debitNonCashTransactionObj) {
            creditNonCashAmount = numberPrecision(wonAmount) ? Math.min(numberPrecision(wonAmount), numberPrecision(debitNonCashAmount)) : 0
            wonAmount = wonAmount ? numberPrecision(wonAmount - creditNonCashAmount) : 0
            creditNonCashTransactionObj = {
              ...transactionObject,
              transactionType: TRANSACTION_TYPES.CREDIT_NO_CASH,
              transactionId: creditTransactionID,
              debitTransactionId: debitTransactionID,
              amount: creditNonCashAmount,
              targetBeforeBalance: walletNonCashAmount,
              targetAfterBalance: walletNonCashAmount + creditNonCashAmount,
              targetWalletId: +userWallet.id,
              targetCurrencyId: +userWallet.currencyId,
            }

            walletNonCashAmount = np.plus(walletNonCashAmount, creditNonCashAmount)

            creditNonCashTransactionObj = await v3CurrencyConversion(sequelizeTransaction, creditNonCashTransactionObj, user?.currencyId, tenantId, creditNonCashTransactionObj.amount)

          }

          if (!creditNonCashTransactionObj || wonAmount > 0) {
            creditCashAmount = wonAmount
            creditCashTransactionObj = {
              ...transactionObject,
              transactionType: TRANSACTION_TYPES.CREDIT,
              transactionId: creditTransactionID,
              debitTransactionId: debitTransactionID,
              amount: creditCashAmount,
              targetBeforeBalance: walletCashAmount,
              targetAfterBalance: walletCashAmount + creditCashAmount,
              targetWalletId: +userWallet.id,
              targetCurrencyId: +userWallet.currencyId,
            }

            walletCashAmount = np.plus(walletCashAmount, creditCashAmount)

            creditCashTransactionObj = await v3CurrencyConversion(sequelizeTransaction, creditCashTransactionObj, user?.currencyId, tenantId, creditCashTransactionObj.amount)

          }
        }
        else if (transaction.result === 'rollback') {

          transactionObject.transactionId = rollbackTransactionID

          if (debitNonCashTransactionObj) {
            rollbackNonCashTransactionObj = {
              ...transactionObject,
              transactionType: TRANSACTION_TYPES.ROLLBACK_NO_CASH,
              amount: debitNonCashAmount,
              targetBeforeBalance: walletNonCashAmount,
              targetAfterBalance: walletNonCashAmount + debitNonCashAmount,
              targetWalletId: +userWallet.id,
              targetCurrencyId: +userWallet.currencyId,
            }
            walletNonCashAmount = np.plus(walletNonCashAmount, debitNonCashAmount)

            debitNonCashTransactionObj.cancelTransactionId = rollbackTransactionID
            rollbackNonCashTransactionObj = await v3CurrencyConversion(sequelizeTransaction, rollbackNonCashTransactionObj, user?.currencyId, tenantId, rollbackNonCashTransactionObj.amount)
          }

          if (debitCashTransacationObj) {
            rollbackCashTransactionObj = {
              ...transactionObject,
              transactionType: TRANSACTION_TYPES.ROLLBACK,
              amount: debitCashAmount,
              targetBeforeBalance: walletCashAmount,
              targetAfterBalance: walletCashAmount + debitCashAmount,
              targetWalletId: +userWallet.id,
              targetCurrencyId: +userWallet.currencyId,
            }
            walletCashAmount = np.plus(walletCashAmount + debitCashAmount)

            debitCashTransacationObj.cancelTransactionId = rollbackTransactionID
            rollbackCashTransactionObj = await v3CurrencyConversion(sequelizeTransaction, rollbackCashTransactionObj, user?.currencyId, tenantId, rollbackCashTransactionObj.amount)

          }
        }

        if (debitNonCashTransactionObj) {
          allTransactions.push(debitNonCashTransactionObj)
        }
        if (debitCashTransacationObj) {
          allTransactions.push(debitCashTransacationObj)
        }
        if (creditNonCashTransactionObj) {
          allTransactions.push(creditNonCashTransactionObj)
        }
        if (creditCashTransactionObj) {
          allTransactions.push(creditCashTransactionObj)
        }
        if (rollbackNonCashTransactionObj) {
          allTransactions.push(rollbackNonCashTransactionObj)
        }
        if (rollbackCashTransactionObj) {
          allTransactions.push(rollbackCashTransactionObj)
        }

        userWallet.nonCashAmount = walletNonCashAmount
        userWallet.amount = walletCashAmount

        await TransactionModel.bulkCreate(allTransactions, { transaction: sequelizeTransaction })
        await userWallet.save({ transaction: sequelizeTransaction })

        currentTotalDebitAmount += trxnBetAmount;
        currentTotalCreditAmount += (transaction.result != 'rollback' ? trxnWonAmount : 0);
        currentTotalRollbackAmount += (transaction.result == 'rollback' ? trxnBetAmount : 0);

        // Recalculate current GGR after the transaction :
        // Changing for bot users so credit - debit
        currentGgr = currentTotalCreditAmount - (currentTotalDebitAmount - currentTotalRollbackAmount);

        cronData.currentGgr = currentGgr
        cronData.totalDrAmount = currentTotalDebitAmount
        cronData.totalCrAmount = currentTotalCreditAmount
        cronData.totalRbAmount = currentTotalRollbackAmount

        if (transaction.result == 'rollback') {
          totalRollbackTrxns++
        }

        currentUsersTransactions++;
        noOfTransactionsAdded++

        await TenantGGRSummaryModel.update({
          cronId: cronCreated.id,
          currentGgr: currentGgr,
          totalDebitAmount: currentTotalDebitAmount,
          totalCreditAmount: currentTotalCreditAmount,
          totalRollbackAmount: currentTotalRollbackAmount,
          totalDebitTrxns: noOfTransactionsAdded,
          totalCreditTrxns: noOfTransactionsAdded - totalRollbackTrxns,
          totalRollbackTrxns: totalRollbackTrxns,
          message: isTargetWillBeAchieved ? 'Target GGR Achieved' : '',
          status: isTargetWillBeAchieved ? 4 : 2
        }, {
          where: {
            id: ggrSummary.id
          },
        }, { transaction: sequelizeTransaction })

        await sequelizeTransaction.commit();

        // If target GGR is achieved then stop the process.
        if (isTargetWillBeAchieved) {
          isTargetReached = true
          cronData.message = 'Target GGR Achieved'
          break;
        }

      } catch (error) {
        await sequelizeTransaction.rollback()
        throw error
      }
    }

    cronLog.status = CRON_LOG_STATUS.SUCCESS
    await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)

    responseObject.success = true
    responseObject.message = 'Cron Completed'
    responseObject.data = cronData
    return responseObject
  } catch (error) {
    cronData.message = "Cron Failed"
    cronData.error = error.message || null
    cronLog.status = CRON_LOG_STATUS.FAILED
    await updateCronLog(cronCreated, cronLog, cronData, ggrSummary)
    responseObject.Error = {
      stack: error.stack
    }
    return responseObject
  }
}

function generateTransaction (minDebitAmount, maxDebitAmount, rollbackPercentage, tableId, totalUserWalletAmount) {

  let r = Math.random();
  let skewed = Math.pow(r, 2); // Try 2, 3, or more for stronger skew
  let betAmount = Math.round(skewed * (maxDebitAmount - minDebitAmount) + minDebitAmount);
  let outcome = Math.random();
  let isRollback = outcome * 3 < rollbackPercentage; // Generating Less Rollbacks
  let creditAmount = 0;

  if (isRollback) {
    return { debitAmount: betAmount, creditAmount: 0, rollbackAmount: betAmount, result: 'rollback' };
  } else {

    // Round Bet Amount here only
    let roundedBetAmount = betAmount
    if (betAmount < 2000) {
      roundedBetAmount = Math.round(betAmount / 100) * 100; // Round to nearest 100
    } else if (betAmount < 5000) {
      roundedBetAmount = Math.round(betAmount / 500) * 500; // Round to nearest 500
    } else {
      roundedBetAmount = Math.round(betAmount / 1000) * 1000; // Round to nearest 1000
    }

    if (roundedBetAmount > totalUserWalletAmount) {
      roundedBetAmount = totalUserWalletAmount - (totalUserWalletAmount % 100)
    }

    betAmount = roundedBetAmount

    const gameRatios = gameWiseWinRatios[tableId];
    const finalRatio = gameRatios[Math.floor(Math.random() * gameRatios.length)];

    creditAmount = betAmount * finalRatio;

    return { debitAmount: betAmount, creditAmount, rollbackAmount: 0, result: 'credit' };
  }
}

function checkIsTargetGGRAchieved (currentGgr, targetGGR) {
  return currentGgr >= targetGGR
}

function getTolerance (targetGGR, percentage) {
  return targetGGR + (targetGGR * (percentage / 100))
}

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))

async function updateCronLog (cronCreated, cronLog, cronData, ggrSummary) {
  cronLog.errorMsg = JSON.stringify(cronData)
  cronLog.endTime = new Date()
  if (!cronCreated) {
    cronCreated = await db.CronLog.create(cronLog)
  }
  else {
    await db.CronLog.update(cronLog, {
      where: {
        id: cronCreated.id
      }
    })
  }
  await updateGgrSummary(ggrSummary, cronCreated, cronData)
}

async function updateGgrSummary (ggrSummary, cronCreated, cronData, message = null) {
  if (!ggrSummary) {
    return;
  }

  await db.TenantGGRSummary.update({
    cronId: cronCreated?.id,
    status: cronData?.message == 'Target GGR Achieved' ? 4 : 3,
    message: message ? message : cronData?.message,
    currentGgr: cronData?.currentGgr?.toFixed(3)
  }, {
    where: {
      id: ggrSummary.id
    }
  })
}

function shuffleArray (array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    // Swap elements at positions i and j
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}

async function checkIsCancelled (id) {
  const summary = await db.TenantGGRSummary.findOne({
    where: {
      id: id,
      status: 3
    },
    attributes: ['id']
  })
  return summary
}

async function adjustBetAndWin (betAmount, wonAmount, targetGGR) {
  const allowedMultipliers = [2, 0]; // Descending
  let best = null;

  const betSteps = [];
  for (let bet = betAmount; bet >= 100;) {
    let roundedBet = bet;

    if (bet < 2000) {
      roundedBet = Math.round(bet / 100) * 100;
      bet -= 100;
    } else if (bet < 5000) {
      roundedBet = Math.round(bet / 500) * 500;
      bet -= 500;
    } else {
      roundedBet = Math.round(bet / 1000) * 1000;
      bet -= 1000;
    }

    if (!betSteps.includes(roundedBet) && roundedBet <= betAmount) {
      betSteps.push(roundedBet);
    }
  }

  for (let bet of betSteps) {
    for (let multiplier of allowedMultipliers) {
      const win = bet * multiplier;
      const ggr = win - bet;

      if (win > wonAmount) continue; // Can't exceed original win
      if (ggr <= targetGGR) {
        if (!best || ggr > best.ggr) {
          best = { bet, win, ggr };
        }
      }
    }
  }

  if (best) {
    // console.log(`✅ Adjusted Values Found:
    // - Bet: ${best.bet}
    // - Win: ${best.win}
    // - GGR: ${best.ggr}`);
    return best;
  } else {
    // console.log("❌ No valid adjustment found under constraints.");
    return null;
  }
}
