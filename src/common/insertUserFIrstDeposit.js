import { Sequelize } from 'sequelize'
import db, { sequelize } from '../db/models'
import {  CRON_LOG_STATUS } from '../common/constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
  const queueProcessStatus = await db.QueueProcessStatus.findOne({
    where: {
      service: 'user_first_deposit_cron',
      status: 1
    },
    attributes: ['id']
  })
  if(queueProcessStatus){
    cronLog.cronId = queueProcessStatus?.id
    const query = 'CALL user_first_deposit_daily();'
    await sequelize.query(query, {
      type: Sequelize.QueryTypes.INSERT,
    })
    return true
  } } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
