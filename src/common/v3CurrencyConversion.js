import { sequelize } from '../db/models';

export default async (sequelizeTransaction, transactionObject, userCurrencyId, tenantId, amountMain) => {

  const sql = `SELECT * FROM currency_view WHERE tenant_id = :tenantId;`

  const currencyAllAllowed = await sequelize.query(sql, {
    replacements: { tenantId },
    type: sequelize.QueryTypes.SELECT
  })

  const exMainTransactionCurrency = userCurrencyId

  const currencyExchangeRate = currencyAllAllowed.find(i => i.id === exMainTransactionCurrency)
  const otherCurrencyAmount = currencyAllAllowed.reduce((acc, i) => {
    if (i.id !== exMainTransactionCurrency) {
      acc[i.code] = parseFloat(
        (parseFloat(amountMain) * (i.exchange_rate / currencyExchangeRate.exchange_rate)).toFixed(4)
      )
    } else {
      acc[i.code] = parseFloat(amountMain)
    }
    return acc
  }, {})

  transactionObject = { ...transactionObject, otherCurrencyAmount: JSON.stringify(otherCurrencyAmount) }
  return transactionObject
}
