import { Op } from 'sequelize'
import { v4 as uuidv4 } from 'uuid'
import { CASINO_PROVIDERS_PROD, CASINO_PROVIDERS_STAG } from '../common/constants'
import config from '../configs/app.config'
import db, { sequelize } from '../db/models'

const convertToTimestamp = dateField => new Date(dateField).getTime()
const extractDomain = email => email.split('@')[1]

const getUserDataEzugi = async (ids, eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled) => {
  let marina888UserMapping = {}
  ids = ids.map(id => parseInt(id))

  if (isMarina888Tenant) {
    const userMappings = await db.Marina888User.findAll({
      where: {
        internalUserId: { [Op.in]: ids }
      },
      attributes: ['internalUserId', 'externalUserId']
    })

    // Use reduce to create the mapping object
    marina888UserMapping = userMappings.reduce((acc, mapping) => {
      acc[mapping.internalUserId] = mapping.externalUserId
      return acc
    }, {})
  }

  const usersData = []
  const users = await db.User.findAll({
    where: {
      id: { [Op.in]: ids },
      tenantId
    },
    attributes: ['id', 'createdAt', 'vipLevel',
      'email', 'dateOfBirth', 'countryCode', 'email',
      'firstName', 'lastName', 'userName', 'phone', 'phoneCode'],
    include: [
      {
        model: db.Wallet,
        attributes: ['id'],
        required: true,
        as: 'W',
        include: {
          model: db.Currency,
          attributes: ['code'],
          required: true
        }
      },
      {
        model: db.AdminUser,
        attributes: ['id', 'agentName', 'agentType'],
        required: true
      }
    ]
  })
  const agentDetails = {}
  if (users && users.length > 0) {
    for (const user of users) {
      agentDetails.ezugi_agent_name = user.AdminUser.agentName
      if (isMarina888Tenant) {
        agentDetails.ezugi_agent_id = await getMarina888AgentId(user.AdminUser.id)
      } else {
        agentDetails.ezugi_agent_id = user.AdminUser.id
      }

      let convertedDOB = convertToTimestamp(user.dateOfBirth);
      if (convertedDOB <= 0 || convertedDOB >= new Date().getTime()) {
        convertedDOB = 0
      }
      const userData = {
        eid: uuidv4(),
        event_type: eventType,
        event_date: Date.now(), // Use Date.now() for UTC timestamp in milliseconds
        user_ext_id: isMarina888Tenant && marina888UserMapping[user.id] ? marina888UserMapping[user.id] : getFormattedUserId(user.id, tenantId, tenantName),
        ext_brand_id: config.get('smartiCo.extBrandId'),
        payload: {
          core_registration_date: convertToTimestamp(user.createdAt),
        //  ...(user.locale && { core_user_language: user.locale }),
          ...(agentDetails),
          ezugi_user_name: user.userName ? user.userName : '',
          ezugi_vip_level: `${user.vipLevel}`,
          user_birthdate: convertedDOB,
          ...(user.countryCode && { user_country: user.countryCode }),
          user_email: user.email ? user.email : '',
          user_first_name: user.firstName ? user.firstName : '',
          user_last_name: user.lastName ? user.lastName : '',
          user_phone: `+${user.phoneCode ? user.phoneCode : ''} ${user.phone}`,
          core_wallet_currency: user.W?.Currency?.code
        }
      }
      usersData.push(userData)
    }
    return usersData
  }

  return []
}

const getWithdrawalDataEzugi = async (ids, eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled) => {
  ids = ids.map(id => parseInt(id))
  const query = `
  SELECT
  t.id AS acc_last_transaction_id,
  t.amount AS acc_last_withdrawal_amount,
  t.source_after_balance AS balance,
  t.comments as acc_withdrawal_status_change_reason,
  t.actionee_id,
  t.actionee_type,
  t.transaction_type,
  t.updated_at,
  w.owner_id AS wallet_user_id
  FROM
    public.transactions t
  LEFT JOIN
    public.wallets w
  ON
    CAST(t.source_wallet_id AS INTEGER) = w.id AND w.owner_type = 'User'
  WHERE
    t.id IN (:ids) and tenant_id = :tenant_id
  `
  const withdrawalTransactions = await sequelize.query(query, {
    replacements: { ids, tenant_id: tenantId },
    type: sequelize.QueryTypes.SELECT
  })

  const res = []
  let additionalPayload = {}
  for (const withdrawalTransaction of withdrawalTransactions) {
    // code to ignore withdrawal non-cash ,otb  on production //
    if ([6, 49].includes(+withdrawalTransaction.transaction_type)) { continue } // skip noncash, otb withdrawal
    // code to ignore withdrawal non-cash ,otb  on production //
    const userExtId = withdrawalTransaction.actionee_type === 'User'
      ? +withdrawalTransaction.actionee_id
      : +withdrawalTransaction.wallet_user_id
    if (withdrawalTransaction.actionee_type === 'AdminUser' && withdrawalTransaction.acc_withdrawal_status_change_reason !== 'approved by admin') {
      eventType = 'ezugi_agent_withdrawal_approved'
    }

    if (eventType === 'ezugi_online_withdrawal_pending') {
      additionalPayload = {
        ezugi_last_withdrawal_request_amount: parseFloat(withdrawalTransaction.acc_last_withdrawal_amount).toFixed(5),
        ezugi_last_online_withdrawal_request_date: convertToTimestamp(withdrawalTransaction.updated_at)
      }
    } else if (eventType === 'ezugi_online_withdrawal_approved') {
      additionalPayload = {
        ezugi_last_withdrawal_amount: parseFloat(withdrawalTransaction.acc_last_withdrawal_amount).toFixed(5),
        ezugi_wallet_balance: parseFloat(withdrawalTransaction.balance).toFixed(5),
        ezugi_last_approved_withdrawal_date: convertToTimestamp(withdrawalTransaction.updated_at)
      }
    } else if (eventType === 'ezugi_agent_withdrawal_approved') {
      additionalPayload = {
        ezugi_last_withdrawal_amount: parseFloat(withdrawalTransaction.acc_last_withdrawal_amount).toFixed(5),
        ezugi_wallet_balance: parseFloat(withdrawalTransaction.balance).toFixed(5),
        ezugi_last_approved_withdrawal_date: convertToTimestamp(withdrawalTransaction.updated_at)
      }
    }

    res.push({
      eid: uuidv4(),
      event_type: eventType,
      event_date: Date.now(), // UTC timestamp in milliseconds
      user_ext_id: isMarina888Tenant ? await getMarina888UserId(userExtId, tenantId, tenantName) : getFormattedUserId(userExtId, tenantId, tenantName),
      actionee_type: withdrawalTransaction.actionee_type,
      ext_brand_id: config.get('smartiCo.extBrandId'),
      payload: {
        ...additionalPayload
      }
    })
  }
  return res
}

const getWithdrawalCancelledDataEzugi = async (ids, eventType, tenantId, tenantName, isMarina888Tenant) => {
  ids = ids.map(id => parseInt(id))
  const query = `
    SELECT
      t.id AS acc_last_transaction_id,
      t.amount AS acc_last_withdrawal_amount,
      t.target_after_balance,
      t.actionee_id,
      t.actionee_type,
      t.transaction_type,
      w.owner_id AS wallet_user_id
    FROM
      public.transactions t
    LEFT JOIN
      public.wallets w
    ON
      CAST(t.target_wallet_id AS INTEGER) = w.id AND w.owner_type = 'User'
    WHERE
      t.id IN (:ids) and tenant_id = :tenant_id
  `

  const cancelledTransactions = await sequelize.query(query, {
    replacements: { ids, tenant_id: tenantId },
    type: sequelize.QueryTypes.SELECT
  })

  const res = []

  for (const cancelledTransaction of cancelledTransactions) {
    const userExtId = cancelledTransaction.actionee_type === 'User'
      ? +cancelledTransaction.actionee_id
      : +cancelledTransaction.wallet_user_id

    const accRealBalance = cancelledTransaction.target_after_balance

    const transactionData = {
      eid: uuidv4(),
      event_type: eventType,
      event_date: Date.now(), // UTC timestamp in milliseconds
      user_ext_id: isMarina888Tenant ? await getMarina888UserId(userExtId, tenantId, tenantName) : getFormattedUserId(userExtId, tenantId, tenantName),
      ext_brand_id: config.get('smartiCo.extBrandId'),
      payload: {
        ezugi_last_rejected_withdrawal_amount: parseFloat(cancelledTransaction.acc_last_withdrawal_amount).toFixed(5),
        ezugi_wallet_balance: parseFloat(accRealBalance).toFixed(5)
      }
    }

    res.push(transactionData)
  }

  return res
}

const getDepositDataEzugi = async (ids, eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled) => {
  ids = ids.map(id => parseInt(id))
  const query = `
    SELECT
      t.id AS acc_last_transaction_id,
      t.amount AS acc_last_deposit_amount,
      t.created_at AS acc_last_deposit_date,
      t.target_after_balance,
      t.actionee_id,
      t.actionee_type,
      t.transaction_type,
      t.created_at as acc_last_deposit_date,
      w.owner_id AS wallet_user_id,
      t.target_wallet_id,
      tmd.meta_data AS meta_data
    FROM
      public.transactions t
    LEFT JOIN
      public.wallets w
    ON
      CAST(t.target_wallet_id AS INTEGER) = w.id AND w.owner_type = 'User'
    LEFT JOIN
        public.transactions_meta_data tmd
    ON
        tmd.transaction_id = t.id
    WHERE
      t.id IN (:ids) and t.tenant_id = :tenant_id
  `

  const depositTransactions = await sequelize.query(query, {
    replacements: { ids, tenant_id: tenantId },
    type: sequelize.QueryTypes.SELECT
  })
  const res = []

  for (const depositTransaction of depositTransactions) {
    // code to ignore deposit noncash ,otb  on production //
    if ([5, 48].includes(+depositTransaction.transaction_type)) { continue } // skip non-cash, otb deposit
    // code to ignore deposit noncash ,otb  on production //
    const userExtId = depositTransaction.actionee_type === 'User'
      ? +depositTransaction.actionee_id
      : +depositTransaction.wallet_user_id

    if (depositTransaction.comments !== 'Deposit Request Approved' && !depositTransaction.meta_data) {
      eventType = 'ezugi_agent_deposit_approved'
    }

    // Fetch total deposit count and amount for the target wallet
    const totalDepositQuery = `
      SELECT
        COUNT(t.id) AS total_deposit_count,
        SUM(t.amount) AS total_deposit_amount
      FROM
        public.transactions t
      WHERE
        t.transaction_type = 3 AND t.target_wallet_id = :target_wallet_id AND tenant_id = :tenant_id
    `

    const totalDepositData = await sequelize.query(totalDepositQuery, {
      replacements: { target_wallet_id: depositTransaction.target_wallet_id, tenant_id: tenantId },
      type: sequelize.QueryTypes.SELECT
    })

    const transactionData = {
      eid: uuidv4(),
      event_type: eventType,
      event_date: Date.now(), // UTC timestamp in milliseconds
      user_ext_id: isMarina888Tenant ? await getMarina888UserId(userExtId, tenantId, tenantName) : getFormattedUserId(userExtId, tenantId, tenantName),
      ext_brand_id: config.get('smartiCo.extBrandId'),
      payload: {
        ezugi_last_deposit_date: convertToTimestamp(depositTransaction.acc_last_deposit_date),
        ezugi_last_deposit_amount: parseFloat(depositTransaction.acc_last_deposit_amount).toFixed(5),
        ezugi_wallet_balance: parseFloat(depositTransaction.target_after_balance).toFixed(5),
        ezugi_deposits_count: totalDepositData[0].total_deposit_count || 0,
        ezugi_total_deposits_amount: parseFloat(totalDepositData[0].total_deposit_amount || 0).toFixed(5)
      }
    }

    res.push(transactionData)
  }
  return res
}

const getCasinoBetWinDataEzugi = async (ids, eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled) => {
  const data = []
  const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

  // check casino-transaaction queue to debug //

  const smarticoType = eventType === 'casino_win' ? 'ezugi_game_played' : eventType
  const cashWinTransactionType = 1 // win
  const cashBetTransactionType = 0 // bet
  const casinoIdsArray = ids
  let isCashBet = false
  let isOnlyNonCashBet = false
  let betID = uuidv4()
  const conditions = [
  ]
  for (let casinoIds of casinoIdsArray) {
    delay(10)
    casinoIds = casinoIds.map(id => parseInt(id))
    const casinoTransactionObj = await db.Transaction.findOne({
      where: {
        id: { [Op.in]: casinoIds },
        tenantId
      },
      attributes: ['debitTransactionId', 'transactionId'],
      useMaster: true,
      raw: true
    })
    if (!casinoTransactionObj) {
      throw new Error(`casino-transaction not found-1 :: casinoIds - ${casinoIds}`)
    }
    const casinoTransactionId = casinoTransactionObj.debitTransactionId
    if (casinoTransactionId) {
      conditions.push({ transactionId: casinoTransactionId })
      conditions.push({ debitTransactionId: casinoTransactionId })
    }
    else{
      throw new Error('No debit transaction id!')
    }

    const payload = {}
    const transactions = await db.Transaction.findAll({
      where: {
        [Op.or]: conditions,
        tenantId
      },
      useMaster: true,
      attributes: ['id', 'actioneeId', 'createdAt', 'transactionType', 'transactionId', 'debitTransactionId', 'amount', 'targetAfterBalance', 'sourceAfterBalance', 'gameId', 'providerId', 'seatId', 'tableId'],
    })
    if (!transactions || transactions?.length === 0) {
      throw new Error(`casino-transactions not found-2! :: conditions - ${conditions}`)
    }
    // code to ignore bets placed when there is not win in realcash //
    const filteredTransactions = transactions.filter(transaction => transaction.transactionType === 1)
    if (!filteredTransactions || filteredTransactions?.length === 0) {
      throw new Error('casino cash-win transaction not found!')
    }
    // code to ignore bets placed when there is not win in realcash //

    let userId
    let gameId, providerId, tableId, seatId
    for (const transaction of transactions) {
      payload.casino_last_bet_dt = convertToTimestamp(transaction.createdAt)
      // payload.casino_last_bet_id = transaction.debitTransactionId ? transaction.debitTransactionId : payload.casino_last_bet_id
      userId = transaction.actioneeId
      if (transaction.gameId) { gameId = transaction.gameId }
      if (transaction.tableId) { tableId = transaction.tableId }
      if (transaction.seatId) { seatId = transaction.seatId }
      providerId = parseInt(transaction.providerId)

      if (transaction.debitTransactionId && transaction.transactionType === cashWinTransactionType) { // win transactions
        payload.ezugi_wallet_balance = parseFloat(transaction.targetAfterBalance).toFixed(5)
        payload.ezugi_last_win_amount = parseFloat(transaction.amount).toFixed(5)
        betID = transaction.id.toString()
      } else if (transaction.transactionType === cashBetTransactionType) { // bet transactions
        payload.ezugi_last_bet_amount = parseFloat(transaction.amount).toFixed(5)
        payload.ezugi_last_game_date = convertToTimestamp(transaction.createdAt)
        isCashBet = true
      } else if ([8, 46].includes(transaction.transactionType)) { // bet transactions
        payload.ezugi_last_game_date = convertToTimestamp(transaction.createdAt)
        isOnlyNonCashBet = true
      }
    }
    if (!isCashBet && isOnlyNonCashBet) {
      payload.ezugi_last_bet_amount = (0).toFixed(5)
    }
    // const casinoProviders = config.get('env') === 'development' ? CASINO_PROVIDERS_STAG : CASINO_PROVIDERS_PROD
    // providerId = casinoProviders[providerId] ? casinoProviders[providerId] : providerId
    const casinoItemWhereClause = {
      tenantId
    }
    const tableIdProviders = config.get('env') === 'development' ? [1, 2, 5067, 5000] : [10, 1, 81, 49]; // Ezugi, Evolution, Arcade, pgsoft
    const gameIdProviders = config.get('env') === 'development' ? [13] : [13]; // Spribe
    const seatIdProviders = config.get('env') === 'development' ? [5034, 5099, 16] : [114, 147, 48]; // Darwin ,Funky Games, St8

    const isTableId = tableIdProviders.includes(providerId)
    const isGameId = gameIdProviders.includes(providerId)
    const isSeatId = seatIdProviders.includes(providerId)

    if (isTableId) { casinoItemWhereClause.uuid = `${tableId}` }
    if (isGameId) { casinoItemWhereClause.uuid = `${gameId}` }
    if (isSeatId) { casinoItemWhereClause.uuid = `${seatId}` }

    const casinoItem = await db.CasinoItem.findOne({
      where: casinoItemWhereClause,
      attributes: ['name'],
      orderBy: [['id', 'DESC']],
      raw: true
    })
    payload.ezugi_last_game_name = casinoItem?.name || 'Not Available'

    payload.ezugi_last_bet_type = 1 // Table Bet
    data.push({
      eid: betID,
      event_type: smarticoType,
      event_date: Date.now(), // UTC timestamp in milliseconds
      user_ext_id: isMarina888Tenant ? await getMarina888UserId(userId, tenantId, tenantName) : getFormattedUserId(userId, tenantId, tenantName),
      ext_brand_id: config.get('smartiCo.extBrandId'),
      payload
    })
  }
  return data
}

const getEzugiWalletUpdate = async (ids, eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled) => {
  let marina888UserMapping = {}
  ids = ids.map(id => parseInt(id))
  const usersData = []
  if (isMarina888Tenant) {
    const userMappings = await db.Marina888User.findAll({
      where: {
        internalUserId: { [Op.in]: ids }
      },
      attributes: ['internalUserId', 'externalUserId']
    })

    // Use reduce to create the mapping object
    marina888UserMapping = userMappings.reduce((acc, mapping) => {
      acc[mapping.internalUserId] = mapping.externalUserId
      return acc
    }, {})
  }
  const users = await db.User.findAll({
    where: {
      id: { [Op.in]: ids },
      tenantId
    },
    attributes: ['id'],
    include: [
      {
        model: db.Wallet,
        attributes: ['nonCashAmount', 'oneTimeBonusAmount'],
        required: true,
        as: 'W'
      }
    ]

  })

  if (users && users.length > 0) {
    for (const user of users) {
      const updateBonusBalance = parseFloat(user.W.nonCashAmount + (isOTBEnabled ? parseFloat(user.W.oneTimeBonusAmount) : 0)).toFixed(5)
      const userData = {
        eid: uuidv4(),
        event_type: eventType,
        event_date: Date.now(), // Use Date.now() for UTC timestamp in milliseconds
        user_ext_id: isMarina888Tenant && marina888UserMapping[user.id] ? marina888UserMapping[user.id] : getFormattedUserId(user.id, tenantId, tenantName),
        ext_brand_id: config.get('smartiCo.extBrandId'),
        payload: {
          ezugi_losing_bonus_amount: updateBonusBalance
        }
      }
      usersData.push(userData)
    }
    return usersData
  }

  return []
}

const getEzugiWalletUpdateBulk = async (ids, eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled) => {
  let marina888UserMapping = {}
  ids = ids.map(id => parseInt(id))
  const usersWalletData = []
  if (isMarina888Tenant) {
    const userMappings = await db.Marina888User.findAll({
      where: {
        internalUserId: { [Op.in]: ids }
      },
      attributes: ['internalUserId', 'externalUserId']
    })

    // Use reduce to create the mapping object
    marina888UserMapping = userMappings.reduce((acc, mapping) => {
      acc[mapping.internalUserId] = mapping.externalUserId
      return acc
    }, {})
  }
  const usersWallets = await db.Wallet.findAll({
    where: {
      ownerId: { [Op.in]: ids },
      ownerType: 'User'
    },
    useMaster: true,
    attributes: ['ownerId', 'amount', 'nonCashAmount', 'oneTimeBonusAmount']
  })

  if (usersWallets && usersWallets.length > 0) {
    for (const wallet of usersWallets) {
      const updateBonusBalance = parseFloat(wallet.nonCashAmount + (isOTBEnabled ? parseFloat(wallet.oneTimeBonusAmount) : 0)).toFixed(5)
      const realBalance = parseFloat(wallet.amount).toFixed(5)
      const userData = {
        eid: uuidv4(),
        event_type: eventType,
        event_date: Date.now(), // Use Date.now() for UTC timestamp in milliseconds
        user_ext_id: isMarina888Tenant && marina888UserMapping[wallet.ownerId] ? marina888UserMapping[wallet.ownerId] : getFormattedUserId(wallet.ownerId, tenantId, tenantName),
        ext_brand_id: config.get('smartiCo.extBrandId'),
        payload: {
          ezugi_losing_bonus_amount: updateBonusBalance,
          ezugi_wallet_balance: realBalance
        }
      }
      usersWalletData.push(userData)
    }
    return usersWalletData
  }

  return []
}

const getUserLoginStats = async (ids, eventType, tenantId, tenantName, isMarina888Tenant, isOTBEnabled) => {
  let marina888UserMapping = {}
  ids = ids.map(id => parseInt(id))
  const usersData = []
  if (isMarina888Tenant) {
    const userMappings = await db.Marina888User.findAll({
      where: {
        internalUserId: { [Op.in]: ids }
      },
      attributes: ['internalUserId', 'externalUserId']
    })

    // Use reduce to create the mapping object
    marina888UserMapping = userMappings.reduce((acc, mapping) => {
      acc[mapping.internalUserId] = mapping.externalUserId
      return acc
    }, {})
  }
  const usersLoginCounts = await db.User.findAll({
    where: {
      id: { [Op.in]: ids },
      tenantId
    },
    attributes: ['signInCount', 'id']

  })

  if (usersLoginCounts && usersLoginCounts.length > 0) {
    for (const user of usersLoginCounts) {
      const userData = {
        eid: uuidv4(),
        event_type: eventType,
        event_date: Date.now(), // Use Date.now() for UTC timestamp in milliseconds
        user_ext_id: isMarina888Tenant && marina888UserMapping[user.id] ? marina888UserMapping[user.id] : getFormattedUserId(user.id, tenantId, tenantName),
        ext_brand_id: config.get('smartiCo.extBrandId'),
        payload: {
          ezugi_login_count: user.signInCount ? user.signInCount : 0
        }
      }
      usersData.push(userData)
    }
    return usersData
  }

  return []
}

const getMarina888AgentId = async (id) => {
  const agent = await db.Marina888Agent.findOne({ where: { internalAgentId: parseInt(id) }, attributes: ['externalAgentId'], raw: true })
  return agent?.externalAgentId || id
}

// Utility function to get acc_last_transaction_id from payload where actionee_type matches
const getTransactionIdsByActioneeType = (transactions, actioneeType) => {
  return transactions
    .filter(transaction => transaction.actionee_type === actioneeType && transaction.payload.acc_withdrawal_status_change_reason !== 'approved by admin')
    .map(transaction => transaction.payload.acc_last_transaction_id)
}

const getMarina888UserId = async (userId, tenantId, tenantName) => {
  const user = await db.Marina888User.findOne({ where: { internalUserId: parseInt(userId) }, attributes: ['externalUserId'], raw: true })
  return user?.externalUserId || getFormattedUserId(userId, tenantId, tenantName)
}

const getFormattedUserId = (userId, tenantId, tenantName) => {
  // Remove spaces from tenantName
  const formattedTenantName = tenantName.replace(/\s+/g, '')
  // Format and return the string
  return `${formattedTenantName}_${tenantId}_${userId}`
}

export { getCasinoBetWinDataEzugi, getDepositDataEzugi, getEzugiWalletUpdate, getEzugiWalletUpdateBulk, getTransactionIdsByActioneeType, getUserDataEzugi, getUserLoginStats, getWithdrawalCancelledDataEzugi, getWithdrawalDataEzugi }
