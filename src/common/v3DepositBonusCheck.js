import { BONUS_TYPES, DEPOSIT_ROLLING_CALCULATION_METHODS } from './constants'
import { instantDepositBonus } from './instantDepositBonusCheck'

export const v3DepositBonusCheck = async (sequelizeTransaction, amount, user, userWallet, txnIds, depositTxnId, bonus, userActiveDepositBonus) => {
  // instant deposit bonus check
  if (bonus.kind === BONUS_TYPES.DEPOSIT_INSTANT) {
   let result = await instantDepositBonus(sequelizeTransaction, amount, user, txnIds, userActiveDepositBonus, bonus, depositTxnId)
   if (result) {
     return true
    }
    return null
  }
  const bonusToBeGiven = (amount * (bonus?.percentage / 100)) > bonus?.DepositBonusSetting?.maxBonus ? bonus?.DepositBonusSetting?.maxBonus : +parseFloat(amount * (bonus?.percentage / 100)).toFixed(5)
  userActiveDepositBonus.bonusAmount = bonusToBeGiven

  let rolling
  if (bonus?.DepositBonusSetting?.rolloverCalculationType === DEPOSIT_ROLLING_CALCULATION_METHODS.TOTAL_BASED_ROLLING) {
    rolling = (parseFloat(bonusToBeGiven) + parseFloat(amount))
  } else if (bonus?.DepositBonusSetting?.rolloverCalculationType === DEPOSIT_ROLLING_CALCULATION_METHODS.DEPOSIT_BASED_ROLLING) {
    rolling = parseFloat(amount)
  } else {
    rolling = parseFloat(bonusToBeGiven)
  }

  userActiveDepositBonus.rolloverBalance = rolling * bonus?.DepositBonusSetting?.rolloverMultiplier
  userActiveDepositBonus.initialRolloverBalance = rolling * bonus?.DepositBonusSetting?.rolloverMultiplier
  userActiveDepositBonus.expiresAt = new Date(new Date().setDate(new Date().getDate() + bonus.DepositBonusSetting.validForDays)).toISOString()
  await userActiveDepositBonus.save({ transaction: sequelizeTransaction })
  return true
}
