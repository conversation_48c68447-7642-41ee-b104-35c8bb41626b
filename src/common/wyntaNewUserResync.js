
import { Op } from 'sequelize'
import db from '../db/models'

export default async (jobData) => {
  try {
    const userId = jobData?.transactionId
    const userData = await db.User.findOne({
      where: {
        id: userId
      },
      attributes: ['id', 'tenantId', 'createdAt'],
      raw: true
    })
    const tenantId = userData.tenantId
    //const tenantId = 152
    const createdDate = userData.createdAt
    const todayDate = new Date().toISOString()
    const result = []

    // const startOfCreatedDay = new Date(createdDate)
    // startOfCreatedDay.setHours(0, 0, 0, 0)

    // const endOfCreatedDay = new Date(createdDate)
    // endOfCreatedDay.setHours(23, 59, 59, 999);
    // const exportRegData = await db.ExportCsvCenter.findOne({
    //   where: {
    //     createdAt: {
    //       [Op.between]: [startOfCreatedDay, endOfCreatedDay]
    //     },
    //     type: {
    //       [Op.like]: `%wynta_${tenantId}_reg_report%`
    //     }
    //   },
    //   order: [['id', 'DESC']],
    //   raw: true,
    //   attributes: ['id', 'type', 'createdAt']
    // })
    // if (exportRegData) {
    //   result.push(exportRegData)
    // }

    const exportSalesData = await db.ExportCsvCenter.findAll({
      where: {
        createdAt: {
          [Op.between]: [createdDate, todayDate],
        },
        type: {
          [Op.like]: `%wynta_${tenantId}%`
          //[Op.like]: `%wynta_${tenantId}_sales_report%`
        }
      },
      order: [['id', 'DESC']],
      raw: true,
      attributes: ['id', 'type', 'createdAt']
    })

    if (exportSalesData.length > 0) {
      const seen = new Set()
      exportSalesData.forEach(item => {
        const dateKey = item.createdAt.toISOString().slice(0, 10)
        const typeKey = item.type
        const key = `${dateKey}_${typeKey}`

        if (!seen.has(key)) {
          seen.add(key)
          result.push(item)
        }
      })

      //const idsArray = result.map(item => item.id)
      const salesReports = result.filter(item => item.type.includes('sales_report'))

      const regReports = result.filter(item => item.type.includes('reg_report'))

      const earliestRegReport = regReports.reduce((earliest, current) =>
        current.createdAt < earliest.createdAt ? current : earliest
      )

      const finalResult = [...salesReports, earliestRegReport].sort((a, b) => a.createdAt - b.createdAt)

      const idsArray = finalResult.map(item => item.id)
      await db.ExportCsvCenter.update(
        {
          status: 0,
          csvUrl: null
        },
        {
          where: {
            id: {
              [Op.in]: idsArray
            }
          }
        }
      )
      const insertData = {
        type: 'create_csv',
        status: 0,
        ids: idsArray
      }
      await db.QueueLog.create(insertData)
      // const rowsToInsert = idsArray.map(id => ({
      //   type: 'create_csv',
      //   status: 0,
      //   ids: [id]
      // }))
      // await db.QueueLog.bulkCreate(rowsToInsert)
    }

    return true
  } catch (e) {
    throw e
  }

}
