import { Op, Sequelize } from 'sequelize'
import { BONUS_RECURRING_STATUS, BONUS_STATUS,BONUS_TIER_CONFIG_TYPE, BONUS_TIER_TYPE,  BONUS_TYPES, DEPOSIT_BONUS_ALLOWED, DEPOSIT_INSTANT_BONUS_TYPES, DEPOSIT_ROLLING_CALCULATION_METHODS, RECURRING_BONUS_TYPES } from '../common/constants'
import db from '../db/models'
import { burningBonusCheck } from './burningBonusCheck'
import { instantDepositBonus } from './instantDepositBonusCheck'

export const depositBonusCheck = async (amount, user, txnIds, sequelizeTransaction, depositTxnId, paymentProviders, skipManualDepositCheck = false) => {
  amount = +(amount)

  const userActiveDepositBonus = await db.UserBonus.findOne({
    attributes: ["id", "status", "bonusId", "bonusAmount", "rolloverBalance", "expiresAt", "transactionId", "initialRolloverBalance", "kind"],
    where: {
      status: BONUS_STATUS.ACTIVE,
      expiresAt: { [Op.gte]: Sequelize.literal('CURRENT_TIMESTAMP') },
      userId: user.id,
      [Op.or]: [
        {
          kind: {
            [Op.in]: [ BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_INSTANT, BONUS_TYPES.DEPOSIT_BOTH]
          },
          bonusAmount: 0,
          rolloverBalance: 0
        },
        {
          kind: {
            [Op.in]: [ BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH]
          },
          '$DepositBonusSetting.deposit_bonus_type$': DEPOSIT_INSTANT_BONUS_TYPES.RECURRING
        }
      ]
    },
    include: [
      {
        model: db.DepositBonusSetting,
        required: false
      }
    ]
  })
  if (!userActiveDepositBonus) {
    return null
  }

  const bonus = await db.Bonus.findOne({
    where: {
      id: userActiveDepositBonus.bonusId,
      enabled: true,
      [Op.or]: [
        {
          '$DepositBonusSetting.tier_config_type$': BONUS_TIER_CONFIG_TYPE.TIER_BASED,
          [Op.or]: [
            {
              '$DepositBonusSetting.tier_type$': 1,
              '$DepositBonusSetting.min_deposit$': { [Op.lte]: amount },
              '$DepositBonusSetting.max_deposit$': { [Op.gte]: amount }
            },
            {
              '$DepositBonusSetting.tier_type$': 2,
              '$DepositBonusSetting.DepositBonusTiers.min_deposit_amount$': { [Op.lte]: amount },
              '$DepositBonusSetting.DepositBonusTiers.max_deposit_amount$': { [Op.gte]: amount }
            }
          ]
        },
        {
          '$DepositBonusSetting.tier_config_type$': BONUS_TIER_CONFIG_TYPE.SEQUENCE_BASED
        }
      ]
    },
    include: {
      model: db.DepositBonusSetting,
      required: true,
      include: {
        model: db.DepositBonusTier,
        required: false
      }
    },
    attributes: ["id", "percentage", "kind", "walletType", "depositBonusType"]
  })

  // bonus not found check
  if (!bonus) {
    // userActiveDepositBonus.status = BONUS_STATUS.EXPIRED
    // await userActiveDepositBonus.save({ transaction: sequelizeTransaction })
    return null
  }

  // If bonus have payment gateway and both option  in deposit type then only active it
  if (!skipManualDepositCheck && bonus?.DepositBonusSetting?.depositType === DEPOSIT_BONUS_ALLOWED.MANUAL) {
    return null
  }

  // if the deposit uses the specified payment gateway
  if (paymentProviders && bonus?.DepositBonusSetting?.allowedPaymentProviders &&
      !bonus?.DepositBonusSetting?.allowedPaymentProviders?.includes(paymentProviders))
    return null

  // checking week day for bonus if there
  if (bonus?.DepositBonusSetting?.weekDay) {
    const today = new Date()
    const weekdaysMap = { sunday: 0, monday: 1, tuesday: 2, wednesday: 3, thursday: 4, friday: 5, saturday: 6 }
    const specifiedWeekdayNum = weekdaysMap[bonus?.DepositBonusSetting?.weekDay?.toLowerCase()]
    if (today.getDay() !== specifiedWeekdayNum) return null
  }

  //checking if they have any active bonuses with burning days remaining that they haven't utilized yet. (only if this bonus has burning days)
  if (bonus?.DepositBonusSetting?.burningDays) {
    const givable = await burningBonusCheck(user.id, user.tenantId)
    if (!givable) return null
  }

  if (bonus.DepositBonusSetting.tierType === BONUS_TIER_TYPE.TIER_2 && Array.isArray(bonus.DepositBonusSetting.DepositBonusTiers) && bonus.DepositBonusSetting.DepositBonusTiers.length > 0) {
    const { percentage, minDepositAmount, maxDepositAmount, maxBonus } = bonus.DepositBonusSetting.DepositBonusTiers[0];
    bonus.percentage = percentage;
    bonus.DepositBonusSetting.minDeposit = minDepositAmount;
    bonus.DepositBonusSetting.maxDeposit = maxDepositAmount;
    bonus.DepositBonusSetting.maxBonus = maxBonus;
  }
  const isSequenceBased = bonus?.DepositBonusSetting?.tierConfigType === BONUS_TIER_CONFIG_TYPE.SEQUENCE_BASED;
  // deposit amount less than min deposit amount and greater than max deposit amount check
  if (!isSequenceBased && (amount < bonus?.DepositBonusSetting?.minDeposit || amount > bonus?.DepositBonusSetting?.maxDeposit)) {
    return null
  }

  // instant deposit bonus check
  if (bonus.kind === BONUS_TYPES.DEPOSIT_INSTANT) {
    const instantDeposit = await instantDepositBonus(sequelizeTransaction, amount, user, txnIds, userActiveDepositBonus, bonus, depositTxnId)
    if (instantDeposit) {
      return true
    } else {
      return false
    }
  }

  if (bonus.DepositBonusSetting.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(bonus.kind)) {
    // Check recurring bonus limits
    if (bonus?.DepositBonusSetting?.recurringBonusType === RECURRING_BONUS_TYPES.CUSTOM_DEPOSITS) {
      const today = Sequelize.literal("DATE_TRUNC('day', CURRENT_TIMESTAMP)");
      const count = await db.UserBonusRecurringRollover.count({
        where: {
          bonusId: bonus?.id,
          userId: user?.id,
          userBonusId: userActiveDepositBonus?.id,
          createdAt: {
            [Op.gte]: today
          }
        },
        transaction: sequelizeTransaction
      });

      if (count >= bonus?.DepositBonusSetting?.customDeposits) {
        return null;
      }
    }
  }
  
  const isRecurring = bonus.DepositBonusSetting.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(bonus.kind);
  let matchedTier = null;

  // Handle sequence-based tier logic
  if (isSequenceBased) {
    // Find or create user deposit sequence record
    let userSequence = await db.UserDepositSequence.findOne({
      where: { 
        userId: user.id, 
        bonusId: bonus.id 
      }
    });
    
    // If no sequence record exists, create one starting at sequence 0
    if (!userSequence) {
      userSequence = await db.UserDepositSequence.create({
        userId: user.id,
        bonusId: bonus.id,
        currentSequence: 1,
      }, { transaction: sequelizeTransaction });
    } 
    else {
      userSequence.currentSequence += 1;
      await userSequence.save({ transaction: sequelizeTransaction });
    }
    
    // Determine which sequence number to match
    let sequenceToMatch = userSequence.currentSequence;
    const applyAfterFixedNumber = bonus.DepositBonusSetting.applyToDepositSequenceGte;
    
    // Handle the "greater than or equal to" rule
    if (applyAfterFixedNumber && userSequence.currentSequence > applyAfterFixedNumber) {
      sequenceToMatch = applyAfterFixedNumber;
      if (amount < bonus.DepositBonusSetting.minDeposit) {
        return null; 
      }
    }
    
    // Get all tiers for this bonus and order them
    const allTiers = await db.DepositBonusTier.findAll({
      where: {
        depositBonusSettingId: bonus.DepositBonusSetting.id
      },
      order: [['id', 'ASC']]
    });
    
    // Find the matching tier for this deposit sequence (1-indexed, so subtract 1 for array index)
    const tierIndex = sequenceToMatch - 1;
    
    if (tierIndex >= 0 && tierIndex < allTiers.length) {
      const candidateTier = allTiers[tierIndex];
      
      // Check if deposit amount meets the tier's minimum requirement
      if (amount >= candidateTier.minDepositAmount) {
        matchedTier = candidateTier;
      } else {
        // Deposit doesn't meet the minimum requirement for this sequence
        return null;
      }
    } else {
      // No matching tier for this sequence
      return null;
    }
  }else if (bonus.DepositBonusSetting.tierType === BONUS_TIER_TYPE.TIER_2) {
    matchedTier = await db.DepositBonusTier.findOne({
      where: {
        depositBonusSettingId: bonus.DepositBonusSetting.id,
        minDepositAmount: { [Op.lte]: amount },
        maxDepositAmount: { [Op.gte]: amount }
      },
      transaction: sequelizeTransaction
    });

    // If no matching tier found, exit
    if (!matchedTier) {
      return null;
    }
  }

  // Calculate bonus amount once
  let bonusToBeGiven = 0;
  if (matchedTier) {
    bonusToBeGiven = (amount * (matchedTier.percentage / 100)) > matchedTier.maxBonus
      ? matchedTier.maxBonus
      : +parseFloat(amount * (matchedTier.percentage / 100)).toFixed(5);
  } else {
    bonusToBeGiven = (amount * (bonus.percentage / 100)) > +(bonus.DepositBonusSetting.maxBonus)
      ? +(bonus.DepositBonusSetting.maxBonus)
      : +parseFloat(amount * (bonus.percentage / 100)).toFixed(5);
  }

  // Calculate rollover target once
  let rolling;
  if (bonus?.DepositBonusSetting?.rolloverCalculationType === DEPOSIT_ROLLING_CALCULATION_METHODS.TOTAL_BASED_ROLLING) {
    rolling = (parseFloat(bonusToBeGiven) + parseFloat(amount));
  } else if (bonus?.DepositBonusSetting?.rolloverCalculationType === DEPOSIT_ROLLING_CALCULATION_METHODS.DEPOSIT_BASED_ROLLING) {
    rolling = parseFloat(amount);
  } else {
    rolling = parseFloat(bonusToBeGiven);
  }

  const rolloverTarget = rolling * bonus.DepositBonusSetting.rolloverMultiplier;

  // Handle tier-based rollover if enabled
  if (isRecurring) {
    // Create recurring rollover record
    await db.UserBonusRecurringRollover.create({
      userId: user.id,
      bonusId: bonus.id,
      userBonusId: userActiveDepositBonus.id,
      depositBonusTierId: matchedTier ? matchedTier.id : null,
      bonusAmount: bonusToBeGiven,
      rolloverTarget: rolloverTarget,
      remainingRollover: rolloverTarget,
      transactionId: null,
      status: BONUS_RECURRING_STATUS.ACTIVE,
      createdAt: new Date(),
      updatedAt: new Date()
    }, { transaction: sequelizeTransaction });

    // Update parent userBonus record with the new rollover target
    const currentRolloverBalance = userActiveDepositBonus.rolloverBalance || 0;
    const initialRolloverBalance = userActiveDepositBonus.initialRolloverBalance || 0;
    const currentBonusAmount = userActiveDepositBonus.bonusAmount || 0;
    userActiveDepositBonus.bonusAmount = currentBonusAmount + bonusToBeGiven;

    userActiveDepositBonus.rolloverBalance = currentRolloverBalance + rolloverTarget;
    userActiveDepositBonus.initialRolloverBalance = initialRolloverBalance + rolloverTarget;

    await userActiveDepositBonus.save({ transaction: sequelizeTransaction });
  }

  // For one-time bonuses, we still need to set the main bonus information
  if (bonus.DepositBonusSetting.depositBonusType !== DEPOSIT_INSTANT_BONUS_TYPES.RECURRING && [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(bonus.kind)) {
    userActiveDepositBonus.bonusAmount = bonusToBeGiven;
    userActiveDepositBonus.rolloverBalance = rolloverTarget;
    userActiveDepositBonus.initialRolloverBalance = rolloverTarget;
    userActiveDepositBonus.expiresAt = new Date(new Date().setDate(new Date().getDate() + bonus.DepositBonusSetting.validForDays)).toISOString();

    await userActiveDepositBonus.save({ transaction: sequelizeTransaction });
  } else {
    // For recurring bonuses, update expiry date if not already set
    if (!userActiveDepositBonus.expiresAt || new Date(userActiveDepositBonus.expiresAt) < new Date()) {
      userActiveDepositBonus.expiresAt = new Date(new Date().setDate(new Date().getDate() + bonus.DepositBonusSetting.validForDays)).toISOString();
      await userActiveDepositBonus.save({ transaction: sequelizeTransaction });
    }
  }


  // Check if rollover transaction already exists
  const exists = await db.UserBonusRolloverTransaction.findOne({
    where: {
      userId: user.id,
      bonusId: userActiveDepositBonus.bonusId,
      depositTransactionId: depositTxnId,
    },
  }, {
    useMaster: true
  });

  // If the record does not exist, create a new rollover transaction entry within the current DB transaction
  if (!exists) {
    await db.UserBonusRolloverTransaction.create({
      userId: user.id,
      bonusId: userActiveDepositBonus.bonusId,
      depositTransactionId: depositTxnId,
      createdAt: new Date()
    }, { transaction: sequelizeTransaction });
  }

  return true
}
