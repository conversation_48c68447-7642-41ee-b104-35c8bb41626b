import { Op } from 'sequelize'
import { AFFILIATE, ALANBASE_EVENT_TYPES_MAPPING } from '../common/constants'
import db, { sequelize } from '../db/models'

const getRegistrationData = async (userIds, eventType, tenantId) => {
  eventType = ALANBASE_EVENT_TYPES_MAPPING.alanbase_registration
  userIds = userIds.map(id => parseInt(id))
  const data = []
  const users = await db.User.findAll({
    where: {
      id: { [Op.in]: userIds },
      tenantId
    },
    attributes: ['id', 'email', 'userName', 'createdAt',],
    include: [
      {
        model: db.Wallet,
        attributes: ['id'],
        required: true,
        as: 'W',
        include: {
          model: db.Currency,
          attributes: ['code'],
          required: true
        }
      }
    ]
  })
  if (users && users.length > 0) {
    for (const user of users) {
      const clickId = await getClickIdByUserId(user.id, tenantId)
      if (clickId) {
        const dateTime = Math.floor(user.createdAt.getTime() / 1000);
        const userData = {
          click_id: clickId,
          goal: eventType,
          datetime: dateTime,
          tid: `${user.id}_registration`,
          custom1: user.id, // Custom 1 Fix For userId
          custom2: user.userName ? user.userName : '',
          custom3: user.W?.Currency?.code,
          custom4: "",
          custom5: "",
          idempotency_key: user.id
        }
        data.push(userData)
      }
    }
    return data
  }

  return []
}

const getDepositData = async (transactionIds, eventType, tenantId) => {
  eventType = ALANBASE_EVENT_TYPES_MAPPING.alanbase_deposit
  const data = [];

  transactionIds = transactionIds.map(id => parseInt(id))
  const query = `
    SELECT
    t.id,
    t.amount,
    t.transaction_id,
    t.created_at,
    t.target_after_balance,
    t.actionee_id,
    t.actionee_type,
    t.transaction_type,
    w.owner_id AS wallet_user_id,
    t.target_wallet_id,
    au.click_id AS click_id,
    c.code AS currency_code,
    tmd.meta_data AS meta_data
    FROM
        public.transactions t
    LEFT JOIN
        public.wallets w
    ON
        CAST(t.target_wallet_id AS INTEGER) = w.id AND w.owner_type = 'User'
    LEFT JOIN
        public.users_affiliate au
    ON
        au.user_id = w.owner_id AND au.affiliate =:affiliate
    LEFT JOIN
        public.currencies c
    ON
        c.id = w.currency_id
    LEFT JOIN
        public.transactions_meta_data tmd
    ON
        tmd.transaction_id = t.id
    WHERE
        t.id IN (:ids)
        AND t.tenant_id = :tenant_id
  `
  const depositTransactions = await sequelize.query(query, {
    replacements: { ids: transactionIds, tenant_id: tenantId, affiliate : AFFILIATE.ALANBASE },
    type: sequelize.QueryTypes.SELECT
  })

  for (const transaction of depositTransactions) {
    const userId = transaction.actionee_type === 'User' ? +transaction.actionee_id : +transaction.wallet_user_id

    const depositType = transaction.comments !== 'Deposit Request Approved' && !transaction.meta_data ? 'Agent Deposit' : 'User Deposit'

    const dateTime = Math.floor(transaction.created_at.getTime() / 1000);

    if ([55, 57].includes(tenantId) && transaction.currency_code == 'chips') {
      transaction.currency_code = 'INR'
    }

    const transactionData = {
      click_id: transaction.click_id,
      event: eventType,
      value: transaction.amount,
      currency: transaction.currency_code,
      datetime: dateTime,
      custom1: userId.toString(), // Custom 1 Fix For userId
      custom2: transaction.transaction_id,
      custom3: depositType,
      custom4: "",
      custom5: "",
      idempotency_key: transaction.transaction_id
    }

    data.push(transactionData)
  }
  return data
}

const getWithdrawalData = async (transactionIds, eventType, tenantId) => {
  eventType = ALANBASE_EVENT_TYPES_MAPPING.alanbase_withdrawal_completed
  transactionIds = transactionIds.map(id => parseInt(id));
  const data = []
  const query = `
  SELECT
  t.id,
  t.amount,
  t.transaction_id,
  t.source_after_balance,
  t.comments,
  t.actionee_id,
  t.actionee_type,
  t.transaction_type,
  t.updated_at,
  w.owner_id AS wallet_user_id,
  au.click_id AS click_id,
  c.code AS currency_code
  FROM
    public.transactions t
  LEFT JOIN
    public.wallets w
  ON
    CAST(t.source_wallet_id AS INTEGER) = w.id AND w.owner_type = 'User'
  LEFT JOIN
    public.users_affiliate au
  ON
    au.user_id = w.owner_id AND au.affiliate =:affiliate
  LEFT JOIN
    public.currencies c
  ON
    c.id = w.currency_id
  WHERE
    t.id IN (:ids) and t.tenant_id = :tenant_id
  `
  const withdrawalTransactions = await sequelize.query(query, {
    replacements: { ids: transactionIds, tenant_id: tenantId, affiliate : AFFILIATE.ALANBASE },
    type: sequelize.QueryTypes.SELECT
  })

  for (const transaction of withdrawalTransactions) {
    const userId = transaction.actionee_type === 'User' ? +transaction.actionee_id : +transaction.wallet_user_id

    let withdrawalType = 'Online withdrawal approved.'
    if (transaction.actionee_type === 'AdminUser' && transaction.comments !== 'approved by admin') {
      withdrawalType = 'Agent Withdrawal Approved'
    }

    const dateTime = Math.floor(transaction.updated_at.getTime() / 1000);

    if ([55, 57].includes(tenantId) && transaction.currency_code == 'chips') {
      transaction.currency_code = 'INR'
    }

    const transactionData = {
      click_id: transaction.click_id,
      goal: eventType,
      value: transaction.amount,
      currency: transaction.currency_code,
      datetime: dateTime,
      tid: transaction.transaction_id,
      custom1: userId.toString(), // Custom 1 Fix For userId
      custom2: transaction.transaction_id,
      custom3: withdrawalType,
      custom4: "",
      custom5: "",
      idempotency_key: transaction.transaction_id
    }
    data.push(transactionData)
  }
  return data
}

const getCasinoBetWinData = async (transactionIds, eventType, tenantId,) => {
  const data = []
  const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
  for (let txnId of transactionIds) {
    delay(10)
    const casinoTransactionObj = await db.Transaction.findOne({
      where: {
        id: parseInt(txnId),
        tenantId,
        debitTransactionId: {
          [Op.ne]: null
        }
      },
      attributes: ['debitTransactionId', 'transactionId'],
      useMaster: true,
      raw: true
    })
    if (!casinoTransactionObj) {
      throw new Error('casino-transaction not found!')
    }
    const debitTransactionId = casinoTransactionObj.debitTransactionId
    const transactions = await db.Transaction.findAll({
      where: {
        [Op.or]: [
          { transactionId: debitTransactionId },
          { debitTransactionId: debitTransactionId }
        ],
        tenantId
      },
      include: [
        {
          model: db.Currency,
          as: 'tc',
          attributes: ['code']
        },
        {
          model: db.Currency,
          as: 'sc',
          attributes: ['code']
        }
      ],
      raw: true,
      nest: true,
      useMaster: true,
      attributes: ['id', 'actioneeId', 'createdAt', 'transactionId', 'debitTransactionId', 'amount', 'gameId', 'seatId', 'tableId'],
    })
    if (!transactions || transactions?.length === 0) {
      throw new Error('casino-transactions not found!')
    }

    for (const transaction of transactions) {
      let alanbaseEventType = '';
      let idempotencyKey = '';
      let currencyCode = '';
      const dateTime = Math.floor(transaction.createdAt.getTime() / 1000);

      if (transaction.debitTransactionId && transaction.tc?.code) { // win transactions
        alanbaseEventType = ALANBASE_EVENT_TYPES_MAPPING.alanbase_win
        idempotencyKey = `${dateTime}_${transaction.id}_win`
        currencyCode = transaction.tc?.code
      }
      else { // bet transactions
        alanbaseEventType = ALANBASE_EVENT_TYPES_MAPPING.alanbase_bet
        idempotencyKey = `${dateTime}_${transaction.id}_bet`;
        currencyCode = transaction.sc?.code
      }

      if ([55, 57].includes(tenantId) && currencyCode == 'chips') {
        currencyCode = 'INR'
      }

      const userId = transaction.actioneeId;
      const gameId = transaction.gameId;
      const seatId = transaction.seatId;
      const roundId = transaction.roundId;

      const clickId = await getClickIdByUserId(userId, tenantId)

      if (clickId) {
        data.push({
          click_id: clickId,
          event: alanbaseEventType,
          value: transaction.amount,
          currency: currencyCode,
          datetime: dateTime,
          custom1: userId.toString(),
          custom2: transaction.transactionId,
          custom3: gameId || '',
          custom4: seatId || '',
          custom5: roundId || '',
          idempotency_key: idempotencyKey
        })
      }
    }
  }
  return data
}

const getSportsBookData = async (transactionIds, eventType, tenantId,) => {
  const alanbaseEventType = ALANBASE_EVENT_TYPES_MAPPING[eventType];
  transactionIds = transactionIds.map(id => parseInt(id));
  const data = [];
  const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
  const transactions = await db.BetsTransaction.findAll({
    where: {
      id: {
        [Op.in]: transactionIds,
      },
      tenantId
    },
    include: [
      {
        model: db.Currency,
        as: 'tc',
        attributes: ['code']
      },
      {
        model: db.Currency,
        as: 'sc',
        attributes: ['code']
      }
    ],
    raw: true,
    nest: true,
    useMaster: true,
    attributes: ['id', 'createdAt', 'journalEntry', 'userId', 'transactionId', 'amount', 'marketId'],
  })

  if (!transactions || transactions?.length === 0) {
    throw new Error('Sports Transaction not found!')
  }

  for (const transaction of transactions) {
    delay(10);
    let currencyCode = '';

    const dateTime = Math.floor(transaction.createdAt.getTime() / 1000);

    const userId = transaction.userId;
    const marketId = transaction.marketId;

    const clickId = await getClickIdByUserId(userId, tenantId)
    if (clickId) {
      let totalAmount = +parseInt(transaction.amount) + (transaction.nonCashAmount ? parseInt(transaction.nonCashAmount) : 0);

      if (transaction.journalEntry == 'DR') {
        currencyCode = transaction.sc?.code
        totalAmount = -totalAmount
      }
      else {
        currencyCode = transaction.tc?.code
      }

      if ([55, 57].includes(tenantId) && currencyCode == 'chips') {
        currencyCode = 'INR'
      }

      data.push({
        click_id: clickId,
        event: alanbaseEventType,
        value: totalAmount,
        currency: currencyCode,
        datetime: dateTime,
        custom1: userId.toString(),
        custom2: transaction.transactionId,
        custom3: marketId,
        custom4: transaction.journalEntry,
        custom5: "",
        idempotency_key: `${transaction.journalEntry}_${dateTime}_${transaction.transactionId}`
      })
    }
  }

  return data
}

const getClickIdByUserId = async (userId, tenantId) => {
  const clickData = await db.UsersAffiliate.findOne({
    where: {
      userId,
      tenantId,
      affiliate : AFFILIATE.ALANBASE
    },
    attributes: ['clickId']
  })
  return clickData?.clickId
}

export { getCasinoBetWinData, getDepositData, getRegistrationData, getSportsBookData, getWithdrawalData }
