import axios from 'axios'
import moment from 'moment'
import { Op, QueryTypes } from 'sequelize'
import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import Logger from '../libs/logger'
import { BET_TYPE, CRON_LOG_STATUS, PROD_WHITECLIFF_PROVIDER, SPORT_CASINO_TXN_TYPE, STAGE_WHITECLIFF_PROVIDER, TRANSACTION_TYPES, WHITECLIFF_CREDENTIALS, WHITECLIFF_INTEGRATION_CONSTANT, WHITECLIFF_SPORTS_PROVIDERS } from './constants'
export default async (requestData) => {

  const cronLog = {}
  cronLog.startTime = new Date()
  try {

    const {
      TenantCredential: TenantCredentialModel,
      BetsTransaction: BetsTransactionModel,
      BetsBetslip: BetsBetslipModel,
      BetsBet: BetsBetModel,
      Transaction: TransactionModel,
    } = db
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'white_cliff_sports_rawdata',
        status: 1
      },
      attributes: ['id']
    })

    if (!queueProcessStatus) {
      return false;
    }

    cronLog.cronId = queueProcessStatus?.id
    const casinoProviderId = config.get('env') === 'production' ? PROD_WHITECLIFF_PROVIDER : STAGE_WHITECLIFF_PROVIDER

    const tenants = await sequelize.query(`
      SELECT "tenant_id" AS "tenantId"
      FROM "public"."tenant_theme_settings" AS "TenantThemeSetting" WHERE '${casinoProviderId}' = ANY (string_to_array(assigned_providers, ','))`,
      { type: QueryTypes.SELECT, useMaster: false })

    for (const tenant of tenants) {
      const tenantId = tenant.tenantId

      let allowedCurrencies = await sequelize.query(`
        SELECT  c.code,c.id
        FROM  tenant_configurations tc
        INNER JOIN currencies c
        ON c.id = ANY (STRING_TO_ARRAY(tc.allowed_currencies, ',')::BIGINT[])
        WHERE tc.tenant_id = '${tenantId}'`,
        { type: QueryTypes.SELECT, useMaster: false })

      if (allowedCurrencies.length <= 0) {
        throw new Error('whiteCliffWorker: No Allowed currencies')
      }
      allowedCurrencies = [{ code: 'INR', id: 2 }]
      for (const currencyObj of allowedCurrencies) {

        const currencyCode = currencyObj.code
        let keyCurrency = currencyCode == 'chips' ? 'LKR' : currencyCode
        const keys = {
          url: WHITECLIFF_CREDENTIALS.WHITECLIFF_URL,
          agCode: WHITECLIFF_CREDENTIALS[keyCurrency]?.WHITECLIFF_AG_CODE,
          agToken: WHITECLIFF_CREDENTIALS[keyCurrency]?.WHITECLIFF_AG_TOKEN,
        };

        const credentials = await TenantCredentialModel.findAll({
          where: {
            key: Object.values(keys),
            tenantId: tenantId
          },
          raw: true
        });

        const values = {};
        Object.keys(keys).forEach((creds) => {
          const val = credentials.find(obj => obj.key === keys[creds]);
          if (val) {
            values[creds] = val.value;
          }
        });

        if (!values.url || !values.agCode || !values.agToken) {
          continue
        }
        const currentDate = moment();

        let startDate = moment(currentDate).subtract(5, 'minutes').format('YYYY-MM-DD HH:mm:ss');
        let endDate = currentDate.format('YYYY-MM-DD HH:mm:ss');

        if (requestData?.startDate && requestData?.endDate) {
          startDate = requestData.startDate
          endDate = requestData.endDate
        }

        const rawDataUrl = `${values.url}/getRawData`;

        for (const provider of WHITECLIFF_SPORTS_PROVIDERS) {

          const whiteCliffRawData = await axios({
            url: rawDataUrl,
            method: 'post',
            headers: {
              'ag-code': values.agCode,
              'ag-token': values.agToken,
              'Content-Type': 'application/json'
            },
            data: {
              prd_id: provider.prdId,
              start_date: startDate,
              end_date: endDate,
              // "is_settle": 0
            }
          })

          if (!whiteCliffRawData?.data?.raw_data) {
            continue
          }

          const sequelizeTransaction = await sequelize.transaction()
          try {
            const whiteCliffRawDataObject = Object.entries(whiteCliffRawData?.data?.raw_data || {}).reduce((acc, [trxnId, trxnData]) => {
              let trxnObj = {
                trxnId: trxnId,
                status: trxnData.transaction_status
              }
              if (trxnData.transaction_status != 0) {
                trxnObj = {
                  trxnId: trxnId,
                  status: trxnData.transaction_status,
                  betId: trxnData?.Bet['@attributes'].BetID,
                  betTypeId: trxnData?.Bet['@attributes'].BetTypeID,
                  betTypeName: trxnData?.Bet['@attributes'].BetTypeName,
                  stake: trxnData?.Bet['@attributes'].Stake,
                  betStatus: trxnData?.Bet['@attributes'].Status,
                  odds: trxnData?.Bet['@attributes'].Odds,
                  leagueId: trxnData?.Bet['@attributes'].LeagueID,
                  marketId: trxnData?.Bet['@attributes'].MarketID,
                  eventName: trxnData?.Bet['@attributes'].EventName,
                  leagueName: trxnData?.Bet['@attributes'].LeagueName,
                  eventTypeName: trxnData?.Bet['@attributes'].EventTypeName,
                  eventTypeId: trxnData?.Bet['@attributes'].EventTypeID,
                  eventId: trxnData?.Bet['@attributes'].EventID,
                  points: trxnData?.Bet['@attributes'].Points,
                  isEventClosed: trxnData?.Bet['@attributes'].isEventClosed,
                  isLive: trxnData?.Bet['@attributes'].IsLive,
                  lineTypeName: trxnData?.Bet['@attributes'].LineTypeName,
                  oddsDec: trxnData?.Bet['@attributes'].OddsDec,
                  eventDate: trxnData?.Bet['@attributes'].EventDate,
                  yourBet: trxnData?.Bet['@attributes'].YourBet
                }
              }

              acc.allTransactions.push(trxnObj)
              return acc

            }, { allTransactions: [] })

            for (const trxn of whiteCliffRawDataObject.allTransactions) {

              const trasactions = await TransactionModel.findAll({
                where: {
                  tenantId: +tenantId,
                  [Op.or]: [
                    { transactionId: '' + trxn.trxnId },
                    { debitTransactionId: '' + trxn.trxnId }
                  ],
                  providerId: casinoProviderId,
                },
                useMaster: true,
                raw: true,
              })

              if (trasactions.length <= 0) {
                continue
              }

              const userId = trasactions[0].actioneeId;

              const existingBetsTransactions = await BetsTransactionModel.findAll({
                where: {
                  transactionId: trxn.trxnId,
                  tenantId,
                  userId,
                  providerId: casinoProviderId,
                },
                attributes: ['id', 'betslipId', 'reverseTransactionId'],
                raw: true
              })

              let betslipId;
              let existingReverseIds = []  // reference: TransactionModel.id
              if (existingBetsTransactions.length > 0) {
                existingReverseIds = existingBetsTransactions.map(el => el.reverseTransactionId)
                betslipId = existingBetsTransactions[0].betslipId
              }
              else {
                const betSlipObject = {
                  bettype: BET_TYPE.BACK,
                  stake: trxn.stake,
                  userId: userId,
                  multiPrice: trxn.oddsDec,
                  betslipStatus: WHITECLIFF_INTEGRATION_CONSTANT.BET_SLIP_STATUS_ACCEPTED,
                  possibleWinAmount: 0,
                  settlementStatus: WHITECLIFF_INTEGRATION_CONSTANT.IN_GAME,
                  // run: '',
                  isDeleted: false,
                  createdAt: new Date(),
                  updatedAt: new Date()
                }

                const betSlip = await BetsBetslipModel.create(betSlipObject, { transaction: sequelizeTransaction })
                betslipId = betSlip.id;

                const betsBetsObject = {
                  marketId: trxn.eventId,
                  // marketId: trxn.marketId,  // type Integer
                  market: trxn.lineTypeName,
                  fixtureId: trxn.eventId,
                  eventId: trxn.eventId,
                  betId: trxn.betId,
                  price: trxn.oddsDec,
                  match: trxn.eventName,
                  startDate: trxn.eventDate,
                  betslipId: betslipId,
                  betStatus: 1,
                  isDeleted: false,
                  createdAt: new Date(),
                  updatedAt: new Date()
                }

                await BetsBetModel.create(betsBetsObject, { transaction: sequelizeTransaction })
              }

              let transactionObject = {
                transactionId: trxn.trxnId,
                tenantId: tenantId,
                userId: userId,
                reference: `${userId}-${new Date().toISOString()}`,
                actioneeId: userId,
                marketId: trxn.marketId,
                runnerName: trxn.yourBet,
                providerId: casinoProviderId,
                isDeleted: false,
                betslipId: betslipId,
              }

              const betTransactions = []

              for (const element of trasactions) {

                if (existingReverseIds.includes(element.id)) {
                  continue
                }

                const trxnDetails = {
                  sourceWalletId: element.sourceWalletId,
                  sourceCurrencyId: element.sourceCurrencyId,
                  conversionRate: element.conversionRate,
                  targetWalletId: element.targetWalletId,
                  targetCurrencyId: element.targetCurrencyId,
                  createdAt: element.createdAt,
                  updatedAt: element.updatedAt,
                  description: element.comments,
                  reverseTransactionId: element.id  // Storing TransactionModel.id for avoiding Duplicacy
                }

                if (element.transactionType == TRANSACTION_TYPES.DEBIT) {
                  let debitTransactionObj = {
                    ...transactionObject,
                    ...trxnDetails,
                    journalEntry: WHITECLIFF_INTEGRATION_CONSTANT.DEBIT,
                    transactionCode: SPORT_CASINO_TXN_TYPE.EXCHANGE_PLACE_BET_CASH_DEBIT,
                    paymentFor: WHITECLIFF_INTEGRATION_CONSTANT.PLACE_BET,
                    amount: element.amount,
                    sourceBeforeBalance: element.sourceBeforeBalance,
                    sourceAfterBalance: element.sourceAfterBalance,
                    otherCurrencyAmount: element.otherCurrencyAmount,
                  }
                  betTransactions.push(debitTransactionObj)
                }

                if (element.transactionType == TRANSACTION_TYPES.DEBIT_NO_CASH) {

                  let debitNonCashTransactionObj = {
                    ...transactionObject,
                    ...trxnDetails,
                    journalEntry: WHITECLIFF_INTEGRATION_CONSTANT.DEBIT,
                    transactionCode: SPORT_CASINO_TXN_TYPE.EXCHANGE_PLACE_BET_NON_CASH_DEBIT,
                    paymentFor: WHITECLIFF_INTEGRATION_CONSTANT.PLACE_BET,
                    amount: element.amount,
                    nonCashAmount: element.amount,
                    sourceNonCashBeforeBalance: element.sourceBeforeBalance,
                    sourceNonCashAfterBalance: element.sourceAfterBalance,
                    otherCurrencyNonCashAmount: element.otherCurrencyAmount
                  }

                  betTransactions.push(debitNonCashTransactionObj)
                }

                if (element.transactionType == TRANSACTION_TYPES.DEBIT_OTB_CASH) {

                  let debitOtbCashTransactionObj = {
                    ...transactionObject,
                    ...trxnDetails,
                    journalEntry: WHITECLIFF_INTEGRATION_CONSTANT.DEBIT,
                    transactionCode: SPORT_CASINO_TXN_TYPE.EXCHANGE_PLACE_BET_NON_CASH_DEBIT,
                    paymentFor: WHITECLIFF_INTEGRATION_CONSTANT.PLACE_BET,
                    nonCashAmount: element.amount,
                    sourceNonCashBeforeBalance: element.sourceBeforeBalance,
                    sourceNonCashAfterBalance: element.sourceAfterBalance,
                    otherCurrencyNonCashAmount: element.otherCurrencyAmount
                  }

                  betTransactions.push(debitOtbCashTransactionObj)
                }

                if (element.transactionType == TRANSACTION_TYPES.CREDIT) {
                  let creditTransactionObj = {
                    ...transactionObject,
                    ...trxnDetails,
                    journalEntry: WHITECLIFF_INTEGRATION_CONSTANT.CREDIT,
                    transactionCode: SPORT_CASINO_TXN_TYPE.EXCHANGE_SETTLE_MARKET_CASH_CREDIT,
                    paymentFor: WHITECLIFF_INTEGRATION_CONSTANT.SETTLE_MARKET_WON,
                    amount: element.amount,
                    targetBeforeBalance: element.targetBeforeBalance,
                    targetAfterBalance: element.targetAfterBalance,
                    otherCurrencyAmount: element.otherCurrencyAmount,
                  }
                  betTransactions.push(creditTransactionObj)
                }

                if (element.transactionType == TRANSACTION_TYPES.CREDIT_NO_CASH) {
                  let creditNonCashTransactionObj = {
                    ...transactionObject,
                    ...trxnDetails,
                    journalEntry: WHITECLIFF_INTEGRATION_CONSTANT.CREDIT,
                    transactionCode: SPORT_CASINO_TXN_TYPE.EXCHANGE_SETTLE_MARKET_CASH_CREDIT,
                    paymentFor: WHITECLIFF_INTEGRATION_CONSTANT.SETTLE_MARKET_WON,
                    nonCashAmount: element.amount,
                    targetNonCashBeforeBalance: element.targetBeforeBalance,
                    targetNonCashAfterBalance: element.targetAfterBalance,
                    otherCurrencyNonCashAmount: element.otherCurrencyAmount
                  }
                  betTransactions.push(creditNonCashTransactionObj)
                }
              }

              await BetsTransactionModel.bulkCreate(betTransactions, { transaction: sequelizeTransaction })
            }
            await sequelizeTransaction.commit()
          }
          catch (error) {
            await sequelizeTransaction.rollback()
            throw error
          }

        }
      }
    }

    cronLog.endTime = new Date()
    await db.CronLog.create(cronLog)
    return {
      success: true
    }
  } catch (error) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = error.message || null
    cronLog.endTime = new Date()
    await db.CronLog.create(cronLog)
    Logger.info(error, '=========WhiteCliff game population error==========\n')
    return {
      success: false,
      Error: {
        stack: error.stack
      }
    }
  }
}
