import moment from 'moment'
import { Sequelize } from 'sequelize'
import { v4 as getUUIDTransactionId } from 'uuid'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../common/errorLog'
import db, { sequelize } from '../../db/models'
import { TRANSACTION_STATUS_APPROVED, TRANSACTION_STATUS_REJECTED } from '../../utils/constants/constant'
import { updateUserBonus } from '../bonusEngine/updateUserBonusManual'
import { ALANBASE_EVENT_TYPES, ALLOWED_PERMISSIONS, DEPOSIT_REQUEST_STATUS, DEPOSIT_WITHDRAW_JOB_STATUS, DEPOSIT_WITHDRAW_USER_STATUS, QUEUE_WORKER_CONSTANT, TABLES, TRANSACTION_TYPES, USER_TYPE, UTR } from '../constants'
import { publishToRedis } from '../queueService/publishToRedis'
import superTenantId from '../superTenantId'
import { userFirstDeposit } from '../userFirstDeposit'
import { verifyReferralCode } from '../verifyReferralCode'


export default async (payload) => {
  const {
    DepositRequest: DepositRequestModel,
    Transaction: TransactionModel,
    Wallet: WalletModel,
    QueueLog: QueueLogModel,
    DepositWithdrawJob: DepositWithdrawJobModel,
    DepositWithdrawUser: DepositWithdrawUserModel,
    AdminUser: AdminUserModel,
    TenantThemeSetting: TenantThemeSettingModel,
    TenantBankConfiguration: TenantBankConfigurationModel,
    UtrHistory: UtrHistoryModel,
    Currency: CurrencyModel,
    User: UserModel
  } = db
  const { transactionId: depositWithdrawUserId } = payload
  const sequelizeTransaction = await sequelize.transaction()
  let jobId = null
  let queueLogInfo = []

  try {
    const depositWithdrawUser = await DepositWithdrawUserModel.findOne({
      where: { id: depositWithdrawUserId },
      include: [{ model: DepositWithdrawJobModel, attributes: ['id', 'type', 'tenantId'] }]
    })

    if (!depositWithdrawUser) {
      throw Object.assign(new Error(`DepositWithdrawUser not found for ID: ${depositWithdrawUserId}`), { notFound: true })
    }

    jobId = depositWithdrawUser.jobId
    const depositCsvData = depositWithdrawUser?.jobData

    await DepositWithdrawJobModel.update({ status: DEPOSIT_WITHDRAW_JOB_STATUS.IN_PROGRESS }, {
      where: {
        id: jobId,
        status: DEPOSIT_WITHDRAW_JOB_STATUS.NOT_STARTED
      }
    })

    const depositRequest = await DepositRequestModel.findOne({
      where: {
        id: depositCsvData?.id, verifyStatus: DEPOSIT_REQUEST_STATUS.VERIFIED, status: 'opened'
      },
      attributes: ['id', 'amount', 'status', 'userId', 'manualDepositType', 'countryCode', 'createdAt', 'utrNumber', 'checkerData', 'makerData', 'verifyStatus'],
      raw: true
    })

    if (!depositRequest) {
      await DepositWithdrawUserModel.update({
        status: DEPOSIT_WITHDRAW_USER_STATUS.FAILED,
        error: { message: 'DepositRequest not found' }
      }, { where: { id: depositWithdrawUserId } })

      throw Object.assign(new Error('DepositRequest not found for Id'), { notFound: true })
    }

    const adminUser = await AdminUserModel.findOne({
      where: { id: depositCsvData?.admin_user },
      attributes: ['id', 'agentName', 'email', 'tenantId', 'parentType'],
      raw: true
    })

    let makerData = null
    if (adminUser) {
      makerData = {
        id: adminUser.id,
        email: adminUser.email,
        user_name: adminUser.agentName,
        remark: depositCsvData?.remark,
        time_stamp: new Date()
      }
    }

    await DepositRequestModel.update({ makerData }, { where: { id: depositCsvData.id } }, { transaction: sequelizeTransaction })

    let updateArr = {}
    let trnAmount = depositRequest.amount
    let sourceWallet, targetWallet, sourceExchangeRate, targetExchangeRate
    let status = depositCsvData?.status?.toLowerCase()?.trim()
    let trackingId = ''
    const queueIds = []
    let cryptoExchangeRate; let verifyStatus = depositRequest.verifyStatus; let utrStatus = UTR.STATUS.OPENED; let createdValues

    [sourceWallet, targetWallet] = await Promise.all([WalletModel.findOne({
      attributes: ['id', 'currencyId', 'amount'], where: { id: depositCsvData?.source_wallet }
    }), WalletModel.findOne({
      where: { ownerId: depositRequest.userId, ownerType: TABLES.USER }
    })])

    const tenantId = await superTenantId(targetWallet.ownerId, targetWallet.ownerType)
    if (!tenantId) throw new Error('Tenant ID not found')

    if (TRANSACTION_STATUS_APPROVED.includes(status)) {

      if (!targetWallet) {
        await DepositWithdrawUserModel.update({
          status: DEPOSIT_WITHDRAW_USER_STATUS.FAILED,
          error: { message: 'Target wallet not found' }
        }, { where: { id: depositWithdrawUserId } })

        throw Object.assign(new Error('Target wallet not found'), { notFound: true })
      }

      [sourceExchangeRate, targetExchangeRate] = await Promise.all([CurrencyModel.findOne({
        where: { id: sourceWallet.currencyId },
        attributes: ['exchangeRate']
      }), CurrencyModel.findOne({
        where: { id: targetWallet.currencyId },
        attributes: ['exchangeRate', 'internalCode']
      })])

      if (depositRequest.countryCode) {
        const formattedDate = moment(depositRequest.createdAt).format('YYYY-MM-DD')

        const [getOldExchangeRate, getTargetOldExchangeRate] = await Promise.all([sequelize.query(`
            SELECT currencies.id, currencies.internal_code, currencies.exchange_rate, conversion_history.new_exchange_rate
            FROM public.currencies
            JOIN public.conversion_history
            ON currencies.id = conversion_history.currency_id
            WHERE currencies.country_code = :countryCode
            AND CAST(conversion_history.created_at AS TEXT) ILIKE :createdAt
            LIMIT 1
          `, {
          replacements: {
            countryCode: depositRequest.countryCode, createdAt: `%${formattedDate}%`
          },
          type: sequelize.QueryTypes.SELECT
        }), sequelize.query(`
            SELECT new_exchange_rate
            FROM public.conversion_history
            WHERE currency_id = :currencyId
            AND CAST(created_at AS TEXT) ILIKE :createdAt
            LIMIT 1
          `, {
          replacements: {
            currencyId: targetWallet.currencyId, createdAt: `%${formattedDate}%`
          },
          type: sequelize.QueryTypes.SELECT
        })])

        const oldExchangeRate = getOldExchangeRate && getOldExchangeRate.length > 0 ? getOldExchangeRate[0] : null
        const targetOldExchangeRate = getTargetOldExchangeRate && getTargetOldExchangeRate.length > 0 ? getTargetOldExchangeRate[0] : null
        if (!oldExchangeRate || !targetOldExchangeRate) throw new Error('Exchange rate not found for the given date')
        updateArr = {
          requestedCurrencyCode: oldExchangeRate?.internal_code,
          requestedAmount: depositRequest.amount,
          currencyConversion: targetOldExchangeRate.new_exchange_rate / targetOldExchangeRate.new_exchange_rate
        }

        if (oldExchangeRate.internal_code !== targetExchangeRate.internal_code) {
          depositRequest.amount = parseFloat(depositRequest.amount) * (parseFloat(targetOldExchangeRate.new_exchange_rate) / parseFloat(oldExchangeRate.new_exchange_rate))
          depositRequest.amount = parseFloat(depositRequest.amount.toFixed(5))
          updateArr.amount = depositRequest.amount
        }

        await DepositRequestModel.update(updateArr, { where: { id: depositRequest.id } }, { transaction: sequelizeTransaction })
      }

      trnAmount = depositRequest.amount
      if (depositRequest.manualDepositType === 2) {
        cryptoExchangeRate = await TenantBankConfigurationModel.findOne({
          where: {
            type: 2,
            tenantId: adminUser.tenantId
          },
          attributes: ['cryptoExchangeRate', 'bankName']
        })
        if (!cryptoExchangeRate) throw new Error('Add virtual wallet before proceeding further.')

        depositRequest.amount = Number((depositRequest.amount * cryptoExchangeRate.cryptoExchangeRate).toFixed(4))
        await DepositRequestModel.update({
          requestedCurrencyCode: cryptoExchangeRate.bankName,
          amount: depositRequest.amount,
          requestedAmount: trnAmount,
          currencyConversion: cryptoExchangeRate.cryptoExchangeRate
        }, { where: { id: depositRequest.id } }, { transaction: sequelizeTransaction })
      } else {
        depositRequest.amount = Number((depositRequest.amount * (sourceExchangeRate.exchangeRate / targetExchangeRate.exchangeRate)).toFixed(4))
      }

      if (Number(sourceWallet.amount.toFixed(5)) < Number(depositRequest.amount.toFixed(5))) {
        await DepositWithdrawUserModel.update({
          status: DEPOSIT_WITHDRAW_USER_STATUS.FAILED,
          error: { message: 'Insufficient amount in your wallet!' }
        }, { where: { id: depositWithdrawUserId } })

        throw Object.assign(new Error('"Insufficient amount in your wallet!', { notFound: true }))
      }

      status = DEPOSIT_REQUEST_STATUS.COMPLETED
      utrStatus = UTR.STATUS.APPROVED
      trackingId = getUUIDTransactionId()



      const insertData = {
        actioneeType: adminUser.parentType,
        actioneeId: adminUser.id,
        targetWalletId: targetWallet.id,
        targetBeforeBalance: targetWallet.amount,
        status: 'success',
        tenantId,
        targetCurrencyId: targetWallet.currencyId,
        createdAt: new Date(),
        comments: 'Deposit Request Approved',
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        amount: depositRequest.manualDepositType === 2 ? depositRequest.amount : trnAmount,
        paymentMethod: 'manual',
        transactionId: trackingId,
        sourceWalletId: sourceWallet.id,
        sourceBeforeBalance: sourceWallet.amount,
        // metaData: {
        //   maker_data: {
        //     user_name: adminUser.agentName,
        //     remark: depositCsvData.remark,
        //     id: adminUser.id,
        //     email: adminUser.email,
        //     time_stamp: new Date()
        //   },
        //   checker_data: {
        //     user_name: depositRequest.checkerData?.user_name || '',
        //     remark: depositRequest.checkerData?.remark || '',
        //     id: depositRequest.checkerData?.id || '',
        //     email: depositRequest.checkerData?.email || '',
        //     time_stamp: depositRequest.checkerData?.time_stamp || ''
        //   }
        // }
      }

      let amount
      if (depositRequest.manualDepositType === 2) {
        insertData.conversionRate = parseFloat(cryptoExchangeRate.cryptoExchangeRate)
        amount = parseFloat(depositRequest.amount)
      } else {
        insertData.conversionRate = parseFloat(targetExchangeRate.exchangeRate / sourceExchangeRate.exchangeRate)
        amount = parseFloat(depositRequest.amount) * parseFloat(targetExchangeRate.exchangeRate / sourceExchangeRate.exchangeRate)
      }

      insertData.sourceAfterBalance = parseFloat(sourceWallet.amount) - parseFloat(depositRequest.amount)
      insertData.targetAfterBalance = parseFloat(targetWallet.amount) + parseFloat(amount)

      const amountInCurrency = await sequelize.query('select v3_currency_conversion  as otherconversions  from v3_currency_conversion (:currencyId, :tenantId, :amount)', {
        replacements: {
          currencyId: targetWallet.currencyId, tenantId: tenantId, amount: depositRequest.amount
        },
        type: Sequelize.QueryTypes.SELECT
      })
      insertData.otherCurrencyAmount = JSON.stringify(amountInCurrency[0].otherconversions)
      createdValues = await TransactionModel.create(insertData, { transaction: sequelizeTransaction })

      // Meta data
      const txnMetaData = {
        transactionId: createdValues?.id,
        tenantId: tenantId,
        transactionType: 2,
        createdAt:new Date(),
        updatedAt:new Date(),
        metaData: {
          maker_data: {
            user_name: adminUser.agentName,
            remark: depositCsvData.remark,
            id: adminUser.id,
            email: adminUser.email,
            time_stamp: new Date()
          },
          checker_data: {
            user_name: depositRequest.checkerData?.user_name || '',
            remark: depositRequest.checkerData?.remark || '',
            id: depositRequest.checkerData?.id || '',
            email: depositRequest.checkerData?.email || '',
            time_stamp: depositRequest.checkerData?.time_stamp || ''
          }
        }
      }
      await db.TransactionsMetaData.create(txnMetaData, { transaction: sequelizeTransaction })

      const txnIds = []
      if (createdValues) {
        await userFirstDeposit(sequelizeTransaction, createdValues)
        txnIds.push(createdValues?.id)
      }
      await verifyReferralCode(sequelizeTransaction, createdValues, DEPOSIT_REQUEST_STATUS.COMPLETED, depositCsvData.id, tenantId)

    const [sourceWalletLocking, targetWalletLocking] = await Promise.all([
      WalletModel.findOne({
        where: { id: sourceWallet.id },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: WalletModel
        },
        skipLocked: false
      }),
      WalletModel.findOne({
        where: { id: targetWallet.id },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: WalletModel
        },
        skipLocked: false
      })
    ]);

    await Promise.all([sourceWalletLocking.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction }), targetWalletLocking.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })])

    sourceWalletLocking.amount = parseFloat(insertData.sourceAfterBalance)
    targetWalletLocking.amount = parseFloat(insertData.targetAfterBalance);

    await Promise.all([
      sourceWalletLocking.save({ transaction: sequelizeTransaction }),
      targetWalletLocking.save({ transaction: sequelizeTransaction })
    ]);

      if (createdValues && createdValues?.id) {
        const allowedModulesData = await TenantThemeSettingModel.findOne({
          attributes: ['allowedModules'], where: { tenantId }, raw: true
        })

        queueLogInfo.push({
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          ids: [createdValues.id],
          createdAt: new Date(),
          updatedAt: new Date(),
          status: QUEUE_WORKER_CONSTANT.READY,
          tenantId: adminUser.tenantId
        })

        if (allowedModulesData?.allowedModules?.split(',').includes(ALLOWED_PERMISSIONS.AFFILIATED_SYSTEM_SMARTICO)) {
          queueLogInfo.push({
            type: QUEUE_WORKER_CONSTANT.SMARTICO_DEPOSIT_APPROVED,
            ids: [createdValues.id],
            status: QUEUE_WORKER_CONSTANT.READY,
            tenantId: adminUser.tenantId
          })
        }

        if (allowedModulesData?.allowedModules?.split(',').includes(ALLOWED_PERMISSIONS.AFFILIATED_SYSTEM_ALANBASE)) {
          queueLogInfo.push({
            type: ALANBASE_EVENT_TYPES.DEPOSIT,
            ids: [createdValues.id],
            status: QUEUE_WORKER_CONSTANT.READY,
            tenantId: adminUser.tenantId
          })
        }

        const user = await UserModel.findOne({
          where: {
            id: depositRequest.userId
          },
          attributes: ['id', 'withdrawWagerAllowed'],
        })

        if (allowedModulesData && allowedModulesData?.allowedModules && allowedModulesData?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION) && !user?.withdrawWagerAllowed ) {
          queueLogInfo.push({
            type: QUEUE_WORKER_CONSTANT.DEPOSIT_WAGER_TRACKING,
            status: QUEUE_WORKER_CONSTANT.READY,
            ids: [createdValues.id],
            tenantId: adminUser.tenantId
          })
        }

        if (targetWallet.ownerType === USER_TYPE) {
          const userBonusData = {
            targetEmail: depositRequest.userId,
            transactionAmount: depositRequest.manualDepositType === 2 ? parseFloat(depositRequest.amount) : trnAmount,
            transactionType: 'deposit'
          }
          const result = await updateUserBonus(userBonusData, createdValues, sequelizeTransaction, depositCsvData?.id)
          if (result && result.length > 0) {
            queueLogInfo = queueLogInfo.concat(result)
          }
        }
      }

    } else if (TRANSACTION_STATUS_REJECTED.includes(status)) {
      status = verifyStatus = DEPOSIT_REQUEST_STATUS.REJECTED
      utrStatus = UTR.STATUS.REJECTED
      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED, depositCsvData.id, tenantId)

    } else {
      status = depositRequest.status
    }

    const updateDeposit = {
      status,
      remark: depositCsvData?.remark,
      updatedAt: new Date(),
      actionId: adminUser.id,
      actionType: adminUser.parentType,
      verifyStatus,
      ...(trackingId && { trackingId })
    }

    await DepositRequestModel.update(updateDeposit, { where: { id: depositCsvData.id } }, { transaction: sequelizeTransaction })

    await UtrHistoryModel.update({
      status: utrStatus,
      transactionId: createdValues?.id || null,
      transactionDate: createdValues?.createdAt || null,
      currencyId: targetWallet?.currencyId || null
    }, { where: { utrNumber: depositRequest.utrNumber } }, { transaction: sequelizeTransaction })

    await DepositWithdrawUserModel.update({ status: DEPOSIT_WITHDRAW_USER_STATUS.COMPLETED }, { where: { id: depositWithdrawUserId } })

    if (queueLogInfo.length > 0) {
      const queueEntries = await QueueLogModel.bulkCreate(queueLogInfo, { transaction: sequelizeTransaction })

      for (const queueLog of queueEntries) {
        queueIds.push(queueLog.id)
      }
    }

    await sequelizeTransaction.commit()

    const pendingTasks = await DepositWithdrawUserModel.count({
      where: {
        jobId: jobId, status: DEPOSIT_WITHDRAW_USER_STATUS.NOT_STARTED ,
        [Sequelize.Op.not]: [{ id: depositWithdrawUserId }]
      }
    })

    if (pendingTasks === 0) {
      const failedTasks = await DepositWithdrawUserModel.count({
        where: {
          jobId: jobId, status: DEPOSIT_WITHDRAW_USER_STATUS.FAILED
        }
      })

      const jobStatus = failedTasks === 0 ? DEPOSIT_WITHDRAW_JOB_STATUS.COMPLETED : DEPOSIT_WITHDRAW_JOB_STATUS.PARTIALLY_FAILED

      await DepositWithdrawJobModel.update({ status: jobStatus }, { where: { id: jobId } })
    }

    if (queueIds.length > 0) {
      try {
        for (const eachQueue of queueIds) {
          await publishToRedis.publishToQueueService({ QueueLog: { queueLogId: eachQueue } })
        }
      } catch (error) {
        console.error('Error in Redis publishing:', error)
      }
    }

    return { status: 'success' }
  } catch (error) {

    await sequelizeTransaction.rollback()

    // Update the job status to PARTIALLY_FAILED in case of any error
    if (jobId) {
      await DepositWithdrawJobModel.update({ status: DEPOSIT_WITHDRAW_JOB_STATUS.PARTIALLY_FAILED }, { where: { id: jobId } })
    }
    if (!error.notFound) {
      await ErrorLogHelper.logError(error, null, null)
      throw error
    }
  }
}
