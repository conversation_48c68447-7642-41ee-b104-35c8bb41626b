import db, { sequelize } from '../../db/models'
import {
  ALANBASE_EVENT_TYPES,
  ALLOWED_PERMISSIONS,
  BONUS_COMMENT_ABBREVIATIONS,
  BONUS_STATUS, DEPOSIT_WITHDRAW_JOB_STATUS, DEPOSIT_WITHDRAW_USER_STATUS, QUEUE_WORKER_CONSTANT,
  TABLES,
  UTR,
  WITHDRAW_REQUEST_STATUS
} from '../constants'

export default async (payload) => {
  const {
    WithdrawRequest: WithdrawRequestModel,
    UserBonus: UserBonusModel,
    Transaction: TransactionModel,
    Wallet: WalletModel,
    QueueLog: QueueLogModel,
    DepositWithdrawJob: DepositWithdrawJobModel,
    DepositWithdrawUser: DepositWithdrawUserModel,
    AdminUser: AdminUserModel,
    Bonus: BonusModel,
    BetsTransaction: BetsTransactionModel,
    TenantThemeSetting: TenantThemeSettingModel,
    UtrHistory: UtrHistoryModel,
    TransactionsMetaData:TransactionsMetaDataModel
  } = db

  const sequelizeTransaction = await sequelize.transaction()
  let jobId = null

  try {
    const depositWithdrawUserId = payload.transactionId

    const depositWithdrawUser = await DepositWithdrawUserModel.findOne({
      where: {
        id: depositWithdrawUserId
      },
      include: [
        {
          model: DepositWithdrawJobModel,
          attributes: ['id', 'type', 'tenantId']
        }
      ]
    })

    if (!depositWithdrawUser) {
      throw new Error(`DepositWithdrawUser not found for ID: ${depositWithdrawUserId}`)
    }

    jobId = depositWithdrawUser?.jobId
    const withdrawCsvData = depositWithdrawUser.jobData
    //   // Update job status to IN_PROGRESS
    await DepositWithdrawJobModel.update(
      { status: DEPOSIT_WITHDRAW_JOB_STATUS.IN_PROGRESS },
      { where: { id: jobId, status: DEPOSIT_WITHDRAW_JOB_STATUS.NOT_STARTED } }
    )

    const withdrawRequest = await WithdrawRequestModel.findOne({
      where: { id: withdrawCsvData?.id, verify_status: WITHDRAW_REQUEST_STATUS.VERIFIED, status: WITHDRAW_REQUEST_STATUS.PENDING_BY_BANK },
      attributes: ['id', 'userId', 'transactionId', 'tenantId', 'bankName', 'accountNumber', 'amount', 'status', 'processedBankDetails', 'verify_status', 'makerData'],
      raw: true
    })

    if (!withdrawRequest) {
      await DepositWithdrawUserModel.update({ status: DEPOSIT_WITHDRAW_USER_STATUS.FAILED, error: { message: 'WithdrawRequest not found' } }, { where: { id: depositWithdrawUserId } })
      throw Object.assign(new Error('WithdrawRequest not found for Id'), { notFound: true })
    }

    const transactionDetail = await TransactionModel.findOne({
      where: {
        id: withdrawRequest.transactionId
      },
      attributes: ['id', 'status', 'comments', 'amount', 'metaData', 'tenantId', 'actioneeType', 'actioneeId', 'sourceWalletId', 'sourceCurrencyId', 'createdAt'],
      raw: true
    })

    if (!transactionDetail) {
      await DepositWithdrawUserModel.update({ status: DEPOSIT_WITHDRAW_USER_STATUS.FAILED, error: { message: 'Transaction not Found' } }, { where: { id: depositWithdrawUserId } })
      throw new Error('Transaction not Found')
    }

    const processedBankDetails = {
      process_bank_name: withdrawCsvData?.processed_bank_name || 'NA',
      process_account_number: withdrawCsvData?.processed_account_number || 'NA',
      process_utr_number: withdrawCsvData?.processed_utr_number || 'NA'
    }

    const adminUser = await AdminUserModel.findOne({
      where: {
        id: withdrawCsvData?.admin_user
      },
      attributes: ['id', 'agentName', 'email', 'tenantId'],
      raw: true
    })
    let makerData = null; let updatedMetaData = {};  const withdrawRequestCsvStatus = withdrawCsvData?.status?.toLowerCase()
    const findMetadata = await TransactionsMetaDataModel.findOne({
      where: {
        transactionId: withdrawRequest.transactionId,
        tenantId: transactionDetail.tenantId
      },
      attributes: ['id','metaData'],
      raw: true
    })
    if(findMetadata){
      updatedMetaData = findMetadata?.metaData
    }

    if (adminUser) {
      makerData = {
        id: adminUser.id,
        email: adminUser.email,
        user_name: adminUser.agentName,
        remark: withdrawCsvData?.remark,
        time_stamp: new Date()
      }

      updatedMetaData.maker_data = makerData
    }

    const withdrawalUpdateInfo = {
      processedBankDetails: processedBankDetails,
      remark: withdrawCsvData?.remark,
      makerData,
      actionedAt: new Date()
    }

    let depositWithdrawUserJobStatus = DEPOSIT_WITHDRAW_USER_STATUS.COMPLETED; let jobStatusErrorMessage = null

    if (['approved', 'approve', 'successful', 'completed', 'confirmed', 'accepted', 'validated'].includes(withdrawRequestCsvStatus)) {
      withdrawalUpdateInfo.status = WITHDRAW_REQUEST_STATUS.APPROVED
      await WithdrawRequestModel.update(withdrawalUpdateInfo,
        {
          where: { id: withdrawRequest.id },
          transaction: sequelizeTransaction
        }
      )

      // Cancel all active user bonuses
      const userBonuses = await UserBonusModel.findAll({
        attributes: ['id', 'transactionId'],
        where: {
          userId: withdrawRequest.userId,
          status: BONUS_STATUS.ACTIVE
        },
        include: {
          model: BonusModel,
          attributes: ['bonusCancellationType', 'kind'],
          required: true
        },
        subQuery: false
      })

      if (userBonuses && userBonuses.length > 0) {
        for (const usrbonus of userBonuses) {
          if (['none', 'multiple_deposit'].includes(usrbonus.bonusCancellationType) || usrbonus.kind === 'deposit_instant') {
            continue
          }

          // Update the UserBonus status to 'cancelled'
          await UserBonusModel.update(
            { status: 'cancelled', updatedAt: new Date() },
            { where: { id: usrbonus.id }, transaction: sequelizeTransaction }
          )

          // Check if transaction_id exists and update the transaction status
          if (usrbonus.transactionId) {
            const userTransactionUpdated = await TransactionModel.update({ status: 'failed', comments: BONUS_COMMENT_ABBREVIATIONS.DBCDTW }, {
              where: {
                id: usrbonus.transactionId,
                tenantId: transactionDetail.tenantId
              },
              transaction: sequelizeTransaction
            })

            if (userTransactionUpdated && userTransactionUpdated[0] === 1) {
              await QueueLogModel.create({
                type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
                ids: [+usrbonus.transactionId]
              })
            }

            // Update the bet transaction status
            const betTransactionUpdated = await BetsTransactionModel.update({ status: 'failed', description: BONUS_COMMENT_ABBREVIATIONS.DBCDTW }, {
              where: {
                tenantId: transactionDetail.tenantId,
                id: usrbonus.transactionId
              },
              transaction: sequelizeTransaction
            })

            if (betTransactionUpdated && betTransactionUpdated[0] === 1) {
              await QueueLogModel.create({
                type: QUEUE_WORKER_CONSTANT.BET_TRANSACTION,
                ids: [+usrbonus.transactionId]
              })

              const casinoBonusTransaction = await TransactionModel.findOne({
                where: {
                  debitTransactionId: usrbonus.transactionId,
                  tenantId: transactionDetail.tenantId
                },
                attributes: ['id'],
                raw: true
              })

              if (casinoBonusTransaction) {
                // Update the related casino bonus transaction status
                const casinoTransactionUpdated = await TransactionModel.update({ status: 'failed', comments: BONUS_COMMENT_ABBREVIATIONS.DBCDTW }, {
                  where: {
                    id: casinoBonusTransaction?.id
                  },
                  transaction: sequelizeTransaction
                })

                if (casinoTransactionUpdated && casinoTransactionUpdated[0] === 1) {
                  await QueueLogModel.create({
                    type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
                    ids: [+casinoBonusTransaction.id]
                  })
                }
              }
            }
          }
        }
      }

      // Update transaction record
      await TransactionModel.update({
        actioneeType: 'AdminUser',
        actioneeId: adminUser.id,
        status: 'success',
        // metaData: updatedMetaData,
        comments: 'Approved by admin'
      }, {
        where: {
          id: withdrawRequest.transactionId
        },
        transaction: sequelizeTransaction
      })

      // find and update meta data

      if (findMetadata) {
        // Update meta data record
        await TransactionsMetaDataModel.update({
          metaData: updatedMetaData,
        }, {
          where: {
            id: findMetadata?.id
          },
          transaction: sequelizeTransaction
        })
      }
      // Lock the wallet row for update
      const tenantWallet = await WalletModel.findOne({
        attributes: ['id', 'withdrawalAmount'],
        where: { ownerId: adminUser.id, ownerType: TABLES.ADMIN_USER, currencyId: transactionDetail.sourceCurrencyId },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: WalletModel
        },
        skipLocked: false
      })

      await tenantWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })

      // Update the withdrawal amount
      const newTenantWithdrawalAmount = (parseFloat(tenantWallet.withdrawalAmount) + parseFloat(transactionDetail.amount))?.toFixed(5)

      tenantWallet.withdrawalAmount = newTenantWithdrawalAmount
      await tenantWallet.save({ transaction: sequelizeTransaction })

      // Retrieve allowed modules data for the tenant
      const tenantTheme = await TenantThemeSettingModel.findOne({
        where: {
          tenantId: transactionDetail.tenantId
        },
        attributes: ['allowedModules'],
        transaction: sequelizeTransaction,
        raw: true
      })

      // Initialize queue logs with the casino transaction entry
      const queueLogs = [
        {
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          ids: [withdrawRequest.transactionId],
          createdAt: new Date(),
          updatedAt: new Date(),
          tenantId: transactionDetail.tenantId
        }
      ]

      // Check if the smartico key is in the allowed modules and add the entry if it is
      if (tenantTheme && tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(QUEUE_WORKER_CONSTANT.AFFILIATED_SYSTEM_SMARTICO)) {
        queueLogs.push({
          type: QUEUE_WORKER_CONSTANT.SMARTICO_WITHDRAWAL_COMPLETED,
          ids: [withdrawRequest.transactionId],
          createdAt: new Date(),
          updatedAt: new Date(),
          tenantId: transactionDetail.tenantId
        })
      }

      // Check if the alanbase key is in the allowed modules and add the entry if it is
      if (tenantTheme && tenantTheme.allowedModules && tenantTheme.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.AFFILIATED_SYSTEM_ALANBASE)) {
        queueLogs.push({
          type: ALANBASE_EVENT_TYPES.WITHDRAWAL_COMPLETED,
          ids: [withdrawRequest.transactionId],
          createdAt: new Date(),
          updatedAt: new Date(),
          tenantId: transactionDetail.tenantId
        })
      }

      // Bulk insert queue logs
      await QueueLogModel.bulkCreate(queueLogs, { transaction: sequelizeTransaction })
    } else if (['rejected', 'reject', 'failed', 'error', 'invalid', 'canceled', 'declined', 'cancelled'].includes(withdrawRequestCsvStatus)) {
      withdrawalUpdateInfo.verify_status = WITHDRAW_REQUEST_STATUS.REJECTED
      withdrawalUpdateInfo.status = WITHDRAW_REQUEST_STATUS.REJECTED

      await WithdrawRequestModel.update(withdrawalUpdateInfo,
        {
          where: { id: withdrawRequest.id },
          transaction: sequelizeTransaction
        }
      )

      // Update transaction
      await TransactionModel.update({
        actioneeType: 'AdminUser',
        actioneeId: adminUser.id,
        status: 'cancelled',
        comments: 'cancelled by admin',
        // metaData: updatedMetaData
      }, {
        where: {
          id: withdrawRequest.transactionId
        },
        transaction: sequelizeTransaction
      })


      if (findMetadata) {
        // Update meta data record
        await TransactionsMetaDataModel.update({
          metaData: updatedMetaData,
        }, {
          where: {
            id: findMetadata?.id
          },
          transaction: sequelizeTransaction
        })
      }

      // Update user wallet
      const userWallet = await WalletModel.findOne({
        attributes: ['id', 'amount'],
        where: { id: transactionDetail.sourceWalletId },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: WalletModel
        },
        skipLocked: false
      })

      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: WalletModel }, transaction: sequelizeTransaction })
      const updatedAmount = withdrawRequest.amount + userWallet.amount
      userWallet.amount = updatedAmount
      await userWallet.save({ transaction: sequelizeTransaction })

      // Initialize queue logs with the user transaction entry
      const queueLogs = [
        {
          type: QUEUE_WORKER_CONSTANT.USER_TRANSACTION,
          ids: [withdrawRequest.userId],
          createdAt: new Date(),
          updatedAt: new Date(),
          tenantId: adminUser.tenantId
        },
        {
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          ids: [withdrawRequest.transactionId],
          createdAt: new Date(),
          updatedAt: new Date(),
          tenantId: transactionDetail.tenantId
        }
      ]

      // Insert the queue logs into the database
      await QueueLogModel.bulkCreate(queueLogs, { transaction: sequelizeTransaction })
    } else if (['pending', 'in_progress', 'awaiting', 'processing', 'pending_by_bank'].includes(withdrawRequestCsvStatus)) {
      await WithdrawRequestModel.update(withdrawalUpdateInfo,
        {
          where: { id: withdrawRequest.id },
          transaction: sequelizeTransaction
        }
      )
    } else {
      depositWithdrawUserJobStatus = DEPOSIT_WITHDRAW_USER_STATUS.FAILED
      jobStatusErrorMessage = { message: 'Invalid status. The provided status is not supported.' }
    }

    // Insert a new record into UtrHistory if the UTR number is processed, marking it as an approved UTR.
    if (processedBankDetails.process_utr_number) {
      const utrHistoryData = {
        utrNumber: processedBankDetails?.process_utr_number.trim(),
        tenantId: adminUser.tenantId,
        actioneeId: adminUser.id,
        actioneeType: 'AdminUser',
        amount: withdrawRequest?.amount,
        remarks: withdrawCsvData?.remark,
        utrType: UTR.TYPE.WITHDRAW,
        status: UTR.STATUS.APPROVED,
        transactionDate: transactionDetail?.createdAt,
        currencyId: transactionDetail?.sourceCurrencyId
      };

      await UtrHistoryModel.create(utrHistoryData)
    }

    await DepositWithdrawUserModel.update({ status: depositWithdrawUserJobStatus, error: jobStatusErrorMessage },
      { where: { id: depositWithdrawUserId } })

    const pendingTasks = await DepositWithdrawUserModel.count({
      where: {
        jobId: jobId,
        status: DEPOSIT_WITHDRAW_USER_STATUS.NOT_STARTED
      }
    })

    if (pendingTasks === 0) {
      const failedTasks = await DepositWithdrawUserModel.count({
        where: {
          jobId: jobId,
          status: DEPOSIT_WITHDRAW_USER_STATUS.FAILED
        }
      })

      const jobStatus = failedTasks === 0 ? DEPOSIT_WITHDRAW_JOB_STATUS.COMPLETED : DEPOSIT_WITHDRAW_JOB_STATUS.PARTIALLY_FAILED

      await DepositWithdrawJobModel.update({ status: jobStatus }, { where: { id: jobId } })
    }

    // sending sms alerts on withdraw request
    const sendSmsAlerts = {
      type: QUEUE_WORKER_CONSTANT.DEP_WITHDRAW_SMS_OTP,
      status: QUEUE_WORKER_CONSTANT.READY,
      ids: [{ "id": +withdrawRequest?.id }],
      tenantId: withdrawRequest?.tenantId
    }
    await QueueLogModel.create(sendSmsAlerts, { transaction: sequelizeTransaction })

    // Commit the transaction
    await sequelizeTransaction.commit()

    return { message: 'Success' }
  } catch (error) {
    await sequelizeTransaction.rollback()

    // Update the job status to PARTIALLY_FAILED in case of any error
    if (jobId) {
      await DepositWithdrawJobModel.update({ status: DEPOSIT_WITHDRAW_JOB_STATUS.PARTIALLY_FAILED }, { where: { id: jobId } })
    }

    // If the error is not due to a "not found" condition, rethrow the error
    if (!error.notFound) {
      throw error
    }
  }
}
