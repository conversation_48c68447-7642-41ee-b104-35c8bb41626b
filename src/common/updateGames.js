import axios from 'axios';
import { StatusCodes } from 'http-status-codes';
import { Op, QueryTypes, Sequelize } from 'sequelize';
import { PassThrough } from 'stream';
import XLSX from 'xlsx';
import db, { sequelize } from '../db/models';
import Logger from '../libs/logger';
import { CRON_LOG_STATUS } from './constants';
import { getCasinoProvider } from './getCasinoProvider';
import syncOrOverrideCasinoProvider from './syncOrOverrideCasinoProvider';

const menuImageMappings = {
  'hot games':    'tenants/1/menus/fa02a6e6-f247-45d9-9de4-590847c6fd13____5560f15f-1599-48a4-8606-f7befcca1666____promotion-icon-png-3406.png',
  'game shows':   'tenants%2F2%2Fmenus%2Fd77eb28e-a84d-4df5-b05d-2b461dbbb417____Gameshows.png',
  'roulette':     'tenants%2F2%2Fmenus%2Fc2e1d61c-6c4f-4415-9b69-7ac594e49305____casino-roulette_%281%29.png',
  'baccarat':     'tenants%2F2%2Fmenus%2Fc211a41e-69fc-4058-b956-5872e91588a7____baccarat_%281%29.png',
  'blackjack':    'tenants%2F2%2Fmenus%2F2078c859-b608-490b-8b57-53bb2e32b1ad____Blackjack.png',
  'poker':        'tenants%2F2%2Fmenus%2Fb2c0c638-afe0-448f-b09f-07ef9e518779____poker.png',
  'dice games':   'tenants%2F2%2Fmenus%2Ff9e9c85b-e515-4226-9a1e-568b6c5a70b7____3d-dice.png',
  'fan tan':      'tenants/2/menus/59f007d1-96d4-4532-86ed-301028176c87____Cas.png',
  'andar bahar':  'tenants%2F2%2Fmenus%2Fc1aeddfa-7d34-4f96-bed3-acdeba6b155c____ANdar_bhaar.png',
  'lucky 7':      'tenants%2F2%2Fmenus%2Fae402cea-41ba-4746-8c69-f2b7c921bf38____lucky7_%2801%29.png',
  '32 cards':     'tenants%2F2%2Fmenus%2F49fe1de1-c566-444c-9ecb-d5d512f27da7____32cards-logo.png',
  'video slots':  'tenants%2F1%2Fmenus%2F0a9a6190-1667-4184-aa96-40380e993e8a____1000216216.png',
};

const pageImageMappings = {
  'ezugi':      'pages%2F9%2Flogo%2Fa8c81a99-5275-4537-8ef7-6672c34858ca____transparent-ezugi-smart-m-logo.png',
  'evolution':  'pages%2F16%2Flogo%2Fb6153375-bc0a-4ed3-9b6d-8fdb243d68c4____201-2019605_evolution-gaming-logo-svg-hd-png-download-removebg-preview.png',
  'red tiger':  'pages%2F37%2Flogo%2F4c07666e-df40-4f31-8f49-6eee79eda506____dTiger.svg',
  'netent' :    'pages%2F38%2Flogo%2Fb7fa06e6-cd4c-4c23-9930-39d1e707d9ba____netent-logotype.svg',
};

export default async (tokenToUpdateGames, spreadsheetId, tenantIdsToUpdateGames, onlyMasterTenant = false, cronGameSeeding = 0,gameSeed = false, override_other_providers = false, sync = false) => {
  const sequelizeTransaction = await sequelize.transaction();
  const cronLog = {}
  cronLog.startTime = new Date()
  cronLog.cronId = cronGameSeeding
  let providerName = ''
  try {
    if (!tokenToUpdateGames && !cronGameSeeding) throw new Error("Please send token!");

    if (!spreadsheetId) throw new Error("Sheet Id is required!");

    if (!cronGameSeeding) {
      // Validate token against database
      const isTokenVerified = await db.TenantCredential.findOne({
        raw: true,
        where: { tenantId: 0, key: 'GAMES_AUTH_TOKEN', value: tokenToUpdateGames },
        attributes: ['id']
      });

      if (!isTokenVerified) throw new Error("Invalid Token!");

      if (tenantIdsToUpdateGames.some(id => !id.trim() || isNaN(id))) throw new Error("Invalid Tenant Ids!");
    }

    // Build the export URL for XLSX format
    const exportUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/export?format=xlsx`;

    // Make a request to the export URL
    let response;
    try {
      response = await axios({
        url: exportUrl,
        method: 'GET',
        responseType: 'stream', // Use stream response type
      });
    } catch (error) {
      throw new Error('Can not access file!');
    }

    // Create a PassThrough stream to handle the incoming data
    const passThrough = new PassThrough();
    response.data.pipe(passThrough);

    // Collect the data chunks into a buffer
    const chunks = [];
    passThrough.on('data', chunk => {
      chunks.push(chunk);
    });

    // Once the stream is finished, concatenate the chunks into a single buffer
    const buffer = await new Promise((resolve, reject) => {
      passThrough.on('end', () => resolve(Buffer.concat(chunks)));
      passThrough.on('error', reject);
    });

    // Read XLSX file
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheetNameList = workbook.SheetNames;
    let gameProviderArray = XLSX.utils.sheet_to_json(
      workbook.Sheets[sheetNameList[0]],
      {
        blankrows: false,
        defval: '',
        raw: true,
        header: ['provider', 'game', 'game_id', 'table_name', 'table_id', 'game_enabled', 'menu'],
      }
    );

    // Validate Headers in JSON
    const xlsxHeadersMapping = {
      provider: 'Provider',
      game: 'Game',
      game_id: 'Game ID',
      table_name: 'Table Name',
      table_id: 'Table ID',
      game_enabled: 'Game Enabled',
      menu: 'Menu'
    };

    const headerObj = gameProviderArray[0];
    Object.keys(headerObj).forEach(headerKey => {
      if (headerObj[headerKey] !== xlsxHeadersMapping[headerKey]) {
        throw new Error("Invalid Headers!");
      }
    });

    gameProviderArray = gameProviderArray.slice(1);

    // Validate Empty Fields and Trim all.
    gameProviderArray.forEach(gameObj => {
      Object.entries(gameObj).forEach(([key, value]) => {
        let modifiedValue = value;
        if (typeof modifiedValue !== 'string') {
          modifiedValue = String(value);
        }
        modifiedValue = modifiedValue.trim();
        if (!modifiedValue) throw new Error("Empty Cell Found!");
        gameObj[key] = modifiedValue;
      });
    });

    const isMultiProviders = new Set(gameProviderArray.map(gamedata => gamedata.provider.toLowerCase())).size > 1;

    if (isMultiProviders) throw new Error("Cannot Process Multiple Providers!");

    const providerTenantsMapping = {};

    // Store all table ids from excel.
    const excelTableIds = [];

    const casinoItemImgFolderUrl = 'provider-images' + '/' + (['red tiger', 'netent'].includes(gameProviderArray[0]?.provider?.toLowerCase()) ? 'net_red' : gameProviderArray[0]?.provider?.toLowerCase()) + '/thumbnail';
    // if multple records has same table id then keep the last one.
    const uniqueGameProviderArray = gameProviderArray.reduce((acc, curr) => {
      const existingIndex = acc.findIndex(item => item.table_id === curr.table_id);
      if (existingIndex !== -1) {
        acc[existingIndex] = curr;
      } else {
        acc.push(curr);
      }
      return acc;
    }, []);
    gameProviderArray = uniqueGameProviderArray;

    // Add data from CSV to Database
    for (const gameData of gameProviderArray) {
      // Destructure
      const {
        provider: csvProvider,
        game: csvGame,
        game_id: csvGameId,
        table_name: csvTableName,
        table_id: csvTableId,
        game_enabled: csvGameEnabled,
        menu: csvMenu,
      } = gameData;
      if(providerName === '') {
        providerName = csvProvider
      }

      excelTableIds.push(csvTableId);
      let allTenantsSeeded = true
      let provider = providerTenantsMapping[csvProvider.toLowerCase()];
      if (!provider) {
        // Create Provider If Not Exists
        provider = await db.CasinoProvider.findOne({
          raw: true,
          where: {
            name: Sequelize.literal(`LOWER("name") = LOWER('${csvProvider}')`)
            },
          attributes: ['id', 'name'],
          transaction: sequelizeTransaction
        });

        if (!provider) {
          provider = await db.CasinoProvider.create({ name: csvProvider }, { transaction: sequelizeTransaction });
        }
        providerTenantsMapping[provider.name.toLowerCase()] = provider;
      }

      let tenantIds = providerTenantsMapping[provider.name.toLowerCase()]['tenantIds'];
      if (!tenantIds) {
        // Find Tenants associated with Given Provider
        let tenants = await db.TenantThemeSetting.findAll({
          raw: true,
          where: Sequelize.literal(`'${provider.id}' = ANY (string_to_array(assigned_providers,','))`),
          attributes: ['id', 'tenant_id', 'assigned_providers'],
          transaction: sequelizeTransaction
        });
        tenantIds = tenants.map(x => x.tenant_id);
        if (!cronGameSeeding || gameSeed) {
          if(!tenantIdsToUpdateGames.includes('0')) {
          allTenantsSeeded = true
          tenantIds = tenantIds.filter(id => tenantIdsToUpdateGames.includes(id));
          if (!tenantIds.length && !onlyMasterTenant) throw new Error("No tenants found with given provider and tenant ids filter!");
          }
        }
        providerTenantsMapping[provider.name.toLowerCase()]['tenantIds'] = tenantIds;
      }

      if (onlyMasterTenant && !cronGameSeeding) {
        tenantIds = [0];
      } else {
        if (!tenantIds.includes(0)) {
          tenantIds.push(0);
        }
      }
      if (gameSeed && tenantIdsToUpdateGames.includes('0')) {
        allTenantsSeeded = true
      } else if (gameSeed) {
        allTenantsSeeded = false
      }

      // Create Game If Not Exists Under That Provider. If it exists then Update its name if the it is changed.
      let game = await db.CasinoGame.findOne({
        where: { gameId: csvGameId, casinoProviderId: provider.id },
        attributes: ['id', ['game_id', 'gameId'], 'name'],
        transaction: sequelizeTransaction
      });

      if (!game) {
        game = await db.CasinoGame.create({ name: csvGame, casinoProviderId: provider.id, gameId: csvGameId }, { transaction: sequelizeTransaction });
      } else if (game.name.toLowerCase() !== csvGame.toLowerCase()) {
        game.name = csvGame;
        game = await game.save({ transaction: sequelizeTransaction });
      }

      // Create Table If Not Exists Under That Game. If it exists then Update its name if the it is changed.
      let gameTable = await db.CasinoTable.findOne({
        where: { gameId: game.gameId, tableId: csvTableId, providerId: provider.id },
        attributes: ['id', 'name', ['table_id', 'tableId']],
        transaction: sequelizeTransaction
      });

      if (!gameTable) {
        gameTable = await db.CasinoTable.create({
          name: csvTableName,
          gameId: game.gameId,
          tableId: csvTableId,
          providerId: provider.id,
          isLobby: true
        }, { transaction: sequelizeTransaction });
      } else if (gameTable.name.toLowerCase() !== csvTableName.toLowerCase()) {
        gameTable.name = csvTableName;
        gameTable = await gameTable.save({ transaction: sequelizeTransaction });
      }

      // Loop through all the tenants and Create Casino Items tenant-wise.
      for (const tenantId of tenantIds) {
        // Find if Casino Items Exists For Game Table For The Given Tenant, if not then Create. If it exists then Update its name if the it is changed.
        let casinoItem = await db.CasinoItem.findOne({
          where: { uuid: gameTable.tableId, tenantId, provider: provider.id },
          attributes: ['id', 'name', 'active'],
          transaction: sequelizeTransaction
        });

        if (!casinoItem) {
          casinoItem = await db.CasinoItem.create({
            name: gameTable.name,
            uuid: gameTable.tableId,
            provider: provider.id,
            tenantId,
            image: casinoItemImgFolderUrl + '/' + gameTable.tableId + '.webp',
            active: csvGameEnabled.toLowerCase() !== 'false'
          }, { transaction: sequelizeTransaction });
        } else if (casinoItem.name.toLowerCase() !== gameTable.name.toLowerCase()) {
          casinoItem.name = gameTable.name;
        }

        casinoItem.active = csvGameEnabled.toLowerCase() !== 'false';

        casinoItem = await casinoItem.save({ transaction: sequelizeTransaction });
      }

      const menuMasterDataMap = {
        'Ezugi': { 'name': 'Live Casino', 'image': 'menus%2F4%2Flogo%2Fe236bb08-0ee4-4662-bd9d-1f067867edbd____live-casino.svg' },
        'Evolution': { 'name': 'Live Casino', 'image': 'menus%2F4%2Flogo%2Fe236bb08-0ee4-4662-bd9d-1f067867edbd____live-casino.svg' },
        'Red Tiger': { 'name': 'Casino', 'image': 'menus%2F3%2Flogo%2F91f05f34-314f-4535-9d87-5545f120f9b2____casino.svg' },
        'NetEnt': { 'name': 'Casino', 'image': 'menus%2F3%2Flogo%2F91f05f34-314f-4535-9d87-5545f120f9b2____casino.svg' },
      };

      const menuMasterItem = menuMasterDataMap[provider.name];

      if (!menuMasterItem) {
        throw new Error("Menu master mapping not found for the given provider!");
      }

      // Find Menu Master, if not exists then create
      let menuMaster = await db.MenuMaster.findOne({
        raw: true,
        attributes: ['id'],
        where: {
          name: Sequelize.literal(`LOWER("name") = LOWER('${menuMasterItem.name.replace(/'/g, "''")}')`)
         },
        transaction: sequelizeTransaction
      });

      if (!menuMaster) {
        menuMaster = await db.MenuMaster.create({
          name: menuMasterItem.name,
          path: '/' + menuMasterItem.name.toLowerCase(),
          active: true,
          component: 'Casino',
          componentName: menuMasterItem.name.toLowerCase(),
          image: menuMasterItem.image
        }, { transaction: sequelizeTransaction });
      }

      // Loop through all the tenants and Create Other Menu related items.
      for (const tenantId of tenantIds) {
        // Find If Menu Settings Exists For Tenant, If Not Then Create.
        let menuTenantSetting;
        if (tenantId != 0) {
          menuTenantSetting = await db.MenuTenantSetting.findOne({
            raw: true,
            where: { tenant_id: tenantId, menu_id: menuMaster.id },
            attributes: ['id'],
            transaction: sequelizeTransaction
          });

          if (!menuTenantSetting) {
            menuTenantSetting = await db.MenuTenantSetting.create({
              tenant_id: tenantId,
              menu_id: menuMaster.id,
            }, { transaction: sequelizeTransaction });
          }
        } else {
          menuTenantSetting = menuMaster;
        }


        const currentDate = new Date().toISOString();

        let casinoPage = await getCasinoProvider(provider.name, provider.id, tenantId, menuTenantSetting.id, sequelizeTransaction, currentDate, {}, pageImageMappings[provider.name.toLowerCase()], override_other_providers)

        // Find If Casino Menu Exists For the Tenant, If not then Create. If it exists and name changed then update it.
        let casinoMenu = await db.CasinoMenu.findOne({
          raw: true,
          attributes: ['id', 'name'],
          where: {
            name: Sequelize.literal(`LOWER("name") = LOWER('${csvMenu.replace(/'/g, "''")}')`)
            , tenantId },
          transaction: sequelizeTransaction
        });

        if (!casinoMenu) {
          casinoMenu = await db.CasinoMenu.create({
            name: csvMenu,
            enabled: true,
            tenantId,
            imageUrl: menuImageMappings[csvMenu.toLowerCase()]
          }, { transaction: sequelizeTransaction });
        }

        // Find If Casino Page Menu Exists For Casino Page And Game Menu, If Not Then Create. If it exists and name changed then update it.
        let casinoPageMenu = await db.PageMenu.findOne({
          raw: true,
          where: { pageId: casinoPage.id, casinoMenuId: casinoMenu.id },
          attributes: ['id', 'name'],
          transaction: sequelizeTransaction
        });

        if (!casinoPageMenu) {
          casinoPageMenu = await db.PageMenu.create({
            name: casinoMenu.name,
            pageId: casinoPage.id,
            casinoMenuId: casinoMenu.id
          }, { transaction: sequelizeTransaction });
        }

        // Find Casino Item For Given Game Code and Tenant
        const casinoItem = await db.CasinoItem.findOne({
          raw: true,
          attributes: ['id', 'name'],
          where: {
            uuid: csvTableId, tenantId, provider: provider.id,
            name: Sequelize.literal(`LOWER("name") = LOWER('${csvTableName.replace(/'/g, "''")}')`)
          },
          transaction: sequelizeTransaction
        });

        if (casinoItem) {
          // Find Casino Menu Item Exists, If not then create else update.
          let menuItems = await db.MenuItem.findAll({
            attributes: ['id', 'active', 'name', 'casinoItemId', 'pageMenuId'],
            where: { casinoItemId: casinoItem.id },
            transaction: sequelizeTransaction
          });

          const currentPageMenuId = menuItems.find(item => item.pageMenuId === casinoPageMenu.id);
          if (!currentPageMenuId) {
            let menuItem = await db.MenuItem.create({
              name: casinoItem.name,
              pageMenuId: casinoPageMenu.id,
              casinoItemId: casinoItem.id,
              active: csvGameEnabled.toLowerCase() !== 'false'
            }, { transaction: sequelizeTransaction });
            menuItems.push(menuItem);
          }

          for (const menuItem of menuItems) {
            if (menuItem.name.toLowerCase() !== casinoItem.name.toLowerCase()) {
              menuItem.name = casinoItem.name;
            }
            menuItem.active = csvGameEnabled.toLowerCase() !== 'false';

            await menuItem.save({ transaction: sequelizeTransaction });
          }

          let menuItemsNotFromCurrentCasinoItem = menuItems.filter(item => item.pageMenuId !== casinoPageMenu.id);
          if (menuItemsNotFromCurrentCasinoItem.length) {
            let menuListToBeDeleted = await sequelize.query(`
            SELECT "MenuItem".id
            FROM "public"."menu_items" AS "MenuItem"
            JOIN public.page_menus on "MenuItem".page_menu_id = page_menus.id
            WHERE "MenuItem"."id" IN (:menuids) and page_id in (:pageIds)`, {
              replacements: {
                menuids: menuItemsNotFromCurrentCasinoItem.map(item => item.id),
                pageIds: [casinoPage.id]
              },
              type: QueryTypes.SELECT,
              transaction: sequelizeTransaction
            })

            let allPageMenu = menuItemsNotFromCurrentCasinoItem.map(item => item.pageMenuId)
            let allAvailablePageMenu = await db.PageMenu.findAll({
              attributes: ['id'],
              where: {
                id: {
                  [Op.in]: allPageMenu
                }
              },
              raw: true
            })
            let allAvailablePageMenuIds = allAvailablePageMenu.map(item => item.id)
            let allPageMenuIds = allPageMenu.filter(item => !allAvailablePageMenuIds.includes(item))
            let allPageMenuIdsToBeDeleted = menuItemsNotFromCurrentCasinoItem.filter(item => allPageMenuIds.includes(item.pageMenuId))
            menuListToBeDeleted.push(...allPageMenuIdsToBeDeleted)

            // only delete the menu items that are belonging to the current page
            if (menuListToBeDeleted.length) {
              await db.MenuItem.destroy({
                where: {
                  id: {
                    [Op.in]: menuListToBeDeleted.map(item => item.id)
                  },
                  casinoItemId: casinoItem.id

                },
                transaction: sequelizeTransaction
              })
            }
          }
        }
      }

  if (override_other_providers || sync) {
      await syncOrOverrideCasinoProvider(tenantIds,  provider.id, provider.name, override_other_providers, sync )
  }


    }

    // // Disable all games which are present in database and not in excel sheet.
    // let provider = providerTenantsMapping[Object.keys(providerTenantsMapping)[0]];
    // if (provider) {
    //   let tenantIds = provider.tenantIds;

    //   let casinoItemIdsToDisable = await db.CasinoItem.findAll({
    //     where: { uuid: { [Op.notIn]: excelTableIds }, provider: provider.id, tenantId: { [Op.in]: tenantIds } },
    //     attributes: ['id', 'name'],
    //     transaction: sequelizeTransaction
    //   });
    //   casinoItemIdsToDisable = casinoItemIdsToDisable.map(o => o.id);

    //   if (casinoItemIdsToDisable.length) {
    //     await db.CasinoItem.update({ active: false }, { where: { id: { [Op.in]: casinoItemIdsToDisable } }, transaction: sequelizeTransaction });
    //     await db.MenuItem.update({ active: false }, { where: { casinoItemId: { [Op.in]: casinoItemIdsToDisable } }, transaction: sequelizeTransaction });
    //   }
    // }

    // Commit the transaction
    await sequelizeTransaction.commit();
    if (cronGameSeeding) {
      cronLog.endTime = new Date()
      cronLog.errorMsg = `Provider ${providerName ? providerName : ''}`
      cronLog.status = CRON_LOG_STATUS.SUCCESS
      await db.CronLog.create(cronLog)
    }


    return { message: 'Games updated successfully!', status: StatusCodes.OK }
  } catch (error) {
    await sequelizeTransaction.rollback();
    if (cronGameSeeding) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message || null
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
    }
    Logger.info(error, '========Ezugi game population error======')
    return {
      success: StatusCodes.UNPROCESSABLE_ENTITY,
      Error: {
        stack: error.stack
      }
    }
  }
};
