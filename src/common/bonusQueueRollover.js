// rolloverCheck

import { Sequelize } from 'sequelize';
import db, { sequelize } from '../db/models';
import { MULTI_BONUS_STATUS } from '../utils/constants/constant';
import { ALLOWED_PERMISSIONS, BONUS_STATUS, BONUS_TYPES, DEPOSIT_INSTANT_BONUS_TYPES, TRANSACTION_PAYMENT_METHOD } from './constants';
import <PERSON>rrorLogHelper from './errorLog';
import { rolloverCheck } from './rolloverCheck';

export const bonusQueueRollover = async ( amount, depositTransId, userId, paymentProviders = null, depositBonusID, sequelizeTransaction, depositID = null ) => {
  let newSequelizeTransaction = false;
  let depositRequestID = depositID;

  // Begin a new transaction if not passed
  if (!sequelizeTransaction) {
    newSequelizeTransaction = true;
  }

  const {
    UserBonusQueue: UserBonusQueueModel,
    Bonus: BonusModel,
    User: UserModel,
    UserBonus: UserBonusModel,
    Transaction: TransactionModel,
    TenantThemeSetting: TenantThemeSettingModel,
    DepositRequest: DepositRequestModel,
    UserBonusRolloverTransaction : UserBonusRolloverTransactionModel
  } = db;

  // Exit early if depositBonusID is provided
  if (depositBonusID) {
    return null;
  }

  // Helper function to fetch allowed modules for a tenant
  const getAllowedModules = async (tenantId) => {
    const allowedModulesRow = await TenantThemeSettingModel.findOne({
      attributes: ['allowedModules'],
      where: { tenantId },
      raw: true,
    });
    return allowedModulesRow ? allowedModulesRow.allowedModules : null;
  };

  // Fetch user details
  const userDetails = await UserModel.findOne({
    attributes: [
      'id',
      'tenantId',
      'parentId',
      [
        Sequelize.literal(`(
          SELECT currencies.id
          FROM currencies, wallets
          WHERE currencies.id = wallets.currency_id
          AND wallets.owner_id = "User".id
          AND wallets.owner_type = 'User'
        )`),
        'currencyId',
      ],
    ],
    where: { id: userId },
    raw: true,
  });

  if (!userDetails) {
    return null;
  }

  // Get allowed modules for the user's tenant
  const allowedModules = await getAllowedModules(userDetails.tenantId);

  // Check if the user has the required permission
  if (!allowedModules?.includes(ALLOWED_PERMISSIONS.MULTI_BONUS_ALLOWANCE)) {
    return null;
  }

  // Fetch transaction details
  const transactionDetails = await TransactionModel.findOne({
    attributes: ['transactionId', 'paymentMethod'],
    where: { id: depositTransId },
    ...(sequelizeTransaction && { transaction: sequelizeTransaction }),
  });

  if (!transactionDetails) {
    return null;
  }

  const isManual = transactionDetails?.paymentMethod == 'manual';
  const depositField = isManual ? 'trackingId' : 'orderId';

  // Only fetch if needed (manual: only if ID not already set, non-manual: always)
  if (!isManual || (isManual && !depositRequestID)) {
    const depositDetails = await DepositRequestModel.findOne({
      where: {
        userId,
        [depositField]: transactionDetails.transactionId,
      },
      attributes: ['id'],
      ...(sequelizeTransaction && { transaction: sequelizeTransaction }),
    });

    if (!depositDetails) return null;

    depositRequestID = depositDetails.id;
  }

  // Check for existing user bonus queue entry
  const userBonusExists = await UserBonusQueueModel.findOne({
    attributes: ['id'],
    where: {
      status: MULTI_BONUS_STATUS.PENDING,
      depositId: depositRequestID,
      userId: userId,
    },
  });

  if (userBonusExists) {
    return null;
  }

  // Fetch active deposit bonuses
  const userActiveDepositBonuses = await sequelize.query(`
    SELECT
      "UserBonusQueue"."id",
      "UserBonusQueue"."bonus_id" AS "bonusId",
      "UserBonusQueue"."bonus_amount" AS "bonusAmount",
      "UserBonusQueue"."remaining_rollover" AS "remainingRollover",
      "UserBonusQueue"."ordering"
    FROM "user_bonus_queue" AS "UserBonusQueue"
    JOIN "bonus" AS "Bonus"
      ON "UserBonusQueue"."bonus_id" = "Bonus"."id"
    WHERE "UserBonusQueue"."status" = 0
      AND "UserBonusQueue"."user_id" = :userId
      AND "UserBonusQueue"."bonus_amount" IS NULL
      AND "Bonus"."kind" in (:kind)
      AND ("Bonus"."deposit_bonus_type" != :depositBonusType OR "Bonus"."deposit_bonus_type" IS NULL)
    ORDER BY "UserBonusQueue"."ordering" ASC;
  `, {
    replacements: {
      userId,
      kind: [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_BOTH, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_INSTANT],
      depositBonusType: DEPOSIT_INSTANT_BONUS_TYPES.RECURRING
    },
    type: sequelize.QueryTypes.SELECT,
  });

  if (!userActiveDepositBonuses.length) {
    return null;
  }

  try {
    // Begin a new transaction if required
    if (newSequelizeTransaction) {
      sequelizeTransaction = await sequelize.transaction();
    }

    function getBonusOrder (data, newTopId) {
      const IndexList = data.map(item => item.ordering);
      let modifiedRecord = data.filter(item => item.id === newTopId);
      const bonusRecords = data
      modifiedRecord[0].ordering = IndexList[0];
      let otherBonusRecords = bonusRecords.filter(item => item.id != newTopId);
      otherBonusRecords.map((item, index) => {
        item.ordering = IndexList[index + 1];
      });
      return [...modifiedRecord, ...otherBonusRecords].sort((a, b) => a.id - b.id)
    }

    for (const bonus of userActiveDepositBonuses) {
      const result = await rolloverCheck(bonus.bonusId, userDetails, amount, paymentProviders, sequelizeTransaction, depositTransId, isManual);
      if (result) {
        if (result?.bonusAmount) {

          const activeDepositBonus = await UserBonusModel.findOne({
            attributes: ['id', 'bonusId'],
            where: {
              userId: userId,
              status: BONUS_STATUS.ACTIVE,
              rolloverBalance: 0,
              bonusAmount: 0,
              transactionId: null
            },
            include: [
              {
                model: BonusModel,
                as: 'Bonus',
                where: {
                  kind: [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_BOTH, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_INSTANT],
                  enabled: true
                },
                attributes: ['id','depositBonusType'],
                required: true
              }
            ]
          });

          if (activeDepositBonus) {
            await UserBonusModel.update({
              bonusAmount: result.bonusAmount,
              initialRolloverBalance: result.rolloverBalance,
              rolloverBalance: result.rolloverBalance,
              bonusId: bonus.bonusId,
              kind: result.bonusKind,
              expiresAt: result.expiresAt
            }, { where: { id: activeDepositBonus.id }, transaction: sequelizeTransaction })

            if(activeDepositBonus.Bonus.depositBonusType === DEPOSIT_INSTANT_BONUS_TYPES.RECURRING) {
            // insert new bonus queue for recurring bonus in end
              await UserBonusQueueModel.create({
                userId: userId,
                bonusId: activeDepositBonus.bonusId,
                status: MULTI_BONUS_STATUS.PENDING
              }, { transaction: sequelizeTransaction })

              await UserBonusQueueModel.update({
                status: MULTI_BONUS_STATUS.COMPLETED,
              }, { where: { id: bonus.id }, transaction: sequelizeTransaction })
            } else {

            await UserBonusQueueModel.update({
              bonusId: activeDepositBonus.bonusId,
            }, { where: { id: bonus.id }, transaction: sequelizeTransaction })
          }

          } else {
            await UserBonusQueueModel.update(
              {
                bonusAmount: result.bonusAmount,
                rolloverTarget: result.rolloverBalance,
                remainingRollover: result.rolloverBalance,
                depositId: depositRequestID,
              },
              {
                where: { id: bonus.id },
                transaction: sequelizeTransaction,
              }
            );
          }

          let newOrder = getBonusOrder(userActiveDepositBonuses, bonus.id)
          for (const item of newOrder) {
            await UserBonusQueueModel.update(
              {
                ordering: item.ordering
              },
              {
                where: { id: item.id },
                transaction: sequelizeTransaction,
              }
            );
          }

        } else if (result.instantDeposit) {
          // Update status of bonus to COMPLETED
          await UserBonusQueueModel.update(
            {
              status: MULTI_BONUS_STATUS.COMPLETED,
            },
            {
              where: { id: bonus.id },
              transaction: sequelizeTransaction,
            }
          );
        }

        await UserBonusRolloverTransactionModel.create({
          bonusId: bonus.bonusId,
          userId,
          depositTransactionId: depositTransId
        }, { transaction: sequelizeTransaction });

        // Commit if this is a new transaction
        if (newSequelizeTransaction) {
          await sequelizeTransaction.commit();
        }

        return true;
      }
    }

    // Commit if this is a new transaction
    if (newSequelizeTransaction) {
      await sequelizeTransaction.commit();
    }

    return true;
  } catch (error) {
    // Rollback the transaction if any error occurs
    if (newSequelizeTransaction) {
      await sequelizeTransaction.rollback();
    }
    await ErrorLogHelper.logError(error, null, '')
    throw error;
  }
};
