import config from '../configs/app.config';
import db, { sequelize } from '../db/models';
import <PERSON>rrorLogHelper from './errorLog';
import { CRON_LOG_STATUS } from '../common/constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const isProdEnv = config.get('env') === 'production';
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'player_revenue_report_cron',
        status: 1
      },
      attributes: ['id']
    })
    if(queueProcessStatus){
      cronLog.cronId = queueProcessStatus?.id
      // Delete all records from player revenue tables for yesterday
      const currentISTDateTime = "NOW()::timestamp AT TIME ZONE 'UTC' at time zone 'Asia/Kolkata'";
      const yesterdayISTDate = `date(date(${currentISTDateTime}) - INTERVAL '1 day')`;

      let deleteQuery = `
        delete from player_provider_other_currency where player_summary_provider_id in (
          select id from player_summary_provider_wise where date = ${yesterdayISTDate}
        );
        delete from player_summary_provider_wise where date = ${yesterdayISTDate};
      `;

      await sequelize.query(deleteQuery);

      // Call stored procedure
      const callProviderWiseSP = `CALL update_player_summary_provider_wise_sp(
        CONCAT((CURRENT_DATE - INTERVAL '1 day')::date, ' 18:30:00.000000')::timestamp,
        CONCAT(CURRENT_DATE,' 18:29:59.999999')::timestamp,
        ${isProdEnv}
      )`

      await sequelize.query(callProviderWiseSP);

      // Adding delay of 1 minute (60000 ms) between stored procedure calls
      const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
      await delay(60000)

      // Call stored procedure
      const callGameWiseTractionSP = `CALL update_player_summary_provider_game_wise_transaction_sp(
        CONCAT((CURRENT_DATE - INTERVAL '1 day')::date, ' 18:30:00.000000')::timestamp,
        CONCAT(CURRENT_DATE,' 18:29:59.999999')::timestamp,
        ${isProdEnv}
      )`

      await sequelize.query(callGameWiseTractionSP);
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log("=============> Error in add Player Revenue Report cron:", e)
    await ErrorLogHelper.logError(e, null, null)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
