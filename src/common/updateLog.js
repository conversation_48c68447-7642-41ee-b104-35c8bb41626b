import db from '../db/models'

export default async (logId,apiResponse) => {
  let responseStatus
  if (apiResponse.success === 0) {
    responseStatus = 'failed'
  } else {
    responseStatus = 'success'
  }
  await db.RequestResponseLog.update({
    responseJson: apiResponse,
    responseStatus: responseStatus,
    responseCode: apiResponse.success,
  },
    {
      where: { id: logId }
    }
  )

}
