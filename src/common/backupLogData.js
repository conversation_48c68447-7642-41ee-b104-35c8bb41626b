import db, { sequelize } from '../db/models'
import { CRON_LOG_STATUS } from '../common/constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try{
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'req_res_log_cron',
        status: 1
      },
      attributes: ['id']
    })
    if(queueProcessStatus){
      cronLog.cronId = queueProcessStatus?.id
      await sequelize.query(`call move_to_logs();` )
    }
  }catch(e){
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log("==========error===in=req_res_log_cron",e)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
