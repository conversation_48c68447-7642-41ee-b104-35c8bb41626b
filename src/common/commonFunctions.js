import { ALANBASE_EVENT_TYPES, PROD_ALLOWED_TENANTS_SMARTICO, QUEUE_WORKER_CONSTANT, SMARTICO_EVENT_TYPES, STAG_ALLOWED_TENANTS_ALANBASE, STAG_ALLOWED_TENANTS_SMARTICO } from '../common/constants'
import config from '../configs/app.config'

export const bulkDataForSmartico = (bulkData, tenantId, isSuccess, transactionId) => {
  const smarticoTenants = config.get('env') === 'production'
    ? PROD_ALLOWED_TENANTS_SMARTICO
    : STAG_ALLOWED_TENANTS_SMARTICO

  if (smarticoTenants?.includes(+tenantId)) {
    if (isSuccess) {
      bulkData.push(
        {
          type: SMARTICO_EVENT_TYPES.WITHDRAWAL_APPROVED,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [transactionId.toString()],
          tenantId: +tenantId
        },
        {
          type: SMARTICO_EVENT_TYPES.WITHDRAWAL_COMPLETED,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [transactionId.toString()],
          tenantId: +tenantId
        }
      )
    } else {
      bulkData.push(
        {
          type: SMARTICO_EVENT_TYPES.WITHDRAWAL_CANCELLED,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [transactionId.toString()],
          tenantId: +tenantId
        }
      )
    }
  }

  //Alanbase Integration
  const alanbaseTenants = config.get('env') === 'production' ? STAG_ALLOWED_TENANTS_ALANBASE.PROD : STAG_ALLOWED_TENANTS_ALANBASE.STAGE
  if (alanbaseTenants?.includes(tenantId)) {
    if (isSuccess) {
      bulkData.push(
        {
          type: ALANBASE_EVENT_TYPES.WITHDRAWAL_COMPLETED,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [transactionId.toString()],
          tenantId: tenantId
        }
      )
    }
  }
}
