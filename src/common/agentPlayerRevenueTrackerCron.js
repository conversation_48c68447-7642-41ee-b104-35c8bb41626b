import moment from 'moment';
import config from '../configs/app.config';
import db, { sequelize } from '../db/models';
import { AGENT_PLAYER_REVENUE_TRACKER_SP_TYPE, PROGRESS_TRACKER_STATUS } from '../utils/constants/constant';
import ErrorLogHelper from './errorLog';
moment.tz.setDefault("UTC");

export default async () => {
  try {
    const isProdEnv = config.get('env') === 'production';
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'agent_player_revenue_progress',
        status: 1
      },
      attributes: ['id']
    })
    if(queueProcessStatus){
        // Query to check for an entry with status = 0
        const progressTrackerEntry = await sequelize.query(`
          SELECT id, status
          FROM public.agent_player_revenue_summary_progress_tracker
          WHERE status in (1)
          ORDER BY id ASC
          LIMIT 1;
        `, { type: sequelize.QueryTypes.SELECT, useMaster: true });


        // console.log('progressTrackerEntry => ', progressTrackerEntry);

        // length == 0
        if(progressTrackerEntry.length == 0){ // changes
          // if(progressTrackerEntry.length > 0 && progressTrackerEntry[0].status == 0){ // changes
          // const job_id = progressTrackerEntry[0].id

          const checkPendingTask = await sequelize.query(`
            SELECT id, range_from, range_to, status, range_count, current_range_index
            FROM public.agent_player_revenue_summary_progress_tracker
            WHERE status = 0
            ORDER BY id ASC
            LIMIT 1;
          `, { type: sequelize.QueryTypes.SELECT , useMaster: true});

          if(checkPendingTask.length > 0){
            // const job_id = checkPendingTask[0].id
            // Destructure the range_from and range_to values from the entry
            const { range_from, range_to, range_count, current_range_index} = checkPendingTask[0];
            const range = {
              from: moment(range_from).utc().format('YYYY-MM-DD 18:30:00.000000'),
              to: moment(range_to).utc().format('YYYY-MM-DD 18:29:59.999999'),
            };
            await processRange(range, isProdEnv, range_count, current_range_index);

            console.log(`Stored procedure called with range_from: ${range_from}, range_to: ${range_to}`);
          }else{
            console.log('No Pending task found in agent_player_revenue_summary_progress_tracker.');
          }
        }
    }
  } catch (e) {
    console.log("=============> Error in Agent Player Revenue Progress Tracker cron:", e)
    await ErrorLogHelper.logError(e, null, null)
  }
}

async function processRange(range, isProdEnv, range_count, current_range_index) {
  try {
    // Mark the range as "In Progress" in the progress tracker table
    await updateProgressTracker(range, AGENT_PLAYER_REVENUE_TRACKER_SP_TYPE.UPDATE_PLAYER_SUMMARY_PROVIDER_WISE, PROGRESS_TRACKER_STATUS.IN_PROGRESS, range_count, current_range_index);

    // Delete all records from player revenue tables for date
    let deleteQuery = `
      delete from player_provider_other_currency where player_summary_provider_id in (
        select id from player_summary_provider_wise where date = date('${range.to}'::timestamp)
      );
      delete from player_summary_provider_wise where date = date('${range.to}'::timestamp);
    `;

    await sequelize.query(deleteQuery);

    const callPlayerSummaryProvWiseSp = `CALL update_player_summary_provider_wise_sp(
      '${range.from}'::TIMESTAMP,
      '${range.to}'::TIMESTAMP,
       ${isProdEnv}
    )`

    await sequelize.query(callPlayerSummaryProvWiseSp);

    // Adding delay of 500 ms between stored procedure calls
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
    await delay(500)

    const callPlayerSummaryProvGameWiseTransSp = `CALL update_player_summary_provider_game_wise_transaction_sp(
      '${range.from}'::TIMESTAMP,
      '${range.to}'::TIMESTAMP,
       ${isProdEnv}
    )`

    await sequelize.query(callPlayerSummaryProvGameWiseTransSp);

    // Update progress to "Completed" after successful processing
    await updateProgressTracker(range, AGENT_PLAYER_REVENUE_TRACKER_SP_TYPE.UPDATE_PLAYER_SUMMARY_PROVIDER_GAME_WISE_TRANS, PROGRESS_TRACKER_STATUS.IN_PROGRESS, range_count, current_range_index);

    await delay(500)

    // Delete all records from agent revenue tables for date
    deleteQuery = `
      delete from agent_revenue_report_currency where agent_revenue_report_id in (
        select id from agent_revenue_report where date = date('${range.to}'::timestamp)
      );
      delete from agent_revenue_report where date = date('${range.to}'::timestamp);
    `;

    await sequelize.query(deleteQuery);

    const callAgentRevenueReportSp = `CALL update_agent_revenue_report(
      '${range.from}'::TIMESTAMP,
      '${range.to}'::TIMESTAMP,
      ${isProdEnv},
      '${range.from}'::TIMESTAMP,
      '${range.to}'::TIMESTAMP
    )`

    await sequelize.query(callAgentRevenueReportSp);

    // Update progress to "Completed" after successful processing
    await updateProgressTracker(range, AGENT_PLAYER_REVENUE_TRACKER_SP_TYPE.UPDATE_AGENT_REVENUE_REPORT, PROGRESS_TRACKER_STATUS.COMPLETED, range_count, current_range_index);

  } catch (error) {
    console.error(`[ERROR]_Error_processing_range: ${range.from} - ${range.to}`, error);
    await updateProgressTracker(range, 1, PROGRESS_TRACKER_STATUS.FAILED, range_count, current_range_index);
    await ErrorLogHelper.logError(error, null, null)
    throw error;
  }
}

async function updateProgressTracker(range, type, status, range_count, current_range_index) {
  const query = `
    INSERT INTO public.agent_player_revenue_summary_progress_tracker (range_from, range_to, type, status, range_count, current_range_index, created_at, updated_at)
    VALUES (:rangeFrom, :rangeTo, :type, :status, :range_count, :current_range_index, NOW(), NOW())
    ON CONFLICT (range_from, range_to)
    DO UPDATE SET type = :type, status = :status, current_range_index = :current_range_index, updated_at = NOW()
  `;

  await sequelize.query(query, {
    replacements: {
      rangeFrom: range.from,
      rangeTo: range.to,
      type: type,
      status: status,
      range_count:range_count,
      current_range_index: current_range_index
    },
  });

  console.log(`[Progress_Tracker] Range: ${range.from} - ${range.to}, Status: ${status}`);
}
