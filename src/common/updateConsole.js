import axios from 'axios';
import { StatusCodes } from 'http-status-codes';
import { PassThrough } from 'stream';
import XLSX from 'xlsx';
import db, { sequelize } from '../db/models';


const { Op, fn, col, literal } = require('sequelize');

export default async (spreadsheetId) => {
  const sequelizeTransaction = await sequelize.transaction();
  try {
    let noToInsert = []

    if (!spreadsheetId) throw new Error("Sheet Id is required!");

    const exportUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/export?format=xlsx`;
    // Make a request to the export URL
    let response;
    try {
      response = await axios({
        url: exportUrl,
        method: 'GET',
        responseType: 'stream', // Use stream response type
      });
    } catch (error) {
      throw new Error('Can not access file!');
    }

    // Create a PassThrough stream to handle the incoming data
    const passThrough = new PassThrough();
    response.data.pipe(passThrough);

    // Collect the data chunks into a buffer
    const chunks = [];
    passThrough.on('data', chunk => {
      chunks.push(chunk);
    });

    // Once the stream is finished, concatenate the chunks into a single buffer
    const buffer = await new Promise((resolve, reject) => {
      passThrough.on('end', () => resolve(Buffer.concat(chunks)));
      passThrough.on('error', reject);
    });

    // Read XLSX file
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheetNameList = workbook.SheetNames;
    let gameProviderArray = XLSX.utils.sheet_to_json(
      workbook.Sheets[sheetNameList[0]],
      {
        blankrows: false,
        defval: '',
        raw: true,
        header: ['provider', 'top_menu', 'display_provider', 'page_menu', 'game_name', 'table_id', 'status', 'uuid', 'catogory']
      }
    );

    // Validate Headers in JSON
    const xlsxHeadersMapping = {
      provider: 'Providers',
      top_menu: 'Main Top Menus',
      display_provider: 'Display Provider name',
      page_menu: 'Display Game Category/Menu Name',
      game_name: 'Display Game Name',
      table_id: 'Table ID',
      status: 'Enabled',
      uuid: 'Actual Game Name (By Provider)',
      catogory: 'NEW CATEGORY'
    };

    const headerObj = gameProviderArray[0];
    Object.keys(headerObj).forEach(headerKey => {
      if (headerObj[headerKey] !== xlsxHeadersMapping[headerKey]) {
        throw new Error("Invalid Headers!");
      }
    });

    gameProviderArray = gameProviderArray.slice(1);
    let allProviderIds = gameProviderArray.map(item => item.provider).filter((value, index, self) => self.indexOf(value) === index);

    if (allProviderIds.length) {
      let lowerProviderIds = allProviderIds.map(provider => provider.toLowerCase());

      let existingProviders = await db.CasinoProvider.findAll({
        raw: true,
        where: {
          [Op.or]: lowerProviderIds.map(provider => ({
            [Op.and]: [
              literal(`LOWER("name") = '${provider}'`) // Case-insensitive comparison
            ]
          }))
        },
        attributes: ['id', 'name'],
        transaction: sequelizeTransaction
      });

      gameProviderArray = gameProviderArray.map(item => {
        const provider = item.provider.toLowerCase();
        const existingProvider = existingProviders.find(p => p.name.toLowerCase() === provider);
        if (existingProvider) {
          item.providerId = existingProvider.id;
        } else {
          item.providerId = null;

        }
        return item;
      });

      noToInsert = gameProviderArray.filter(item => item.providerId === null).map(item => {
        item.message = `Provider ${item.provider} not found!`;
        return item;
      });
      gameProviderArray = gameProviderArray.filter(item => item.providerId !== null);

    }

    let topMenus = gameProviderArray.map(item => item.top_menu).filter((value, index, self) => self.indexOf(value) === index);
    let existingTopMenus = await db.MenuMaster.findAll({
      raw: true,
      where: {
        [Op.or]: topMenus.map(menu => ({
          [Op.and]: [
            literal(`LOWER("name") = '${menu.toLowerCase()}'`) // Case-insensitive comparison
          ]
        }))
      },
      attributes: ['id', 'name'],
      transaction: sequelizeTransaction
    });


    //add top_menu_id in gameProviderArray record
    gameProviderArray = gameProviderArray.map(item => {
      const topMenu = item.top_menu.toLowerCase();
      const existingTopMenu = existingTopMenus.find(p => p.name.toLowerCase() === topMenu);
      if (existingTopMenu) {
        item.topMenuId = existingTopMenu.id;
      } else {
        item.topMenuId = null; // or handle as needed
      }
      return item;
    });

    noToInsert = [...noToInsert, ...gameProviderArray.filter(item => item.topMenuId === null).map(item => {
      item.message = `Top Menu ${item.top_menu} not found!`;
      return item;
    })];
    gameProviderArray = gameProviderArray.filter(item => item.topMenuId !== null);


    // page_menu Names
    let pageMenuNames = gameProviderArray.map(item => item.catogory ? item.catogory.toString() : '').filter((value, index, self) => self.indexOf(value) === index);
    pageMenuNames = pageMenuNames.filter(menu => menu.trim() !== '');
    if (pageMenuNames.length) {
      let existingPageMenus = await db.CasinoMenu.findAll({
        raw: true,
        where: {
          [Op.or]: pageMenuNames.map(menu => ({
            [Op.and]: [
              literal(`LOWER("name") = '${menu.toLowerCase()}'`) // Case-insensitive comparison
            ]
          })),
          tenantId: 0
        },
        attributes: ['id', 'name'],

        transaction: sequelizeTransaction
      });

      let existingPageMenuNames = existingPageMenus.map(p => p.name.toLowerCase());
      let missingPageMenus = pageMenuNames.filter(name => !existingPageMenuNames.includes(name.toLowerCase()));

      let newlyCreatedPageMenus = []
      if (missingPageMenus.length) {
        const pageMenuInsertPromises = missingPageMenus.map(menu => {
          return {
            name: menu,
            menuType: null,
            menuOrder: null,
            enabled: true,
            tenantId: 0,
            imageUrl: 'category-icons/' + menu.toLowerCase().replace(/\s+/g, '-') + '.svg'
          };
        });
        newlyCreatedPageMenus = await db.CasinoMenu.bulkCreate(pageMenuInsertPromises, { transaction: sequelizeTransaction });
      }

      let allPageMenus = [...existingPageMenus,
      ...newlyCreatedPageMenus.map(menu => ({
        id: menu.dataValues.id,
        name: menu.dataValues.name
      }))
      ];
      gameProviderArray = gameProviderArray.map(item => {
        const pageMenu = item.catogory ? item.catogory.toString() : '';
        const existingPageMenu = allPageMenus.find(p => p.name.toString().toLowerCase() === pageMenu.toString().toLowerCase());
        if (existingPageMenu) {
          item.pageMenuId = existingPageMenu.id;
        } else {
          item.pageMenuId = null;
        }
        return item;
      });

      noToInsert = [...noToInsert, ...gameProviderArray.filter(item => item.pageMenuId === null).map(item => {
        item.message = `Icon ${item.page_menu} not found!`;
        return item;
      })];
      gameProviderArray = gameProviderArray.filter(item => item.pageMenuId !== null);
    }


    let categoryGameNames = gameProviderArray.map(item => item.table_id ? item.table_id.toString() : '').filter((value, index, self) => self.indexOf(value) === index);
    categoryGameNames = categoryGameNames.filter(menu => menu.toString().trim() !== '');

    let allInfo = await sequelize.query(`
    select "pages"."id" as page_id, "casino_items"."provider" as "provider_id", "pages"."top_menu_id" as "top_menu_id",
    CASE
        WHEN "pages"."enabled" = false THEN false
        WHEN "casino_items"."active" = false THEN false
        WHEN "menu_items"."active" =  false THEN false
        WHEN "menu_master"."active" = false THEN false
        WHEN "casino_menus"."enabled" = false THEN false
        ELSE true
    END AS status,
    "pages".id as "page_status_id",pages.enabled as "page_status",
    "casino_items".id as "casino_items_status_id", "casino_items"."active" as "casino_items_status",
    "menu_items".id as "menu_items_status_id", "menu_items"."active" as "menu_items_status",
    "menu_master".id as "menu_master_status_id", "menu_master"."active" as "menu_master_status",
    "casino_menus".id as "casino_menus_status_id", "casino_menus"."enabled" as "casino_menus_status",
    "casino_items"."uuid" as "uuid", "pages"."title" as "page_name", "page_menus"."id" as "page_menu_id",
      "casino_menus"."name" as "casino_menu_name", "menu_items"."id" as "menu_item_id", "menu_items"."name" as "name", "casino_items"."id" as "id",
      "casino_items"."uuid" as "uuid" from "casino_items" inner join "menu_items" on "casino_items"."id" = "menu_items"."casino_item_id" inner join "page_menus" on "menu_items"."page_menu_id" = "page_menus"."id"
        inner join "casino_menus" on "page_menus"."casino_menu_id" = "casino_menus"."id" and "casino_items"."tenant_id" = "casino_menus"."tenant_id" inner join "pages" on "page_menus"."page_id" = "pages"."id"
        and "pages"."tenant_id" = "casino_items"."tenant_id" inner join "menu_master" on "pages"."top_menu_id" = "menu_master"."id" where
        "pages"."tenant_id" = 0
      and casino_items.uuid in (:categoryNames)
    `, {
      replacements: { categoryNames: categoryGameNames },
      type: sequelize.QueryTypes.SELECT,
      transaction: sequelizeTransaction
    });

    gameProviderArray = gameProviderArray.map(item => {
      const categoryName = item.table_id ? item.table_id.toString() : '';
      const existingCategory = allInfo.find(p => p.uuid === categoryName && p.provider_id == item.providerId && p.top_menu_id == item.topMenuId);
      if (existingCategory && existingCategory.status) {
        item.basicInfo = existingCategory;
      } else if (existingCategory && !existingCategory.status) {
        item.basicInfo = existingCategory;
        item.status = false;
      } else {
        item.basicInfo = null;
      }
      return item;

    }
    );
    noToInsert = [...noToInsert, ...gameProviderArray.filter(item => item.status == false).map(item => {
      item.message = `Game ${item.table_id} is inactive!`;
      return item;
    }
    )];
    gameProviderArray = gameProviderArray.filter(item => item.status !== false);
    noToInsert = [...noToInsert, ...gameProviderArray.filter(item => item.basicInfo === null).map(item => {
      item.message = `Game ${item.table_id} not found!`;
      return item;
    }
    )];

    gameProviderArray = gameProviderArray.filter(item => item.basicInfo !== null);

    let allCatogeryNames = gameProviderArray.map(item => item.catogory ? item.catogory.toString() : '').filter((value, index, self) => self.indexOf(value) === index);

    let allCatogeryNamesWithTopMenu = gameProviderArray.map(item => {
      return {
        catogory: item.catogory ? item.catogory.toString() : '',
        topMenuId: item.topMenuId
      }
    } ).filter((value, index, self) => {
      return self.findIndex(item => item.catogory == value.catogory && item.topMenuId == value.topMenuId) === index;
    });
    allCatogeryNames = allCatogeryNamesWithTopMenu.map(item => item.catogory).filter((value, index, self) => self.indexOf(value) === index);
    allCatogeryNames = allCatogeryNames.filter(menu => menu.toString().trim() !== '');

    let existingCatogeryNames = await db.CustomCategory.findAll({
      raw: true,
      where: {
        [Op.or]: allCatogeryNames.map(menu => ({
          [Op.and]: [
            literal(`LOWER("title") = '${menu.toLowerCase()}'`) // Case-insensitive comparison
          ]
        })),
        tenantId: 0
      },
      attributes: ['id', 'title', 'topMenuId'],
      transaction: sequelizeTransaction
    });

    let gameWithCatogery = [];
    let nogames = [];

    for (let i = 0; i < allCatogeryNamesWithTopMenu.length; i++) {
      let catogeryName = allCatogeryNamesWithTopMenu[i].catogory;
      let topMenuId = allCatogeryNamesWithTopMenu[i].topMenuId;
      let obj = {}
      let existingCatogery = existingCatogeryNames.find(p => p.title && (p.title.toLowerCase() === catogeryName.toLowerCase()) && p.topMenuId == topMenuId);
      if (existingCatogery) {

        obj.id = existingCatogery.id;
        obj.title = existingCatogery.title;

      } else {
        obj.id = null;
        obj.title = catogeryName;
      }


      const gamesList = gameProviderArray.filter(p => (p.catogory.toString().toLowerCase() === catogeryName.toLowerCase()) && p.topMenuId == topMenuId && p.providerId == p.basicInfo.provider_id);
      if (gamesList.length) {
        obj.gamesList = gamesList
      } else {
        nogames.push(catogeryName)
        obj.gamesList = [];
      }

      gameWithCatogery.push(obj);

    }

    let gameListToInsert = []
    for (let i = 0; i < gameWithCatogery.length; i++) {

      if (gameWithCatogery[i].gamesList.length) {
        let gameList = gameWithCatogery[i].gamesList;
        let catogeryId = gameWithCatogery[i].id;
        let catogeryName = gameWithCatogery[i].title;
        let menuIconId = gameList[0].pageMenuId;
        let topMenuId = gameList[0].topMenuId

        let newCatogery = false
        if (!catogeryId) {
          let categogryInsert = await db.CustomCategory.create({
            tenantId: 0,
            menuIconId: menuIconId,
            isMenuIconModify: false,
            title: catogeryName,
            isMenuModify: false,
            topMenuId: topMenuId,
            createdBy: 0,
            isOrderModify: false,
            ordering: null,
            status: true,
            isStatusModify: false,
            referenceId: null,
            isDeleted: false,
            lastSyncedAt: new Date(),
          }, { transaction: sequelizeTransaction })
          catogeryId = categogryInsert.dataValues.id;
          newCatogery = true
        }

        let catogeryGameListToInsert = gameList.map((item, index) => {
          return {
            tenantId: 0,
            categoryId: catogeryId,
            uuid: item.basicInfo.uuid,
            pageId: item.basicInfo.page_id,
            providerId: item.providerId,
            ordering: (newCatogery ? index + 1 : null),
            isOrderModify: false,
            isDeleted: false,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        })
        gameListToInsert = [...gameListToInsert, ...catogeryGameListToInsert]
        await db.CustomCategoryGames.update(
          { isDeleted: false },
          {
            where: {
              categoryId: catogeryId,
              uuid: { [Op.in]: catogeryGameListToInsert.map(item => item.uuid ? item.uuid.toString() : '') }
            },
            transaction: sequelizeTransaction
          }
        );
      }
    }

    if (gameListToInsert.length) {
      await db.CustomCategoryGames.bulkCreate(gameListToInsert, { transaction: sequelizeTransaction, ignoreDuplicates: true });
    }

    let allInactive = {
      pages: new Set(),
      casino_items: new Set(),
      menu_items: new Set(),
      menu_master: new Set(),
      casino_menus: new Set()
    }

    noToInsert.forEach(item => {
      if (item.basicInfo) {
        if (item.basicInfo.page_status == false) {
          allInactive.pages.add(item.basicInfo.page_id)
        }
        if (item.basicInfo.casino_items_status == false) {
          allInactive.casino_items.add(item.basicInfo.casino_items_status_id)
        }
        if (item.basicInfo.menu_items_status == false) {
          allInactive.menu_items.add(item.basicInfo.menu_items_status_id)
        }
        if (item.basicInfo.menu_master_status == false) {
          allInactive.menu_master.add(item.basicInfo.menu_master_status_id)
        }
        if (item.basicInfo.casino_menus_status == false) {
          allInactive.casino_menus.add(item.basicInfo.casino_menus_status_id)
        }
      }
    })


    // convert the Set objects to arrays
    allInactive.pages = Array.from(allInactive.pages);
    allInactive.casino_items = Array.from(allInactive.casino_items);
    allInactive.menu_items = Array.from(allInactive.menu_items);
    allInactive.menu_master = Array.from(allInactive.menu_master);
    allInactive.casino_menus = Array.from(allInactive.casino_menus);



    let noRecords = noToInsert.map(item => {
      return {
        Providers: item.provider,
        MainTopMenus: item.top_menu,
        DisplayProviderName: item.display_provider,
        DisplayGameCategory: item.page_menu,
        DisplayGameName: item.game_name,
        TableID: item.table_id,
        Enabled: item.status ? "true" : "false",
        ActualGameName: item.uuid,
        NEW_CATEGORY: item.catogory,
        Message: item.message,
        page_status: item.basicInfo?.page_status ? "Yes" : item.basicInfo?.page_status_id ? "No" : "--",
        page_status_id: item.basicInfo?.page_status_id,
        casino_items_status: item.basicInfo?.casino_items_status ? "Yes" : item.basicInfo?.casino_items_status_id ? "No" : "--",
        casino_items_id: item.basicInfo?.casino_items_status_id,
        menu_items_status: item.basicInfo?.menu_items_status ? "Yes" : item.basicInfo?.menu_items_status_id ? "No" : "--",
        menu_items_id: item.basicInfo?.menu_items_status_id,
        menu_master_status: item.basicInfo?.menu_master_status ? "Yes" : item.basicInfo?.menu_master_status_id ? "No" : "--",
        menu_master_id: item.basicInfo?.menu_master_status_id
      };
    });

    await sequelizeTransaction.commit();
    return { message: 'Games updated succcessfully!', noToInsert: noRecords, status: StatusCodes.OK, allInactive: allInactive }

  } catch (error) {
    await sequelizeTransaction.rollback();
    return { message: error.message, status: StatusCodes.UNPROCESSABLE_ENTITY }
  }
};
