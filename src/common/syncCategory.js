import { Op } from 'sequelize'
import db, { sequelize } from '../db/models'
import { EVENT, EVENT_TYPE } from './constants'

export default async (data, sequelizeTransaction = null) => {
  let transaction
  try {
    const { CustomCategory, Tenant } = db
    const { id: categoryId, tenantIds, topMenuId, changeOrder } = data.id

    // Fetch categories based on categoryId or topMenuId
    const categories = categoryId ?
    await CustomCategory.findAll({
      where: { id: categoryId, isDeleted: false },
      attributes: ['id', 'ordering', 'isOrderModify', 'referenceId', 'title'],
      raw: true,
      order: [['ordering', 'ASC']]
    }) : await CustomCategory.findAll({
      where: { tenantId: 0, topMenuId, isDeleted: false },
      attributes: ['id', 'ordering', 'isOrderModify', 'referenceId', 'title'],
      raw: true ,
      order: [['ordering', 'ASC']]
    })

    if (!categories.length) return

    // Fetch tenants based on tenantIds or active status
    const tenants = tenantIds?.length
      ? tenantIds.map(id => ({ id: parseInt(id) }))
      : await Tenant.findAll({ where: { active: true }, attributes: ['id'], raw: true })

    transaction = sequelizeTransaction ? sequelizeTransaction : await sequelize.transaction()

    const auditDataList = []
    for (const category of categories) {
      for (const tenant of tenants) {
        const result = await processCategory(tenant.id, category.id, topMenuId, transaction)
        const auditData = {
          tenantId: tenant.id,
          actioneeId: 1,
          eventType: EVENT_TYPE.SyncCategory,
          event: result.create ? EVENT.create : EVENT.update,
          eventId: result ? result.categoryId : category.id,
          actioneeIp: '',
          action: 'Sync Category Super Admin',
          previousData: { id: category.id, topMenuId: topMenuId, status: 'pending', message: result.message , title : category?.title || ''},
          modifiedData: { id: category.id, topMenuId: topMenuId, status: result.status, message: result.message , title : category?.title || ''},
        }
        auditDataList.push(auditData)
      }
    }

    // Create audit logs and queue logs
    if (auditDataList.length) {
      const auditCreated = await db.AuditLog.bulkCreate(auditDataList, { transaction })
      const auditIds = auditCreated.map(a => a.dataValues.id)
      await db.QueueLog.bulkCreate(auditIds.map(id => ({ type: 'audit_log', ids: [String(id)] })), { transaction })
    }

    // Process category ordering if changeOrder is true
    if (changeOrder) {
      for (let i = 0; i < tenants.length; i++) {
        await processCategoryOrdering(tenants[i].id, topMenuId, transaction, categories)
      }
    }
    if(!sequelizeTransaction){
        await transaction.commit()
    }
  } catch (error) {

    if (!sequelizeTransaction && transaction) await transaction.rollback()
    throw error
  }
}

// Process individual category for a tenant
async function processCategory (tenantId, categoryId, topMenuId, transaction) {
  const { CustomCategory, CustomCategoryGames, MenuTenantSetting, MenuMaster } = db
  let [baseCategory, categoryEach, topMenu] = await Promise.all([
    CustomCategory.findOne({ where: { id: categoryId, isDeleted: false }, raw: true }),
    CustomCategory.findOne({ where: { referenceId: categoryId, tenantId }, transaction }),
    MenuTenantSetting.findOne({
      include: [{ model: MenuMaster, where: { active: true }, required: true }],
      where: { tenant_id: tenantId, menu_id: topMenuId },
      attributes: ['id'],
      raw: true
    })
  ])

  if (!baseCategory || !topMenu) {
    return { create: !categoryEach, categoryId, message: 'Category/Top menu not found', status: 'failed' }
  }

  // Check if category title already exists
  const titleExists = await CustomCategory.count({
    where: {
      title: baseCategory.title,
      tenantId,
      topMenuId: topMenu.id,
      isDeleted: false,
      ...categoryEach ? { id: { [Op.ne]: categoryEach.id } } : {}
    },
    transaction
  })
  if (titleExists) {
    return { create: !categoryEach, categoryId, message: 'Category title already exists', status: 'failed' , title : baseCategory.title}
  }

  // Query to fetch games data
  const query = ` SELECT ci.tenant_id, ci.uuid AS uuid, pm.page_id AS page_id, ci.provider::INTEGER AS provider_id, COALESCE(ccg.is_order_modify, FALSE) AS is_order_modify, COALESCE(ccg.is_deleted, FALSE) AS is_deleted, NOW() AS created_at, NOW() AS updated_at
 FROM casino_items ci JOIN custom_category_games ccg ON ccg.uuid = ci.uuid inner join menu_items mi ON ci.id = mi.casino_item_id inner join page_menus pm ON mi.page_menu_id = pm.id inner join "casino_menus" on "pm"."casino_menu_id" = "casino_menus"."id" and "ci"."tenant_id" = "casino_menus"."tenant_id"
 inner join pages p ON pm.page_id = p.id
 inner join menu_tenant_setting mts ON p.top_menu_id = mts.id
 inner join menu_master mm ON mts.menu_id = mm.id
 WHERE ci.tenant_id = :v_tenant_id AND ci.active = TRUE AND ccg.is_deleted = FALSE AND ccg.category_id = :v_reference_id AND mi.active = TRUE AND p.tenant_id = ci.tenant_id AND p.enabled = TRUE AND mts.tenant_id = p.tenant_id AND mts.status = TRUE AND mm.active = TRUE AND "casino_menus"."enabled" = true AND p.top_menu_id = :v_top_menu_id
 ORDER BY  ccg.ordering ASC`

  let gamesData = await sequelize.query(query, {
    replacements: { v_tenant_id: tenantId, v_reference_id: categoryId, v_top_menu_id: topMenu.id },
    type: sequelize.QueryTypes.SELECT,
    raw: true
  })

  gamesData = gamesData.filter((v, i, a) => a.findIndex(t => (t.uuid === v.uuid)) === i)

  // check how many games in super admin are active

  let activeGamesQuery = `select "custom_category_games".uuid
        from "custom_category_games"
                 inner join "pages" on "custom_category_games"."page_id" = "pages"."id"
                 inner join "casino_items" on "custom_category_games"."uuid" = "casino_items"."uuid" and
                                              "pages"."tenant_id" = "casino_items"."tenant_id"
                 inner join "page_menus" on "page_menus"."page_id" = "pages"."id"
                 inner join "casino_menus" on "page_menus"."casino_menu_id" = "casino_menus"."id" and
                                              "casino_items"."tenant_id" = "casino_menus"."tenant_id"
                 inner join "menu_items" on "casino_items"."id" = "menu_items"."casino_item_id" and
                                            "page_menus"."id" = "menu_items"."page_menu_id"
                 inner join "menu_master" on "pages"."top_menu_id" = "menu_master"."id"
        where "custom_category_games"."category_id" = :categoryId
          and "custom_category_games"."is_deleted" = false
          and "pages"."enabled" = true
          and "casino_items"."active" = true
          and "menu_items"."active" = true
          and "casino_menus"."enabled" = true
          and "menu_master"."active" = true
          `
  let activeGames = await sequelize.query(activeGamesQuery, {
    replacements: { categoryId: categoryId },
    type: sequelize.QueryTypes.SELECT,
    raw: true
  })

  // gamesData only keeps the uuid of the active games
  gamesData = gamesData.filter((v, i, a) => activeGames.findIndex(t => (t.uuid === v.uuid)) !== -1)

  let newlyCreated = false

  const updates = {}
  if (!categoryEach) {
    if (!gamesData.length) return { create: true, categoryId, message: 'No games found', status: 'failed', title : baseCategory.title}
    newlyCreated = true
    categoryEach = await CustomCategory.create({
      tenantId,
      menuIconId: baseCategory.menuIconId,
      title: baseCategory.title,
      topMenuId: topMenu.id,
      createdBy: baseCategory.createdBy,
      status: baseCategory.status,
      referenceId: baseCategory.id,
      isDeleted: false,
      lastSyncedAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    }, { transaction })
  } else {
    if (!categoryEach.isMenuIconModify) updates.menuIconId = baseCategory.menuIconId
    if (!categoryEach.isMenuModify) updates.title = baseCategory.title
    if (!categoryEach.isStatusModify) updates.status = baseCategory.status
    updates.lastSyncedAt = new Date()
    updates.updatedAt = new Date()
    await categoryEach.update(updates, { transaction })
  }
  const isOrderModify = await CustomCategoryGames.count({
    where: { categoryId: categoryEach.id, isOrderModify: true },
    transaction
  })

  // Fetch existing games for the category
  const games = await CustomCategoryGames.findAll({
    where: { categoryId: categoryEach.id },
    attributes: ['uuid', 'ordering', 'isOrderModify', 'isDeleted', 'id', 'pageId', 'providerId', 'tenantId'],
    transaction
  })

  // Remove games that are not in the new games data
  await CustomCategoryGames.destroy({
    where: {
      categoryId: categoryEach?.id,
      uuid: {
        [Op.notIn]: gamesData.map(g => g.uuid)
      }
    },
    transaction
  })

  if (isOrderModify) {
    const newGames = gamesData.filter(g => !games.some(gg => gg.dataValues.uuid === g.uuid))
    let maxOrder = await CustomCategoryGames.max('ordering', { where: { categoryId: categoryEach.id }, transaction })
    newGames.forEach((game) => {
      game.tenantId = tenantId
      game.categoryId = categoryEach.id
      game.createdBy = 0
      game.ordering = maxOrder + 1
      game.isOrderModify = true
      game.pageId = game.page_id
      game.providerId = game.provider_id
      maxOrder++
    })

    await CustomCategoryGames.bulkCreate(newGames, { transaction }, { ignoreDuplicates: true })
  } else {
    const creates = []

    for (let i = 0; i < gamesData.length; i++) {
      const existingGame = games.find(g => g.dataValues.uuid === gamesData[i].uuid)
      if (existingGame) {
        if (existingGame.ordering !== i + 1) {
          existingGame.ordering = i + 1
          await existingGame.save({ transaction })
        }
      } else {
        creates.push({
          tenantId,
          categoryId: categoryEach?.id,
          uuid: gamesData[i].uuid,
          pageId: gamesData[i].page_id,
          providerId: gamesData[i].provider_id,
          createdAt: new Date(),
          updatedAt: new Date(),
          ordering: i + 1
        })
      }
    }
    if (creates.length) await CustomCategoryGames.bulkCreate(creates, { transaction }, { ignoreDuplicates: true })
  }
  return { create: newlyCreated ? true : false, categoryId, message: 'Synced successfully', status: 'success' , title : baseCategory.title }
}

// Process category ordering for a tenant
async function processCategoryOrdering (tenantId, topMenuId, transaction, categories) {
  const { CustomCategory } = db

  const [allcategies] = await Promise.all([
    CustomCategory.findAll({
      where: {
        tenantId: tenantId,
        referenceId: categories.map(c => c.id),
        isDeleted: false
      },
      attributes: ['id', 'ordering', 'isOrderModify', 'referenceId']
    },
    { transaction }
    )

  ])

  const anyOrderModify = allcategies.some(c => c.dataValues.isOrderModify)
  if (anyOrderModify) return

  for (let i = 0; i < allcategies.length; i++) {
    const category = allcategies[i]
    const newOrdering = categories.findIndex(c => c.id == category.referenceId) + 1
    if (category.ordering !== newOrdering) {
      category.ordering = newOrdering
      await category.save({ transaction })
    }
  }
}
