import axios from 'axios'
import { Op, QueryTypes, Sequelize } from 'sequelize'
import { v4 as uuidv4 } from 'uuid'
import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import Logger from '../libs/logger'
import { CASINO_MENU_IMAGES, CRON_LOG_STATUS, FUNKY_CERTIFIED_GAMES, FUNKY_ICON, PROD_FUNKY_GAMES_PROVIDER, STAGE_FUNKY_GAMES_PROVIDER, TOP_MENU } from './constants'
import { getCasinoProvider } from './getCasinoProvider'
import syncOrOverrideCasinoProvider from './syncOrOverrideCasinoProvider'

export default async (reqBody = null) => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const {
      TenantCredential: TenantCredentialModel,
      CasinoGame: CasinoGameModel,
      CasinoTable: CasinoTableModel,
      CasinoItem: CasinoItemModel,
      Page: PageModel,
      CasinoMenu: CasinoMenuModel,
      PageMenu: PageMenuModel,
      MenuItem: MenuItemModel,
      MenuMaster: MenuMasterModel
    } = db

    // Check if the queue process status is active
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'funky_games_game_population',
        status: 1
      },
      attributes: ['id']
    })
    if (!queueProcessStatus) {
      return {
        success: false,
        message:"Queue Process Stopped."
       }
    }
    if (queueProcessStatus) cronLog.cronId = queueProcessStatus?.id

    // Determine the casino provider ID based on the environment
    const casinoProviderId = config.get('env') === 'production' ? PROD_FUNKY_GAMES_PROVIDER : STAGE_FUNKY_GAMES_PROVIDER

      // Fetch tenant IDs and menu IDs for processing
      let tenantIdsPromise = sequelize.query(`
        SELECT "tenant_id" AS "tenantId", ( SELECT "id" FROM "menu_tenant_setting" WHERE "menu_id" = (SELECT "id" FROM "menu_master" WHERE "name" = 'Casino')
        AND "tenant_id" = "TenantThemeSetting"."tenant_id" ) AS "casinoId",
        ( SELECT "id" FROM "menu_tenant_setting" WHERE "menu_id" = (SELECT "id" FROM "menu_master" WHERE "name" = 'Fishing')
        AND "tenant_id" = "TenantThemeSetting"."tenant_id" ) AS "fishingId",
        ( SELECT "id" FROM "menu_tenant_setting" WHERE "menu_id" = (SELECT "id" FROM "menu_master" WHERE "name" = 'Live Casino')
        AND "tenant_id" = "TenantThemeSetting"."tenant_id" ) AS "liveCasinoId"
        FROM "public"."tenant_theme_settings" AS "TenantThemeSetting" WHERE '${casinoProviderId}' = ANY (string_to_array(assigned_providers, ','))
        ${reqBody?.tenantId ? ` AND "tenant_id" = '${reqBody?.tenantId}'` : ''}
          `,
        { type: QueryTypes.SELECT, useMaster: false })

      const superAdminMenuIdsPromise = MenuMasterModel.findAll({
        attributes: ['id', 'name'],
        where: {
          name: {
            [Op.in]: [TOP_MENU.CASINO, TOP_MENU.FISHING, TOP_MENU.LIVE_CASINO]
          }
        },
        raw: true
      })

      // Wait for both promises to resolve
      let [tenantIds, superAdminMenuIds] = await Promise.all([tenantIdsPromise, superAdminMenuIdsPromise])
      let override_other_providers = reqBody?.override_other_providers
      if(Array.isArray(reqBody?.tenantIds) && !reqBody?.tenantIds.includes('0')){
          tenantIds = tenantIds.filter(tenant => reqBody.tenantIds.includes(tenant.tenantId));
      }
      tenantIds = [{
        tenantId: 0,
        casinoId: superAdminMenuIds.find(i => i.name === TOP_MENU.CASINO).id,
        fishingId: superAdminMenuIds.find(i => i.name === TOP_MENU.FISHING).id,
        liveCasinoId: superAdminMenuIds.find(i => i.name === TOP_MENU.LIVE_CASINO).id
      }, ...tenantIds]


      // Iterate over each tenant ID to process data
      for (let i = 0; i < tenantIds.length; i++) {
        const tenantId = tenantIds[i].tenantId
        const topCasinoId = tenantIds[i].casinoId
        const topFishingId = tenantIds[i].fishingId
        const sequelizeTransaction = await sequelize.transaction()
      try {


        // Fetch credentials for the tenant
        const creds = await TenantCredentialModel.findAll({
          attributes: ['key', 'value'],
          where: {
            key: ['FUNKY_GAMES_GAME_LIST', 'FUNKY_GAMES_AUTHENTICATION', 'FUNKY_GAMES_USER_AGENT'],
            tenantId
          },
          raw: true
        })

        // Extract credentials into variables
        const {
          FUNKY_GAMES_GAME_LIST,
          FUNKY_GAMES_AUTHENTICATION,
          FUNKY_GAMES_USER_AGENT
        } = creds.reduce((acc, cur) => {
          return {
            ...acc,
            [cur.key]: cur.value
          }
        }, { FUNKY_GAMES_GAME_LIST: null, FUNKY_GAMES_AUTHENTICATION: null, FUNKY_GAMES_USER_AGENT: null })

        const currentDate = new Date().toISOString()
        const anyUuid = uuidv4().replace(/-/g, '')
        let funkyGameData = await axios({
            url: `${FUNKY_GAMES_GAME_LIST}`,
            method: 'post',
            maxBodyLength: Infinity,
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': FUNKY_GAMES_USER_AGENT,
              Authentication: FUNKY_GAMES_AUTHENTICATION,
              'X-Request-ID': anyUuid
            },
            data: {
              language: 'EN',
              gameType: '0'
            }
          })


        // Validate the fetched game data
        if (funkyGameData?.data?.gameList === null) {
          cronLog.status = CRON_LOG_STATUS.FAILED
          cronLog.errorMsg =(funkyGameData.data?.error || 'No game list found' ) + ' ' + "TenantId: " + tenantId
          cronLog.endTime = new Date()
          await db.CronLog.create(cronLog)
          continue
        }

        // Map game types to user-friendly names
        const nameChangeObj = {
          Keno: 'Keno',
          SlotGame: 'Slots',
          Bingo: 'Bingo',
          TableGame: 'Table Games',
          FishGame: 'Fishing Games',
          CardGame: 'CardGame'
        }

        // Data for a page model
        const pageData = {
          title: 'Funky',
          enabled: true,
          order: null,
          createdAt: currentDate,
          updatedAt: currentDate,
          tenantId,
          topMenuId: topCasinoId,
          enableInstantGame: null,
          image: FUNKY_ICON
        }

        const pageDataFishing = {
          title: 'Funky',
          enabled: true,
          order: null,
          createdAt: currentDate,
          updatedAt: currentDate,
          tenantId,
          topMenuId: topFishingId,
          enableInstantGame: null,
          image: FUNKY_ICON
        }

        // Process and transform the fetched game data
         /**
        * Dataobject that returns the followin object
        *
        * @Object category @table casino_games
        * @object casinoTable @table casino_table
        * @object tableItems @table casino_item
        * */
        const funkyUpdatedData = funkyGameData?.data?.gameList.map(ele => {
          if (nameChangeObj[ele.gameType]) {
            ele.gameType = nameChangeObj[ele.gameType]
          }
          return ele
        })

        // Reduce the transformed data into structured objects
        const funkyGameDataObject = funkyUpdatedData?.reduce(
          (acc, cur) => {
            if (!acc.category.length || !acc.category.includes(cur.gameType)) {
              acc.category.push(cur.gameType)
            }

            acc.casinoTable.push({
              name: cur.gameName,
              gameId: `${cur.gameType}`,
              createdAt: currentDate,
              updatedAt: currentDate,
              isLobby: cur.onLobby,
              tableId: '' + cur.gameCode,
              providerId: casinoProviderId
            })

            acc.tableIds.push('' + cur.gameCode)
            acc.gameName.push(cur.gameName)
            acc.tableItems.push({
              uuid: '' + cur.gameCode,
              name: cur.gameName,
              image: `provider-images/funky/thumbnail/${cur.gameCode}.webp`,
              provider: casinoProviderId,
              active: !!((cur.gameStatus === 'Ready' && FUNKY_CERTIFIED_GAMES.includes(cur.gameCode))),
              featured: true,
              createdAt: currentDate,
              updatedAt: currentDate,
              tenantId
            })

            return acc
          }, { category: [], casinoTable: [], tableIds: [], tableItems: [], gameName: [], menuItem: [] })


          const funkyGameDataFishingObject = funkyUpdatedData?.reduce(
            (acc, cur) => {
              if (cur.gameType === 'Fishing Games') {
              if (!acc.category.length || !acc.category.includes(cur.gameType)) {
                acc.category.push(cur.gameType)
              }

              acc.casinoTable.push({
                name: cur.gameName,
                gameId: `${cur.gameType}`,
                createdAt: currentDate,
                updatedAt: currentDate,
                isLobby: cur.onLobby,
                tableId: '' + cur.gameCode,
                providerId: casinoProviderId
              })

              acc.tableIds.push('' + cur.gameCode)
              acc.gameName.push(cur.gameName)
              acc.tableItems.push({
                uuid: '' + cur.gameCode,
                name: cur.gameName,
                image: `provider-images/funky/thumbnail/${cur.gameCode}.webp`,
                provider: casinoProviderId,
                active: !!((cur.gameStatus === 'Ready' && FUNKY_CERTIFIED_GAMES.includes(cur.gameCode))),
                featured: true,
                createdAt: currentDate,
                updatedAt: currentDate,
                tenantId
              })
            }

            return acc
            }, { category: [], casinoTable: [], tableIds: [], tableItems: [], gameName: [], menuItem: [] })


        const dataToBeUpdatedIntoCasinoGames = funkyGameDataObject.category.map((item, idx) => {
          return {
            name: `${item}`,
            casinoProviderId,
            createdAt: currentDate,
            updatedAt: currentDate,
            gameId: `${item}`
          }
        })

        // Object to create or update into casino menu specific to a tenant
        const casinoMenuDataToBeUpdated = funkyGameDataObject.category.map((item, idx) => {
          return {
            name: `${item}`,
            menucategory: null,
            menuOrder: null,
            enabled: true,
            createdAt: currentDate,
            updatedAt: currentDate,
            tenantId,
            imageUrl: (CASINO_MENU_IMAGES.find((iurl) => iurl.name === item)) ? CASINO_MENU_IMAGES.find((iurl) => iurl.name === item).url : FUNKY_ICON
          }
        })

        // get existing data in CasinoGame table
        const casinoGamesData = await CasinoGameModel.findAll({
          attributes: ['gameId', 'id', 'name'],
          where: {
            gameId: {
              [Op.in]: dataToBeUpdatedIntoCasinoGames.map(item => item.gameId)
            },
            casinoProviderId: casinoProviderId
          },
          raw: true
        })

        /**
         * @object name: casinoTableData
         * @gets the already existed data in the
         * @table CasinoTableModel
         */

        const casinoTableData = await CasinoTableModel.findAll({
          attributes: ['gameId', 'tableId', 'name', 'id'],
          where: {
            tableId: {
              [Op.in]: funkyGameDataObject.tableIds
            },
            providerId: casinoProviderId
          },
          raw: true,
        })

        /**
         * @object name pagesData
         * @gets the already existing data in
         * @pages table
         */

    let pagesData = await getCasinoProvider(pageData.title, casinoProviderId, tenantId, topCasinoId, sequelizeTransaction, currentDate, {}, pageData.image, override_other_providers)
    let pageDataFishingData = await getCasinoProvider(pageDataFishing.title, casinoProviderId, tenantId, topFishingId, sequelizeTransaction, currentDate, {}, pageDataFishing.image, override_other_providers)


        /**
       * @object casinoItemData
       * @gets the data from table
       * @casinoItemModel
       */
        const casinoItemData = await CasinoItemModel.findAll({
          attributes: ['uuid', 'name', 'id', 'active'],
          where: {
            uuid: {
              [Op.in]: funkyGameDataObject.tableIds
            },
            tenantId,
            provider: casinoProviderId.toString()
          },
          raw: true
        })

        /**
         * @object casinoMenuData
         * gets the data from table
         * @casinoMenu
         */

        const namesToCheck = casinoMenuDataToBeUpdated.map(i => `'${i.name.toLowerCase()}'`).join(',')

        const casinoMenuData = await CasinoMenuModel.findAll({
          attributes: ['name', 'id'],
          where: {
            tenantId,
            name: Sequelize.literal(`LOWER("name") IN (${namesToCheck})`)
          },
          raw: true
        })

        // check update the casinoGamesData object responsible to maintain category based on new data
        const updatedDataToBeUpdatedIntoCasinoGames = dataToBeUpdatedIntoCasinoGames.filter(i => !casinoGamesData.map(i => i.gameId).includes(i.gameId))

        // update the casinoTableData object based on data from casinoTable table
        const updatedCasinoTableData = funkyGameDataObject.casinoTable.filter(i => !casinoTableData.map(i => i.tableId).includes(i.tableId))

        // update casinoItemData object based on data from casinoItem table
        const updatedCasinoItemData = funkyGameDataObject.tableItems.filter(i => !casinoItemData.map(i => i.uuid).includes(i.uuid))

        // existing data with diffrent active status from casinoItem table
        const existingCasinoItemData = casinoItemData.filter(i => funkyGameDataObject.tableItems.some(item => item.uuid === i.uuid && item.active !== i.active))

        const existingCasinoItemsNameToUpdate = funkyGameDataObject.tableItems.filter(item => {
          const existingItem = casinoItemData.find(i => i.uuid === item.uuid)
          return existingItem && existingItem.name !== item.name
        }).map(item => {
          return {
            ...item,
            name: item.name
          }
        })


        // update the name of CasinoGameModel
        await Promise.all(casinoGamesData.map(async item => {
          const fromgame = dataToBeUpdatedIntoCasinoGames.find((i) => (i.gameId === item.gameId))
          if (fromgame && fromgame.name !== item.name) {
            await CasinoGameModel.update({ name: fromgame.name }, { where: { id: item.id }, transaction: sequelizeTransaction })
            item.name = fromgame.name
          }

        }))

        // update the name of CasinoTableModel
        await Promise.all(casinoTableData.map(async item => {
          const fromgame = funkyGameDataObject.casinoTable.find((i) => (i.tableId === item.tableId))
          if (fromgame && fromgame.name !== item.name) {
            await CasinoTableModel.update({ name: fromgame.name }, { where: { id: item.id }, transaction: sequelizeTransaction })
            item.name = fromgame.name
          }
        }))

        await Promise.all(existingCasinoItemData.map(async item => {
          await CasinoItemModel.update({ active: !item.active }, { where: { id: item.id }, transaction: sequelizeTransaction })
          await MenuItemModel.update({ active: !item.active }, { where: { casinoItemId: item.id }, transaction: sequelizeTransaction })
        }))

        for (const item of casinoItemData) {
          const fromgame = existingCasinoItemData.find((i) => (i.uuid === item.uuid))
          if (fromgame) {
            item.active = !fromgame.active
          }
        }

        for (const item of existingCasinoItemsNameToUpdate) {
          await CasinoItemModel.update({ name: item.name }, {
            where: { uuid: item.uuid },
            provider: casinoProviderId.toString(),
            transaction: sequelizeTransaction
          })
          await CasinoTableModel.update({ name: item.name }, {
            where: { tableId: item.uuid },
            transaction: sequelizeTransaction,
            providerId: casinoProviderId
          })
        }


        // if pagesData doesnt exists then create the pageData
      // const updatedPageData = pagesData.id ? pagesData : pageData
        // const updatedPageDataFishing = pageDataFishingData.id ? pageDataFishingData : pageDataFishing

        // const updatedCasinoMenuData = casinoMenuData.length? casinoMenuData: casinoMenuDataToBeUpdated
        const updatedCasinoMenuData = casinoMenuDataToBeUpdated.filter(i => !casinoMenuData.map(i => i.name.toLowerCase()).includes(i.name.toLowerCase()))
        // Logger.info(`casinoTableData: ${JSON.stringify(updatedCasinoTableData)}`)

        // insert updated casino game data to table

        const insertedCasinoGamesData = await CasinoGameModel.bulkCreate(updatedDataToBeUpdatedIntoCasinoGames, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoGamesData: ${insertedCasinoGamesData}`)

        const insertedCasinoTableData = await CasinoTableModel.bulkCreate(updatedCasinoTableData, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoTableData: ${insertedCasinoTableData}`)

        const insertedCasinoItemData = await CasinoItemModel.bulkCreate(updatedCasinoItemData, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoItemData: ${insertedCasinoItemData}`)

        // const insertedPageData = !updatedPageData.length && await PageModel.create(updatedPageData, { transaction: sequelizeTransaction })
        // const insertedPageDataFishing = !updatedPageDataFishing.length && await PageModel.create(updatedPageDataFishing, { transaction: sequelizeTransaction })

        Logger.info(`insertedPageData: ${insertedPageData}`)
        Logger.info(`insertedPageDataFishing: ${insertedPageDataFishing}`)

        const insertedCasinoMenuData = await CasinoMenuModel.bulkCreate(updatedCasinoMenuData, { transaction: sequelizeTransaction })

        Logger.info(`insertedCasinoMenuData: ${insertedCasinoMenuData}`)

        const pageId = pagesData.id
        const pageIdFishing = pageDataFishingData.id

        const casinoMenus = [
          ...casinoMenuData,
          ...insertedCasinoMenuData.map(item => item.dataValues)
        ]

        const pageMenuDataToBeInserted = funkyGameDataObject.category.map((item, idx) => {
          return {
            pageId,
            casinoMenuId: casinoMenus.find(itemm => itemm.name.toLowerCase() === item.toLowerCase()).id,
            name: `${item}`,
            menuOrder: null,
            createdAt: currentDate,
            updatedAt: currentDate
          }
        })
        const pageMenuDataToBeInsertedFishing = funkyGameDataFishingObject.category.map((item, idx) => {
          return {
            pageId: pageIdFishing,
            casinoMenuId: casinoMenus.find(itemm => itemm.name.toLowerCase() === item.toLowerCase()).id,
            name: `${item}`,
            menuOrder: null,
            createdAt: currentDate,
            updatedAt: currentDate
          }
        })

        const pageMenuDataCombined = await PageMenuModel.findAll({
          attributes: ['id', 'pageId', 'casinoMenuId', 'name'],
          where: {
            pageId: {
              [Op.in]: [pageId, pageIdFishing]
            },
            name: {
              [Op.in]: pageMenuDataToBeInserted.map(i => i.name)
            }

          },
          raw: true
        })

        const pageMenuData = pageMenuDataCombined.filter(i => i.pageId == pageId)
        const pageMenuDataFishing = pageMenuDataCombined.filter(i => i.pageId == pageIdFishing)

        const updatedCasinoPageMenuData = pageMenuDataToBeInserted.filter(i => !pageMenuData.map(i => i.name).includes(i.name))
        const updatedCasinoPageMenuDataFishing = pageMenuDataToBeInsertedFishing.filter(i => !pageMenuDataFishing.map(i => i.name).includes(i.name))

        const insertedCasinoPageMenuData = (updatedCasinoPageMenuData.length && await PageMenuModel.bulkCreate(updatedCasinoPageMenuData, { transaction: sequelizeTransaction })) || []
        const insertedCasinoPageMenuDataFishing = (updatedCasinoPageMenuDataFishing.length && await PageMenuModel.bulkCreate(updatedCasinoPageMenuDataFishing, { transaction: sequelizeTransaction })) || []

        const casinoItem = [
          ...casinoItemData,
          ...insertedCasinoItemData.map(item => item.dataValues)
        ]

        const pageMenu = [
          ...pageMenuData,
          ...insertedCasinoPageMenuData.map(item => item.dataValues)
        ]
        const pageMenuFishing = [
          ...pageMenuDataFishing,
          ...insertedCasinoPageMenuDataFishing.map(item => item.dataValues)
        ]

        const casinoGames = [
          ...casinoGamesData,
          ...insertedCasinoGamesData.map(item => item.dataValues)
        ]

          const menuItemsDataToBeInserted = casinoItem.map(item => {

            const fromgame = funkyGameDataObject.casinoTable.find((i) => (i.name === item.name || i.tableId === item.uuid))
            if (fromgame?.gameId == 'Fishing Games') {

              return null
            }
            if (!fromgame) {
              return null
            }

            const gameId = fromgame?.gameId
            const gameName = casinoGames.find(i => i.gameId === gameId)?.name
            const casinoMenuIds = casinoMenus.filter(i => i.name.toLowerCase() === gameName.toLowerCase())
            const mappingCasinoMenuId = casinoMenuIds.map(i => i.id)
            const pmId = casinoMenuIds.length ? pageMenu.find(i => mappingCasinoMenuId.includes(i.casinoMenuId))?.id : null
            if(!pmId) {
              return null
            }

            return {
              pageMenuId: pmId,
              casinoItemId: item.id,
              name: item.name,
              order: null,
              active: item.active,
              featured: true,
              createdAt: currentDate,
              updatedAt: currentDate,
              popular: true
            }
          }).filter(i => i !== null)

        const menuItemsDataToBeInsertedFishing = casinoItem.map(item => {
          const fromgame = funkyGameDataFishingObject.casinoTable.find((i) => (i.name === item.name || i.tableId === item.uuid))
          if (fromgame?.gameId !== 'Fishing Games') {
            return null
          }
          if (!fromgame) {
            return null
          }

          const gameId = fromgame?.gameId
          const gameName = casinoGames.find(i => i.gameId === gameId)?.name
          const casinoMenuId = casinoMenus.find(i => i.name.toLowerCase() === gameName.toLowerCase())?.id
          const pmId = pageMenuFishing.find(i => i.casinoMenuId === casinoMenuId).id
          return {
            pageMenuId: pmId,
            casinoItemId: item.id,
            name: item.name,
            order: null,
            active: item.active,
            featured: true,
            createdAt: currentDate,
            updatedAt: currentDate,
            popular: true
          }
        }
        ).filter(i => i !== null)



        Logger.info(`menuItemsDataToBeInserted: ${menuItemsDataToBeInserted}`)
        Logger.info(`menuItemsDataToBeInsertedFishing: ${menuItemsDataToBeInsertedFishing}`)

        const menuItemData = await MenuItemModel.findAll({
          attributes: ['id', 'pageMenuId', 'casinoItemId', 'name', 'active'],
          where: {
            casinoItemId: {
              [Op.in]: casinoItemData.map(i => i.id)
            }
          },
          raw: true
        })

        const updatedMenuItemsData = menuItemsDataToBeInserted.filter(i =>
          !menuItemData.some(ii => ii.casinoItemId === i.casinoItemId && ii.pageMenuId === i.pageMenuId)
        )
        const updatedMenuItemsDataFishing = menuItemsDataToBeInsertedFishing.filter(i =>
          !menuItemData.some(ii => ii.casinoItemId === i.casinoItemId && ii.pageMenuId === i.pageMenuId)
        )

        const menuItemsStatusUpdate = casinoItem.filter(item => {
          const existingItem = menuItemData.find(i => i.casinoItemId === item.id)
          return existingItem &&  existingItem.name !== item.name
        }).map(item => {
          return {
            ...item,
            name: item.name
          }
        })
        const menuItemsStatusUpdateFishing = casinoItem.filter(item => {
          const existingItem = menuItemData.find(i => i.casinoItemId === item.id)
          return existingItem && existingItem.name !== item.name
        }).map(item => {
          return {
            ...item,
            name: item.name
          }
        })

        for (const item of menuItemsStatusUpdate) {
          await MenuItemModel.update({ name: item.name }, {
            where: { casinoItemId: item.id },
            transaction: sequelizeTransaction
          })
        }

        for (const item of menuItemsStatusUpdateFishing) {
          await MenuItemModel.update({ name: item.name }, {
            where: { casinoItemId: item.id },
            transaction: sequelizeTransaction
          })
        }

        const insertedMenuItemData = (updatedMenuItemsData.length && await MenuItemModel.bulkCreate(updatedMenuItemsData, { transaction: sequelizeTransaction })) || []
        const insertedMenuItemDataFishing = (updatedMenuItemsDataFishing.length && await MenuItemModel.bulkCreate(updatedMenuItemsDataFishing, { transaction: sequelizeTransaction })) || []

        Logger.info(`insertedMenuItemData: ${insertedMenuItemData}`)

        const menuItemList = [
          ...menuItemData,
          ...insertedMenuItemData.map(item => item.dataValues),
          ...insertedMenuItemDataFishing.map(item => item.dataValues)
        ]

        let noLongerExists = []
        menuItemList.forEach(item => {
          const fromgame = casinoItem.find((i) => (i.id === item.casinoItemId))
          const findObj = funkyGameDataObject.casinoTable.find((i) => (i.name === fromgame.name || i.tableId === fromgame.uuid))

          const gameId = findObj?.gameId
          const gameName = casinoGames.find(i => i.gameId === gameId)?.name
          const casinoMenuIds = casinoMenus.filter(i => i.name.toLowerCase() === gameName.toLowerCase())
          const mappingCasinoMenuId = casinoMenuIds.map(i => i.id)
          let pmId
          if (findObj?.gameId !== 'Fishing Games') {
            pmId = mappingCasinoMenuId?.length ? pageMenu.find(i => mappingCasinoMenuId.includes(i.casinoMenuId))?.id : null
          } else {
            pmId = mappingCasinoMenuId?.length ? pageMenuFishing.find(i => mappingCasinoMenuId.includes(i.casinoMenuId))?.id : null
          }
          if (item.pageMenuId !== pmId && pmId) {
            const checkMenuItem = menuItemList.find(i => i.casinoItemId === item.casinoItemId && i.pageMenuId !== pmId)
            if (checkMenuItem) {
              noLongerExists.push(item)
            }
          }
        })

        if (noLongerExists.length) {
          let menuListToBeDeleted = await sequelize.query(`
          SELECT "MenuItem".id
          FROM "public"."menu_items" AS "MenuItem"
          JOIN public.page_menus on "MenuItem".page_menu_id = page_menus.id
          WHERE "MenuItem"."id" IN (:menuids) and page_id in (:pageIds)`, {
            replacements: {
              menuids: noLongerExists.map(item => item.id),
              pageIds: [pageId, pageIdFishing]
            },
            type: QueryTypes.SELECT,
            transaction: sequelizeTransaction
          })

          let allPageMenu = noLongerExists.map(item => item.pageMenuId)
          let allAvailablePageMenu = await PageMenuModel.findAll({
            attributes: ['id'],
            where: {
              id: {
                [Op.in]: allPageMenu
              }
            },
            raw: true
          })
          let allAvailablePageMenuIds = allAvailablePageMenu.map(item => item.id)
          let allPageMenuIds = allPageMenu.filter(item => !allAvailablePageMenuIds.includes(item))
          let allPageMenuIdsToBeDeleted = noLongerExists.filter(item => allPageMenuIds.includes(item.pageMenuId))
          menuListToBeDeleted.push(...allPageMenuIdsToBeDeleted)
          // only delete the menu items that are belonging to the current page
          if (menuListToBeDeleted.length) {
            await MenuItemModel.destroy({
              where: {
                id: {
                  [Op.in]: menuListToBeDeleted.map(item => item.id)
                },
                casinoItemId: {
                  [Op.in]: casinoItem.map(item => item.id)
                }

              },
              transaction: sequelizeTransaction
            })
          }
        }
        // disable the category if all games in that category are inactive
        const categoriesToDisable = await sequelize.query(
          ` UPDATE casino_menus cm
          SET enabled = subquery.result
          FROM (
            SELECT
              ct.game_id AS game_category,
              CASE
                WHEN COUNT(*) = COUNT(CASE WHEN ci.active = false THEN 1 END) THEN false
                ELSE true
              END AS result
            FROM
              casino_tables ct
            JOIN
              casino_items ci ON ct.table_id = ci.uuid
            WHERE
              ci.provider = '${casinoProviderId}'
              AND ci.tenant_id = '${tenantId}' AND ct.provider_id = '${casinoProviderId}'
            GROUP BY
              ct.game_id
          ) AS subquery
          WHERE cm.name = subquery.game_category AND cm.tenant_id = '${tenantId}';
        `, { transaction: sequelizeTransaction }
        )
        await sequelizeTransaction.commit()
        } catch(error){
          await sequelizeTransaction.rollback()
          cronLog.status = CRON_LOG_STATUS.FAILED
          cronLog.errorMsg = error.message + ' TenantId: ' + tenantId
          cronLog.endTime = new Date()
          await db.CronLog.create(cronLog)
          Logger.info(error, '========FUNKY game population error======')
          continue
        }
      }


  if (reqBody?.override_other_providers || reqBody?.sync) {
      await syncOrOverrideCasinoProvider(tenantIds,  casinoProviderId, 'Funky', reqBody?.override_other_providers, reqBody?.sync )
  }
    cronLog.endTime = new Date()
    cronLog.errorMsg = null
    cronLog.status = CRON_LOG_STATUS.SUCCESS
    await db.CronLog.create(cronLog)
    return {
      success: true
    }
  } catch (error) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = error.message || null
    cronLog.endTime = new Date()
    await db.CronLog.create(cronLog)
    Logger.info(error, '========FUNKY game population error======')
    return {
      success: false,
      Error: {
        stack: error.stack
      }
    }
  }
}
