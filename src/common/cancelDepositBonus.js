import { Op } from 'sequelize'
import { BONUS_CANCEL_TYPE, BONUS_COMMENT_ABBREVIATIONS, BONUS_STATUS, BONUS_TYPES, QUEUE_WORKER_CONSTANT } from '../common/constants'
import db from '../db/models'

export default async (sequelizeTransaction, userId, tenantId, type) => {

  let whereCondition = { userId, status: BONUS_STATUS.ACTIVE, kind: { [Op.or]: [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH] } }
  if (type === 'payin') {
    whereCondition.transactionId = { [Op.not]: null }
  }
  const userDepositBonus = await db.UserBonus.findOne({
    attributes: ['id', 'status', 'transactionId', 'kind', 'bonusId'],
    where: whereCondition,
    useMaster: true
  })
  if (!userDepositBonus) return

  const cancelBonusType = (await db.Bonus.findOne({
    attributes: ['bonusCancellationType'],
    where: {
      id: userDepositBonus.bonusId
    },
    raw: true
  }))?.bonusCancellationType

  if (cancelBonusType && (
    (type === 'payin' && (cancelBonusType === BONUS_CANCEL_TYPE.NONE || cancelBonusType === BONUS_CANCEL_TYPE.MULTIPLE_WITHDRAW)) ||
    (type === 'payout' && (cancelBonusType === BONUS_CANCEL_TYPE.NONE || cancelBonusType === BONUS_CANCEL_TYPE.MULTIPLE_DEPOSIT))
  )) return

  userDepositBonus.status = BONUS_STATUS.CANCELLED
  await userDepositBonus.save({ transaction: sequelizeTransaction })

  if (userDepositBonus.transactionId) {
    if (userDepositBonus.kind === BONUS_TYPES.DEPOSIT || userDepositBonus.kind === BONUS_TYPES.DEPOSIT_BOTH) {
      const transaction = await db.Transaction.findOne({
        attributes: ['id', 'comments', 'status'],
        where: {
          id: userDepositBonus.transactionId,
          tenantId
        },
        useMaster: true
      })
      if (transaction) {
        if (userDepositBonus.kind === BONUS_TYPES.DEPOSIT_BOTH) {
          transaction.comments = BONUS_COMMENT_ABBREVIATIONS.DBCFSC
        } else {
          transaction.comments = BONUS_COMMENT_ABBREVIATIONS.DBC
        }
        transaction.status = 'cancelled'
        await transaction.save({ transaction: sequelizeTransaction })
        const txnIds = []
        txnIds.push(transaction.id)
        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds
        }
        await db.QueueLog.create(queueLogObject, { transaction: sequelizeTransaction })
      }
    }
    if (userDepositBonus.kind === BONUS_TYPES.DEPOSIT_SPORTS) {
      const betsTransaction = await db.BetsTransaction.findOne({
        attributes: ['id', 'description', 'status'],
        where: {
          id: userDepositBonus.transactionId,
          tenantId
        },
        useMaster: true
      })
      let bulkData = []
      if (betsTransaction) {
        betsTransaction.description = 'Transaction cancelled for deposit bonus'
        betsTransaction.status = 'cancelled'
        await betsTransaction.save({ transaction: sequelizeTransaction })

        const betsTxn = {
          type: QUEUE_WORKER_CONSTANT.BET_TRANSACTION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [betsTransaction.id]
        }
        bulkData.push(betsTxn)
        const sportBonusTransaction = await db.Transaction.findOne({
          attributes: ['id', 'comments', 'status'],
          where: {
            debitTransactionId: betsTransaction.id,
            tenantId
          },
          useMaster: true
        })

        if (sportBonusTransaction) {
          sportBonusTransaction.comments = BONUS_COMMENT_ABBREVIATIONS.DBC
          sportBonusTransaction.status = 'cancelled'
          await sportBonusTransaction.save({ transaction: sequelizeTransaction })

          const sportsCasinoTxn = {
            type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
            status: QUEUE_WORKER_CONSTANT.READY,
            ids: [sportBonusTransaction.id]
          }
          bulkData.push(sportsCasinoTxn)
        }
        await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })
      }
    }
  }
}
