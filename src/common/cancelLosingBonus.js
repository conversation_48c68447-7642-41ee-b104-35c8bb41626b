import { BONUS_STATUS, BONUS_TYPES, ALLOWED_PARALLEL_BONUS, BONUS_CANCEL_TYPE } from '../common/constants'
import db from '../db/models'
import { Op } from 'sequelize'
export default async (sequelizeTransaction, userId, tenantId, type) => {

  const parallelBonus = await db.TenantThemeSetting.findOne({
    attributes: ['allowedModules'],
    where: {
      tenantId
    },
    raw: true
  })
  const hasParallelBonus = parallelBonus?.allowedModules
    ? parallelBonus.allowedModules.split(',').map(module => module.trim()).includes(ALLOWED_PARALLEL_BONUS)
    : false

  const userDepositBonus = await db.UserBonus.findOne({
    attributes: ['id', 'status', 'bonusId'],
    where: {
      userId,
      status: BONUS_STATUS.ACTIVE,
      kind: {
        [Op.in]: [BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_BOTH]
      }
    },
    useMaster: true
  })
  if (!userDepositBonus) return

  const cancelBonusType = (await db.Bonus.findOne({
    attributes: ['bonusCancellationType'],
    where: {
      id: userDepositBonus.bonusId
    },
    raw: true
  }))?.bonusCancellationType

  if (cancelBonusType && (
    (type === 'payin' && (cancelBonusType === BONUS_CANCEL_TYPE.NONE || cancelBonusType === BONUS_CANCEL_TYPE.MULTIPLE_WITHDRAW)) ||
    (type === 'payout' && (cancelBonusType === BONUS_CANCEL_TYPE.NONE || cancelBonusType === BONUS_CANCEL_TYPE.MULTIPLE_DEPOSIT))
  )) return

  if (!cancelBonusType && hasParallelBonus) return

  userDepositBonus.status = BONUS_STATUS.CANCELLED
  await userDepositBonus.save({ transaction: sequelizeTransaction })
}
