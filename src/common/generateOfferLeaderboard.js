// Generate random integer between min and max (min and max both inclusive)
const randomIntFromInterval = (min, max) => Math.floor(Math.random() * (max - min + 1) + min);

// Generate random float value between min and max (min and max both exclusive)
function getRandomFloatExclusive(min, max) {
  let result;
  let count = 0;
  do {
    result = parseFloat((Math.random() * (max - min) + min).toFixed(5));
    count += 1;
  } while ((result === min || result === max) && count < 500);

  return result;
}

// Shuffle an array
function shuffle(array) {
  let currentIndex = array.length;

  // While there remain elements to shuffle...
  while (currentIndex != 0) {

    // Pick a remaining element...
    let randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;

    // And swap it with the current element.
    [array[currentIndex], array[randomIndex]] = [
      array[randomIndex], array[currentIndex]];
  }
}

export default (realUsers, fakeUsers, minNoOfFakeUsers, noOfPrizes) => {
  // Convert winning amount to float
  realUsers = realUsers.map(u => ({ ...u, winningAmount: parseFloat(u.winningAmount) }));
  fakeUsers = fakeUsers.map(u => ({ ...u, winningAmount: parseFloat(u.winningAmount) }));

  const lastLeaderboardIdx = noOfPrizes - 1;

  // Mix all real and fake users and sort in descending order of winning amount
  const leaderboardUsersDescending = realUsers.concat(fakeUsers);
  leaderboardUsersDescending.sort((a, b) => b.winningAmount - a.winningAmount);

  // Generate random number to add number of fake users that will be there in leaderboard
  // If realUsers are less than no of prizes then increase fake users
  const noOfFakeUsersMustBeAdded = Math.min(Math.max(randomIntFromInterval(minNoOfFakeUsers, noOfPrizes), noOfPrizes - realUsers.length), fakeUsers.length);

  // Initial leaderboard
  let genLeaderboard = leaderboardUsersDescending.slice(0, noOfPrizes);

  // Find fake users ids added/not added in leaderboard and its count
  let fakeUsersIdsAdded = genLeaderboard.filter(u => u.isFake).map(u => u.id);
  let noOfFakeUsersAdded = fakeUsersIdsAdded.length;
  let fakeUsersIdsNotAdded = fakeUsers.filter(u => !fakeUsersIdsAdded.includes(u.id)).map(u => u.id);

  // Loop until minimum no of fake users are added in leaderboard
  // If there is no change in leaderboard then, at least run loop once to shuffle leaderboard.
  while (noOfFakeUsersAdded < noOfFakeUsersMustBeAdded) {
    // Shuffle an array of fake users ids
    shuffle(fakeUsersIdsNotAdded);

    // Randomly select any fake user from 'fakeUsersIdsNotAdded' array
    const fakeUserIdx = randomIntFromInterval(0, fakeUsersIdsNotAdded.length - 1);
    const selectedFakeUserId = fakeUsersIdsNotAdded[fakeUserIdx];
    const selectedFakeUser = fakeUsers.find(u => u.id === selectedFakeUserId);

    // Remove selected fake user from an array
    const fakeUserCurrentPos = leaderboardUsersDescending.findIndex(u => u.id === selectedFakeUser.id);
    leaderboardUsersDescending.splice(fakeUserCurrentPos, 1);

    /*
    // Find min and max index on which this fake user can be added in leaderboard
    const availStartIdx = 0;
    let availLastIdx = leaderboardUsersDescending.findIndex(u => u.winningAmount <= selectedFakeUser.winningAmount);

    if (availLastIdx === -1 || availLastIdx > lastLeaderboardIdx) {
      availLastIdx = lastLeaderboardIdx;
    }

    // Generate random number between available start and end index to place this fake user
    const generatedPosIdx = randomIntFromInterval(availStartIdx, availLastIdx);
    */

    // Generate random number on leaderboard to place this fake user
    const generatedPosIdx = randomIntFromInterval(0, lastLeaderboardIdx);

    // If generated index is 0, then set new winning amount for this fake user equals to 20%+ of first user's winning amount.
    // Else generate random winning amount between selected position's before and ahead users' winning amount.
    let fakeUserNewAmt = selectedFakeUser.winningAmount;
    if (generatedPosIdx === 0) {
      fakeUserNewAmt = parseFloat(((leaderboardUsersDescending[0].winningAmount) * (100 + 10) / 100).toFixed(5));
      // if (parseFloat(leaderboardUsersDescending[0].winningAmount) === 0) {
      //   fakeUserNewAmt = 20;
      // } else {
      //   fakeUserNewAmt = parseFloat(((leaderboardUsersDescending[0].winningAmount) * (100 + 20) / 100).toFixed(5));
      // }
    } else {
      fakeUserNewAmt = getRandomFloatExclusive(
        (leaderboardUsersDescending[generatedPosIdx - 1]).winningAmount,
        (leaderboardUsersDescending[generatedPosIdx]).winningAmount
      );
    }
    selectedFakeUser.winningAmount = fakeUserNewAmt;

    // Add fake user in new generated position
    leaderboardUsersDescending.splice(generatedPosIdx, 0, selectedFakeUser);

    // Check leaderboard and find how many fake users are added in it
    genLeaderboard = leaderboardUsersDescending.slice(0, noOfPrizes);
    fakeUsersIdsAdded = genLeaderboard.filter(u => u.isFake).map(u => u.id);
    noOfFakeUsersAdded = fakeUsersIdsAdded.length;
    fakeUsersIdsNotAdded = fakeUsers.filter(u => !fakeUsersIdsAdded.includes(u.id)).map(u => u.id);
  }

  // If fake users have 0 winning amount then increase.
  let fakeWinningAmount = 10;
  for (let i = genLeaderboard.length - 1; i >= 0; i--) {
    const currentUser = genLeaderboard[i];
    if (currentUser.isFake && currentUser.winningAmount === 0) {
      currentUser.winningAmount = fakeWinningAmount;
      fakeWinningAmount += 5;
    }
  }

  return genLeaderboard;
}
