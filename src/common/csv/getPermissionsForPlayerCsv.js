import { QueryTypes } from "sequelize";
import { sequelize } from "../../db/models";

export default async (attributePermissions, adminUserId, tenantId, parent_type) => {
  if (parent_type !== "SuperAdminUser" && parent_type !== "Manager") {
    const permissions = await getPermissionsForAdminUser(adminUserId, tenantId);

    const allowedAttributes = [...(permissions.players_key || []), ...(permissions.report_attributes || []), ...(permissions.financial_attributes || [])] || null;

    Object.keys(attributePermissions).forEach(attribute => {
      const permissionKey = attributePermissions[attribute];
      if (permissionKey !== null) {
        if (!allowedAttributes || !allowedAttributes.includes(permissionKey)) {
          delete attributePermissions[attribute];
        }
      }
    });
  }

  const finalAttributes = Object.keys(attributePermissions);
  return finalAttributes;
}

export async function getColumnPermissions(columnKeyPermissions, adminUserId, tenantId, parent_type) {
  if (parent_type !== "SuperAdminUser" && parent_type !== "Manager") {
    let finalAttributes = [];
    const permissionsObj = await getPermissionsForAdminUser(adminUserId, tenantId);

    if (!permissionsObj) {
      finalAttributes = Object.keys(columnKeyPermissions);
      return finalAttributes;
    }

    Object.entries(columnKeyPermissions).forEach(([columnKey, requiredPermission]) => {
      if (!requiredPermission || permissionsObj[requiredPermission[0]]?.includes(requiredPermission[1])) {
        finalAttributes.push(columnKey);
      }
    });
    return finalAttributes;
  }
  return Object.keys(columnKeyPermissions);
}

async function getPermissionsForAdminUser (adminUserId, tenantId) {
  const ownerRole = await sequelize.query(
    `SELECT admin_user_id FROM admin_users_admin_roles WHERE admin_user_id = :adminUserId AND admin_role_id = 1`,
    {
      replacements: { adminUserId },
      type: QueryTypes.SELECT
    }
  );

  if (ownerRole.length > 0) {
    const permissions = await sequelize.query(
      `SELECT permission FROM tenant_permissions WHERE tenant_id = :tenantId`,
      {
        replacements: { tenantId },
        type: QueryTypes.SELECT
      }
    );
    let beforeParse = permissions[0].permission;
    return permissions.length > 0 ? beforeParse : [];
  }

  const permissionRole = await sequelize.query(
    `SELECT permission_role_id FROM admin_users_permission_roles WHERE admin_user_id = :adminUserId`,
    {
      replacements: { adminUserId },
      type: QueryTypes.SELECT
    }
  );

  if (permissionRole.length > 0) {
    const permissions = await sequelize.query(
      `SELECT permission FROM permission_role WHERE id = :permissionRoleId`,
      {
        replacements: { permissionRoleId: permissionRole[0].permission_role_id },
        type: QueryTypes.SELECT
      }
    );

    return permissions.length > 0 ? permissions[0].permission : [];
  }

  return [];
}

export async function getParentAgent (idAgent) {
  const result = await sequelize.query(
    `SELECT parent_id FROM admin_users WHERE id = :idAgent`,
    {
      replacements: { idAgent },
      type: QueryTypes.SELECT
    }
  );

  return result.length > 0 ? result[0].parent_id : null;
}

export async function getRoleByAdminId (adminId) {
  const result = await sequelize.query(
    `SELECT admin_role_id FROM admin_users_admin_roles WHERE admin_user_id = :adminId LIMIT 1`,
    {
      replacements: { adminId },
      type: QueryTypes.SELECT
    }
  );
  return result[0];
}

export async function countTotalRecords (whereStr, tenant_id, botUserJoin='') {
  let sqlCheckV = `
      SELECT count(u.id)
      FROM users as u
      LEFT JOIN wallets as w on u.id = w.owner_id and w.owner_type = 'User'
      ${botUserJoin}
      ${whereStr ? `WHERE ${whereStr}` : ''}
  `;

  if (tenant_id != 0) {
    sqlCheckV += `${whereStr ? ' AND' : ' WHERE'} u.tenant_id = ${tenant_id}`;
  }
  const countTotalRecord = await sequelize.query(sqlCheckV, {
    type: QueryTypes.SELECT
  });

  return parseInt(countTotalRecord[0].count);
}
