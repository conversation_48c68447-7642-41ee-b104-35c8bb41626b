
export default  (searchKey) => {

let keyword = searchKey
  const result = {
  bool: {
    should: [
      {
        dis_max: {
          queries: [
            {
              bool: {
                must: {
                  bool: {
                    should: [
                      {
                        match: {
                          internal_tracking_id: {
                            query: keyword,
                            boost: 10,
                            operator: "and",
                            analyzer: "searchkick_word_search"
                          }
                        }
                      }
                    ]
                  }
                },
                should: {
                  match: {
                    internal_tracking_id: {
                      query: keyword,
                      boost: 10,
                      operator: "and",
                      analyzer: "searchkick_word_search"
                    }
                  }
                }
              }
            }
          ]
        }
      },
      {
        dis_max: {
          queries: [
            {
              bool: {
                must: {
                  bool: {
                    should: [
                      {
                        match: {
                          market_id: {
                            query: keyword,
                            boost: 10,
                            operator: "and",
                            analyzer: "searchkick_word_search"
                          }
                        }
                      }
                    ]
                  }
                },
                should: {
                  match: {
                    market_id: {
                      query: keyword,
                      boost: 10,
                      operator: "and",
                      analyzer: "searchkick_word_search"
                    }
                  }
                }
              }
            }
          ]
        }
      },
      {
        dis_max: {
          queries: [
            {
              bool: {
                must: {
                  bool: {
                    should: [
                      {
                        match: {
                          amount: {
                            query: keyword,
                            boost: 10,
                            operator: "and",
                            analyzer: "searchkick_word_search"
                          }
                        }
                      }
                    ]
                  }
                },
                should: {
                  match: {
                    amount: {
                      query: keyword,
                      boost: 10,
                      operator: "and",
                      analyzer: "searchkick_word_search"
                    }
                  }
                }
              }
            }
          ]
        }
      },
      {
        dis_max: {
          queries: [
            {
              bool: {
                must: {
                  bool: {
                    should: [
                      {
                        match: {
                          'player_details.player_id': {
                            query: keyword,
                            boost: 10,
                            operator: "and",
                            analyzer: "searchkick_word_search"
                          }
                        }
                      }
                    ]
                  }
                },
                should: {
                  match: {
                    'player_details.player_id': {
                      query: keyword,
                      boost: 10,
                      operator: "and",
                      analyzer: "searchkick_word_search"
                    }
                  }
                }
              }
            }
          ]
        }
      },
      {
        dis_max: {
          queries: [
            {
              bool: {
                must: {
                  bool: {
                    should: [
                      {
                        match: {
                          'player_details.phone': {
                            query: keyword,
                            boost: 10,
                            operator: "and",
                            analyzer: "searchkick_word_search"
                          }
                        }
                      }
                    ]
                  }
                },
                should: {
                  match: {
                    'player_details.phone': {
                      query: keyword,
                      boost: 10,
                      operator: "and",
                      analyzer: "searchkick_word_search"
                    }
                  }
                }
              }
            }
          ]
        }
      },
      {
        dis_max: {
          queries: [
            {
              bool: {
                must: {
                  bool: {
                    should: [
                      {
                        match: {
                          round_id: {
                            query: keyword,
                            boost: 10,
                            operator: "and",
                            analyzer: "searchkick_word_search"
                          }
                        }
                      }
                    ]
                  }
                },
                should: {
                  match: {
                    round_id: {
                      query: keyword,
                      boost: 10,
                      operator: "and",
                      analyzer: "searchkick_word_search"
                    }
                  }
                }
              }
            }
          ]
        }
      }
    ]
  }
}
return result
}
