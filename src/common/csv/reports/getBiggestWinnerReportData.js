import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3'
import { createObjectCsvStringifier } from 'csv-writer'
import { Readable } from 'stream'
import { v4 as uuidv4 } from 'uuid'
import { BOT_ALLOWED_TENANTS, CURRENCY_LIST_PROD, CURRENCY_LIST_STAGE, EXPORT_CSV_STATUS, NEW_NGR_FORMULA_TENANT_IDS } from '../../../common/constants'
import config from '../../../configs/app.config'
import db, { sequelize } from '../../../db/models'
import { s3 } from '../../../libs/awsS3ConfigV2'
import { CHECK_SBO_DOMAIN } from '../../../utils/constants/constant'
import { fetchAgentIds, formatAmount, getTenantBaseCurrency } from '../../helpers'
import getRolesDetails from '../getAdminRolesDetail'
import { getColumnPermissions } from '../getPermissionsForPlayerCsv'
import { getDateRange } from './getStartEndDates'

export default async (data) => {
  try {
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.IN_PROGRESS
    }, {
      where: { id: data.id }
    });

    const payload = data.payload;
    const checkSboRequest = payload.sbo_request;
    let isSuperAdmin = false;

    let currentUser = { id: data.adminId, parentType: data.adminType };

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      })
      payload.tenantId = adminData.tenantId
    }
    const tenantId = Number(payload.tenant_id || payload.tenantId)

    const tenantBaseCurrency = await getTenantBaseCurrency(currentUser, parseInt(tenantId))

    const tenantBaseCurrencyId = (await db.Currency.findOne({
      raw: true,
      attributes: ['id'],
      where: {
        code: tenantBaseCurrency
      }
    }))?.id

    const inputParams = {
      tenantIds: [],
      tenantBaseCurrencyId: tenantBaseCurrencyId,
      subAgentIds: [],
      sortBy: payload.sort_by || 'user_id',
      sortOrder: payload.order || 'desc',
      userId: payload.user_id || ''
    }

    // Tenant Filter Gathering
    if (tenantId) {
      inputParams.tenantIds = [tenantId]
    } else if (data.adminType == 'SuperAdminUser') {
      const adminData = await db.SuperAdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['parentType', 'tenantIds']
      })
      if (adminData.parentType === 'Manager') {
        inputParams.tenantIds = adminData.tenantIds.map(id => parseInt(id));
      }
    }

    // Agent Filter Gathering
    if (data.adminType == "AdminUser") {
      const currentUserRole = await getRolesDetails(data.adminId);
      if (currentUserRole.includes("agent")) {
        inputParams.subAgentIds = await fetchAgentIds(data.adminId);
      }
    }

    // Time Filter Gathering
    if (!payload.time_period) { payload.time_period = {} }
    if (!payload.time_type) { payload.time_type = 'yesterday' }

    const { start, end } = getDateRange(payload.time_type, 'UTC', payload.time_period.start_date, payload.time_period.end_date)

    inputParams.startDate = start.format('YYYY-MM-DD')
    inputParams.endDate = end.format('YYYY-MM-DD')

    const filterConditions = []

    // Check SBO User Logic
    isSuperAdmin = currentUser.parentType === 'SuperAdminUser'
    let botUsersFilterType = 'all'
    let botUserIds = []
    let addBotJoin = false
    if (
      (tenantId && BOT_ALLOWED_TENANTS.
        includes(String(tenantId))) ||
      isSuperAdmin
    ) {
      if (
        isSuperAdmin ||
        (checkSboRequest === CHECK_SBO_DOMAIN[1] && checkSboRequest !== CHECK_SBO_DOMAIN[2])
      ) {
        addBotJoin = true
        if (payload.playerType) {
          if (payload.playerType === 'bot_players') {
            botUsersFilterType = 'bot'
          } else if (payload.playerType === 'real_players') {
            botUsersFilterType = 'real'
          }
        }
      } else if (!checkSboRequest) {
        botUsersFilterType = 'real'
      }

      if (botUsersFilterType !== 'all') {
        // Find all bot users
        botUserIds = await sequelize.query(`
          SELECT
            user_id
          FROM
            bot_users
          ${tenantId ? 'WHERE tenant_id = :tenantId' : ''}
        `, {
          type: sequelize.QueryTypes.SELECT,
          replacements: { tenantId }
        })
        botUserIds = botUserIds.map(rec => rec.user_id)

        if (botUserIds.length > 0) {
          if (botUsersFilterType === 'bot') {
            filterConditions.push(`user_id IN (${botUserIds})`)
          } else if (botUsersFilterType === 'real') {
            filterConditions.push(`user_id NOT IN (${botUserIds})`)
          }
        }
      }
    }

    if (inputParams.startDate && inputParams.endDate) {
      filterConditions.push('ps.date BETWEEN :startDate AND :endDate')
    }

    if (inputParams.tenantIds.length > 0) {
      filterConditions.push('ps.tenant_id IN (:tenantIds)')
    }

    if (inputParams.userId && !isNaN(inputParams.userId)) {
      filterConditions.push('ps.user_id IN (:userId)')
    }

    filterConditions.push('ps.amount > 0')

    let calculationType = payload.calculation_based
    if (payload.calculation_based === 'rollover_winning') {
      filterConditions.push('ps.type = 20')
    } else if (payload.calculation_based === 'ggr') {
      filterConditions.push('ps.type IN (20, 29)')
    } else {
      if (NEW_NGR_FORMULA_TENANT_IDS.includes(String(tenantId))) {
        filterConditions.push('ps.type IN (29, 20, 35, 37)')
        calculationType = 'new_ngr'
      } else {
        filterConditions.push('ps.type IN (29, 20, 31)')
        calculationType = 'old_ngr'
      }
    }

    const filterClause = 'WHERE ' + filterConditions.join(' AND ')

    const isAgentLogin = inputParams.subAgentIds.length > 0

    // Sorting
    const sortCondition = 'ORDER BY ' + payload.sort_by + ' ' + payload.order

    // CSV column permissions
    const columnKetNameMap = {
      user_id: 'Player ID',
      user_name: 'Player Name',
      email: 'Email',
      phone: 'Phone Number',
      amount: 'Amount',
    };

    const columnKeyPermissions = {
      user_id: ["report_attributes", "player_id"],
      user_name: ["players_key", "user_name"],
      email: ["players_key", "email"],
      phone: ["players_key", "phone"],
      amount: null,
    };

    const allowedColumns = await getColumnPermissions(columnKeyPermissions, data.adminId, inputParams.tenantIds[0], data.adminType);
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    // S3 Upload
    let awsTenantFolderName = 0;
    if (inputParams.tenantIds.length <= 1) {
      awsTenantFolderName = inputParams.tenantIds[0];
    }

    const chunkSize = 5000
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3

    const uuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${awsTenantFolderName}/csv/unfied_trn/transaction_${uuid}.csv`

    const csvStringifier = createObjectCsvStringifier({
      header: allowedColumns.map(columnKey => ({ id: columnKey, title: columnKetNameMap[columnKey] }))
    })

    const allowedColumnsObj = {};
    allowedColumns.forEach(c => allowedColumnsObj[c] = true);

    // Get count
    const countQuery = prepareCountQuery({
      filterClause,
      isAgentLogin
    })
    const totalRecords = await sequelize.query(countQuery, { type: sequelize.QueryTypes.SELECT, useMaster: false, replacements: inputParams });
    const totalRecordsCount = totalRecords[0]?.total_count || 0
    const totalChunks = Math.ceil(totalRecordsCount / chunkSize)

    // Loop through each chunk
    for (let i = 0; i < totalChunks; i++) {
      const offset = i * chunkSize
      const paginationCondition = 'LIMIT ' + chunkSize + ' OFFSET ' + offset;

      const dataQuery = prepareDataQuery({
        filterClause,
        isAgentLogin,
        calculationType,
        addBotJoin,
        sortCondition,
        paginationCondition
      })
      const reportData = await sequelize.query(dataQuery, { type: sequelize.QueryTypes.SELECT, userMaster: false, replacements: inputParams });
      if (reportData) {
        await Promise.all(reportData.map(async (report) => {
          const currencyList = (config.get('env') === 'production') ? CURRENCY_LIST_PROD : CURRENCY_LIST_STAGE;
          const currencyEntry = currencyList.find(currency => currency.id === Number(report.currency_id));
          const currencyLabel = currencyEntry ? currencyEntry.value : '';
          report.amount = formatAmount(report.amount, tenantId, currencyLabel)
        }));
      }
      let mainArr = reportData;

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(mainArr)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }
      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      await delay(500)
    }
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )
    return true
  } catch (error) {
    // Update export status to failed
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.FAILED
    },
      {
        where: { id: data.id }
      }
    )
    throw error
  }
}

function prepareDataQuery ({
  filterClause,
  isAgentLogin,
  calculationType,
  addBotJoin,
  sortCondition,
  paginationCondition
}) {
  let query = ''

  const typeAmountSelectMap = {
    rollover_winning: 'SUM(pc.amount) AS amount',
    ggr: 'COALESCE(SUM(pc.amount) FILTER (WHERE type = 29), 0) - COALESCE(SUM(pc.amount) FILTER (WHERE type = 20), 0) AS amount',
    new_ngr: `COALESCE(SUM(pc.amount) FILTER (WHERE type = 29), 0)
            - COALESCE(SUM(pc.amount) FILTER (WHERE type = 20), 0)
            - COALESCE(SUM(pc.amount) FILTER (WHERE type = 37), 0)
            + COALESCE(SUM(pc.amount) FILTER (WHERE type = 35), 0) AS amount`,
    old_ngr: `COALESCE(SUM(pc.amount) FILTER (WHERE type = 29), 0)
            - COALESCE(SUM(pc.amount) FILTER (WHERE type = 20), 0)
            - COALESCE(SUM(pc.amount) FILTER (WHERE type = 31), 0) AS amount`
  }

  const botUsersJoin = addBotJoin ? 'LEFT JOIN bot_users bu ON (u.id = bu.user_id)' : ''
  const botUsersSelect = addBotJoin ? ', bu.id AS bot_user_id' : ''

  if (isAgentLogin) {
    query = `
      WITH agent_users AS (
        SELECT id FROM users WHERE parent_id IN (:subAgentIds)
      ),
      winners_data AS (
        SELECT
          ps.user_id,min(ps.currency_id)as currency_id,
          ${typeAmountSelectMap[calculationType]}
        FROM player_summary_provider_wise ps
          JOIN agent_users au ON (ps.user_id = au.id)
          JOIN player_provider_other_currency pc ON (ps.id = pc.player_summary_provider_id AND pc.currency_id = :tenantBaseCurrencyId)
        ${filterClause}
        GROUP BY
          ps.user_id
        ${sortCondition}
        ${paginationCondition}
      )
      SELECT
        wd.user_id,wd.,currency_id,
        wd.amount::NUMERIC(15,2) AS amount,
        u.user_name,
        u.email,
        u.phone
        ${botUsersSelect}
      FROM
        winners_data wd
        JOIN users u ON (u.id = wd.user_id)
        ${botUsersJoin}
      ${sortCondition}
    `
  } else {
    query = `
      WITH winners_data AS (
        SELECT
          ps.user_id,min(ps.currency_id)as currency_id,
          ${typeAmountSelectMap[calculationType]}
        FROM player_summary_provider_wise ps
          JOIN player_provider_other_currency pc ON (ps.id = pc.player_summary_provider_id AND pc.currency_id = :tenantBaseCurrencyId)
        ${filterClause}
        GROUP BY
          ps.user_id
        ${sortCondition}
        ${paginationCondition}
      )
      SELECT
        wd.user_id,wd.currency_id,
        wd.amount::NUMERIC(15,2) AS amount,
        u.user_name,
        u.email,
        u.phone
        ${botUsersSelect}
      FROM
        winners_data wd
        JOIN users u ON (u.id = wd.user_id)
        ${botUsersJoin}
      ${sortCondition}
    `
  }
  return query
}

function prepareCountQuery ({
  filterClause,
  isAgentLogin
}) {
  let query = ''

  if (isAgentLogin) {
    query = `
      WITH agent_users AS (
        SELECT id FROM users WHERE parent_id IN (:subAgentIds)
      )
      SELECT
        COUNT(DISTINCT user_id) AS total_count
      FROM player_summary_provider_wise ps
        JOIN agent_users au ON (ps.user_id = au.id)
      ${filterClause}
    `
  } else {
    query = `
      SELECT
        COUNT(DISTINCT user_id) AS total_count
      FROM player_summary_provider_wise ps
      ${filterClause}
    `
  }
  return query
}
