import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3'
import { createObjectCsvStringifier } from 'csv-writer'
import moment from 'moment'
import { Readable } from 'stream'
import { v4 as uuidv4 } from 'uuid'
import config from '../../../configs/app.config'
import db, { sequelize } from '../../../db/models'
import { s3 } from '../../../libs/awsS3ConfigV2'
import { EXPORT_CSV_STATUS } from '../../constants'
import { findAdminRole } from '../../findAdminRole'
import { expandBonusComment, fetchAgentIds, formatAmount, getCurrencyById, getDateInStringFormat } from '../../helpers'
import getActionType from './getActionType'

export default async (data) => {
  try {
    const payload = data.payload;
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.IN_PROGRESS
    },
      {
        where: { id: data.id }
      }
    )
    // Query replacements
    let inputParams = {
      tenantId: 0,
      managerTenantIds: [],
      subAgentIds: [],
      start: payload.datetime.startDate,
      end: payload.datetime.endDate,
      currencyId: payload.currencyId,
      search: payload.search,
      emailsearch: payload.emailsearch,
      actionType: payload.actionType,
      providerId: payload.providerId,
      marketName: payload.marketName,
    }

    let isSuperAdmin = false;
    if (payload.timeZoneName === 'UTC +00:00') {
      payload.timeZoneName = 'UTC'
    }

    // Tenant ID Gathering
    inputParams.tenantId = !isNaN(payload.tenantId) ? Number(payload.tenantId) : 0;

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      })
      inputParams.tenantId = adminData.tenantId;
    } else {
      const adminData = await db.SuperAdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['parentType', 'tenantIds']
      })
      if (adminData.parentType === 'Manager') {
        inputParams.managerTenantIds = adminData.tenantIds.map(id => parseInt(id));
      }
      isSuperAdmin = adminData.parentType === 'SuperAdminUser';
    }

    // Agent ID Gathering
    if (data.adminType == 'AdminUser') {
      const currentUserRole = await findAdminRole(undefined, data.adminId)
      if (currentUserRole.name === 'agent') {
        inputParams.subAgentIds = await fetchAgentIds(data.adminId)
      }
    }

    // Build filters
    const filters = [];
    if (inputParams.start && inputParams.end) filters.push(`bt.created_at BETWEEN :start AND :end`)
    if (inputParams.tenantId) filters.push(`bt.tenant_id = :tenantId`);
    if (inputParams.managerTenantIds.length > 0) filters.push(`bt.tenant_id IN (:managerTenantIds)`);
    if (inputParams.subAgentIds.length > 0) filters.push(`u.parent_id IN (:subAgentIds)`)

    if (inputParams.currencyId) filters.push(`c.id = :currencyId`);
    if (inputParams.search) filters.push(`(bt.transaction_id = :search OR bt.id::TEXT = :search OR bt.market_id = :search OR bt.amount = :search)`);
    if (inputParams.emailsearch) filters.push(`u.email ILIKE '%${inputParams.emailsearch}%'`);
    if (inputParams.actionType) filters.push(`bt.payment_for = :actionType`);
    if (inputParams.providerId) filters.push(`bt.provider_id = :providerId`);
    if (inputParams.marketName) filters.push(`bb.market ILIKE '%${inputParams.marketName}%'`);

    // Check SBO User Logic
    let botUserJoin = ''
    let botUserSelect = ''

    const checkSboRequest = payload.sbo_request;
    if (isSuperAdmin || checkSboRequest === true) {
      botUserSelect = ', bu.id as bot_user_id'
      botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
      if (payload.playerType === 'bot_players') {
        filters.push('bu.id IS NOT NULL')
      } else if (payload.playerType === 'real_players') {
        filters.push('bu.id IS NULL')
      }
    } else if (checkSboRequest === false) {
      botUserSelect = ', bu.id as bot_user_id'
      botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
      filters.push('bu.id IS NULL')
    }

    // Sorting
    let orderCondition = ''
    if (payload.sortBy && payload.order) {
      const columnMapping = {
        _id: 'id',
        id: 'id',
        internal_tracking_id: 'id',
        internalTrackingId: 'id',
        created_at: 'created_at',
        createdAt: 'created_at',
        market_id: 'market_id',
        marketId: 'market_id',
        runner_name: 'runner_name',
        runnerName: 'runner_name',
        journal_entry: 'journal_entry',
        journalEntry: 'journal_entry'
      }

      const sortColumn = columnMapping[payload.sortBy];
      if (sortColumn) {
        orderCondition = `ORDER BY ${sortColumn} ${payload.order}`
      }
    }

    const whereCondition = filters.length ? `WHERE ${filters.join(' AND ')}` : ''

    // AWS tenant folder name
    let awsTenantFolderName = inputParams.tenantId || 0;
    if (!awsTenantFolderName && inputParams.managerTenantIds.length === 1) awsTenantFolderName = inputParams.managerTenantIds[0];

    // new code
    const chunkSize = 5000
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3
    inputParams.limit = chunkSize
    inputParams.offset = 0
    const query = `
    SELECT
    bt.id, -- id
    bt.created_at::text, -- timestamp
    u.id AS user_id, -- player id
    bt.market_id, -- Market Id
    bt.runner_name, -- team name
    bt.transaction_id, -- bet id
    bt.amount, -- transaction amount
    bt.conversion_rate, -- Conversion rate
    bt.journal_entry, -- Journal entry
    bt.non_cash_amount, -- transaction amount
    bt.payment_for, -- Action Type
    bt.transaction_code, -- status
    bt.status, -- transaction status
    bt.description, -- comment
    bt.net_pl, -- commission details
    bt.commission_per, -- commission details
    bt.commission_amount, -- commission details
    bt.other_currency_amount AS amount_in_currencies,
    c.code AS currency, -- currency code,
    bt.source_currency_id,
    bt.target_currency_id,
    bt.conversion_rate,
    bt.betslip_id ${botUserSelect} ,
    CASE
    WHEN bsl.bettype IS NOT NULL THEN
      JSON_BUILD_OBJECT(
      'bettype', bsl.bettype,
      'betslip_id', bt.betslip_id
      )
    ELSE NULL
    END AS betslip_details, -- Conditional JSON object for betslip details
    JSON_BUILD_OBJECT(
    'first_name', u.first_name,
    'last_name', u.last_name,
    'email', u.email,
    'user_name', u.user_name,
    'id', u.id
    ) AS player_details,
    CASE
    WHEN bt.source_before_balance IS NOT NULL THEN
      JSON_BUILD_OBJECT(
      'before_balance', bt.source_before_balance,
      'after_balance', bt.source_after_balance,
      'non_cash_before_balance', bt.source_non_cash_before_balance,
      'non_cash_after_balance', bt.source_non_cash_after_balance
      )
    ELSE NULL
    END AS source_wallet,
    CASE
    WHEN bt.target_before_balance IS NOT NULL THEN
      JSON_BUILD_OBJECT(
      'before_balance', bt.target_before_balance,
      'after_balance', bt.target_after_balance,
      'non_cash_before_balance', bt.target_non_cash_before_balance,
      'non_cash_after_balance', bt.target_non_cash_after_balance
      )
    ELSE NULL
    END AS target_wallet,
    CASE
WHEN bsl.bettype IS NOT NULL THEN bb.market
ELSE NULL
END AS market
  FROM
    bets_transactions bt
  LEFT JOIN bets_betslip bsl ON bt.betslip_id = bsl.id
  LEFT JOIN (
    SELECT
        STRING_AGG(market, ',') AS market,
        min(bet_id) as bet_id
    FROM bets_bets
    GROUP BY bet_id
 ) AS bb ON  bb.bet_id =bt.transaction_id
  LEFT JOIN users u ON u.id = bt.user_id
  LEFT JOIN wallets w ON u.id = w.owner_id AND w.owner_type = 'User'
  LEFT JOIN currencies c ON c.id = w.currency_id
  ${botUserJoin}
  ${whereCondition}
  ${orderCondition}
  LIMIT :limit offset :offset
  `

    const uuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${awsTenantFolderName}/csv/bet_report/transaction_${uuid}.csv`
    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'id', title: 'ID' },
        { id: 'created_at', title: 'Created At' },
        { id: 'transaction_id', title: 'Transaction ID' },
        { id: 'market_id', title: 'Market ID'},
        { id: 'market', title: 'Market'},
        { id: 'team_name', title: 'Team Name'},
        { id: 'journal_entry', title: 'Journal Entry' },
        { id: 'wallet', title: 'Wallet' },
        { id: 'currency', title: 'Currency' },
        { id: 'from_before_balance', title: 'From Before Balance' },
        { id: 'from_after_balance', title: 'From After Balance' },
        { id: 'to_before_balance', title: 'To Before Balance' },
        { id: 'to_after_balance', title: 'To After Balance' },
        { id: 'amount', title: 'Amount' },
        { id: 'conversion_rate', title: 'Conversion rate' },
        { id: 'action_type', title: 'Action Type' },
        { id: 'comments', title: 'Comments' },
        { id: 'action_by', title: 'Action by' },
      ]
    })

    //count query
    const totalRecords = await sequelize.query(
      `SELECT
       COUNT(*) AS total
    FROM
      bets_transactions bt
    LEFT JOIN bets_betslip bsl ON bt.betslip_id = bsl.id
    LEFT JOIN (
      SELECT
          STRING_AGG(market, ',') AS market,
          min(bet_id) as bet_id
      FROM bets_bets
      GROUP BY bet_id
   ) AS bb ON  bb.bet_id =bt.transaction_id
    LEFT JOIN users u ON u.id = bt.user_id
    LEFT JOIN wallets w ON u.id = w.owner_id AND w.owner_type = 'User'
    LEFT JOIN currencies c ON c.id = w.currency_id
    ${botUserJoin}
    ${whereCondition}`,
      {
        replacements: inputParams,
        type: sequelize.QueryTypes.SELECT
      })

    const totalRecordsCount = totalRecords[0].total
    const totalChunks = Math.ceil(totalRecordsCount / chunkSize)
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
    // Loop through each chunk
    for (let i = 0; i < totalChunks; i++) {
      inputParams.offset = i * chunkSize
      // Fetch the data for the current chunk
      const reportData = await sequelize.query(query, {
        replacements: inputParams,
        type: sequelize.QueryTypes.SELECT
      })

      let mainArr = []
      if (reportData.length > 0) {
        reportData.forEach((report) => {
          if (payload.timeZoneName && report.created_at) {
            report.created_at = moment(report.created_at)
              .tz(payload.timeZoneName)
              .format('DD-MM-YYYY HH:mm:ss')
          }
          if (report.source_currency_id) {
            const id = Number(report.source_currency_id || 0)
            report.source_currency = getCurrencyById(id)?.value || null
          }
          if (report.target_currency_id) {
            const id = Number(report.target_currency_id || 0)
            report.target_currency = getCurrencyById(id)?.value || null
          }
        })

        for (const txn of reportData) {
          let playerId = txn.player_details.id;
          let playerEmail = txn.player_details.email;
          let playerFname = txn.player_details.first_name;
          let playerLname = txn.player_details.last_name;
          let walletName = playerEmail + ' ' + playerFname + ' ' + playerLname;

          let fromBeforeBalance = (txn.source_wallet?.before_balance + txn.source_wallet?.non_cash_before_balance) || '0.00';
          let fromAfterBalance = (txn.source_wallet?.after_balance + txn.source_wallet?.non_cash_after_balance) || '0.00';
          let toBeforeBalance = (txn.target_wallet?.before_balance + txn.target_wallet?.non_cash_before_balance) || '0.00';
          let toAfterBalance = (txn.target_wallet?.after_balance + txn.target_wallet?.non_cash_after_balance) || '0.00';
          let currency= txn.source_currency || txn.target_currency;

          let object = {
            id: txn.id,
            created_at: getDateInStringFormat(txn.created_at),
            transaction_id: txn.transaction_id ? txn.transaction_id : '',
            market_id: txn.market_id || '-',
            market: txn.market || '-',
            team_name: txn.runner_name || '-',
            journal_entry: txn.journal_entry || '-',
            wallet: walletName,
            currency: txn.source_currency || txn.target_currency,
            from_before_balance: formatAmount(fromBeforeBalance, inputParams.tenantId, currency),
            from_after_balance: formatAmount(fromAfterBalance, inputParams.tenantId, currency),
            to_before_balance: formatAmount(toBeforeBalance, inputParams.tenantId, currency),
            to_after_balance: formatAmount(toAfterBalance, inputParams.tenantId, currency),
            amount: formatAmount(txn.amount, inputParams.tenantId, currency) || '0.00',
            conversion_rate: txn.conversion_rate || '-',
            action_type: getActionType(txn.payment_for) || '-',
            comments:expandBonusComment(txn.description) || '-',
            action_by: playerId + '-' + playerEmail + ' ' + playerFname + ' ' + playerLname
          }
          mainArr = [...mainArr, object]
        }
      }

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(mainArr)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }

      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      delay(500)
    }

    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )
    return true
  } catch (error) {
    console.log("=======errorr", error)
    throw error
  }
}
