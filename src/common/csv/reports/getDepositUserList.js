import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import moment from 'moment';
import { QueryTypes } from 'sequelize';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { BANK_TYPE_MAP, CURRENCY_LIST_PROD, CURRENCY_LIST_STAGE, EXPORT_CSV_STATUS } from '../../constants';
import { formatAmount } from '../../helpers';

const CHUNK_SIZE = 1000;

const fillEmptyFields = (data) => {
  const fields = [
    'id', 'user_name', 'status', 'execution_timestamp', 'transaction_id', 'transaction_receipt', 'currency_conversion',
    'country_code', 'requestamount', 'amount', 'depositType',
    'remark', 'user_remark', 'agent_name', 'provider_name',
    'created_at', 'updated_at', 'bank_type', 'excuted_by'
  ];

  return data.map(record => {
    return fields.reduce((acc, field) => {
      acc[field] = record[field] || 'N/A';
      return acc;
    }, {});
  });
};

function getBankType(type) {
  return BANK_TYPE_MAP[type]
}


export default async (data) => {
  const payload = data.payload;


  await db.ExportCsvCenter.update(
    { status: EXPORT_CSV_STATUS.IN_PROGRESS },
    { where: { id: data.id } }
  );

  let params = {
    tenantId: 0,
    adminId: data.adminId,
    transactionId: payload.transaction_id,
    depositType: payload.deposit_type,
  };
  let isSuperAdmin = false;

  if (data.adminType === 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: { id: data.adminId },
      attributes: ['tenantId'],
    });
    params.tenantId = adminData.tenantId;
  }
  isSuperAdmin = data.adminType === 'SuperAdminUser';

  // Check SBO User Logic
  let botUserJoin = ''
  let botUserSelect = ''
  let whereStr = ''

  const checkSboRequest = payload.sbo_request;
  if (isSuperAdmin || checkSboRequest === true) {
    botUserSelect = ', bu.id as bot_user_id'
    botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = users.id'
    if (payload.playerCategory === 'bot_players') {
      whereStr += `${whereStr ? ' AND ' : ''} bu.id IS NOT NULL`
    } else if (payload.playerCategory === 'real_players') {
      whereStr += `${whereStr ? ' AND ' : ''} bu.id IS NULL`
    }
  } else if (checkSboRequest === false) {
    botUserSelect = ', bu.id as bot_user_id'
    botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = users.id'
    whereStr += `${whereStr ? ' AND ' : ''} bu.id IS NULL`
  }

  try {

    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'id', title: 'ID' },
        { id: 'user_name', title: 'Username' },
        { id: 'status', title: 'Status' },
        { id: 'transaction_id', title: 'UTR Number/Transaction Id' },
        { id: 'execution_timestamp', title: 'Execution Timestamp' },
        { id: 'transaction_receipt', title: 'Transaction Receipt' },
        { id: 'country_code', title: 'Country Code' },
        { id: 'requestamount', title: 'Requested Amount' },
        { id: 'amount', title: 'Credit Amount' },
        { id: 'currency_conversion', title: 'Exchange Rate' },

        { id: 'depositType', title: 'Deposit Type' },
        { id: 'bank_type', title: 'Bank Type' },
        { id: 'remark', title: 'Remark' },
        { id: 'user_remark', title: 'User Remark' },
        { id: 'agent_name', title: 'Agent Name' },
        { id: 'provider_name', title: 'Payment Gateway' },
        { id: 'excuted_by', title: 'Executed By' },
        { id: 'created_at', title: 'Requested At' },
        { id: 'updated_at', title: 'Actioned At' },
      ]
    })

    let offset = 0;
    let totalRecords = 0;
    let hasMoreRecords = true;
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3
    const csvUuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${params.tenantId}/csv/deposit_requests/deposit_${csvUuid}.csv`
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    while (hasMoreRecords) {

      let sql = `
    SELECT
    deposit_requests.*,
    CONCAT(admin_users.first_name,admin_users.last_name) AS excuted_by,
    wallets.id as wallet_id,
    wallets.currency_id,
    users.user_name,
    deposit_requests.country_code,
    payment_providers.provider_name,
    parent_agent.agent_name,
        COALESCE(deposit_requests.utr_number, deposit_requests.order_id) AS "transaction_id",
            COALESCE(deposit_requests.old_amount, deposit_requests.requested_amount, deposit_requests.amount) AS "requestamount",

              CASE
        WHEN deposit_requests.deposit_type = 'payment_providers' THEN 'paymentGateway'
        ELSE 'Manual'
    END AS "depositType" ${botUserSelect}


FROM
    deposit_requests
LEFT JOIN
    users ON users.id = deposit_requests.user_id AND users.demo = 'false'
LEFT JOIN
    admin_users ON admin_users.id = deposit_requests.action_id
LEFT JOIN
    admin_users AS parent_agent ON parent_agent.id = users.parent_id AND parent_agent.parent_type = 'AdminUser'
LEFT JOIN
    wallets ON wallets.owner_id = deposit_requests.user_id AND wallets.owner_type = 'User'
LEFT JOIN
    payment_providers ON payment_providers.id = deposit_requests.payment_provider_id
${botUserJoin}
        WHERE 1 = 1
      `;

      if(params.tenantId){
        sql += ` AND deposit_requests.tenant_id = ${params.tenantId}`
      }
      if (payload.id) {
        sql += ` AND deposit_requests.id = :id`;
      }
      if (payload.amount) {
        sql += ` AND deposit_requests.amount = :amount`;
      }
      if (payload.deposit_type) {
        sql += ` AND deposit_requests.deposit_type = :depositType`;
      }
      if (payload.utr_number ) {
        sql += ` AND deposit_requests.utr_number = :utr_number`;
      }
      if (payload.transaction_id ) {
        sql += ` AND deposit_requests.order_id = :transaction_id`;
      }
      if (payload.tenant_id ) {
        sql += ` AND deposit_requests.tenant_id = :tenant_id`;
      }
      if (payload?.time_period && payload?.time_period?.start_date && payload?.time_period?.end_date) {
        if (payload?.time_zone_name) {
          payload.time_period.start_date = moment.tz(payload.time_period.start_date, payload.time_zone_name).utc().format()
          payload.time_period.end_date = moment.tz(payload.time_period.end_date, payload.time_zone_name).utc().format()
        }

        sql += ` AND deposit_requests.created_at BETWEEN :startDate AND :endDate`;
      }
      if (payload.datetimeActionedAt && payload.datetimeActionedAt.start_date && payload.datetimeActionedAt.end_date) {
        sql += ` AND deposit_requests.updated_at BETWEEN :startDateActioned AND :endDateActioned`;
      }
      if (payload.user_remark) {
        sql += ` AND deposit_requests.user_remark = :user_remark`;
      }
      if (payload.user_id ) {
        sql += ` AND deposit_requests.user_id = :user_id`;
      }
      if (payload.payment_provider_id && payload.payment_provider_id.length > 0) {
        const paymentProviderIds = Array.isArray(payload.payment_provider_id)
            ? payload.payment_provider_id
            : payload.payment_provider_id.split(',');

        const formattedPaymentProviderIds = paymentProviderIds.map(id => `'${id.trim()}'`).join(',');

        sql += ` AND deposit_requests.payment_provider_id IN (${formattedPaymentProviderIds})`;
    }


      if (payload.country_code) {
        sql += ` AND deposit_requests.country_code = :countryCode`;
      }
      if (payload.vip_levels && payload.vip_levels.length > 0) {
        const vipLevels = Array.isArray(payload.vip_levels)
            ? payload.vip_levels
            : payload.vip_levels.split(',');

        const formattedVipLevels = vipLevels.map(level => `'${level.trim()}'`).join(',');

        sql += ` AND users.vip_level IN (${formattedVipLevels})`;
    }



      if (payload.status) {
        sql += ` AND deposit_requests.status = :status`;
      }
      if (payload.adminId) {
        sql += ` AND admin_users.id = :adminId`;
      }
      if (payload.manual_deposit_type) {
        sql += ` AND deposit_requests.manual_deposit_type = :manual_deposit_type`;
      }

      if (payload.type == 'pending' && payload.module == 'pending_deposit_requests') {
        sql += `
            AND (deposit_requests.verify_status IS NULL OR deposit_requests.verify_status = 'pending')
            AND (deposit_requests.status = 'opened' OR deposit_requests.status = 'in_process')
        `;
      }
      if (payload.type == 'verified' && payload.module == 'verified_deposit_requests') {
        sql += `
                 AND  (deposit_requests.verify_status = 'verified' OR (deposit_requests.deposit_type = 'payment_providers' AND deposit_requests.status = 'completed'))
      `;
      }
      if (payload.type == 'rejected' && payload.module == 'rejected_deposit_requests') {
        sql += `
 AND (deposit_requests.verify_status IS NULL OR deposit_requests.verify_status = 'rejected')
            AND (deposit_requests.status = 'cancelled' OR deposit_requests.status = 'failed' OR deposit_requests.status = 'rejected')
        `;
      }
      // bot user
      if(sql && botUserJoin && whereStr){
        sql += ` AND ${whereStr}`
      }

      sql += ` ORDER BY deposit_requests.id DESC LIMIT :limit OFFSET :offset`;

      const users = await sequelize.query(sql, {
        type: QueryTypes.SELECT,
        replacements: {
          id: payload.id || null,
          amount: payload.amount || null,
          status: payload.status || null,
          depositType: payload.deposit_type || null,
          manual_deposit_type: payload.manual_deposit_type || null,

          user_id: payload.user_id || null,
          adminId: payload.adminId || null,
          utr_number: payload.utr_number || null,
          transaction_id: payload.transaction_id || null,
          tenant_id: payload.tenant_id || null,
          user_remark: payload.user_remark || null,
          startDate: payload.time_period ? payload.time_period.start_date : null,
          endDate: payload.time_period ? payload.time_period.end_date : null,
          startDateActioned: payload.datetimeActionedAt ? payload.datetimeActionedAt.start_date : null,
          endDateActioned: payload.datetimeActionedAt ? payload.datetimeActionedAt.end_date : null,
          countryCode: payload.country_code || null,
          limit: CHUNK_SIZE,
          offset: offset,
        },
      });


      if (users.length < CHUNK_SIZE) {
        hasMoreRecords = false;
      }

      if (payload.time_zone_name) {
        users.forEach(async (user) => {
          const createdDate = payload.time_zone_name ? moment.tz(user.created_at, payload.time_zone_name) : moment(user.created_at);
          const updatedDate = payload.time_zone_name ? moment.tz(user.updated_at, payload.time_zone_name) : moment(user.updated_at);
          user.created_at = createdDate.format('DD-MM-YYYY HH:mm:ss');
          user.updated_at = updatedDate.format('DD-MM-YYYY HH:mm:ss');
          if(user.status != 'opened'){
            user.execution_timestamp = updatedDate.format('DD-MM-YYYY HH:mm:ss');
          }else{
            user.execution_timestamp = 'NA'
          }
          //Bank type
          user.bank_type = getBankType(user.manual_deposit_type)
          const currencyList = (config.get('env') === 'production') ? CURRENCY_LIST_PROD : CURRENCY_LIST_STAGE;
          const currencyEntry = currencyList.find(currency => currency.id === Number(user.currency_id));
          const currencyLabel = currencyEntry ? currencyEntry.value : '';
          user.requestamount= formatAmount(user.requestamount,params.tenantId,currencyLabel)
          user.amount= formatAmount(user.amount,params.tenantId,currencyLabel)
        });
      }

      // Fill empty fields with 'N/A'
      const cleanedUsers = fillEmptyFields(users);

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(cleanedUsers)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }
      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      totalRecords += users.length
      offset += CHUNK_SIZE
      await delay(500)
    }

    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    return {
      totalRecords,
      csvUrl: key,
    };
  } catch (error) {
    throw error;
  }
};
