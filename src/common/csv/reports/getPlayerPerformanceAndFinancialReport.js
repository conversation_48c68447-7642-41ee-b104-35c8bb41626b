import moment from 'moment-timezone'
import { v4 as uuidv4 } from 'uuid'
import config from '../../../configs/app.config'
import db, { sequelize } from '../../../db/models'
import { s3 } from '../../../libs/awsS3Config'
import { EXPORT_CSV_STATUS } from '../../constants'
import { fetchAgentIds, formatAmount, getDateInStringFormat, getPlayerType } from '../../helpers'
import getRolesDetails from '../getAdminRolesDetail'
import getPermissionsForPlayerCsv from '../getPermissionsForPlayerCsv'
import { getDateRange } from './getStartEndDates'

const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')

export default async (data) => {
  try {
    const payload = data.payload
    await db.ExportCsvCenter.update(
      {
        status: EXPORT_CSV_STATUS.IN_PROGRESS
      },
      {
        where: { id: data.id }
      }
    )

    const inputParams = {
      tenantIds: [],
      startDate: '',
      endDate: '',
      agentIds: [],
      search: payload.search,
      startDateTime: null,
      endDateTime: null,
    }

    const sort = payload.sort || 'user_id';
    const sortOrder = payload.sortOrder || 'asc';
    let isSuperAdmin = false;
    const checkSboRequest = payload.sbo_request

    // Tenant and Agent Filter Gathering
    const tenantId = Number(payload.tenantId);

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      })
      inputParams.tenantIds = [adminData.tenantId];
      const roles = await getRolesDetails(data.adminId);
      if (roles.includes('agent')) {
        inputParams.agentIds = await fetchAgentIds(data.adminId)
      }
    }

    if (data.adminType == 'SuperAdminUser') {
      if (tenantId) {
        inputParams.tenantIds = [tenantId];
      } else {
        const adminData = await db.SuperAdminUser.findOne({
          where: {
            id: data.adminId
          },
          attributes: ['parentType', 'tenantIds']
        })
        if (adminData.parentType === 'Manager') {
          inputParams.tenantIds = adminData.tenantIds.map(id => parseInt(id));
        }
      }
    }
    isSuperAdmin = data?.adminType === 'SuperAdminUser';

    // Time Filter Gathering
    if (payload.timeType !== 'lifetime') {
      if (payload.timeType === 'custom') {
        const { start, end } = getDateRange(payload.timeType, payload.time_zone_name, payload.startDate, payload.endDate);
        inputParams.startDate = payload.startDate;
        inputParams.endDate = payload.endDate;
        inputParams.startDateTime = start.toISOString()
        inputParams.endDateTime = end.endOf('day').toISOString()
      } else {
        const {start, end} = getDateRange(payload.timeType, payload.time_zone_name);
        inputParams.startDate = start.format('YYYY-MM-DD');
        inputParams.endDate = end.format('YYYY-MM-DD');
        inputParams.startDateTime = start.toISOString()
        inputParams.endDateTime = end.toISOString()
      }
      // Check if start date and end date difference is not more than 90 days
      if (moment(inputParams.endDate).diff(inputParams.startDate, 'days', true) > 90) {
        throw new Error('The selected time range cannot exceed 3 months.');
      }
    }

    // Filters
    const betWinFilters = [];
    const failedDepositFilters = [];
    const failedWithdrawFilters = [];
    const combinedCTEFilters = [];
    const lastLoginFilters = [];
    const userBankDetailsFilter = ["status = 'active'", 'is_deleted = false'];
    const betWinJoinAgentCondition = [];
    const failedFinancialJoinAgentCondition = [];

    if (inputParams.startDate && inputParams.endDate) {
      betWinFilters.push('date between :startDate and :endDate');
    }
    if (inputParams.startDateTime && inputParams.endDateTime) {
      failedDepositFilters.push('deposit_requests.updated_at between :startDateTime and :endDateTime')
      failedWithdrawFilters.push('withdraw_requests.updated_at between :startDateTime and :endDateTime')
    }
    if (inputParams.tenantIds.length > 0) {
      betWinFilters.push('player_summary_provider_wise.tenant_id in (:tenantIds)');
      lastLoginFilters.push('tenant_id in (:tenantIds)');
      userBankDetailsFilter.push('tenant_id in (:tenantIds)');
      failedDepositFilters.push('deposit_requests.tenant_id in (:tenantIds)')
      failedWithdrawFilters.push('withdraw_requests.tenant_id in (:tenantIds)')
    }
    if (inputParams.search) {
      betWinFilters.push('user_id = :search');
      lastLoginFilters.push('user_id = :search');
      userBankDetailsFilter.push('user_id = :search');
      failedDepositFilters.push('deposit_requests.user_id = :search');
      failedWithdrawFilters.push('withdraw_requests.user_id = :search');
    }
    betWinFilters.push('type in (19,25,29,30,32,33,20,26,23,24,7,1,6,10,9,11)');
    betWinFilters.push('amount > 0')
    failedDepositFilters.push("status in ('rejected', 'failed')")
    failedWithdrawFilters.push("status in ('rejected', 'rejected_by_gateway', 'cancelled')")

    // Check SBO User Logic
    let botUserReportQueryJoin = ''
    // payload.playerCategory = payload.playerCategory || payload.playerType
    payload.playerCategory = getPlayerType(payload)

    if (isSuperAdmin || checkSboRequest === true) {
      botUserReportQueryJoin = 'LEFT JOIN bot_users bu ON bu.user_id = coalesce(t1.user_id, t2.user_id)'
      if (payload.playerCategory === 'bot_players') {
        combinedCTEFilters.push('bu.id IS NOT NULL')
      } else if (payload.playerCategory === 'real_players') {
        combinedCTEFilters.push('bu.id IS NULL')
      }
    } else if (checkSboRequest === false) {
      botUserReportQueryJoin = 'LEFT JOIN bot_users bu ON bu.user_id = coalesce(t1.user_id, t2.user_id)'
      combinedCTEFilters.push('bu.id IS NULL')
    }

    const betWinWhereCondition = betWinFilters.length ? `where ${betWinFilters.join(' and ')}` : '';
    const combinedCTEWhereCondition = combinedCTEFilters.length ? `where ${combinedCTEFilters.join(' and ')}` : '';
    const failedDepositWhereCondition = failedDepositFilters.length ? `where ${failedDepositFilters.join(' and ')}` : '';
    const failedWithdrawWhereCondition = failedWithdrawFilters.length ? `where ${failedWithdrawFilters.join(' and ')}` : '';
    const lastLoginWhereCondition = lastLoginFilters.length ? `where ${lastLoginFilters.join(' and ')}` : '';
    const userBankDetailsWhereCondition = userBankDetailsFilter.length ? `where ${userBankDetailsFilter.join(' and ')}` : '';

    if (inputParams.agentIds.length > 0) {
      betWinJoinAgentCondition.push('join users on (users.id = player_summary_provider_wise.user_id and users.parent_id in (:agentIds))');
      failedFinancialJoinAgentCondition.push('join users on (users.id = user_id and users.parent_id in (:agentIds))');
    }

    // Sorting
    let sortCondition = '';
    let sortConditionWithAlias = '';
    if (sort && sortOrder) {
      sortCondition = 'order by ' + sort + ' ' + sortOrder;
      sortConditionWithAlias = 'order by c.' + sort + ' ' + sortOrder;
    }

    // Query preparation and fetch results
    const reportQuery = getPlayerPerformanceAndFinancialReportQuery({
      betWinWhereCondition,
      failedDepositWhereCondition,
      failedWithdrawWhereCondition,
      sortCondition,
      sortConditionWithAlias,
      betWinJoinCondition: betWinJoinAgentCondition,
      failedFinancialJoinAgentCondition,
      lastLoginWhereCondition,
      userBankDetailsWhereCondition,
      botUserJoin: botUserReportQueryJoin,
      combinedCTEWhereCondition,
      timezone:payload.time_zone_name
    });

    const reportData = await sequelize.query(reportQuery, { type: sequelize.QueryTypes.SELECT, useMaster: false, replacements: inputParams });

    // Create CSV
    const uuid = uuidv4().replace(/-/g, '');
    const filePath = `/tmp/player_performance_and_financial_${uuid}.csv`;

    const columnKetNameMap = {
      user_id: "Player Id",
      user_name: "User Name",
      nick_name: "Nick Name",
      first_name: "First Name",
      last_name: "Last Name",
      email: "Email",
      phone: "Phone",
      active: "Active",
      kyc_done: "KYC Done",
      player_category: "Player Category",
      registration_date: "Registration Date",
      bank_ifsc_code: "IFSC Code",
      account_number: "Account Number",
      last_login_date: "Last Login Date",
      last_login_ip: "Last Login IP",
      agent_email: "Agent Email",
      agent_phone: "Agent Phone",
      wallet_cash_balance: "Wallet Cash Balance",
      wallet_non_cash_balance: "Wallet Non Cash Balance",
      total_wallet_balance: "Total Wallet Balance",
      currency: "Currency",
      bet_count: "Bet Count",
      bet: "Bet",
      bet_refund_count: "Bet Refund Count",
      bet_refund: "Bet Refund",
      bet_after_refund_count: "Bet After Refund Count",
      bet_after_refund: "Bet After Refund",
      win_count: "Win Count",
      win: "Win",
      win_refund_count: "Win Refund Count",
      win_refund: "Win Refund",
      win_after_refund_count: "Win After Refund Count",
      win_after_refund: "Win After Refund",
      ggr: "GGR",
      automatic_deposit_count: "Automatic Deposit Count",
      automatic_deposit_amount: "Automatic Deposit Amount",
      manual_deposit_count: "Manual Deposit Count",
      manual_deposit_amount: "Manual Deposit Amount",
      total_deposit: "Total Deposit",
      failed_deposit_count: "Failed Deposit Count",
      automatic_withdraw_count: "Automatic Withdraw Count",
      automatic_withdraw_amount: "Automatic Withdraw Amount",
      manual_withdraw_count: "Manual Withdraw Count",
      manual_withdraw_amount: "Manual Withdraw Amount",
      total_withdrawal: "Total Withdrawal",
      failed_withdraw_count: "Failed Withdraw Count",
      claimed_bonus_amount: "Claimed Bonus Amount",
      withdraw_bonus_amount: "Withdraw Bonus Amount",
    };

    const columnKeyPermissions = {
      user_id: 'player_id',
      user_name: 'user_name',
      nick_name: 'nick_name',
      first_name: 'first_name',
      last_name: 'last_name',
      email: 'email',
      phone: 'phone',
      active: null,
      kyc_done: 'kyc_done',
      player_category: null,
      registration_date: 'player_registration_date',
      bank_ifsc_code: 'ifsc_code',
      account_number: 'account_number',
      last_login_date: 'last_login_date',
      last_login_ip: 'ip_address',
      agent_email: 'agent_details',
      agent_phone: 'agent_details',
      wallet_cash_balance: 'total_cash_balance',
      wallet_non_cash_balance: 'total_non_cash_balance',
      total_wallet_balance: 'total_wallet_balance',
      currency: 'currency',
      bet_count: 'total_bet',
      bet: 'total_bet_amount',
      bet_refund_count: 'bet_refund_count',
      bet_refund: 'bet_refund',
      bet_after_refund_count: 'bet_after_refund_count',
      bet_after_refund: 'bet_after_refund',
      win_after_refund: 'win_after_refund',
      win_count: 'total_win',
      win: 'win',
      win_refund_count: 'win_refund_count',
      win_refund: 'win_refund',
      win_after_refund_count: 'win_after_refund_count',
      ggr: 'ggr',
      automatic_deposit_count: 'automatic_deposit_count',
      automatic_deposit_amount: 'automatic_deposit_amount',
      manual_deposit_count: 'manual_deposit_count',
      manual_deposit_amount: 'manual_deposit_amount',
      total_deposit: 'total_deposit',
      failed_deposit_count: 'failed_deposit_count',
      automatic_withdraw_count: 'automatic_withdraw_count',
      automatic_withdraw_amount: 'automatic_withdraw_amount',
      manual_withdraw_count: 'manual_withdraw_count',
      manual_withdraw_amount: 'manual_withdraw_amount',
      total_withdrawal: 'total_withdrawals',
      failed_withdraw_count: 'failed_withdraw_count',
      claimed_bonus_amount: "bonus_amount",
      withdraw_bonus_amount: "bonus_amount",
    };

    const allowedColumns = await getPermissionsForPlayerCsv(columnKeyPermissions, data.adminId, inputParams.tenantIds[0], data.adminType)

    const allowedCsvHeaders = allowedColumns.map(columnKey => ({ id: columnKey, title: columnKetNameMap[columnKey] }));

    const csvWriter = createCsvWriter({
      path: filePath,
      header: allowedCsvHeaders
    });
    const csvData = reportData;

    const allowedColumnsObj = {};
    allowedColumns.forEach(c => allowedColumnsObj[c] = true);

    let mainArr = []
    if (csvData.length > 0) {
      for (const txn of csvData) {
        let object = {
          ...(allowedColumnsObj.user_id && { user_id: txn.user_id }),
          ...(allowedColumnsObj.user_name && { user_name: txn.user_name }),
          ...(allowedColumnsObj.nick_name && { nick_name: txn.nick_name }),
          ...(allowedColumnsObj.first_name && { first_name: txn.first_name }),
          ...(allowedColumnsObj.last_name && { last_name: txn.last_name }),
          ...(allowedColumnsObj.email && { email: txn.email }),
          ...(allowedColumnsObj.phone && { phone: txn.phone }),
          active: txn.active,
          ...(allowedColumnsObj.kyc_done && { kyc_done: txn.kyc_done }),
          player_category: txn.player_category,
          // ...(allowedColumnsObj.registration_date && { registration_date: moment(txn.registration_date).toISOString() }),
          ...(allowedColumnsObj.registration_date && { registration_date: getDateInStringFormat(txn.registration_date) }),
          ...(allowedColumnsObj.bank_ifsc_code && { bank_ifsc_code: txn.bank_ifsc_code }),
          ...(allowedColumnsObj.account_number && { account_number: txn.account_number }),
          // ...(allowedColumnsObj.last_login_date && { last_login_date: moment(txn.last_login_date).toISOString() }),
          ...(allowedColumnsObj.last_login_date && { last_login_date: getDateInStringFormat(txn.last_login_date) }),
          ...(allowedColumnsObj.last_login_ip && { last_login_ip: txn.last_login_ip }),
          ...(allowedColumnsObj.agent_email && { agent_email: txn.agent_email }),
          ...(allowedColumnsObj.agent_phone && { agent_phone: txn.agent_phone }),
          ...(allowedColumnsObj.wallet_cash_balance && { wallet_cash_balance: formatAmount(txn.wallet_cash_balance,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.wallet_non_cash_balance && { wallet_non_cash_balance: formatAmount(txn.wallet_non_cash_balance,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.total_wallet_balance && { total_wallet_balance: formatAmount(txn.total_wallet_balance,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.currency && { currency: txn.currency }),
          ...(allowedColumnsObj.bet_count && { bet_count: txn.bet_count }),
          ...(allowedColumnsObj.bet && { bet: formatAmount(txn.bet,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.bet_refund_count && { bet_refund_count: txn.bet_refund_count }),
          ...(allowedColumnsObj.bet_refund && { bet_refund: formatAmount(txn.bet_refund,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.bet_after_refund_count && { bet_after_refund_count: txn.bet_after_refund_count }),
          ...(allowedColumnsObj.bet_after_refund && { bet_after_refund: formatAmount(txn.bet_after_refund,inputParams.tenantIds,txn.currency)}),
          ...(allowedColumnsObj.win_after_refund && { win_after_refund: formatAmount(txn.win_after_refund,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.win_count && { win_count: txn.win_count }),
          ...(allowedColumnsObj.win && { win: formatAmount(txn.win,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.win_refund_count && { win_refund_count: txn.win_refund_count }),
          ...(allowedColumnsObj.win_refund && { win_refund: formatAmount(txn.win_refund,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.win_after_refund_count && { win_after_refund_count: txn.win_after_refund_count }),
          ...(allowedColumnsObj.ggr && { ggr: formatAmount(txn.ggr,inputParams.tenantIds,txn.currency)}),
          ...(allowedColumnsObj.automatic_deposit_count && { automatic_deposit_count: txn.automatic_deposit_count }),
          ...(allowedColumnsObj.automatic_deposit_amount && { automatic_deposit_amount: formatAmount(txn.automatic_deposit_amount,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.manual_deposit_count && { manual_deposit_count: txn.manual_deposit_count }),
          ...(allowedColumnsObj.manual_deposit_amount && { manual_deposit_amount: formatAmount(txn.manual_deposit_amount,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.total_deposit && { total_deposit: formatAmount(txn.total_deposit,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.failed_deposit_count && { failed_deposit_count: txn.failed_deposit_count }),
          ...(allowedColumnsObj.automatic_withdraw_count && { automatic_withdraw_count: txn.automatic_withdraw_count }),
          ...(allowedColumnsObj.automatic_withdraw_amount && { automatic_withdraw_amount: formatAmount(txn.automatic_withdraw_amount,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.manual_withdraw_count && { manual_withdraw_count: txn.manual_withdraw_count }),
          ...(allowedColumnsObj.manual_withdraw_amount && { manual_withdraw_amount: formatAmount(txn.manual_withdraw_amount,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.total_withdrawal && { total_withdrawal: formatAmount(txn.total_withdrawal,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.failed_withdraw_count && { failed_withdraw_count: txn.failed_withdraw_count }),
          ...(allowedColumnsObj.claimed_bonus_amount && { claimed_bonus_amount: formatAmount(txn.claimed_bonus_amount,inputParams.tenantIds,txn.currency) }),
          ...(allowedColumnsObj.withdraw_bonus_amount && { withdraw_bonus_amount: formatAmount(txn.withdraw_bonus_amount,inputParams.tenantIds,txn.currency) }),
        };

        mainArr = [...mainArr, object]
      }
    }
    await Promise.all([csvWriter.writeRecords(mainArr)])

    // S3 Upload
    let awsTenantFolderName = tenantId;
    if (data.adminType == 'SuperAdminUser' && inputParams.tenantIds.length > 1) {
      awsTenantFolderName = inputParams.tenantIds[0];
    }

    const s3Config = config.getProperties().s3
    const fileContent = await fs.promises.readFile(filePath);
    const key = `tenants/${awsTenantFolderName}/csv/player/player_performance_and_financial_${uuid}.csv`

    const s3Params = {
      ACL: 'public-read',
      Bucket: s3Config.bucket,
      Key: key,
      Body: fileContent
    }
    const uploadedFile = await s3.upload(s3Params).promise()
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    fs.unlink(filePath, (err) => {
      if (err) {
        throw err
      }
    });

  } catch(e) {
    console.log("Player_performance_and_financial_report_error:", e)
    throw e
  }
}

function getPlayerPerformanceAndFinancialReportQuery({
  betWinWhereCondition,
  failedDepositWhereCondition,
  failedWithdrawWhereCondition,
  sortCondition,
  sortConditionWithAlias,
  betWinJoinCondition,
  failedFinancialJoinAgentCondition,
  lastLoginWhereCondition,
  userBankDetailsWhereCondition,
  botUserJoin,
  combinedCTEWhereCondition,
  timezone
}) {
  return `
    with bet_win_data as (
      select
        user_id,
        sum(amount) filter(where type = 19) as bet,
        sum(amount) filter(where type = 25) as bet_count,
        sum(amount) filter(where type = 29) as bet_after_refund,
        sum(amount) filter(where type = 30) as bet_after_refund_count,
        sum(amount) filter(where type = 32) as win,
        sum(amount) filter(where type = 33) as win_count,
        sum(amount) filter(where type = 20) as win_after_refund,
        sum(amount) filter(where type = 26) as win_after_refund_count,
        sum(amount) filter(where type = 19) - sum(amount) filter(where type = 29) as bet_refund,
        sum(amount) filter(where type = 25) - sum(amount) filter(where type = 30) as bet_refund_count,
        sum(amount) filter(where type = 32) - sum(amount) filter(where type = 20) as win_refund,
        sum(amount) filter(where type = 33) - sum(amount) filter(where type = 26) as win_refund_count,
        sum(amount) filter(where type = 23) as claimed_bonus_amount,
        sum(amount) filter(where type = 24) as withdraw_bonus_amount,
        sum(amount) filter(where type = 7) as automatic_deposit_amount,
        sum(amount) filter(where type IN (1, 6)) as manual_deposit_amount,
        sum(amount) filter(where type = 10) as automatic_withdraw_amount,
        sum(amount) filter(where type IN (9, 11)) as manual_withdraw_amount,
        sum(tx_count) filter(where type = 7) as automatic_deposit_count,
        sum(tx_count) filter(where type IN (1, 6)) as manual_deposit_count,
        sum(tx_count) filter(where type = 10) as automatic_withdraw_count,
        sum(tx_count) filter(where type IN (9, 11)) as manual_withdraw_count
      from
        player_summary_provider_wise
        ${betWinJoinCondition}
      ${betWinWhereCondition}
      group by user_id
    ),
    failed_data as (
      with failed_deposit_data as (
        select
          user_id,
          count(*) AS failed_deposit_count
        from
          deposit_requests
          ${failedFinancialJoinAgentCondition}
        ${failedDepositWhereCondition}
        group by
          user_id
      ),
      failed_withdraw_data as (
        select
          user_id,
          count(*) AS failed_withdraw_count
        from
          withdraw_requests
          ${failedFinancialJoinAgentCondition}
        ${failedWithdrawWhereCondition}
        group by
          user_id
      )
      select
        coalesce(fd.user_id, fw.user_id) as user_id,
        coalesce(fd.failed_deposit_count, 0) as failed_deposit_count,
        coalesce(fw.failed_withdraw_count, 0) as failed_withdraw_count
      from
        failed_deposit_data fd
        full outer join failed_withdraw_data fw on (fd.user_id = fw.user_id)
    ),
    combined_data as (
      select
        coalesce(t1.user_id, t2.user_id) as user_id,
        coalesce(t1.bet, 0) as bet,
        coalesce(t1.bet_count, 0) as bet_count,
        coalesce(t1.bet_after_refund, 0) as bet_after_refund,
        coalesce(t1.bet_after_refund_count, 0) as bet_after_refund_count,
        coalesce(t1.win, 0) as win,
        coalesce(t1.win_count, 0) as win_count,
        coalesce(t1.win_after_refund, 0) as win_after_refund,
        coalesce(t1.win_after_refund_count, 0) as win_after_refund_count,
        coalesce(t1.bet_refund, 0) as bet_refund,
        coalesce(t1.bet_refund_count, 0) as bet_refund_count,
        coalesce(t1.win_refund, 0) as win_refund,
        coalesce(t1.win_refund_count, 0) as win_refund_count,
        coalesce(t1.automatic_deposit_amount, 0) as automatic_deposit_amount,
        coalesce(t1.manual_deposit_amount, 0) as manual_deposit_amount,
        coalesce(t1.automatic_deposit_count, 0) as automatic_deposit_count,
        coalesce(t1.manual_deposit_count, 0) as manual_deposit_count,
        coalesce(t1.automatic_withdraw_amount, 0) as automatic_withdraw_amount,
        coalesce(t1.manual_withdraw_amount, 0) as manual_withdraw_amount,
        coalesce(t1.automatic_withdraw_count, 0) as automatic_withdraw_count,
        coalesce(t1.manual_withdraw_count, 0) as manual_withdraw_count,
        coalesce(t2.failed_deposit_count, 0) as failed_deposit_count,
        coalesce(t2.failed_withdraw_count, 0) as failed_withdraw_count,
        coalesce(t1.claimed_bonus_amount, 0) as claimed_bonus_amount,
        coalesce(t1.withdraw_bonus_amount, 0) as withdraw_bonus_amount
      from
        bet_win_data t1
        full outer join failed_data t2 on (t1.user_id = t2.user_id)
        ${botUserJoin}
      ${combinedCTEWhereCondition}
      ${sortCondition}
    ),
    last_login_data as (
      select * from (
        select
          user_id,
          last_login_date,
          ip as last_login_ip,
          row_number() over (partition by user_id order by last_login_date desc) as rn
        from user_login_history
        ${lastLoginWhereCondition}
      ) q1 where rn = 1
    ),
    user_first_bank_details as (
      select user_id, bank_ifsc_code, account_number from (
        select user_id, bank_ifsc_code, account_number, row_number() over(partition by user_id) as rn
        from user_bank_details ub
        ${userBankDetailsWhereCondition}
      ) s1 where rn = 1
    )
    select
      c.*,
      (c.automatic_deposit_amount + c.manual_deposit_amount) as total_deposit,
      (c.automatic_withdraw_amount + c.manual_withdraw_amount) as total_withdrawal,
      (c.bet_after_refund - c.win_after_refund) as ggr,
      u.user_name,
      u.nick_name,
      u.first_name,
      u.last_name,
      u.email,
      u.phone,
      u.active,
      u.kyc_done,
      case when u.category_type = 1 then 'A'
        when u.category_type = 2 then 'B'
        when u.category_type = 3 then 'C'
        when u.category_type = 4 then 'D'
        else u.category_type::text
      end as player_category,
      TO_CHAR(u.created_at AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}', 'DD-MM-YYYY HH24:MI:SS') AS registration_date,
      adu.email as agent_email,
      adu.phone as agent_phone,
      TO_CHAR(l.last_login_date AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}', 'DD-MM-YYYY HH24:MI:SS') AS last_login_date,
      l.last_login_ip,
      w.amount::numeric(20,5) as wallet_cash_balance,
      w.non_cash_amount::numeric(20,5) as wallet_non_cash_balance,
      cr.code as currency,
      (w.amount + w.non_cash_amount)::numeric(20,5) as total_wallet_balance,
      ub.bank_ifsc_code,
      ub.account_number
    from
      combined_data c
      join users u on (u.id = c.user_id)
      left join admin_users adu on (adu.id = u.parent_id)
      join wallets w on (w.owner_id = c.user_id and w.owner_type = 'User')
      left join last_login_data l on (l.user_id = c.user_id)
      join currencies cr on (cr.id = w.currency_id)
      left join user_first_bank_details ub on (ub.user_id = u.id)
    ${sortConditionWithAlias}
  `;
}
