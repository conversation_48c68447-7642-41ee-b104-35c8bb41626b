import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { EXPORT_CSV_STATUS } from '../../../common/constants'
import config from '../../../configs/app.config'
import db, { sequelize } from '../../../db/models'
import { s3 } from '../../../libs/awsS3Config'
import getESclient from '../../../libs/getEsClient'
import getRolesDetails from '../getAdminRolesDetail'
import checkIfEmpty from './checkIfEmpty'
import getActionType from './getActionType'
import { getDateInStringFormat } from '../../helpers'
const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')


export default async (data) => {
  let esClient = getESclient();
  const payload = data.payload
  const isProduction = process.env.NODE_ENV === 'production'
  await db.ExportCsvCenter.update({
    status: EXPORT_CSV_STATUS.IN_PROGRESS
  },
    {
      where: { id: data.id }
    }
  )
  let filter = []
  let mustArray = []
  let query
  let params = {
    tenantId: 0,
    agentId: 0
  }

  // ====================filters here ========================

  if (data.adminType == 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: {
        id: data.adminId
      },
      attributes: ['tenantId']
    }
    )
    params.tenantId = adminData.tenantId
    const roles = await getRolesDetails(data.adminId)
    if (!roles.includes('owner')) {
      //condition for new sub-admin role
      if (roles.includes('owner') || roles.includes('sub-admin')) {
        params.agentId = payload?.agent_id ? payload.agent_id : null
      } else {
        params.agentId = payload?.agent_id ? payload.agent_id : data.adminId
      }
    } else {
      params.agentId = payload?.agent_id ? payload.agent_id : null
    }

  } else {
    params.tenantId = payload?.tenant_id ? payload.tenant_id : null
    params.agentId = payload?.agent_id ? payload.agent_id : null
  }

  if (payload.start_date && payload.end_date) {
    if (payload.time_zone_name) {
      payload.start_date = moment.tz(payload.start_date, payload.time_zone_name).utc().format()
      payload.end_date = moment.tz(payload.end_date, payload.time_zone_name).utc().format()
    }

    let endDate = payload.end_date
    filter = [...filter, {
      range: {
        created_at: {
          from: payload.start_date,
          include_lower: true,
          to: endDate.replace("000000", "999999"),
          include_upper: true
        }
      }
    }]
  }

  if (payload.start_date_schedule_at && payload.end_date_schedule_at) {
    if (payload.time_zone_name) {
      payload.start_date_schedule_at = moment.tz(payload.start_date_schedule_at, payload.time_zone_name).utc().format()
      payload.end_date_schedule_at = moment.tz(payload.end_date_schedule_at, payload.time_zone_name).utc().format()
    }

    let endDate = payload.end_date_schedule_at
    filter = [...filter, {
      range: {
        'betslip_details.bets.start_date': {
          from: payload.start_date_schedule_at,
          include_lower: true,
          to: endDate.replace("000000", "999999"),
          include_upper: true
        }
      }
    }]
  }

  if (payload.action_type.length > 0) {
    filter = [...filter, { terms: { payment_for: payload.action_type } }]
  }

  if (params.tenantId) {
    filter = [...filter, { term: { tenant_id: params.tenantId } }]
  }

  if (payload.journal_entry) {
    filter = [...filter, { term: { journal_entry: payload.journal_entry } }]
  }

  if (payload.user_id) {
    filter = [...filter, { term: { user_id: payload.user_id } }]
  }

  if (payload.search_id) {
    filter = [...filter, { term: { internal_tracking_id: payload.search_id } }]
  }

  if (payload.bet_type !== '') {
    if (payload.bet_type === 'not_combo') {
        filter.push({ term: { "betslip_details.bettype": 1 } });
    } else if (payload.bet_type === 'multibets') {
        filter.push({ term: { "betslip_details.bettype": 2 } });
    }
    else if (payload.bet_type == 'lay_back') {
      filter.push({
        bool: {
          must: {
            terms: { "betslip_details.bettype": [1, 2] }
          }
        }
      });
    }
    else if (payload.bet_type == 'settled_or_bonus') {
      filter.push({ "bool": { "must_not": { "exists": { "field": "betslip_details" } } } })
    } else {
      filter.push({
        bool: {
          must_not: {
            terms: { "betslip_details.bettype": [1, 2] }
          }
        }
      });
    }
  }

  if (payload.search_market_id != '') {
    filter = [...filter, { term: { market_id: payload.search_market_id } }]
  }

  if (payload.search_runner_name != '') {
    filter = [...filter, { term: { "runner_name" : { value: payload.search_runner_name }} }];
  }


  if (payload.status != '') {
    if(payload.status == 'SettledMarket'){
      filter.push({"bool":{"should":[{"term":{"betslip_details.settlement_status": payload.status }},{"term":{"payment_for":2}}]}})
    }else{
        filter = [...filter, { term: { "betslip_details.settlement_status" : payload.status} }];
    }
  }

  if (payload.stake_min_amt != '' || payload.stake_max_amt != '') {

    let r = {};
    if(payload.stake_min_amt){
        r.from = parseInt(payload.stake_min_amt)
    }
    if(payload.stake_max_amt){
        r.to = parseInt(payload.stake_max_amt)
    }

    if(r) {
       filter.push({ range : {"betslip_details.stake":r } })
    }
  }

  if (payload.winning_min_amt != '') {
    filter.push({ range :{"betslip_details.possible_win_amount" : { gte :payload.winning_min_amt } }})
  }

  if (payload.winning_max_amt != '') {
    filter.push({ range :{"betslip_details.possible_win_amount" : { lte :payload.winning_max_amt } }})
  }

  if (payload?.owner_id) {
    filter.push({term :{ parent_id :{ value :payload?.owner_id } } })
  }


if(payload?.currency_id && payload?.currency_id !='') {
  mustArray.push({ 'match' : { "player_details.currency_id": payload?.currency_id } });
}

if (payload.currency && payload.currency !== '') {
  filter.push({ term: { "player_details.currency": { value: payload.currency } } });
}

let str = [];
let queryForFilter = [];

if (payload.sport_id !== '') {
    str.push(`bb.sport_id = '${payload.sport_id}'`)
}

if (payload.country !== '') {
  str.push(`bb.location_id = '${payload.country}'`)
}

if (payload.tournament_id !== '') {
    str.push(`bb.league_id = '${payload.tournament_id}'`)
}

if (payload.match_id !== '') {
  str.push(`bb.event_id = '${payload.match_id}'`)
}

if (payload.search !== '') {
    str.push(`bb.bet_id LIKE '%${payload.search.trim()}%'`)
}

if (payload.search_match !== '') {
    str.push(`bb.name_en LIKE '%${payload.search_match.trim()}%'`)
}

if (payload.search_market !== '') {
    str.push(`bb.market LIKE '%${payload.search_market.trim()}%'`)
}

if (str.length > 0) {
  const temp = str.join(' and ');
  queryForFilter = await sequelize.query(`select "bb"."betslip_id" from "bets_bets" as "bb" left join "bets_betslip" as "bs" on "bb"."betslip_id" = "bs"."id" left join "pulls_events" as "pe" on "bb"."event_id" = "pe"."id" where ${temp}`, {
    type: sequelize.QueryTypes.SELECT
  })
}

if (queryForFilter.length > 0) {
  filter = [...filter, { term: { betslip_id: queryForFilter[0].betslip_id } }]
} else if(str.length > 0){
  filter = [...filter, { term: { betslip_id: -1 } }]
}
  if (params.agentId) {
    filter = [...filter, { term: { "" : { value: params.agentId }} }];
  }

  if (payload.provider_id) {
    filter = [...filter, { term: { provider_id : { value: payload.provider_id }} }];
  }else{
    const jetFairId = isProduction ? 15 : 14
    const powerPlayId = isProduction ? 50 : 5035

    filter = [...filter, {
      bool: {
        should: [
          { term: { provider_id: jetFairId } },
          { term: { provider_id: powerPlayId } }
        ],
        minimum_should_match: 1
      }
    }];
  }

// ========================filter ends here================================

if (mustArray.length > 0) {
  query = mustArray
} else {
  query = {
    match_all: {}
  }
}

if (filter.length > 0) {
  filter = {
    bool: {
      filter: filter
    }
  }
}

let sortData = {}
let sortOrder = payload?.order ? payload.order : 'ASC'
if (payload?.sort_by) {
  let sortKey = payload.sort_by
  sortData[sortKey] = sortOrder
}

try {
  let searchDetails
  let limit = 10
  let page = 0
  let offset = page * limit

  searchDetails = await esClient.search({
    index: config.getProperties().es_index.sports_index_name,
    body: {
      query: {
        bool: {
          must: query,
          filter: filter
        }
      },
      sort: sortData,
      timeout: '3000s',
      track_total_hits: true,
      size: limit,
      from: offset
    }
  })

  const uuid = uuidv4().replace(/-/g, '')
  const filePath = `/tmp/transaction_${uuid}.csv`
  const csvWriter = createCsvWriter({
    path: filePath,
    header: [
      { id: 'id', title: 'Id' },
      { id: 'bet_id', title: 'Bet ID' },
      { id: 'created_at', title: 'Created At' },
      { id: 'email', title: 'Email' },
      { id: 'schedule_date', title: 'Schedule at' },
      { id: 'market_name', title: 'Market name' },
      { id: 'market_id', title: 'Market Id' },
      { id: 'team_name', title: 'Team name' },
      { id: 'run', title: 'Run' },
      { id: 'bet_type', title: 'Bet Type' },
      { id: 'journal_entry', title: 'Journal Entry' },
      { id: 'currency', title: 'Currency' },
      { id: 'transaction_amount', title: 'Transaction Amount' },
      // { id: 'total_transaction_amount', title: 'Total Transaction Amount' },
      { id: 'stake', title: 'Stake' },
      { id: 'odds', title: 'Odds' },
      { id: 'possible_win_amount', title: 'Possible Win Amount' },
      { id: 'action_type', title: 'Action Type' },
      { id: 'status', title: 'Status' },
      { id: 'transaction_status', title: 'Transaction Status' },
      { id: 'comment', title: 'Comment' },
      { id: 'commission', title: 'Commission Details' },
      { id: 'player_id', title: 'Player Id' },
      { id: 'player_name', title: 'Player Name' }
    ]
  });
  const csvData = searchDetails.body.hits.hits

  if (payload.time_zone_name) {
    const csvDataList = await Promise.all(
      csvData.map(async object => {
        // Formatting createdAt date with optional user timezone
        const createdDate = payload.time_zone_name ? moment.tz(object._source.created_at, payload.time_zone_name) : moment(object._source.created_at)
        object._source.created_at = createdDate.format('DD-MM-YYYY HH:mm:ss')
        return object
      })
    )
  }

  let mainArr = []
  let tenantId
  if (csvData.length > 0) {
    tenantId = csvData[0]._source.tenant_id
    for (const txn of csvData) {
      let object = {
        id: txn._source.internal_tracking_id || '',
        bet_id: txn._source && txn._source.betslip_details && txn._source.betslip_details.bets && txn._source.betslip_details.bets.length > 0 ? txn._source.betslip_details.bets[0].bet_id || '' : '',
        created_at: getDateInStringFormat(txn._source.created_at || ''),
        email: txn._source.player_details?.email || '',
        schedule_date: txn._source && txn._source.betslip_details && txn._source.betslip_details.bets && txn._source.betslip_details.bets.length > 0 ? txn._source.betslip_details.bets[0].start_date || '' : '',
        market_name: ((txn._source && txn._source.betslip_details && txn._source.betslip_details.bets && txn._source.betslip_details.bets.length > 0) ? txn._source.betslip_details.bets[0].market : txn._source?.market_name) || '',
        market_id: txn._source?.market_id || '',
        team_name: txn._source?.runner_name || '',
        run: txn._source?.betslip_details?.run || '',
        bet_type: (await checkIfEmpty(txn?._source.betslip_details)) ? 'Settled' : (txn?._source?.betslip_details?.bettype == 1) ? 'Back' : 'Lay',
        journal_entry: txn._source?.journal_entry || '',
        currency: txn._source.player_details?.currency || '',
        transaction_amount: 'Cash: ' + (txn._source.amount ? txn._source.amount : 0) + '\n' + 'Non Cash ' + (txn._source.non_cash_amount ? txn._source.non_cash_amount : 0),
        // total_transaction_amount: (txn._source.amount ? txn._source.amount : 0) + (txn._source.non_cash_amount ? txn._source.non_cash_amount : 0),
        stake: txn._source?.betslip_details?.stake || '',
        odds: txn._source?.betslip_details?.multi_price || '',
        possible_win_amount: txn._source?.betslip_details?.possible_win_amount || '',
        action_type: await getActionType(txn._source.payment_for),
        status: txn._source?.betslip_details?.settlement_status || ((txn?._source?.betslip_details?.bettype) ? 'In Game' : txn?._source?.transaction_code || ''),
        transaction_status: txn._source?.status || '-',
        comment: txn._source?.description || '',
        commission:'Net P/L: ' + (txn._source.net_pl ? txn._source.net_pl : 0) + '\n' + 'Percent: ' + (txn._source.commission_per ? txn._source.commission_per : 0) + '\n' + 'Amount' + (txn._source.commission_amount ? txn._source.commission_amount : 0),
        player_id: txn._source.player_details?.player_id,
        player_name: txn._source.player_details?.user_name
      }
      mainArr = [...mainArr, object]
    }
  }
  await Promise.all([csvWriter.writeRecords(mainArr)])

  const totalData = searchDetails.body.hits.total.value
  let itval = parseFloat(totalData) / limit
  const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

  for (let i = 1; i < itval; i++) {
    await delay(1000)
    offset = i * limit
    searchDetails =
      await esClient.search({
        index: config.getProperties().es_index.sports_index_name,
        body: {
          query: {
            bool: {
              must: query,
              filter: filter
            }
          },
          sort: sortData,
          timeout: '3000s',
          track_total_hits: true,
          size: limit,
          from: offset
        }
      })
    let mainArr = []
    let tenantId
    const csvData = searchDetails.body.hits.hits
    if (payload.time_zone_name) {
      const csvDataList = await Promise.all(
        csvData.map(async object => {
          // Formatting createdAt date with optional user timezone
          const createdDate = payload.time_zone_name ? moment.tz(object._source.created_at, payload.time_zone_name) : moment(object._source.created_at)
          object._source.created_at = createdDate.format('DD-MM-YYYY HH:mm:ss')
          return object
        })
      )
    }
    if (csvData.length > 0) {
      tenantId = csvData[0]._source.tenant_id
      for (const txn of csvData) {
        let object = {
          id: txn._source.internal_tracking_id || '',
          bet_id: txn._source && txn._source.betslip_details && txn._source.betslip_details.bets && txn._source.betslip_details.bets.length > 0 ? txn._source.betslip_details.bets[0].bet_id || '' : '',
          created_at: getDateInStringFormat(txn._source.created_at || ''),
          email: txn._source.player_details?.email || '',
          schedule_date: txn._source && txn._source.betslip_details && txn._source.betslip_details.bets && txn._source.betslip_details.bets.length > 0 ? txn._source.betslip_details.bets[0].start_date || '' : '',
          market_name: ((txn._source && txn._source.betslip_details && txn._source.betslip_details.bets && txn._source.betslip_details.bets.length > 0) ? txn._source.betslip_details.bets[0].market : txn._source?.market_name) || '',
          market_id: txn._source?.market_id || '',
          team_name: txn._source?.runner_name || '',
          run: txn._source?.betslip_details?.run || '',
          bet_type: (await checkIfEmpty(txn?._source.betslip_details)) ? 'Settled' : (txn?._source?.betslip_details?.bettype == 1) ? 'Back' : 'Lay',
          journal_entry: txn._source?.journal_entry || '',
          currency: txn._source.player_details?.currency || '',
          transaction_amount: 'Cash: ' + (txn._source.amount ? txn._source.amount : 0) + '\n' + 'Non Cash ' + (txn._source.non_cash_amount ? txn._source.non_cash_amount : 0),
          // total_transaction_amount: (txn._source.amount ? txn._source.amount : 0) + (txn._source.non_cash_amount ? txn._source.non_cash_amount : 0),
          stake: txn._source?.betslip_details?.stake || '',
          odds: txn._source?.betslip_details?.multi_price || '',
          possible_win_amount: txn._source?.betslip_details?.possible_win_amount || '',
          action_type: await getActionType(txn._source.payment_for),
          status: txn._source?.betslip_details?.settlement_status || ((txn?._source?.betslip_details?.bettype) ? 'In Game' : txn?._source?.transaction_code || ''),
          transaction_status: txn._source?.status || '-',
          comment: txn._source?.description || '',
          commission:'Net P/L: ' + (txn._source.net_pl ? txn._source.net_pl : 0) + '\n' + 'Percent: ' + (txn._source.commission_per ? txn._source.commission_per : 0) + '\n' + 'Amount' + (txn._source.commission_amount ? txn._source.commission_amount : 0),
          player_id: txn._source.player_details?.player_id,
          player_name: txn._source.player_details?.user_name
        }
        mainArr = [...mainArr, object]
      }

      await Promise.all([csvWriter.writeRecords(mainArr)])
    }
  }


  // upload file to s3
  const s3Config = config.getProperties().s3
  const fileContent = await fs.promises.readFile(filePath);
  const key = `tenants/${tenantId}/csv/bet_report/transaction_${uuid}.csv`

  const s3Params = {
    ACL: 'public-read',
    Bucket: s3Config.bucket,
    Key: key,
    Body: fileContent
  }
  const uploadedFile = await s3.upload(s3Params).promise()
  await db.ExportCsvCenter.update({
    csvUrl: key,
    status: EXPORT_CSV_STATUS.DONE
  },
    {
      where: { id: data.id }
    }
  )

  fs.unlink(filePath, (err) => {
    if (err) {
      throw err
    }
  });

  return true
} catch (error) {
  console.log("=======errorr", error)
  throw error
}
}
