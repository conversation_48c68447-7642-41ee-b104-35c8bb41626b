import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { CURRENCY_LIST_PROD, CURRENCY_LIST_STAGE, EXPORT_CSV_STATUS, ZAMBIA_TENANT_ID } from '../../../common/constants'
import config from '../../../configs/app.config'
import db, { sequelize } from '../../../db/models'
import { s3 } from '../../../libs/awsS3Config'
import { formatAmount, getDateInStringFormat } from '../../helpers'
import getPermissionsForPlayerCsv from '../getPermissionsForPlayerCsv'

const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')

export default async (data) => {
  const payload = data.payload;

  const uuid = uuidv4().replace(/-/g, '');
  const filePath = `/tmp/transaction_${uuid}.csv`;

  await db.ExportCsvCenter.update({
    status: EXPORT_CSV_STATUS.IN_PROGRESS
  }, {
    where: { id: data.id }
  });

  // ------------------- Start: Setup Filters --------------------------------

  // Set Admin Id and Tenant Id based on role
  const params = {
    tenantId: null,
    agentId: null
  }

  if (data.adminType == 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: {
        id: data.adminId
      },
      attributes: ['tenantId'],
      raw: true,
    });
    params.tenantId = adminData.tenantId || payload.tenant_id;
    params.agentId = payload.agent_id && payload.agent_id != 0 ? payload.agent_id : data.adminId;
  } else if (data.adminType == 'Manager' || data.adminType == 'SuperAdminUser') {
    params.tenantId = payload.tenant_id && payload.tenant_id != 0 ? payload.tenant_id : null;
    params.agentId = payload.agent_id && payload.agent_id != 0 ? payload.agent_id : null;
  } else {
    params.tenantId = payload.tenant_id && payload.tenant_id != 0 ? payload.tenant_id : null;
  }

  // Manage date time filter
  const timePeriod = payload.time_period || {};
  const timezone = (params.tenantId && String(params.tenantId) === ZAMBIA_TENANT_ID) ? 'Africa/Lusaka' : 'Asia/kolkata';
  const dateTimeFormat = 'YYYY-MM-DD HH:mm:ss';

  if (payload.time_period) {
    timePeriod.start_date = moment.tz(timePeriod.start_date, 'YYYY-MM-DD', timezone).format(dateTimeFormat);
    timePeriod.end_date = moment.tz(timePeriod.end_date, 'YYYY-MM-DD', timezone).format(dateTimeFormat);
  } else {
    timePeriod.start_date = moment.tz(timezone).startOf('day').format(dateTimeFormat);
    timePeriod.end_date = moment.tz(timezone).endOf('day').format(dateTimeFormat);
  }

  // Add default values
  payload.order = payload.order || 'asc';
  payload.sort_by = payload.sort_by || 'id';
  payload.amount = payload.amount || 0;

  if (!payload.player_case || !['zero_deposit', 'no_deposit_since_registration', 'last_deposit_n_days', 'wallet_balance_not_played_n_days'].includes(payload.player_case)) {
    throw new Error('Invalid Player Case Type!');
  }

  // ------------------- End: Setup Filters --------------------------------

  // ------------------- Start: Fetch DB records --------------------------------

  let findQuery;
  let sortQuery;
  if (payload.sort_by === 'last_deposit_created_at') {
      sortQuery = `ORDER BY l.last_deposit_created_at ${payload.order}`;
  } else {
      sortQuery = `ORDER BY u.${payload.sort_by} ${payload.order}`;
  }
  let tenantFilterQuery = params.tenantId ? `and tenant_id = ${params.tenantId}` : null;
  let selectAttributesQuery = `u.id as user_id, user_name, email, phone, w.amount,w.currency_id, w.non_cash_amount, active, vip_level`;

  // Add admin filter in case of admin login
  let adminFilterQuery;
  if (params.agentId) {
    const loginAgentRole = await db.AdminUsersAdminRole.findOne({
      where: {
        adminRoleId: params.agentId,
      },
      attributes: ['adminRoleId'],
      raw: true,
    });
    if (loginAgentRole && (loginAgentRole.adminRoleId === 4 || loginAgentRole.adminRoleId === 5)) {
      params.agentId = (await db.AdminUser.findOne({
        where: {
          id: params.agentId,
        },
        attributes: ['parentId'],
        raw: true,
      })).parentId;
    }
    if (loginAgentRole && ![1, 3].includes(parseInt(loginAgentRole.adminRoleId))) {
      adminFilterQuery = `and parent_id = ${params.agentId} and parent_type = 'AdminUser'`;
    }
  }

  // Create query based on case
  if (payload.player_case === 'zero_deposit' || payload.player_case === 'no_deposit_since_registration') {
    findQuery = `
      select
        ${selectAttributesQuery}
      from
        "users" u
        join wallets w on u.id = w.owner_id
        and w.owner_type = 'User'
        left join (
          select
            distinct user_id
          from
            "player_summary_provider_wise"
          where
            type in (6,7)
            ${tenantFilterQuery}
            ${payload.player_case === 'zero_deposit' ? `and date between '${timePeriod.start_date}' and '${timePeriod.end_date}'` : ''}
            ${adminFilterQuery ? adminFilterQuery : ''}
            ) q1 on u.id = q1.user_id
      where
        q1.user_id is null
        ${payload.player_case === 'zero_deposit' ? `and u.created_at <= '${timePeriod.end_date}'` : ''}
        ${tenantFilterQuery}
        ${adminFilterQuery ? adminFilterQuery : ''}
    `;
  } else if (payload.player_case === 'last_deposit_n_days') {
    findQuery = `
    WITH LastTransaction AS (
        SELECT
            MAX(t.created_at) AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}' AS last_deposit_created_at,
            w.owner_id
        FROM transactions t
        JOIN wallets w ON (w.id = t.target_wallet_id AND w.owner_type = 'User')
        Where t.transaction_type = 3  -- Only considering deposit cases
        ${tenantFilterQuery}
        GROUP BY w.owner_id
    )
      SELECT
        ${selectAttributesQuery},l.last_deposit_created_at
      FROM
        users u
        join wallets w on u.id = w.owner_id
        and w.owner_type = 'User'
        join (
          SELECT
            user_id,
            MAX(date) AS last_deposit_date
          FROM
            player_summary_provider_wise t
          WHERE
            type in (6,7)
            ${tenantFilterQuery}
            ${adminFilterQuery ? adminFilterQuery : ''}
          GROUP BY
            user_id
          HAVING
            MAX(date) < CURRENT_DATE - INTERVAL '${payload.no_of_days} days'
        ) t ON u.id = t.user_id
        LEFT JOIN LastTransaction l ON l.owner_id = u.id
      WHERE
        1 = 1
        ${tenantFilterQuery}
        ${adminFilterQuery ? adminFilterQuery : ''}
    `;
  } else if (payload.player_case === 'wallet_balance_not_played_n_days') {
    findQuery = `
      select
        ${selectAttributesQuery}
      from
        users u
        join wallets w on u.id = w.owner_id
        and w.owner_type = 'User'
        and w.amount >= ${payload.amount}
        left join (
          SELECT
            user_id,
            MAX(date) AS last_bet_date
          FROM
            player_summary_provider_wise t
          WHERE
            type = 19
            ${tenantFilterQuery}
            ${adminFilterQuery ? adminFilterQuery : ''}
          group by
            user_id
          having
            MAX(date) >= CURRENT_DATE - INTERVAL '${payload.no_of_days} days'
        ) q1 on u.id = q1.user_id
      where
        q1.user_id is null
        ${tenantFilterQuery}
        ${adminFilterQuery ? adminFilterQuery : ''}
    `;
  }
  findQuery = findQuery + sortQuery;

  const csvData = await sequelize.query(
    findQuery,
    {
      type: sequelize.QueryTypes.SELECT
    }
  );
  // ------------------- End: Fetch DB records --------------------------------

  // ------------------- Start: column process --------------------------------

  const columnsPermissions = {
    'User Id': null,
    'Username': "user_name",
    'Email': "email",
    'Phone': "phone",
    'Total Balance': "total_balance",
    'Active': null,
    'VIP Level': "vip_level"

  };

  if (payload.player_case === 'last_deposit_n_days') {
    columnsPermissions['Last Cashable Deposit Date'] = "last_deposit_date";
  }

  const columns = await getPermissionsForPlayerCsv(columnsPermissions, params.agentId, params.tenantId, data.adminType);

  const mappedFields = columns.filter(column => columnsPermissions[column] !== null)
    .map(column => ({ id: columnsPermissions[column], title: column }));

  mappedFields.unshift({ id: 'user_id', title: 'User ID' });
  mappedFields.push({ id: 'active', title: 'Status' });

  const csvWriter = createCsvWriter({
    path: filePath,
    header: mappedFields
  });

  // ------------------- End: column process --------------------------------

  // ------------------- Start: record process --------------------------------
  const allowedColumns = mappedFields.map(mf => mf.id);
  let mainArr = [];
  if (csvData.length > 0) {
    for (const txn of csvData) {
      let amount=txn.amount + txn.non_cash_amount
      const currencyList = (config.get('env') === 'production') ? CURRENCY_LIST_PROD : CURRENCY_LIST_STAGE;
      const currencyEntry = currencyList.find(currency => currency.id === Number(txn.currency_id));
      const currencyLabel = currencyEntry ? currencyEntry.value : '';
      console.log("currencyLabelcurrencyLabelcurrencyLabelcurrencyLabel",currencyLabel);
      const values = {
        'user_id': txn.user_id,
        'user_name': txn.user_name,
        'email': txn.email,
        'phone': txn.phone,
        'total_balance': formatAmount(amount,params.tenantId,currencyLabel) ,
        'active': txn.active ? 'Active':'Deactive',
        'vip_level': txn.vip_level,
        ...(txn.last_deposit_created_at && { 'last_deposit_date': getDateInStringFormat(moment(txn.last_deposit_created_at).format('DD-MM-YYYY HH:mm:ss')) })
      };
      const finalValues = allowedColumns.reduce((obj, key) => { obj[key] = values[key]; return obj; }, {});

      mainArr = [...mainArr, finalValues];
    }
  }

  function formatValue(value) {
    return value % 1 !== 0 ? value.toFixed(2) : value;
  }

  await Promise.all([csvWriter.writeRecords(mainArr)]);

  // ------------------- End: record process --------------------------------

  // ------------------- Start: S3 upload --------------------------------
  const s3Config = config.getProperties().s3;
  const fileContent = await fs.promises.readFile(filePath);
  const key = `tenants/${params.tenantId}/csv/player/player_filter_${uuid}.csv`; // Need to confirm

  const s3Params = {
    ACL: 'public-read',
    Bucket: s3Config.bucket,
    Key: key,
    Body: fileContent
  };
  const uploadedFile = await s3.upload(s3Params).promise();
  await db.ExportCsvCenter.update({
    csvUrl: key,
    status: EXPORT_CSV_STATUS.DONE
  },
    {
      where: { id: data.id }
    }
  );

  fs.unlink(filePath, (err) => {
    if (err) {
      throw err
    }
  });

  // ------------------- End: S3 upload --------------------------------

  return true;
}
