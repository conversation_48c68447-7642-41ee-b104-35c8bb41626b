import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3'
import { createObjectCsvStringifier } from 'csv-writer'
import { Readable } from 'stream'
import { v4 as uuidv4 } from 'uuid'
import config from '../../../configs/app.config'
import db, { sequelize } from '../../../db/models'
import { s3 } from '../../../libs/awsS3ConfigV2'
import { EXPORT_CSV_STATUS } from '../../constants'
import {
  fetchAgentIds,
  formatAmount,
  getDateInStringFormat,
  getPlayerType
} from '../../helpers'
import getRolesDetails from '../getAdminRolesDetail'
import getCategoryType from './getCategoryType'
import getStartEndDates, { getDateRange } from './getStartEndDates'


export default async (data) => {
  const payload = data.payload
  await db.ExportCsvCenter.update(
    { status: EXPORT_CSV_STATUS.IN_PROGRESS },
    { where: { id: data.id } }
  )
  let currentUser = { id: data.adminId, parentType: data.adminType }

  let agentId,
    playerReportWhere = [],
    firstTimeReportWhere = []
  let agentIdArray = []
  let botUserJoin = ''
  let botUserSelect = ''
  let isSuperAdmin = false
  let isFirstTimeDateFilter = false
  const checkSboRequest = payload.sbo_request
  let modifiedTimePeriod =
    payload?.dateType !== 'custom'
      ? await getStartEndDates(payload?.dateType)
      : { start_date: payload?.startDate, end_date: payload?.endDate }
  const timePeriod = {
    startDate: modifiedTimePeriod.start_date,
    endDate: modifiedTimePeriod.end_date
  }
  const params = {
    tenantIds: [],
    timePeriod: timePeriod,
    agentId: parseInt(agentId),
    agentIds: [],
    subAgentIds: [],
    searchKeyword: payload.search,
    actionType: payload.actionType,
    currency: payload.currency,
    vipLevel: payload.vipLevel,
    dateType: payload?.dateType ? payload?.dateType : 'today'
  }

  if (data?.adminType == 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: {
        id: data?.adminId
      },
      attributes: ['tenantId']
    })
    payload.tenantId = adminData?.tenantId
  }
  payload.agentId = isNaN(payload.agentId) ? '' : Number(payload.agentId)

  let searchAgentId
  if (payload.agentId) {
    searchAgentId = payload.agentId
  } else if (data.adminType === 'AdminUser') {
    const roles = await getRolesDetails(currentUser.id)
    if (roles.includes('agent')) {
      searchAgentId = currentUser.id
    }
  }
  if (searchAgentId) {
    params.agentIds = await fetchAgentIds(searchAgentId)
  }
  if (data.adminType == 'AdminUser') {
    const currentUserRole = await getRolesDetails(data.adminId)
    if (currentUserRole.includes('agent')) {
      params.subAgentIds = await fetchAgentIds(data.adminId)
    }
  }
  isSuperAdmin = data?.adminType === 'SuperAdminUser'
  let tenantId = Number(payload.tenantId)
  if (tenantId) {
    params.tenantIds = [tenantId]
  } else if (data.adminType == 'SuperAdminUser') {
    const adminData = await db.SuperAdminUser.findOne({
      where: {
        id: data.adminId
      },
      attributes: ['parentType', 'tenantIds']
    })
    if (adminData.parentType === 'Manager') {
      params.tenantIds = adminData.tenantIds.map((id) => parseInt(id))
    }
  }
  const [playerCategoryEnabled] = await sequelize.query(
    `SELECT id FROM tenant_theme_settings
    WHERE tenant_id = :tenantId
    AND :allowedModule = ANY(string_to_array(allowed_modules, ','))
    LIMIT 1`,
    {
      replacements: {
        tenantId,
        allowedModule: 'enablePlayerCategory'
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  let sortBy = payload?.sortBy
  let order = payload?.order
  let orderCondition = ''

  if (sortBy && order) {
    const columnMapping = {
      userId: 'user_id',
      userName: 'user_name',
      agentName: 'agent_name',
      country: 'country_code',
      tenantId: 'tenant_id',
      agentId: 'agent_id',
      currencyId: 'currency_id',
      totalBalance: 'total_balance',
      firstDepositAmount: 'first_deposit_amount',
      firstDepositTxnId: 'first_deposit_txn_id',
      firstDepositTime: 'first_deposit_time',
      totalCasinoBets: 'total_casino_bets',
      totalCasinoBetAmount: 'total_casino_bet_amount',
      totalSportsBets: 'total_sports_bets',
      totalSportsBetAmount: 'total_sports_bet_amount',
      lastLoginDate: 'last_login_date',
      vipLevel: 'vip_level',
      createdAt: 'created_at',
      totalBets: 'total_bets',
      totalBetAmount: 'total_bet_amount'
    }
    if (columnMapping[sortBy]) {
      orderCondition = ` ORDER BY ${columnMapping[sortBy]} ${order}`
    }
  }
  let tenantCondition = ''
  if (params.tenantIds.length === 1) {
    tenantCondition = `= ${params.tenantIds[0]}`
  } else if (params.tenantIds.length > 0) {
    tenantCondition = `IN (${params.tenantIds.join(', ')})`
  } else {
    // For superadmin or when no specific tenant is set, don't filter by tenant
    tenantCondition = 'IS NOT NULL' // This will include all tenants
  }

  if (params.agentIds && params.agentIds.length > 0) {
    agentIdArray = params.agentIds
  }
  if (params.agentId) {
    agentIdArray = await fetchAgentIds(params.agentId)
  }

  if (agentIdArray.length > 0) {
    playerReportWhere.push(`u.parent_id in (${agentIdArray})`)
  }

  if (params.currency) {
    playerReportWhere.push(`c.code = '${params.currency}'`)
  }

  if (params.searchKeyword) {
    if (isNaN(params.searchKeyword) === false) {
      playerReportWhere.push(`u.id = ${params.searchKeyword}`)
    } else {
      playerReportWhere.push(
        `(u.user_name LIKE '%${params.searchKeyword}%' OR u.first_name LIKE '%${params.searchKeyword}%' OR u.last_name LIKE '%${params.searchKeyword}%')`
      )
    }
  }
  let finalWhereCondition = ''
  if (params.actionType === 'first-deposit') {
    finalWhereCondition =
      'WHERE fd.first_deposit_amount IS NOT NULL AND fd.first_deposit_amount != 0'
  }
  if (params.actionType === 'no-bet') {
    finalWhereCondition = 'WHERE (ui.total_bets IS NULL OR ui.total_bets = 0)'
  }
  if (params.actionType === 'no-deposit-amount') {
    finalWhereCondition =
      'WHERE (fd.first_deposit_amount IS NULL OR fd.first_deposit_amount = 0)'
  }

  if (params.vipLevel) {
    playerReportWhere.push(`u.vip_level in (${params.vipLevel})`)
  }

  if (payload.dateType) {
    const { start, end } = getDateRange(payload.dateType, payload.timeZoneName, payload.startDate, payload.endDate)

    const isCustom = payload.dateType === 'custom'
    const startStr = isCustom ? start.format('YYYY-MM-DD HH:mm:ss') : start.toISOString()
    const endStr = isCustom ? end.format('YYYY-MM-DD HH:mm:ss') : end.toISOString()

    playerReportWhere.push(`u.created_at BETWEEN '${startStr}' AND '${endStr}'`)
  }

  if (payload.firstTimeDepositDateType) {
    const { start, end } = getDateRange(payload.firstTimeDepositDateType, payload.timeZoneName, payload.firstTimeDepositStartDate, payload.firstTimeDepositEndDate)

    const isCustom = payload.firstTimeDepositDateType === 'custom'
    const startStr = isCustom ? start.format('YYYY-MM-DD HH:mm:ss') : start.toISOString()
    const endStr = isCustom ? end.format('YYYY-MM-DD HH:mm:ss') : end.toISOString()

    firstTimeReportWhere.push(`created_at BETWEEN '${startStr}' AND '${endStr}'`)

    isFirstTimeDateFilter = true
  }

  // payload.playerCategory = payload.playerCategory || payload.playerType
  payload.playerCategory = getPlayerType(payload)

  // Check SBO User Logic
  if (isSuperAdmin || checkSboRequest === true) {
    botUserSelect = ', bu.id as bot_user_id'
    botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
    if (payload.playerCategory === 'bot_players') {
      playerReportWhere.push('bu.user_id IS NOT NULL')
    } else if (payload.playerCategory === 'real_players') {
      playerReportWhere.push('bu.user_id IS NULL')
    }
  } else if (checkSboRequest === false) {
    botUserSelect = ', bu.id as bot_user_id'
    botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
    playerReportWhere.push('bu.user_id IS NULL')
  }
  const whereCondition = playerReportWhere.length
    ? ` AND ${playerReportWhere.join(' AND ')}`
    : ''
  const whereFirsTimeDepositCondition = firstTimeReportWhere.length
    ? ` AND  ${firstTimeReportWhere.join(' AND ')}`
    : ''

  try {
    const uuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${tenantId}/csv/casino_transaction/transaction_${uuid}.csv`
    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'player_id', title: 'Player Id' },
        { id: 'user_name', title: 'User Name' },
        { id: 'status', title: 'Status' },
        { id: 'agent_name', title: 'Agent Name' },
        { id: 'country', title: 'Country' },
        { id: 'vip_level', title: 'Vip Level' },
        { id: 'total_balance', title: 'Current Total Balance' },
        { id: 'first_deposit_amount', title: 'First Deposit Amount' },
        { id: 'first_deposit_amount_txn_id', title: 'First Deposit Amount Trx Id'},
        { id: 'first_deposit_date_time', title: 'First Deposit Date Time' },
        { id: 'total_bets', title: 'Total Bets' },
        { id: 'total_bet_amount', title: 'Total Bet Amount' },
        { id: 'created_at', title: 'Player Registration Date' },
        { id: 'last_login_date', title: 'Last Login Date' },
        { id: 'currency', title: 'Currency' },
        { id: 'phone', title: 'Phone' },
        ...(playerCategoryEnabled ? [{ id: 'player_category', title: 'Player Category' }] : [])
      ]
    })

    const chunkSize = 1000
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3

    const totalsQuery = getPlayerReportTotalsQuery(
      payload.timeZoneName || 'UTC',
      tenantCondition,
      whereCondition,
      botUserJoin,
      whereFirsTimeDepositCondition,
      isFirstTimeDateFilter,
      finalWhereCondition
    )
    const totalsResult = await sequelize.query(totalsQuery, {
      type: sequelize.QueryTypes.SELECT,
      replacements: {
        betCountType: 30
      }
    })
    const totalRecordsCount = totalsResult.length
    const totalChunks = Math.ceil(totalRecordsCount / chunkSize)

    // Loop through each chunk
    for (let i = 0; i < totalChunks; i++) {
      const offset = i * chunkSize
      const limit = chunkSize
      const query = getPlayerReportList(
        tenantCondition,
        payload.timeZoneName || 'UTC',
        whereCondition,
        whereFirsTimeDepositCondition,
        orderCondition,
        limit,
        offset,
        botUserSelect,
        botUserJoin,
        isFirstTimeDateFilter,
        finalWhereCondition
      )
      let playersData = await sequelize.query(query, {
        type: sequelize.QueryTypes.SELECT,
        replacements: {
          betCountType: 30,
          betAmountType: 29,
          limit: limit,
          offset: offset
        }
      })
      let mainArr = []
      if (playersData.length > 0) {
        tenantId = playersData[0].tenant_id

        for (const txn of playersData) {
          const object = {
            player_id: txn.user_id,
            user_name: txn.user_name,
            agent_name: txn?.agent_name || '',
            vip_level: txn.vip_level,
            total_balance: txn?.total_balance
              ? formatAmount(txn.total_balance, tenantId, txn.currency_code)
              : 0,
            currency: txn.currency_code,
            total_bets: parseInt(txn.total_bets || 0),
            total_bet_amount:
              formatAmount(txn.total_bet_amount, tenantId, txn.currency_code) ||
              0,
            country: txn.country_code,
            created_at: getDateInStringFormat(txn.created_at),
            last_login_date: getDateInStringFormat(txn.last_login_date),
            first_deposit_amount: formatAmount(
              txn.first_deposit_amount,
              tenantId,
              txn.currency_code
            ),
            first_deposit_amount_txn_id: txn.first_deposit_txn_id || '--',
            first_deposit_date_time:
              getDateInStringFormat(txn.first_deposit_time) || '--',
            status: txn.active ? 'Active' : 'Inactive',
            phone: txn.phone,
            ...(playerCategoryEnabled && {
              player_category: await getCategoryType(txn?.category_type)
            })
          }
          mainArr = [...mainArr, object]
        }
      }

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(mainArr)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = []
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err // Ignore if file doesn't exist
      }

      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength
      }

      await s3.send(new PutObjectCommand(uploadFileParams))
    }
    await db.ExportCsvCenter.update(
      {
        csvUrl: key,
        status: EXPORT_CSV_STATUS.DONE
      },
      {
        where: { id: data.id }
      }
    )
    return true
  } catch (error) {
    console.log('=======errorr', error)
    throw error
  }
}

function getPlayerReportList(
  tenantCondition,
  timezone,
  whereCondition,
  whereFirsTimeDepositCondition,
  orderCondition,
  limit,
  offset,
  botUserSelect,
  botUserJoin,
  isFirstTimeDateFilter,
  finalWhereCondition = ''
) {
  return `
    WITH user_info AS (
      SELECT
        u.id AS user_id,
        u.tenant_id,
         TO_CHAR(u.last_login_date AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}', 'DD-MM-YYYY HH24:MI:SS') AS last_login_date,
        TO_CHAR(u.created_at AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}', 'DD-MM-YYYY HH24:MI:SS') AS created_at,
        u.user_name, u.email, u.vip_level,
        u.category_type,
        u.country_code AS country_code, c.code AS currency_code, u.active, u.first_name, u.last_name, u.phone,
        COALESCE(w.amount, 0) + COALESCE(w.non_cash_amount, 0) + COALESCE(w.one_time_bonus_amount, 0) + COALESCE(w.sports_freebet_amount, 0) AS total_balance,
        a.first_name AS agent_first_name, a.last_name AS agent_last_name, a.parent_id, a.agent_name,
        COALESCE(bs.total_bets, 0) AS total_bets, COALESCE(bs.total_bet_amount, 0) AS total_bet_amount ${botUserSelect}
      FROM users u
      JOIN wallets w ON (w.owner_id = u.id AND w.owner_type = 'User')
      JOIN currencies c ON c.id = w.currency_id
      LEFT JOIN admin_users a ON a.id = u.parent_id
      ${botUserJoin}
      LEFT JOIN LATERAL (
        SELECT
          SUM(CASE WHEN pspw.type = :betCountType THEN pspw.amount ELSE 0 END) AS total_bets,
          SUM(CASE WHEN pspw.type = :betAmountType THEN pspw.amount ELSE 0 END) AS total_bet_amount
        FROM player_summary_provider_wise pspw
        WHERE pspw.tenant_id ${tenantCondition} AND pspw.user_id = u.id
        AND pspw.date >= (u.created_at AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}')::date AND pspw.date <= CURRENT_DATE  AND pspw.type IN (30, 29)
      ) bs ON TRUE
      WHERE u.tenant_id ${tenantCondition} ${whereCondition}
    ),
    first_deposit AS (
      SELECT user_id,
       amount AS first_deposit_amount,
       TO_CHAR(first_deposit_date AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}', 'DD-MM-YYYY HH24:MI:SS') AS first_deposit_time,
        first_deposit_id AS first_deposit_txn_id
      FROM user_first_deposit
      WHERE tenant_id ${tenantCondition} ${whereFirsTimeDepositCondition}
    )
    SELECT ui.*, fd.first_deposit_amount, fd.first_deposit_time, fd.first_deposit_txn_id
    FROM user_info ui
    ${isFirstTimeDateFilter ? '' : 'LEFT'} JOIN first_deposit fd ON fd.user_id = ui.user_id
    ${finalWhereCondition}
    ${orderCondition}
    LIMIT :limit OFFSET :offset;`
}
function getPlayerReportTotalsQuery(
  timezone,
  tenantCondition,
  whereCondition,
  botUserJoin,
  whereFirsTimeDepositCondition,
  isFirstTimeDateFilter,
  finalWhereCondition = ''
) {
  return `
    WITH user_info AS (
      SELECT
        u.id AS user_id,
        c.code AS currency_code,
        COALESCE(w.amount, 0) + COALESCE(w.non_cash_amount, 0) + COALESCE(w.one_time_bonus_amount, 0) + COALESCE(w.sports_freebet_amount, 0) AS total_balance,
        COALESCE(bs.total_bets, 0) AS total_bets
      FROM users u
      JOIN wallets w ON w.owner_id = u.id AND w.owner_type = 'User'
      JOIN currencies c ON c.id = w.currency_id
      ${botUserJoin}
      LEFT JOIN LATERAL (
        SELECT SUM(CASE WHEN pspw.type = :betCountType THEN pspw.amount ELSE 0 END) AS total_bets
        FROM player_summary_provider_wise pspw
        WHERE pspw.tenant_id ${tenantCondition} AND pspw.user_id = u.id
        AND pspw.date >= (u.created_at AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}')::date AND pspw.date <= CURRENT_DATE  AND pspw.type = 30
      ) bs ON TRUE
      WHERE u.tenant_id ${tenantCondition} ${whereCondition}
    ),
    first_deposit AS (
      SELECT user_id, amount AS first_deposit_amount
      FROM user_first_deposit
      WHERE tenant_id ${tenantCondition} ${whereFirsTimeDepositCondition}
    )
    SELECT ui.currency_code, ui.total_balance, ui.total_bets
    FROM user_info ui
    ${isFirstTimeDateFilter ? '' : 'LEFT'} JOIN first_deposit fd ON fd.user_id = ui.user_id
    ${finalWhereCondition};`
}
