import moment from 'moment-timezone'
import { literal, QueryTypes } from 'sequelize'
import { v4 as uuidv4 } from 'uuid'
import config from '../../../configs/app.config'
import db, { sequelize, Sequelize } from '../../../db/models'
import { s3 } from '../../../libs/awsS3Config'
import { EXPORT_CSV_STATUS, GET_ST8_SPORTS_ACTIONS, PROD_TENANTS_USING_NEW_NGR_FORMULA, STAGE_TENANTS_USING_NEW_NGR_FORMULA } from '../../constants'
import { formatAmount, getPlayerType } from '../../helpers'
import getRolesDetails from '../getAdminRolesDetail'
import getPermissionsForPlayerCsv from '../getPermissionsForPlayerCsv'
import getAgentRevenueReportDataRealTimeSync from './getAgentRevenueReportDataRealTimeSync'
import { getDateRange } from './getStartEndDates'

const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')

export default async (data) => {
  try {
    const payload = data.payload
    await db.ExportCsvCenter.update(
      {
        status: EXPORT_CSV_STATUS.IN_PROGRESS
      },
      {
        where: { id: data.id }
      }
    )

    const checkSboRequest = payload.sbo_request
    let isSuperAdmin = false;
    // ==================== params gathering starts here ====================
    const params = {
      tenantId: 0,
      currencyId: payload?.currency || null,
      dateRange: payload?.datetime || {},
      timeType: payload?.time_type || 'yesterday',
      timeZone: payload?.time_zone_name || 'UTC',
      limit: payload?.limit || 1000,
      offset: payload?.offset || 0,
      sortBy: payload?.sort_by || 'agent_name',
      sortOrder: payload?.order || 'asc',
      agentId: 0,
      agentAction: payload?.agent_action || '',
      actionType: payload?.action_type || '',
      provider_id: payload?.game_provider,
      action_category: payload?.action_category,
      game_type: payload?.game_type,
      agentType: payload?.agent_type,
      isAgentLoggedIn: false,
      loggedInUserId: data.adminId
    }
    if (Array.isArray(payload.agent_id) && payload.agent_id.length <= 0) {
      payload.agent_id = 0;
    }

    params.agentId = payload?.agent_id || data.adminId;
    let sqlView = 'agent_report_view';
    let casinoTable = '';

    // Conditionally set the sqlView and casinoTable
    if (
      (params.action_category && params.action_category !== '') ||
      (params.provider_id && Array.isArray(params.provider_id) && params.provider_id.length > 0) ||
      (params.game_type && params.game_type.length > 0)
    ) {
      sqlView = 'agent_provider_report_view';
      casinoTable = 'LEFT JOIN casino_providers AS cp ON cp.id = a.provider_id';
    }

    payload.tenant_id = parseInt(payload.tenant_id) || "";

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      }
      )
      params.tenantId = adminData.tenantId || payload.tenant_id;
      const roles = await getRolesDetails(data.adminId)
      params.isAgentLoggedIn = roles.includes('agent');
      if (!roles.includes('owner')) {
        //condition for new sub-admin role
        if (roles.includes('owner') || roles.includes('sub-admin')) {
          params.agentId = payload?.agent_id ? payload.agent_id : ''
        } else {
          params.agentId = payload?.agent_id ? payload.agent_id : data.adminId
        }
      } else {
        params.agentId = payload?.agent_id ? payload.agent_id : ''
      }
    } else {
      params.tenantId = payload?.tenant_id || '';
      params.agentId = payload?.agent_id || '';
    }
    if (data.adminType == 'SuperAdminUser' && !params.tenantId) {
      const adminData = await db.SuperAdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['parentType', 'tenantIds']
      })
      if (adminData?.parentType === 'Manager') {
        params.tenantId = adminData.tenantIds.map(id => parseInt(id));
      }
    }
    isSuperAdmin = data.adminType === 'SuperAdminUser';
    payload.isSuperAdmin = isSuperAdmin
    payload.playerCategory = getPlayerType(payload)
    payload.checkSboRequest = checkSboRequest

    let agentReports = [];
    if (payload.time_type === 'real-time-sync') {
      agentReports = await getAgentRevenueReportDataRealTimeSync(payload, params);
    } else {
      // ==================== Date processing ====================
      let startDate, endDate

      if (params.timeType !== 'custom' || !Object.keys(params.dateRange).length) {
        const { start, end } = getDateRange(params.timeType, params.timeZone);
        startDate = start.format('YYYY-MM-DD')
        endDate = end.format('YYYY-MM-DD')
      } else {
        startDate = moment(params.dateRange.start_date).utc().format()
        endDate = moment(params.dateRange.end_date).utc().format()
      }
      // ==================== Prepare dynamic WHERE conditions ====================
      const whereConditions = [
        // `a.tenant_id = ${params.tenantId}`,
        // `a.agent_action = '${params.agentAction}'`
      ]
      if (params.tenantId != 0) {
        if (Array.isArray(params.tenantId)) {
          const tenantIds = params.tenantId.map(id => parseInt(id)).join(',')
          whereConditions.push(`a.tenant_id in (${tenantIds})`)
        } else {
          whereConditions.push(`a.tenant_id = ${params.tenantId}`)
        }
      }
      if (params.agentAction) {
        whereConditions.push(`a.agent_action = '${params.agentAction}'`)
      }

      if (params.game_type && params.game_type.length > 0) {
        params.game_type = params.game_type.map(x => "'" + x.trim().replace(/'/g, "''").toLowerCase() + "'");
        whereConditions.push(`a.game_id IN (${params.game_type})`);
      }
      if (params.agentType && params.agentType.trim() !== '') {
        whereConditions.push(`a.agent_type = ${parseInt(params.agentType)}`)
      }

      if (params.provider_id && Array.isArray(params.provider_id) && params.provider_id.length > 0) {
        const providerIdsLower = params.provider_id.map(val => val.toLowerCase().replace(/'/g, "''")); // Handle apostrophes
        const providerIdsStr = providerIdsLower.map((id) => `'${id}'`).join(",");

        let providerCondition, gameCondition;

        // Fetch provider IDs from database
        const providerList = await db.CasinoProvider.findAll({
          where: literal(`lower(name) in (${providerIdsStr})`),
          attributes: ['id'],
          raw: true
        });

        let providerIds = providerList.map(provider => provider.id);
        if (providerIds.length > 0) {
          const id = providerIds.join(',');
          providerCondition = `a.provider_id IN (${id})`;
        }

        // Fetch transaction seat IDs from database
        const transactionSeatIds = await sequelize.query(
          `SELECT seat_ids
            FROM transactions_providers_list
            WHERE LOWER(title) IN (${providerIdsStr});
            `,
          {
            type: Sequelize.QueryTypes.SELECT,
          }
        )

        // Flatten and process seat IDs
        const seatIds = transactionSeatIds.flatMap((row) =>
          row.seat_ids.map((id) => id.replace(/'/g, "''").toLowerCase())
        );

        const providerToGameId = GET_ST8_SPORTS_ACTIONS;
        for (const [provider, gameId] of Object.entries(providerToGameId)) {
          if (providerIdsLower.includes(provider.toLowerCase())) {
            seatIds.push(gameId.toLowerCase());
          }
        }

        if (seatIds.length > 0) {
          const quotedSeatIds = seatIds.map((id) => `'${id}'`).join(",");
          gameCondition = `LOWER(a.game_id) IN (${quotedSeatIds})`;
        }

        let whereStr = '';
        if (providerCondition && gameCondition) {
          // Both conditions are present, use OR
          whereStr += (whereStr ? ' AND ' : '') + `(${providerCondition } OR ${gameCondition})`;
        } else if (providerCondition) {
          // Only provider condition, use AND
          whereStr += (whereStr ? ' AND ' : '') + providerCondition;
        } else if (gameCondition) {
          // Only game condition, use AND
          whereStr += (whereStr ? ' AND ' : '') + gameCondition;
        }

        whereConditions.push(whereStr);
      }

      if (params.currencyId) {
        whereConditions.push(`a.currency_id = ${params.currencyId}`)
      }

      // if (params.agentIds && params.agentIds.length > 0) {
      if (params.agentId && params.agentId !== 0) {
        sqlView = 'agent_provider_report_view_non_aggregated';
        casinoTable = 'LEFT JOIN casino_providers as cp on cp.id = a.provider_id';

        // Convert agentId to a comma-separated string if it's an array
        const agentIdsStr = Array.isArray(params.agentId) ? params.agentId.map(Number).join(',') : params.agentId;

        // Add the recursive CTE as a subquery for the larger query
        whereConditions.push(`
          a.agent_id IN (
            WITH RECURSIVE parents AS (
              SELECT id, tenant_id
              FROM admin_users
              WHERE (id != parent_id OR parent_id IS NULL)
              AND id IN (${agentIdsStr})
              AND tenant_id = ${params.tenantId}
              UNION ALL
              SELECT child.id, child.tenant_id
              FROM admin_users child
              JOIN parents parent ON parent.id = child.parent_id
              AND parent.tenant_id = child.tenant_id
              WHERE child.parent_type = 'AdminUser'
              AND parent.id != child.id
            )
            SELECT id FROM parents
          )
        `);
      }

      if (params.action_category) {
        whereConditions.push(`(a.action_type = '${params.action_category}' OR a.action_type IS NULL)`)
      }
      whereConditions.push(`(a.date BETWEEN '${startDate}' AND '${endDate}' OR a.date IS NULL)`)

      let commissionRow = '';
      if (params.isAgentLoggedIn) {
        commissionRow = `
          case when a.agent_id = ${params.loggedInUserId}
            then (100-a.agent_commission::numeric(5,2))
            else a.agent_commission end`;
      } else {
        commissionRow = "a.agent_commission";
      }

      // Check SBO User Logic
      if (!params.agentId || params.agentId === 0) {
        if (isSuperAdmin || checkSboRequest === true) {
          if (payload.playerCategory === 'bot_players') {
            whereConditions.push(' a.is_real_player = false')
          } else if (payload.playerCategory === 'real_players') {
            whereConditions.push(' a.is_real_player = true')
          }
        } else if (checkSboRequest === false) {
          whereConditions.push(' a.is_real_player = true')
        }
      }

      const whereStr = whereConditions.join(' AND ')

      const isProduction = process.env.NODE_ENV === 'production';
const tenantIds = isProduction ? PROD_TENANTS_USING_NEW_NGR_FORMULA : STAGE_TENANTS_USING_NEW_NGR_FORMULA;
const id = Number(Array.isArray(params.tenantId) ? params.tenantId[0] : params.tenantId);

let ngr = '';
let ngrInEur = '';
let myRevenueAmount = '';
let myRevenueEuro = '';

if (tenantIds?.includes(id)) {
  // New NGR formula
  ngr =
    "(SUM(CASE WHEN a.type = 21 THEN a.amount ELSE 0 END) - SUM(CASE WHEN a.type = 37 THEN a.amount ELSE 0 END) + SUM(CASE WHEN a.type = 35 THEN a.amount ELSE 0 END)) AS ngr";
  ngrInEur =
    "(SUM(CASE WHEN a.type = 21 THEN a.amount_euro ELSE 0 END) - SUM(CASE WHEN a.type = 37 THEN a.amount_euro ELSE 0 END) + SUM(CASE WHEN a.type = 35 THEN a.amount_euro ELSE 0 END)) AS ngr_in_eur";

  myRevenueAmount = `CASE
    WHEN (SUM(CASE WHEN a.type = 21 THEN a.amount ELSE 0 END) - SUM(CASE WHEN a.type = 37 THEN a.amount ELSE 0 END) + SUM(CASE WHEN a.type = 35 THEN a.amount ELSE 0 END)) != 0
    THEN (SUM(CASE WHEN a.type = 21 THEN a.amount ELSE 0 END) - SUM(CASE WHEN a.type = 37 THEN a.amount ELSE 0 END) + SUM(CASE WHEN a.type = 35 THEN a.amount ELSE 0 END)) * ${commissionRow} / 100
    ELSE 0
  END AS my_revenue_amount`;

  myRevenueEuro = `CASE
    WHEN (SUM(CASE WHEN a.type = 21 THEN a.amount_euro ELSE 0 END) - SUM(CASE WHEN a.type = 37 THEN a.amount_euro ELSE 0 END) + SUM(CASE WHEN a.type = 35 THEN a.amount_euro ELSE 0 END)) != 0
    THEN (SUM(CASE WHEN a.type = 21 THEN a.amount_euro ELSE 0 END) - SUM(CASE WHEN a.type = 37 THEN a.amount_euro ELSE 0 END) + SUM(CASE WHEN a.type = 35 THEN a.amount_euro ELSE 0 END)) * ${commissionRow} / 100
    ELSE 0
  END AS my_revenue_euro`;
} else {
  // Default (Old) NGR formula
  ngr =
    "(SUM(CASE WHEN a.type = 21 THEN a.amount ELSE 0 END) - SUM(CASE WHEN a.type = 31 THEN a.amount ELSE 0 END)) AS ngr";
  ngrInEur =
    "(SUM(CASE WHEN a.type = 21 THEN a.amount_euro ELSE 0 END) - SUM(CASE WHEN a.type = 31 THEN a.amount_euro ELSE 0 END)) AS ngr_in_eur";

  myRevenueAmount = `CASE
    WHEN (SUM(CASE WHEN a.type = 21 THEN a.amount ELSE 0 END) - SUM(CASE WHEN a.type = 31 THEN a.amount ELSE 0 END)) != 0
    THEN (SUM(CASE WHEN a.type = 21 THEN a.amount ELSE 0 END) - SUM(CASE WHEN a.type = 31 THEN a.amount ELSE 0 END)) * ${commissionRow} / 100
    ELSE 0
  END AS my_revenue_amount`;

  myRevenueEuro = `CASE
    WHEN (SUM(CASE WHEN a.type = 21 THEN a.amount_euro ELSE 0 END) - SUM(CASE WHEN a.type = 31 THEN a.amount_euro ELSE 0 END)) != 0
    THEN (SUM(CASE WHEN a.type = 21 THEN a.amount_euro ELSE 0 END) - SUM(CASE WHEN a.type = 31 THEN a.amount_euro ELSE 0 END)) * ${commissionRow} / 100
    ELSE 0
  END AS my_revenue_euro`;
}


      const attributesWithPermissions = {
        'a.agent_id': null,
        'SUM(CASE WHEN a.type = 2 THEN a.amount ELSE 0 END) AS total_deposit': null,
        'SUM(CASE WHEN a.type = 2 THEN a.amount_euro ELSE 0 END) AS total_deposit_euro': null,
        'SUM(CASE WHEN a.type = 16 THEN a.amount ELSE 0 END) AS deposit_count': null,
        'SUM(CASE WHEN a.type = 16 THEN a.amount_euro ELSE 0 END) AS deposit_count_euro': null,
        'SUM(CASE WHEN a.type = 12 THEN a.amount ELSE 0 END) AS total_withdraw': null,
        'SUM(CASE WHEN a.type = 12 THEN a.amount_euro ELSE 0 END) AS total_withdraw_euro': null,
        'SUM(CASE WHEN a.type = 17 THEN a.amount ELSE 0 END) AS withdraw_count': null,
        'SUM(CASE WHEN a.type = 17 THEN a.amount_euro ELSE 0 END) AS withdraw_count_euro': null,
        'SUM(CASE WHEN a.type = 31 THEN a.amount ELSE 0 END) AS bonus': null,
        'SUM(CASE WHEN a.type = 31 THEN a.amount_euro ELSE 0 END) AS bonus_euro': null,
        'SUM(CASE WHEN a.type = 29 THEN a.amount ELSE 0 END) AS bet': null,
        'SUM(CASE WHEN a.type = 29 THEN a.amount ELSE 0 END) AS bet_after_refund': null,
        'SUM(CASE WHEN a.type = 29 THEN a.amount_euro ELSE 0 END) AS bet_after_refund_euro': null,
        'SUM(CASE WHEN a.type = 20 THEN a.amount ELSE 0 END) AS win': null,
        'SUM(CASE WHEN a.type = 20 THEN a.amount_euro ELSE 0 END) AS win_euro': null,
        'SUM(CASE WHEN a.type = 21 THEN a.amount ELSE 0 END) AS ggr': null,
        'SUM(CASE WHEN a.type = 25 THEN a.amount ELSE 0 END) AS player_count': null,
        'SUM(CASE WHEN a.type = 21 THEN a.amount_euro ELSE 0 END) AS ggr_euro': null,
        'a.currency_id': null,
        'a.agent_name': null,
        [`${commissionRow} AS my_commission`]: null,
        'a.currency_code': null,
        '(SUM(CASE WHEN a.type = 21 THEN a.amount ELSE 0 END) - SUM(CASE WHEN a.type = 31 THEN a.amount ELSE 0 END)) AS ngr': null,
        [ngr]: null,
        [ngrInEur]: null,
        [myRevenueAmount]: null,
        [myRevenueEuro]: null
      }

      const finalAttributes = await getPermissionsForPlayerCsv(
        attributesWithPermissions,
        data.adminId,
        params.tenantId,
        data.adminType
      )
      const selectStatement = `${finalAttributes.join(', ')}`
      const paginationStr = ''

      const sqlQuery = `
        SELECT ${selectStatement}
        FROM ${sqlView} AS a
        ${casinoTable}
        ${whereStr ? `WHERE ${whereStr}` : ''}
        GROUP BY a.agent_id, a.agent_name, a.currency_id, a.currency_code, a.agent_commission
        ORDER BY ${params.sortBy} ${params.sortOrder} ${paginationStr}
      `


      // Execute the query
      agentReports = await sequelize.query(sqlQuery, {
        type: QueryTypes.SELECT
      })
    }

    const csvData = agentReports
    // Prepare CSV
    const uuid = uuidv4().replace(/-/g, '')
    const filePath = `/tmp/agent_revenue_${uuid}.csv`

    const columnsPermissions = {
      'Agent ID': null,
      'Agent Name': null,
      'Currency Code': null,
      'My Commission': null,
      'Total Deposit': null,
      'Total Deposit (EUR)': null,
      'Deposit Count': null,
      'Total Deposit Count (EUR)': null,
      'Total Withdraw': null,
      'Total Withdraw (EUR)': null,
      'Withdraw Count': null,
      'Total Withdraw Count (EUR)': null,
      'Bonus': null,
      'Bonus (EUR)': null,
      'Bet': null,
      'Bet After Refund': null,
      'Bet After Refund (EUR)': null,
      'Win': null,
      'Win (EUR)': null,
      'GGR': null,
      'GGR (EUR)': null,
      'Player Count': null,
      'Ngr': null,
      'Ngr (EUR)': null,
      'My Revenue': null,
      'My Revenue (EUR)': null
    }


    const columns = await getPermissionsForPlayerCsv(columnsPermissions, data.adminId, params.tenantId, data.adminType)
    const mappedFields = columns.filter(column => columnsPermissions[column] !== null)
      .map(column => ({ id: columnsPermissions[column], title: column }))

    mappedFields.push({ id: 'agent_id', title: 'Agent ID' })
    mappedFields.push({ id: 'agent_name', title: 'Agent Name' })
    mappedFields.push({ id: 'currency_code', title: 'Currency Code' })
    mappedFields.push({ id: 'bet', title: 'Bet' })
    mappedFields.push({ id: 'win', title: 'Win' })
    mappedFields.push({ id: 'ggr', title: 'GGR' })
    mappedFields.push({ id: 'bonus', title: 'Bonus' })
    mappedFields.push({ id: 'ngr', title: 'Ngr' })
    mappedFields.push({ id: 'my_commission', title: 'My Commission' })
    mappedFields.push({ id: 'my_revenue', title: 'My Revenue' })
    mappedFields.push({ id: 'deposit_count', title: 'No. of Deposits' })
    mappedFields.push({ id: 'total_deposit', title: 'Total Deposit' })
    mappedFields.push({ id: 'withdraw_count', title: 'No. of Withdrawals' })
    mappedFields.push({ id: 'total_withdraw', title: 'Total Withdrawals' })
    mappedFields.push({ id: 'player_count', title: 'Registration' })
    // mappedFields.push({ id: 'currency_id', title: 'Currency ID' })
    // mappedFields.push({ id: 'total_deposit_euro', title: 'Total Deposit (EUR)' })
    // mappedFields.push({ id: 'deposit_count_euro', title: 'Total Deposit Count (EUR)' })
    // mappedFields.push({ id: 'total_withdraw_euro', title: 'Total Withdraw (EUR)' })
    // mappedFields.push({ id: 'withdraw_count_euro', title: 'Total Withdraw Count (EUR)' })
    // mappedFields.push({ id: 'bonus_euro', title: 'Bonus (EUR)' })
    // mappedFields.push({ id: 'bet_after_refund', title: 'Bet After Refund' })
    // mappedFields.push({ id: 'bet_after_refund_euro', title: 'Bet After Refund (EUR)' })
    // mappedFields.push({ id: 'win_euro', title: 'Win (EUR)' })
    // mappedFields.push({ id: 'ggr_euro', title: 'GGR (EUR)' })
    // mappedFields.push({ id: 'ngr_euro', title: 'Ngr (EUR)' })
    // mappedFields.push({ id: 'my_revenue_euro', title: 'My Revenue (EUR)' })



    const csvWriter = createCsvWriter({
      path: filePath,
      header: mappedFields
    })

    // ------------------- End: column process --------------------------------

    let mainArr = []
    if (csvData.length > 0) {
      for (const txn of csvData) {

        // Define the values from the transaction data based on the available fields
        const values = {
          agent_id: txn.agent_id,
          agent_name: txn.agent_name,
          currency_code: txn.currency_code,
          my_commission: txn.my_commission,
          total_deposit: formatAmount(txn.total_deposit, params.tenantId, txn.currency_code),
          total_deposit_euro: formatAmount(txn.total_deposit_euro, params.tenantId, txn.currency_code),
          deposit_count: txn.deposit_count,
          deposit_count_euro: txn.deposit_count_euro,
          total_withdraw: formatAmount(txn.total_withdraw, params.tenantId, txn.currency_code),
          total_withdraw_euro: formatAmount(txn.total_withdraw_euro, params.tenantId, txn.currency_code),
          withdraw_count: txn.withdraw_count,
          withdraw_count_euro: txn.withdraw_count_euro,
          bonus: formatAmount(txn.bonus, params.tenantId, txn.currency_code),
          bonus_euro: formatAmount(txn.bonus_euro, params.tenantId, txn.currency_code),
          bet: formatAmount(txn.bet, params.tenantId, txn.currency_code),
          bet_after_refund: formatAmount(txn.bet_after_refund, params.tenantId, txn.currency_code),
          bet_after_refund_euro: formatAmount(txn.bet_after_refund_euro, params.tenantId, txn.currency_code),
          win: formatAmount(txn.win, params.tenantId, txn.currency_code),
          win_euro: formatAmount(txn.win_euro, params.tenantId, txn.currency_code),
          ggr: formatAmount(txn.ggr, params.tenantId, txn.currency_code),
          ggr_euro: formatAmount(txn.ggr_euro, params.tenantId, txn.currency_code),
          player_count: txn.player_count,
          ngr: formatAmount(txn.ngr, params.tenantId, txn.currency_code),
          ngr_euro: formatAmount(txn.ngr_in_eur, params.tenantId, txn.currency_code),
          my_revenue: formatAmount(txn.my_revenue_amount, params.tenantId, txn.currency_code),
          my_revenue_euro: formatAmount(txn.my_revenue_euro, params.tenantId, txn.currency_code)
        }
        // Use columnsPermissions to set up permission checks for the fields
        const columnsPermissions = {
          agent_id: null,
          agent_name: null,
          currency_code: null,
          my_commission: null,
          total_deposit: null,
          total_deposit_euro: null,
          deposit_count: null,
          deposit_count_euro: null,
          total_withdraw: null,
          total_withdraw_euro: null,
          withdraw_count: null,
          withdraw_count_euro: null,
          bonus: null,
          bonus_euro: null,
          bet: null,
          bet_after_refund: null,
          bet_after_refund_euro: null,
          win: null,
          win_euro: null,
          ggr: null,
          ggr_euro: null,
          player_count: null,
          ngr: null,
          ngr_euro: null,
          my_revenue: null,
          my_revenue_euro: null
        }

        // Get permissions for each of the columns
        const filteredKeys = await getPermissionsForPlayerCsv(columnsPermissions, data.adminId, params.tenantId, data.adminType)
        // Filter the values based on the permissions
        const finalValues = Object.keys(values)
          .filter(key => filteredKeys.includes(key)) // Only include permitted keys
          .reduce((obj, key) => {
            obj[key] = values[key]
            return obj
          }, {})
        // Add the filtered final values to mainArr
        mainArr = [...mainArr, finalValues]
      }
    }
    await Promise.all([csvWriter.writeRecords(mainArr)])

    let awsTenantFolderName = params.tenantId || 0;
    if (data.adminType == 'SuperAdminUser' && Array.isArray(params.tenantId)) {
      if (params.tenantId.length === 1) awsTenantFolderName = params.tenantId[0];
      else awsTenantFolderName = 0;
    }

    // Upload to S3
    const s3Config = config.getProperties().s3
    const fileContent = await fs.promises.readFile(filePath)
    const key = `tenants/${awsTenantFolderName}/csv/agent/agent_revenue_${uuid}.csv`

    const s3Params = {
      ACL: 'public-read',
      Bucket: s3Config.bucket,
      Key: key,
      Body: fileContent
    }
    const uploadedFile = await s3.upload(s3Params).promise()

    // Update export status
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    // Remove temporary file
    fs.unlink(filePath, (err) => {
      if (err) {
        console.error('Error deleting temporary file:', err)
      }
    })

    return true
  } catch (error) {
    console.error('Agent Revenue Report Error:', error)

    // Update export status to failed
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.FAILED
    },
      {
        where: { id: data.id }
      }
    )

    throw error
  }
}
