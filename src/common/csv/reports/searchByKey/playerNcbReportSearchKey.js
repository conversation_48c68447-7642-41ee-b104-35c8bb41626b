export default (str) => {

  const result = {
    "bool": {
      "should": [
        {
          "dis_max": {
            "queries": [
              {
                "bool": {
                  "must": {
                    "bool": {
                      "should": [
                        {
                          "match": {
                            "player_details.player_id_s.word_start": {
                              "query": str,
                              "boost": 10,
                              "operator": "and",
                              "analyzer": "searchkick_word_search"
                            }
                          }
                        }
                      ]
                    }
                  },
                  "should": {
                    "match": {
                      "player_details.player_id_s.analyzed": {
                        "query": str,
                        "boost": 10,
                        "operator": "and",
                        "analyzer": "searchkick_word_search"
                      }
                    }
                  }
                }
              }
            ]
          }
        },
        {
          "dis_max": {
            "queries": [
              {
                "bool": {
                  "must": {
                    "bool": {
                      "should": [
                        {
                          "match": {
                            "player_details.player_name.word_start": {
                              "query": str,
                              "boost": 10,
                              "operator": "and",
                              "analyzer": "searchkick_word_search"
                            }
                          }
                        }
                      ]
                    }
                  },
                  "should": {
                    "match": {
                      "player_details.player_name.analyzed": {
                        "query": str,
                        "boost": 10,
                        "operator": "and",
                        "analyzer": "searchkick_word_search"
                      }
                    }
                  }
                }
              }
            ]
          }
        },
        {
          "dis_max": {
            "queries": [
              {
                "bool": {
                  "must": {
                    "bool": {
                      "should": [
                        {
                          "match": {
                            "transaction_id.word_start": {
                              "query": str,
                              "boost": 10,
                              "operator": "and",
                              "analyzer": "searchkick_word_search"
                            }
                          }
                        }
                      ]
                    }
                  },
                  "should": {
                    "match": {
                      "transaction_id.analyzed": {
                        "query": str,
                        "boost": 10,
                        "operator": "and",
                        "analyzer": "searchkick_word_search"
                      }
                    }
                  }
                }
              }
            ]
          }
        },
        {
          "dis_max": {
            "queries": [
              {
                "bool": {
                  "must": {
                    "bool": {
                      "should": [
                        {
                          "match": {
                            "player_details.phone.word_start": {
                              "query": str,
                              "boost": 10,
                              "operator": "and",
                              "analyzer": "searchkick_word_search"
                            }
                          }
                        }
                      ]
                    }
                  },
                  "should": {
                    "match": {
                      "player_details.phone.analyzed": {
                        "query": str,
                        "boost": 10,
                        "operator": "and",
                        "analyzer": "searchkick_word_search"
                      }
                    }
                  }
                }
              }
            ]
          }
        }
      ]
    }
  };
  return result;
}
