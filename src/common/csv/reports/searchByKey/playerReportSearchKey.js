
export default  (searchKey) => {

  let keyword = searchKey
    const result = {
      bool: {
        should: [
          {
            dis_max: {
              queries: [
                {
                  bool: {
                    must: {
                      bool: {
                        should: [
                          {
                            match: {
                              'player_id_s.word_start': {
                                query: searchKey,
                                boost: 10,
                                operator: "and",
                                analyzer: "searchkick_word_search"
                              }
                            }
                          }
                        ]
                      }
                    },
                    should: {
                      match: {
                        'player_id_s.analyzed': {
                          query: searchKey,
                          boost: 10,
                          operator: "and",
                          analyzer: "searchkick_word_search"
                        }
                      }
                    }
                  }
                }
              ]
            }
          },
          {
            dis_max: {
              queries: [
                {
                  bool: {
                    must: {
                      bool: {
                        should: [
                          {
                            match: {
                              'user_name.word_start': {
                                query: searchKey,
                                boost: 10,
                                operator: "and",
                                analyzer: "searchkick_word_search"
                              }
                            }
                          }
                        ]
                      }
                    },
                    should: {
                      match: {
                        'user_name.analyzed': {
                          query: searchKey,
                          boost: 10,
                          operator: "and",
                          analyzer: "searchkick_word_search"
                        }
                      }
                    }
                  }
                }
              ]
            }
          },
          {
            dis_max: {
              queries: [
                {
                  bool: {
                    must: {
                      bool: {
                        should: [
                          {
                            match: {
                              'nick_name.word_start': {
                                query: search<PERSON>ey,
                                boost: 10,
                                operator: "and",
                                analyzer: "searchkick_word_search"
                              }
                            }
                          }
                        ]
                      }
                    },
                    should: {
                      match: {
                        'nick_name.analyzed': {
                          query: searchKey,
                          boost: 10,
                          operator: "and",
                          analyzer: "searchkick_word_search"
                        }
                      }
                    }
                  }
                }
              ]
            }
          },
          {
            dis_max: {
              queries: [
                {
                  bool: {
                    must: {
                      bool: {
                        should: [
                          {
                            match: {
                              'phone.word_start': {
                                query: searchKey,
                                boost: 10,
                                operator: "and",
                                analyzer: "searchkick_word_search"
                              }
                            }
                          }
                        ]
                      }
                    },
                    should: {
                      match: {
                        'phone.analyzed': {
                          query: searchKey,
                          boost: 10,
                          operator: "and",
                          analyzer: "searchkick_word_search"
                        }
                      }
                    }
                  }
                }
              ]
            }
          }
        ]
      }
    }
  return result
  }
