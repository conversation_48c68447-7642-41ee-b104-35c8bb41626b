export default (str) => {
  const result = {
    bool: {
      should: [
        {
          dis_max: {
            queries: [
              {
                bool: {
                  must: {
                    bool: {
                      should: [
                        {
                          match: {
                            internal_tracking_id: {
                              query: str,
                              boost: 10,
                              operator: "and",
                              analyzer: "searchkick_word_search"
                            }
                          }
                        }
                      ]
                    }
                  },
                  should: {
                    match: {
                      internal_tracking_id: {
                        query: str,
                        boost: 10,
                        operator: "and",
                        analyzer: "searchkick_word_search"
                      }
                    }
                  }
                }
              }
            ]
          }
        },
        {
          dis_max: {
            queries: [
              {
                bool: {
                  must: {
                    bool: {
                      should: [
                        {
                          match: {
                            market_id: {
                              query: str,
                              boost: 10,
                              operator: "and",
                              analyzer: "searchkick_word_search"
                            }
                          }
                        }
                      ]
                    }
                  },
                  should: {
                    match: {
                      market_id: {
                        query: str,
                        boost: 10,
                        operator: "and",
                        analyzer: "searchkick_word_search"
                      }
                    }
                  }
                }
              }
            ]
          }
        },
        {
          dis_max: {
            queries: [
              {
                bool: {
                  must: {
                    bool: {
                      should: [
                        {
                          match: {
                            amount: {
                              query: str,
                              boost: 10,
                              operator: "and",
                              analyzer: "searchkick_word_search"
                            }
                          }
                        }
                      ]
                    }
                  },
                  should: {
                    match: {
                      amount: {
                        query: str,
                        boost: 10,
                        operator: "and",
                        analyzer: "searchkick_word_search"
                      }
                    }
                  }
                }
              }
            ]
          }
        }
      ]
    }
  };

  return result;
}
