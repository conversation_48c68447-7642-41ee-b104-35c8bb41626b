import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { EXPORT_CSV_STATUS } from '../../../common/constants'
import config from '../../../configs/app.config'
import db from '../../../db/models'
import { s3 } from '../../../libs/awsS3Config'
import getESclient from '../../../libs/getEsClient'
import getRolesDetails from '../getAdminRolesDetail'
import sportTransactionSearchKey from './searchByKey/sportTransactionSearchKey'
import { getDateInStringFormat } from '../../helpers'
const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')


export default async (data) => {
  let esClient = getESclient();
  const payload = data.payload
  await db.ExportCsvCenter.update({
    status: EXPORT_CSV_STATUS.IN_PROGRESS
  },
    {
      where: { id: data.id }
    }
  )
  let filter = []
  let mustArray = []
  let query
  let params = {
    tenantId: 0,
    agentId: 0
  }

  // ====================filters here ========================

  if (data.adminType == 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: {
        id: data.adminId
      },
      attributes: ['tenantId']
    }
    )
    params.tenantId = adminData.tenantId
    const roles = await getRolesDetails(data.adminId)
    if (!roles.includes('owner')) {
      //condition for new sub-admin role
      if (roles.includes('owner') || roles.includes('sub-admin')) {
        params.agentId = payload?.agent_id ? payload.agent_id : null
      } else {
        params.agentId = payload?.agent_id ? payload.agent_id : data.adminId
      }
    } else {
      params.agentId = payload?.agent_id ? payload.agent_id : null
    }

  } else {
    params.tenantId = payload?.tenant_id ? payload.tenant_id : null
    params.agentId = payload?.agent_id ? payload.agent_id : null
  }

  if (payload.action_type) {
    filter = [...filter, { term: { payment_for: payload.action_type } }]
  }

  if (params.tenantId) {
    filter = [...filter, { term: { tenant_id: params.tenantId } }]
  }

  if (params.agentId) {
    filter = [...filter, { term: { "player_details.parent_chain_ids" : { value: params.agentId }} }];
  }

  if(payload.emailsearch && payload.emailsearch !='') {
    mustArray.push({ 'match' : { "player_details.email": payload?.emailsearch } });
  }

  if(payload?.currency_id && payload?.currency_id !='') {
  mustArray.push({ 'match' : { "player_details.currency_id": payload?.currency_id } });
  }

  if (payload?.datetime?.start_date && payload?.datetime?.end_date) {
    if (payload.time_zone_name) {
      payload.datetime.start_date = moment.tz(payload.datetime.start_date, payload.time_zone_name).utc().format()
      payload.datetime.end_date = moment.tz(payload.datetime.end_date, payload.time_zone_name).utc().format()
    }else{
      payload.datetime.start_date = moment.tz(payload.datetime.start_date, 'UTC +00:00').utc().format()
      payload.datetime.end_date = moment.tz(payload.datetime.end_date, 'UTC +00:00').utc().format()
    }

    let endDate = payload.datetime.end_date
    filter = [...filter, {
      range: {
        created_at: {
          from: payload.datetime.start_date,
          include_lower: true,
          to: endDate.replace("000000", "999999"),
          include_upper: true
        }
      }
    }]
  }

  if(payload.search != '') {
    if(!isNaN(parseFloat(payload.search))){
      filter.push(sportTransactionSearchKey(payload.search))
    }
  }

// ========================filter ends here================================

if (mustArray.length > 0) {
  query = mustArray
} else {
  query = {
    match_all: {}
  }
}

if (filter.length > 0) {
  filter = {
    bool: {
      filter: filter
    }
  }
}

let sortData = {}
let sortOrder = payload?.order ? payload.order : 'ASC'
if (payload?.sort_by) {
  let sortKey = payload.sort_by
  sortData[sortKey] = sortOrder
}

try {
  let searchDetails
  let limit = 10
  let page = 0
  let offset = page * limit

  searchDetails = await esClient.search({
    index: config.getProperties().es_index.sports_index_name,
    body: {
      query: {
        bool: {
          must: query,
          filter: filter
        }
      },
      sort: sortData,
      timeout: '3000s',
      track_total_hits: true,
      size: limit,
      from: offset
    }
  })

  const uuid = uuidv4().replace(/-/g, '')
  const filePath = `/tmp/transaction_${uuid}.csv`

  const csvWriter = createCsvWriter({
    path: filePath,
    header: [
      { id: 'id', title: 'Id' },
      { id: 'created_at', title: 'Created At' },
      { id: 'bet_ids', title: 'Bet Ids' },
      { id: 'market_id', title: 'Market Id'},
      { id: 'team_name', title: 'Team Name'},
      { id: 'journal_entry', title: 'Journal Entry' },
      { id: 'wallet', title: 'Wallet' },
      { id: 'currency', title: 'Currency' },
      { id: 'from_before_balance', title: 'From Before Balance' },
      { id: 'from_after_balance', title: 'From After Balance' },
      { id: 'to_before_balance', title: 'To Before Balance' },
      { id: 'to_after_balance', title: 'To After Balance' },
      { id: 'amount', title: 'Amount' },
      { id: 'conversion_rate', title: 'Conversion rate' },
      { id: 'action_type', title: 'Action Type' },
      { id: 'comments', title: 'Comments' },
      { id: 'action_by', title: 'Action by' },
    ]
  });
  const csvData = searchDetails.body.hits.hits

  if (payload.time_zone_name) {
    const csvDataList = await Promise.all(
      csvData.map(async object => {
        // Formatting createdAt date with optional user timezone
        const createdDate = payload.time_zone_name ? moment.tz(object._source.created_at, payload.time_zone_name) : moment(object._source.created_at)
        object._source.created_at = createdDate.format('DD-MM-YYYY HH:mm:ss')
        return object
      })
    )
  }

  let mainArr = []
  let tenantId
  if (csvData.length > 0) {
    tenantId = csvData[0]._source.tenant_id
    for (const txn of csvData) {

      let strBetIts = "";
      let strBetIdsArray = [];

      if (txn._source.betslip_details && txn._source.betslip_details.bets) {
        txn._source.betslip_details.bets.forEach((value, key) => {
              strBetIdsArray.push(value.bet_id);
          });
      }

      let strBetIds = '-';
      if (strBetIdsArray.length > 0) {
          strBetIds = strBetIdsArray.join(",")
      }

      let walletName = '-'
      if(txn._source.source_wallet){
         walletName = txn._source.source_wallet?.email + ' ' + txn._source.source_wallet?.first_name + ' ' + txn._source.source_wallet?.first_name;
      }
      if(!txn._source.source_wallet) {
          walletName = txn._source.target_wallet?.email + ' ' + txn._source.target_wallet?.first_name + ' ' + txn._source.target_wallet?.last_name;
      }
      let txnAmount = parseFloat(txn._source?.non_cash_amount ?? 0.00)
      txnAmount = parseFloat(txn._source?.amount) + txnAmount
      let object = {
        id: txn._source.internal_tracking_id,
        created_at: getDateInStringFormat(txn._source.created_at),
        bet_ids: strBetIds,
        market_id: txn._source.market_id ? txn._source.market_id : '-',
        team_name: txn._source.runner_name ? txn._source.runner_name : '-',
        journal_entry: txn._source.journal_entry ? txn._source.journal_entry : '-',
        wallet: walletName,
        currency: txn._source.source_currency || txn._source.target_currency,
        from_before_balance: (txn._source.source_wallet?.before_balance + txn._source.source_wallet?.non_cash_before_balance) || '0.00',
        from_after_balance: (txn._source.source_wallet?.after_balance + txn._source.source_wallet?.non_cash_after_balance) || '0.00',
        to_before_balance: (txn._source.target_wallet?.before_balance + txn._source.target_wallet?.non_cash_before_balance) || '0.00',
        to_after_balance: (txn._source.target_wallet?.after_balance + txn._source.target_wallet?.non_cash_after_balance) || '0.00',
        amount: txnAmount || '-',
        conversion_rate: txn._source.conversion_rate || '-',
        action_type: txn._source.transaction_type ? txn._source.transaction_type : '-',
        comments: txn._source.description || '-',
        action_by: (txn._source.user_id || '-') + '-' + (txn._source.player_details.email || '-') + ' ' + (txn._source.player_details.first_name || '-') + ' ' + (txn._source.player_details.last_name || '-')
      };

      mainArr = [...mainArr, object]
    }
  }
  await Promise.all([csvWriter.writeRecords(mainArr)])

  const totalData = searchDetails.body.hits.total.value
  let itval = parseFloat(totalData) / limit
  const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

  for (let i = 1; i < itval; i++) {
    await delay(1000)
    offset = i * limit
    searchDetails =
      await esClient.search({
        index: config.getProperties().es_index.sports_index_name,
        body: {
          query: {
            bool: {
              must: query,
              filter: filter
            }
          },
          sort: sortData,
          timeout: '3000s',
          track_total_hits: true,
          size: limit,
          from: offset
        }
      })
    let mainArr = []
    let tenantId
    const csvData = searchDetails.body.hits.hits
    if (payload.time_zone_name) {
      const csvDataList = await Promise.all(
        csvData.map(async object => {
          // Formatting createdAt date with optional user timezone
          const createdDate = payload.time_zone_name ? moment.tz(object._source.created_at, payload.time_zone_name) : moment(object._source.created_at)
          object._source.created_at = createdDate.format('DD-MM-YYYY HH:mm:ss')
          return object
        })
      )
    }
    if (csvData.length > 0) {
      tenantId = csvData[0]._source.tenant_id
      for (const txn of csvData) {

        let strBetIts = "";
        let strBetIdsArray = [];

        if (txn._source.betslip_details && txn._source.betslip_details.bets) {
          txn._source.betslip_details.bets.forEach((value, key) => {
                strBetIdsArray.push(value.bet_id);
            });
        }

        let strBetIds = '-';
        if (strBetIdsArray.length > 0) {
            strBetIds = strBetIdsArray.join(",")
        }

        let walletName = '-'
        if(txn._source.source_wallet){
           walletName = txn._source.source_wallet?.email + ' ' + txn._source.source_wallet?.first_name + ' ' + txn._source.source_wallet?.first_name;
        }
        if(!txn._source.source_wallet) {
            walletName = txn._source.target_wallet?.email + ' ' + txn._source.target_wallet?.first_name + ' ' + txn._source.target_wallet?.last_name;
        }
        let txnAmount = parseFloat(txn._source?.non_cash_amount ?? 0.00)
        txnAmount = parseFloat(txn._source?.amount) + txnAmount
        let object = {
          id: txn._source.internal_tracking_id,
          created_at: getDateInStringFormat(txn._source.created_at),
          bet_ids: strBetIds,
          market_id: txn._source.market_id ? txn._source.market_id : '-',
          team_name: txn._source.runner_name ? txn._source.runner_name : '-',
          journal_entry: txn._source.journal_entry ? txn._source.journal_entry : '-',
          wallet: walletName,
          currency: txn._source.source_currency || txn._source.target_currency,
          from_before_balance: (txn._source.source_wallet?.before_balance + txn._source.source_wallet?.non_cash_before_balance) || '0.00',
          from_after_balance: (txn._source.source_wallet?.after_balance + txn._source.source_wallet?.non_cash_after_balance) || '0.00',
          to_before_balance: (txn._source.target_wallet?.before_balance + txn._source.target_wallet?.non_cash_before_balance) || '0.00',
          to_after_balance: (txn._source.target_wallet?.after_balance + txn._source.target_wallet?.non_cash_after_balance) || '0.00',
          amount: txnAmount || '-',
          conversion_rate: txn._source.conversion_rate || '-',
          action_type: txn._source.transaction_type ? txn._source.transaction_type : '-',
          comments: txn._source.description || '-',
          action_by: (txn._source.user_id || '-') + '-' + (txn._source.player_details.email || '-') + ' ' + (txn._source.player_details.first_name || '-') + ' ' + (txn._source.player_details.last_name || '-')
        };

        mainArr = [...mainArr, object]
      }

      await Promise.all([csvWriter.writeRecords(mainArr)])
    }
  }


 // upload file to s3
 const s3Config = config.getProperties().s3
 const fileContent = await fs.promises.readFile(filePath);
 const key = `tenants/${tenantId}/csv/sport_trn/transaction_${uuid}.csv`

 const s3Params = {
   ACL: 'public-read',
   Bucket: s3Config.bucket,
   Key: key,
   Body: fileContent
 }
 const uploadedFile = await s3.upload(s3Params).promise()
 await db.ExportCsvCenter.update({
   csvUrl: key,
   status: EXPORT_CSV_STATUS.DONE
 },
   {
     where: { id: data.id }
   }
 )

 fs.unlink(filePath, (err) => {
   if (err) {
     throw err
   }
 });

  return true
} catch (error) {
  console.log("=======errorr", error)
  throw error
}
}
