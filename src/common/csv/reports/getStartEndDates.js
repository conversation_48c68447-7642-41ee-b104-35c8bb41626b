import moment from "moment";

// Set Monday as the starting day of a week
moment.updateLocale('en', {
  week : {
    dow : 1
  }
});

export default async function getStartEndDates (timePeriod) {
  let fromDate = '';
  let endDate = '';

  if (timePeriod == "yesterday") {
    const yesterday = moment().subtract(1, 'days').format("YYYY-MM-DD")
    fromDate = `${yesterday} 00:00:00`
    endDate = `${yesterday} 23:59:59`
  }

  if (timePeriod == "daily" || timePeriod == "today") {
    const today = moment().format("YYYY-MM-DD")
    fromDate = `${today} 00:00:00`
    endDate = `${today} 23:59:59`
  }

  if (timePeriod == "weekly") {
    const startweek = moment().isoWeekday(1).format("YYYY-MM-DD")
    const endweek = moment().isoWeekday(7).format("YYYY-MM-DD")
    fromDate = `${startweek} 00:00:00`
    endDate = `${endweek} 23:59:59`
  }

  if (timePeriod == "monthly") {
    const startMonth = moment().startOf('month').format("YYYY-MM-DD")
    const endMonth = moment().endOf('month').format("YYYY-MM-DD")
    fromDate = `${startMonth} 00:00:00`
    endDate = `${endMonth} 23:59:59`
  }
  return { start_date: fromDate, end_date: endDate }

}

export const getDateRange = (dateType, timeZone, customStartDate, customEndDate) => {
  let today
  try {
    today = moment.tz(timeZone).isValid() ? moment.tz(timeZone) : moment()
  } catch (err) {
    throw new Error('Invalid timeZone parameter')
  }

  let start, end

  switch (dateType) {
    case 'today':
      start = today.clone().startOf('day')
      end = today.clone().endOf('day')
      break

    case 'yesterday':
      start = today.clone().subtract(1, 'days').startOf('day')
      end = today.clone().subtract(1, 'days').endOf('day')
      break

    case 'weekly':
      start = today.clone().startOf('week')
      end = today.clone().endOf('week')
      break

    case 'monthly':
      start = today.clone().startOf('month')
      end = today.clone().endOf('month')
      break

    case 'lastMonth':
      start = today.clone().subtract(1, 'months').startOf('month')
      end = today.clone().subtract(1, 'months').endOf('month')
      break

    case 'custom':
      if (!customStartDate || !customEndDate) {
        throw new Error("Missing custom start and end date")
      }

      start = moment.tz(customStartDate, 'YYYY-MM-DD HH:mm:ss', timeZone)
      end = moment.tz(customEndDate, 'YYYY-MM-DD HH:mm:ss', timeZone)

      if (!start.isValid() || !end.isValid()) {
        throw new Error("Missing custom start and end date")
      }
      break

    default:
      throw new Error("Missing custom start and end date")
  }

  return { start, end }
}
