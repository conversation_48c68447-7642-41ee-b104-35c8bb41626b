import moment from 'moment';
import { QueryTypes } from 'sequelize';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3Config';
import { EXPORT_CSV_STATUS } from '../../constants';
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const fs = require('fs');

const CHUNK_SIZE = 1000;

// Function to fill empty fields with 'N/A'
const fillEmptyFields = (record) => {
  const fields = ['page', 'key', 'string'];

  fields.forEach(field => {
    if (record[field] === null || record[field] === undefined || record[field] === '') {
      record[field] = 'N/A';
    }
  });

  return record;
};

export default async (data) => {
  const payload = data.payload;

  await db.ExportCsvCenter.update(
    { status: EXPORT_CSV_STATUS.IN_PROGRESS },
    { where: { id: data.id } }
  );

  try {
    const csvUuid = uuidv4().replace(/-/g, '');
    const filePath = `/tmp/language_keys_${csvUuid}.csv`;

    const csvWriter = createCsvWriter({
      path: filePath,
      header: [
        { id: 'page', title: 'Page' },
        { id: 'key', title: 'Key' },
        { id: 'string', title: 'Example Translation' },
      ],
    });

    let offset = 0;
    let totalRecords = 0;
    let hasMoreRecords = true;

    while (hasMoreRecords) {
      let sql = `
        SELECT language_keys.page, language_keys.key, language_strings.string
        FROM language_keys
        LEFT JOIN language_strings
        ON language_keys.id = language_strings.language_key_id
        AND language_strings.tenant_id = 0
        AND language_strings.language_id = 1
        WHERE 1=1
      `;

      if (payload.search_page) {
        sql += ` AND language_keys.page = :searchPage`;
      }


      if (payload.languageKeyId) {
        sql += ` AND language_keys.id = :languageKeyId`;
      }
      if (payload.search_translation) {
        sql += ` AND language_strings.string = :search_translation`;
      }
        sql += `
        ORDER BY language_keys.page, language_keys.key
        LIMIT :limit OFFSET :offset
      `;

      const languageData = await sequelize.query(sql, {
        type: QueryTypes.SELECT,
        replacements: {
          tenantId: payload.tenant_id || 0,
          search_language: payload.search_language || 1,

          searchPage: payload.search_page || null,
          languageKeyId: payload.languageKeyId || null,
          search_translation: payload.search_translation || null,



          limit: CHUNK_SIZE,
          offset: offset,
        },
      });

      if (languageData.length < CHUNK_SIZE) {
        hasMoreRecords = false;
      }

      const processedData = languageData.map(fillEmptyFields);

      await csvWriter.writeRecords(processedData);

      totalRecords += languageData.length;
      offset += CHUNK_SIZE;
    }

    const s3Config = config.getProperties().s3;
    const fileContent = await fs.promises.readFile(filePath);
    const key = `tenants/0/csv/language_keys/language_keys_${csvUuid}.csv`;

    const s3Params = {
      ACL: 'public-read',
      Bucket: s3Config.bucket,
      Key: key,
      Body: fileContent,
    };

    const uploadedFile = await s3.upload(s3Params).promise();

    await db.ExportCsvCenter.update(
      {
        csvUrl: key,
        status: EXPORT_CSV_STATUS.DONE,
      },
      {
        where: { id: data.id },
      }
    );

    fs.unlink(filePath, (err) => {
      if (err) {
        throw err;
      }
    });

    return {
      totalRecords,
      csvUrl: key,
    };
  } catch (error) {
    throw error;
  }
};
