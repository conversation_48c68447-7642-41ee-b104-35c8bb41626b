import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import moment from 'moment';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { BOT_ALLOWED_TENANTS, EXPORT_CSV_STATUS } from '../../constants';
import { fetchAgentIds, formatAmount, getCurrencyById, getCurrencyByValue, getDateInStringFormat, getTransactionTypeByValueOrId } from '../../helpers';
import getRolesDetails from '../getAdminRolesDetail';
import { getColumnPermissions } from '../getPermissionsForPlayerCsv';
import { getDateRange } from './getStartEndDates';

// Define action types based on bonus types
const CASH_BONUS_ACTION_TYPES = [12, 17, 37, 42, 45, 52]
const NON_CASH_BONUS_ACTION_TYPES = [11, 15, 19, 39, 40, 41, 43, 44, 50, 51, 70, 71, 72, 73, 74]
const FREE_BETS_BONUS_ACTION_TYPES = [68, 75]
const SPORTS_FREE_BETS_BONUS_ACTION_TYPES = [69, 76]

export default async (data) => {
  try {
    const payload = data.payload;
    await db.ExportCsvCenter.update({ status: EXPORT_CSV_STATUS.IN_PROGRESS }, { where: { id: data.id } });

    let currentUser = { id: data.adminId, parentType: data.adminType };

    let isSuperAdmin = false;
    if (payload.timeZoneName === 'UTC +00:00') {
      payload.timeZoneName = 'UTC'
    }

    // A lifetime record is valid for only one user. Throw error if user filter is not added
    if (payload.lifetimeRecords && !payload.userId) {
      throw new Error('A lifetime record is valid for only one user!')
    }

    let userFilterApplied = false

    const inputParams = {
      tenantIds: [],
      startDate: '',
      endDate: '',
      agentIds: [],
      subAgentIds: [],
      currency: (payload.currency) ? getCurrencyByValue(payload.currency)?.id : null,
      actionType: [],
      transactionId: payload.transactionId,
      internalTrackingId: payload.internalTrackingId,
      userId: payload.userId
    }

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      })
      payload.tenantId = adminData.tenantId
    }

    // Tenant Filter Gathering
    const tenantId = Number(payload.tenantId)
    if (tenantId) {
      inputParams.tenantIds = [tenantId]
    } else if (data.adminType == 'SuperAdminUser') {
      const adminData = await db.SuperAdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['parentType', 'tenantIds']
      })
      if (adminData.parentType === 'Manager') {
        inputParams.tenantIds = adminData.tenantIds.map(id => parseInt(id));
      }
    }

    // Agent Filter Gathering
    payload.agentId = isNaN(payload.agentId) ? '' : Number(payload.agentId)

    let searchAgentId
    if (payload.agentId) {
      searchAgentId = payload.agentId
    } else if (data.adminType === 'AdminUser') {
      const roles = await getRolesDetails(currentUser.id)
      if (roles.includes('agent')) {
        searchAgentId = currentUser.id
      }
    }
    if (searchAgentId) {
      inputParams.agentIds = await fetchAgentIds(searchAgentId)
    }

    if (data.adminType == "AdminUser") {
      const currentUserRole = await getRolesDetails(data.adminId);
      if (currentUserRole.includes("agent")) {
        inputParams.subAgentIds = await fetchAgentIds(data.adminId);
      }
    }

    // Time Filter Gathering
    // Find user's registration date if lifetime records is true
    if (payload.lifetimeRecords) {
      const regDate = await db.User.findOne({
        where: { id: payload.userId },
        attributes: ['id', 'createdAt'],
        raw: true
      })
      inputParams.startDate = moment(regDate.createdAt).format('YYYY-MM-DD HH:mm:ss').concat('.000000')
      inputParams.endDate = moment().add(5, 'minutes').format('YYYY-MM-DD HH:mm:ss').concat('.999999')
    } else {
      const { start, end } = getDateRange(payload.timeType, payload.timeZoneName, payload.timePeriod.start_date, payload.timePeriod.end_date)
      inputParams.startDate = start.utc().format('YYYY-MM-DD HH:mm:ss').concat('.000000')
      inputParams.endDate = end.utc().format('YYYY-MM-DD HH:mm:ss').concat('.999999')
    }

    // Action Type Filter Gathering
    const actionTypeArr = payload.actionType || []
    if (actionTypeArr.length) {
      inputParams.actionType = await getTransactionTypeByValueOrId(actionTypeArr)
    } else if (payload.bonusType) {
      switch (payload.bonusType) {
        case 'cash': inputParams.actionType = CASH_BONUS_ACTION_TYPES; break
        case 'non_cash': inputParams.actionType = NON_CASH_BONUS_ACTION_TYPES; break
        case 'free_bets': inputParams.actionType = FREE_BETS_BONUS_ACTION_TYPES; break
        case 'sports_free_bets': inputParams.actionType = SPORTS_FREE_BETS_BONUS_ACTION_TYPES; break
        default: inputParams.actionType = [...CASH_BONUS_ACTION_TYPES, ...NON_CASH_BONUS_ACTION_TYPES, ...FREE_BETS_BONUS_ACTION_TYPES, ...SPORTS_FREE_BETS_BONUS_ACTION_TYPES]
      }
    } else {
      inputParams.actionType = [...CASH_BONUS_ACTION_TYPES, ...NON_CASH_BONUS_ACTION_TYPES, ...FREE_BETS_BONUS_ACTION_TYPES, ...SPORTS_FREE_BETS_BONUS_ACTION_TYPES]
    }

    const txsUserDataCteFilters = []
    const ncbTxsCteFilters = []

    // Check SBO User Logic
    isSuperAdmin = currentUser.parentType === 'SuperAdminUser'
    let botUsersFilterType = 'all'
    let botWalletIds = []
    if (
      (tenantId && BOT_ALLOWED_TENANTS.includes(String(tenantId))) ||
      isSuperAdmin
    ) {
      // In case of super admin, do not check SBO request flag. Just check player category flag.
      if (
        isSuperAdmin ||
        (checkSboRequest === CHECK_SBO_DOMAIN[1] && checkSboRequest !== CHECK_SBO_DOMAIN[2])
      ) {
        if (payload.playerCategory === 'bot_players') {
          botUsersFilterType = 'bot'
        } else if (payload.playerCategory === 'real_players') {
          botUsersFilterType = 'real'
        }
      } else if (!checkSboRequest) {
        botUsersFilterType = 'real'
      }

      if (botUsersFilterType !== 'all') {
        // Find all bot users wallet Id
        botWalletIds = await sequelize.query(`
          SELECT
            w.id AS wallet_id
          FROM
            bot_users bu
            JOIN wallets w ON (w.owner_id = bu.user_id AND w.owner_type = 'User')
          ${tenantId ? 'WHERE bu.tenant_id = :tenantId' : ''}
        `, {
          type: sequelize.QueryTypes.SELECT,
          replacements: { tenantId }
        })
        botWalletIds = botWalletIds.map(rec => rec.wallet_id)

        if (botWalletIds.length > 0) {
          if (botUsersFilterType === 'bot') {
            ncbTxsCteFilters.push(`(source_wallet_id IN (${botWalletIds}) OR target_wallet_id IN (${botWalletIds}))`)
          } else if (botUsersFilterType === 'real') {
            ncbTxsCteFilters.push(`COALESCE(source_wallet_id, 0) NOT IN (${botWalletIds}) AND COALESCE(target_wallet_id, 0) NOT IN (${botWalletIds})`)
          }
        }
      }
    }

    // User Id Filter
    if (payload.userId && !isNaN(payload.userId)) {
      let agentCond = ''
      if (inputParams.agentIds.length > 0) {
        agentCond = 'u.parent_id IN (:agentIds)'
      }
      if (inputParams.subAgentIds.length > 0) {
        agentCond = 'u.parent_id IN (:subAgentIds)' // Extra check in case of agent login to validate if the payload agent Id should be from his sub-agents
      }
      const botUserCondMap = {
        bot: 'bu.id IS NOT NULL',
        real: 'bu.id IS NULL',
        all: ''
      }
      const userData = await sequelize.query(`
        SELECT
          w.id AS wallet_id
        FROM
          wallets w
          JOIN users u ON (w.owner_id = u.id)
          LEFT JOIN bot_users bu ON (bu.user_id = w.owner_id)
        WHERE
          w.owner_id = :userId
          AND w.owner_type = 'User'
          ${agentCond ? 'AND ' + agentCond : ''}
          ${botUserCondMap[botUsersFilterType] ? 'AND ' + botUserCondMap[botUsersFilterType] : ''}
      `, {
        type: sequelize.QueryTypes.SELECT,
        replacements: { userId: payload.userId, agentIds: inputParams.agentIds, subAgentIds: inputParams.subAgentIds }
      })
      if (!userData || userData.length <= 0) {
        throw new Error('Data not found!');
      }
      ncbTxsCteFilters.push(`(source_wallet_id = '${userData[0].wallet_id}' OR target_wallet_id = '${userData[0].wallet_id}')`)
      userFilterApplied = true
    }

    if (inputParams.startDate && inputParams.endDate) {
      ncbTxsCteFilters.push('created_at BETWEEN :startDate AND :endDate')
    }
    if (inputParams.currency) {
      ncbTxsCteFilters.push('(source_currency_id = :currency OR target_currency_id = :currency)')
    }
    if (payload.transactionId) {
      ncbTxsCteFilters.push('transaction_id = :transactionId')
    }
    if (payload.internalTrackingId) {
      ncbTxsCteFilters.push('id = :internalTrackingId')
    }
    if (inputParams.actionType.length > 0) {
      ncbTxsCteFilters.push('transaction_type IN (:actionType)')
    }
    if (!userFilterApplied && inputParams.agentIds.length > 0) {
      let agentFilter
      if (inputParams.subAgentIds.length > 0) {
        agentFilter = '(u.parent_id IN (:agentIds) AND u.parent_id IN (:subAgentIds))' // Extra check in case of agent login to validate if the payload agent Id should be from his sub-agents
      } else {
        agentFilter = 'u.parent_id IN (:agentIds)'
      }
      txsUserDataCteFilters.push(agentFilter)
    } else if (!userFilterApplied && inputParams.subAgentIds.length > 0) {
      const agentFilter = 'u.parent_id IN (:subAgentIds)'
      txsUserDataCteFilters.push(agentFilter)
    }
    if (inputParams.tenantIds.length > 0) {
      if (!userFilterApplied) {
        ncbTxsCteFilters.push('tenant_id IN (:tenantIds)')
      }
    }

    const txsUserDataCteWhereCondition = txsUserDataCteFilters.length ? `WHERE ${txsUserDataCteFilters.join(' AND ')}` : ''
    const ncbTxsCteWhereCondition = ncbTxsCteFilters.length ? `WHERE ${ncbTxsCteFilters.join(' AND ')}` : ''

    const sort = payload.sortBy || 'created_at'
    const sortOrder = payload.order || 'DESC'
    let sortCondition = ''
    let sortConditionWithAlias = ''
    if (sort && sortOrder) {
      sortCondition = 'ORDER BY ' + sort + ' ' + sortOrder
      sortConditionWithAlias = 'ORDER BY t.' + sort + ' ' + sortOrder
    }

    // CSV column permissions
    const columnKeyNameMap = {
      user_id: "Player ID",
      user_name: "User Name",
      agent_name: "Agent Name",
      created_at: "Date & Time",
      action_type: "Action Type",
      currency: "Currency",
      before_balance: "Initial Bonus Balance",
      amount: "Amount",
      after_balance: "Ending Bonus Balance",
      transaction_id: "Transaction ID",
      internal_tracking_id: "Internal Tracking ID",
    };

    const columnKeyPermissions = {
      user_id: ["report_attributes", "player_id"],
      user_name: ["players_key", "user_name"],
      agent_name: ["players_key", "agent_details"],
      created_at: ["report_attributes", "timestamp"],
      action_type: ["transaction_attributes", "action_type"],
      currency: ["report_attributes", "currency"],
      before_balance: ["players_key", "before_balance"],
      amount: ["players_key", "total_balance"],
      after_balance: ["players_key", "after_balance"],
      transaction_id: ["transaction_attributes", "transaction_id"],
      internal_tracking_id: ["report_attributes", "internal_tracking_id"],
    };

    const allowedColumns = await getColumnPermissions(columnKeyPermissions, data.adminId, inputParams.tenantIds[0], data.adminType);
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    const chunkSize = 5000
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3

    let awsTenantFolderName = 0;
    if (inputParams.tenantIds.length === 1) {
      awsTenantFolderName = inputParams.tenantIds[0];
    }

    const uuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${awsTenantFolderName}/csv/ncb_reports/report_${uuid}.csv`

    const csvStringifier = createObjectCsvStringifier({
      header: allowedColumns.map(columnKey => ({ id: columnKey, title: columnKeyNameMap[columnKey] }))
    })

    const allowedColumnsObj = {};
    allowedColumns.forEach(c => allowedColumnsObj[c] = true);

    // Get count
    const ncbReportCntQuery = prepareNcbReportCountQuery({
      ncbTxsCteWhereCondition,
      txsUserDataCteWhereCondition
    })
    const totalRecords = await sequelize.query(ncbReportCntQuery, { type: sequelize.QueryTypes.SELECT, useMaster: false, replacements: inputParams });
    const totalRecordsCount = totalRecords[0]?.total_count || 0
    const totalChunks = Math.ceil(totalRecordsCount / chunkSize)
    // Loop through each chunk
    for (let i = 0; i < totalChunks; i++) {
      const offset = i * chunkSize
      const paginationCondition = 'LIMIT ' + chunkSize + ' OFFSET ' + offset
      // Query preparation and fetch results
      const ncbReportQuery = prepareNcbReportQuery({
        ncbTxsCteWhereCondition,
        txsUserDataCteWhereCondition,
        sortCondition,
        paginationCondition,
        sortConditionWithAlias,
        userFilterApplied
      })
      const reportData = await sequelize.query(ncbReportQuery, { type: sequelize.QueryTypes.SELECT, useMaster: false, replacements: inputParams });

      let mainArr = []
      if (reportData.length > 0) {
        for (const txn of reportData) {
          txn.created_at = moment.utc(txn.created_at).tz(payload.timeZoneName).format('YYYY-MM-DD HH:mm:ss');
          txn.action_type = await getTransactionTypeByValueOrId(txn.transaction_type);
          txn.currency = getCurrencyById(parseInt(txn.currency_id || 0))?.value || null;
          const object = {
            user_id: txn.user_id,
            user_name: txn.user_name,
            agent_name: txn.agent_name,
            created_at: getDateInStringFormat(txn.created_at || ''),
            action_type: txn.action_type,
            currency: txn.currency,
            before_balance: formatAmount(txn.before_balance, tenantId, txn.currency) || '',
            amount: formatAmount(txn.amount, tenantId, txn.currency),
            after_balance: formatAmount(txn.after_balance, tenantId, txn.currency) || '',
            transaction_id: txn.transaction_id || '',
            internal_tracking_id: txn.internal_tracking_id,
          }
          mainArr = [...mainArr, object]
        }
      }

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(mainArr)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }

      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      await delay(1000)
    }
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )
    return true
  } catch (error) {
    console.log('=======errorr', error)
    throw error
  }
};

function prepareNcbReportQuery ({
  ncbTxsCteWhereCondition,
  txsUserDataCteWhereCondition,
  sortCondition,
  paginationCondition,
  sortConditionWithAlias,
  userFilterApplied
}) {
  let txsUserDataCte = ''
  let finalCte = 'ncb_txs'

  let walletsJoin = "LEFT JOIN wallets w ON (w.id = t.user_wallet_id AND owner_type = 'User')"
  let usersJoin = 'LEFT JOIN users u ON (w.owner_id = u.id)'
  let userIdAttr = 'u.id AS user_id,'
  let userNameAttr = 'u.user_name AS user_name,'

  // Add the UNION ALL for the User Filter so that Postgres Query Planner uses the Source and Target Wallet ID related Indexes for Fast Result Across the Multiple Partitions.
  let userFilterIndexHelper = ''
  if (userFilterApplied) {
    userFilterIndexHelper = INDEXING_HELPER_USER_FILTER_QUERY + '\n' + 'UNION ALL' + '\n'
  }

  if (txsUserDataCteWhereCondition) {
    finalCte = 'txs_user_data'

    walletsJoin = ''
    usersJoin = ''
    userIdAttr = 't.user_id,'
    userNameAttr = 't.user_name,'

    txsUserDataCte = `
      , txs_user_data AS (
        SELECT
          t.*,
          u.id AS user_id,
          u.user_name AS user_name,
          u.parent_id AS parent_id,
          w.owner_id
        FROM
          ncb_txs t
          LEFT JOIN wallets w ON (w.id = t.user_wallet_id AND owner_type = 'User')
          LEFT JOIN users u ON (w.owner_id = u.id)
        ${txsUserDataCteWhereCondition}
        ${sortConditionWithAlias}
        ${paginationCondition}
      )
    `
    paginationCondition = ''
    sortCondition = ''
  }

  return `
  SET enable_seqscan = OFF;

  WITH ncb_txs AS (
    ${userFilterIndexHelper}
    SELECT
      id,
      source_currency_id,
      target_currency_id,
      transaction_id,
      amount,
      source_before_balance,
      target_before_balance,
      source_after_balance,
      target_after_balance,
      transaction_type,
      created_at,
      source_wallet_id,
      CASE
        WHEN transaction_type IN (39, 40, 41, 42, 43, 50, 51, 73) THEN source_wallet_id
        WHEN transaction_type IN (11, 12, 15, 17, 19, 37, 44, 45, 52, 68, 69, 70, 71, 72, 74) THEN target_wallet_id
        ELSE COALESCE(target_wallet_id, source_wallet_id)
      END AS user_wallet_id
    FROM transactions t
    ${ncbTxsCteWhereCondition}
    ${sortCondition}
    ${paginationCondition}
  )
  ${txsUserDataCte}
    SELECT
      ${userIdAttr}
      ${userNameAttr}
      adu.agent_name,
      t.created_at::text AS created_at,
      t.transaction_type,
      t.amount,
      t.transaction_id,
      t.id as internal_tracking_id,
      t.source_currency_id,
      t.target_currency_id,
      CASE
        WHEN t.transaction_type IN (39, 40, 41, 42, 43, 50, 51, 73) THEN t.source_before_balance::numeric(20,2)
        WHEN t.transaction_type IN (11, 12, 15, 17, 19, 37, 44, 45, 52, 68, 69, 70, 71, 72, 74) THEN t.target_before_balance::numeric(20,2)
        WHEN t.source_wallet_id IS NULL THEN t.target_before_balance::numeric(20,2)
        ELSE t.source_before_balance::numeric(20,2)
      END AS before_balance,
      CASE
        WHEN t.transaction_type IN (39, 40, 41, 42, 43, 50, 51, 73) THEN t.source_after_balance::numeric(20,2)
        WHEN t.transaction_type IN (11, 12, 15, 17, 19, 37, 44, 45, 52, 68, 69, 70, 71, 72, 74) THEN t.target_after_balance::numeric(20,2)
        WHEN t.source_wallet_id IS NULL THEN t.target_after_balance::numeric(20,2) -- For transaction type (10)
        ELSE t.source_after_balance::numeric(20,2)
      END AS after_balance,
      CASE
        WHEN t.transaction_type IN (39, 40, 41, 42, 43, 50, 51, 73) THEN t.source_currency_id
        WHEN t.transaction_type IN (11, 12, 15, 17, 19, 37, 44, 45, 52, 68, 69, 70, 71, 72, 74) THEN t.target_currency_id
        ELSE COALESCE(t.source_currency_id, t.target_currency_id)
      END AS currency_id
    FROM ${finalCte} t
      ${walletsJoin}
      ${usersJoin}
      LEFT JOIN admin_users adu ON (adu.id = ${usersJoin ? 'u.parent_id' : 't.parent_id'})
    ${sortConditionWithAlias};

    SET enable_seqscan = ON;
  `
}

function prepareNcbReportCountQuery ({
  ncbTxsCteWhereCondition,
  txsUserDataCteWhereCondition
}) {
  let finalQ = ''
  // When agent filter is not applied
  if (!txsUserDataCteWhereCondition) {
    finalQ = `
      SET enable_seqscan = OFF;

      SELECT COUNT(*) AS total_count
      FROM transactions
      ${ncbTxsCteWhereCondition};

      SET enable_seqscan = ON;
    `
  } else {
    finalQ = `
      SET enable_seqscan = OFF;

      WITH ncb_txs AS (
        SELECT
          CASE
            WHEN transaction_type IN (39, 40, 41, 42, 43, 50, 51, 73) THEN source_wallet_id
            WHEN transaction_type IN (11, 12, 15, 17, 19, 37, 44, 45, 52, 68, 69, 70, 71, 72, 74) THEN target_wallet_id
            ELSE COALESCE(target_wallet_id, source_wallet_id)
          END AS user_wallet_id
        FROM transactions
        ${ncbTxsCteWhereCondition}
      )
      SELECT
        COUNT(*) AS total_count
      FROM ncb_txs t
        LEFT JOIN wallets w ON (w.id = t.user_wallet_id AND owner_type = 'User')
        LEFT JOIN users u ON (w.owner_id = u.id)
      ${txsUserDataCteWhereCondition};

      SET enable_seqscan = ON;`
  }
  return finalQ
}

const INDEXING_HELPER_USER_FILTER_QUERY =
`
SELECT
  id,
  source_currency_id,
  target_currency_id,
  transaction_id,
  amount,
  source_before_balance,
  target_before_balance,
  source_after_balance,
  target_after_balance,
  transaction_type,
  created_at,
  source_wallet_id,
  1 AS user_wallet_id
FROM
  transactions
WHERE false
`
