import { PROD_TENANTS_USING_NEW_NGR_FORMULA, STAGE_TENANTS_USING_NEW_NGR_FORMULA } from "../../constants";

export function getTenantWiseRealTimeQuery (params) {
  let query = `
    WITH txs AS (
        SELECT
            amount,
            CASE
                WHEN transaction_type IN (0,8,20,21,23,24,27,28,32,34,36,46,54,55,56,61,62,64,66) THEN 'bet'
                WHEN transaction_type IN (1,9,22,33,35,31,29,30,25,26,53,57,58,63,65,67) THEN 'win'
                WHEN transaction_type IN (2,10,47) AND target_wallet_id IS NOT NULL THEN 'bet_refund'
                WHEN transaction_type IN (2,10,47) AND source_wallet_id IS NOT NULL THEN 'win_refund'
                WHEN transaction_type IN (5,11,12,15,17,37,41,44,48,52,59,68,69,70,71,72,74,75,76) THEN 'bonus_claim'
                WHEN transaction_type IN (6,39,40,42,43,49,50,51,60,73) THEN 'bonus_withdraw'
                WHEN transaction_type IN (45) THEN 'royalty_cash_bonus'
                WHEN transaction_type IN (3) THEN
                    CASE
                        WHEN (actionee_type = 'AdminUser' AND COALESCE(comments, '') != 'Deposit Request Approved' AND COALESCE(payment_method, '') = 'manual') THEN 'cash_deposit_by_admin'
                        WHEN (COALESCE(comments, '') = 'Deposit Request Approved') THEN 'manual_cash_deposit_by_user'
                        WHEN (COALESCE(comments, '') = 'Deposit Request') THEN 'gateway_cash_deposit_by_user'
                        ELSE 'unknown'
                    END
                WHEN transaction_type IN (4) THEN
                    CASE
                        WHEN (COALESCE(comments, '') = 'approved by admin') THEN 'manual_cash_withdraw_by_user'
                        WHEN (COALESCE(comments, '') = 'Approved By Payment gateway') THEN 'gateway_cash_withdraw_by_user'
                        WHEN (status = 'success' AND actionee_type = 'AdminUser' AND COALESCE(comments, '') NOT IN ('approved by admin', 'cancelled by admin', 'Approved By Payment gateway')) THEN 'cash_withdraw_by_admin'
                        ELSE 'unknown'
                    END
                ELSE 'unknown'
            END AS type,
            CASE
                WHEN transaction_type IN (0,4,6,8,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,60,61,62,64,66,73) THEN source_wallet_id
                WHEN transaction_type IN (1,3,5,9,11,12,15,17,22,25,26,29,30,31,33,35,37,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_wallet_id
                WHEN transaction_type IN (2,10,47) AND target_wallet_id IS NOT NULL THEN target_wallet_id
                WHEN transaction_type IN (2,10,47) AND source_wallet_id IS NOT NULL THEN source_wallet_id
                ELSE COALESCE(target_wallet_id, source_wallet_id)
            END AS user_wallet_id
        FROM transactions
        WHERE ${params.date} ${params.tenant_filter}
        AND (transaction_type NOT IN (5,11,12,15,17,37,41,44,48,52,59,68,69,70,71,72,74,75,76) OR status = 'success')  -- success status for bonus claim type
        ${params.action_filter}
        ${params.game_provider}
        ${params.game_type}

        UNION ALL

        -- some transaction types are used in more than one types. add them using union all.
        -- for example, transaction type 3 is added in 'bonus claim' as well as 'non cash by admin' also.
        SELECT
            CASE
                WHEN ts.transaction_type IN (31,33) THEN (tsm.meta_data->>'commissionAmount')::double precision
                ELSE ts.amount
            END AS amount,
            CASE
                WHEN ts.transaction_type IN (5) THEN 'non_cash_granted_by_admin'
                WHEN ts.transaction_type IN (6) THEN 'non_cash_withdraw_by_admin'
                WHEN ts.transaction_type IN (48) THEN 'one_time_bonus_deposit'
                WHEN ts.transaction_type IN (49) THEN 'one_time_bonus_withdraw'
                WHEN ts.transaction_type IN (44) THEN 'royalty_non_cash_bonus'
                WHEN ts.transaction_type IN (31,33) THEN 'commission'
                WHEN ts.transaction_type IN (59) THEN 'sports_free_bet_deposit'
            END AS type,
            CASE
                WHEN ts.transaction_type IN (5,31,33,44,48,59) THEN target_wallet_id
                WHEN ts.transaction_type IN (6,49) THEN source_wallet_id
            END AS user_wallet_id
        FROM transactions ts
          LEFT JOIN transactions_meta_data tsm ON (ts.transaction_type IN (31,33) AND ts.id = tsm.transaction_id)
        WHERE ${params.date.replace('created_at', 'ts.created_at')} ${params.tenant_filter.replace('tenant_id', 'ts.tenant_id')}
        AND ts.transaction_type IN (5,6,31,33,44,48,49,59)
        ${params.action_filter_comission.replace('transaction_type', 'ts.transaction_type')}
        ${params.game_provider}
        ${params.game_type}

        UNION ALL

        -- different condition for total withdraw.
        select
            amount,
            'total_withdraw' as type,
            source_wallet_id as user_wallet_id
        from transactions
            where  ${params.date}  ${params.tenant_filter}
            and transaction_type in (4,6,49,60)
          ${params.game_provider}
           ${params.game_type}
            and status = 'success'
                and (comments is null or comments not in ('cancelled by admin', 'cancelled by the player', 'Pending confirmation from admin'))

        UNION ALL

        -- different condition for transaction type deposit.
        select
            amount,
            'transaction_type_deposit' as type,
            target_wallet_id as user_wallet_id
        from transactions
        where  ${params.date}  ${params.tenant_filter}
        and transaction_type in (3)
        ${params.game_provider}
        ${params.game_type}
         ${params.add_noncash_sql}

    ), txs_type AS (
      SELECT
          txs.*,
          w.owner_id AS user_id
      FROM txs
      JOIN wallets w ON (
          w.owner_type = 'User' AND
          w.id = txs.user_wallet_id
      )
      JOIN users u ON (u.id = w.owner_id)
      WHERE txs.type != 'unknown'
      ${params.currency_filter}
      ${params.agent_ids}
      ${params.user_filter}
    ),
    agg_data AS (
      SELECT
          ${params.bot_select}
          txs.user_id AS user_id,
          txs.type AS type,
          SUM(txs.amount)::numeric(20,5) AS amount,
          COUNT(*) AS count
      FROM txs_type txs
      ${params.bot_sql}
      ${params.bot_user_id_check}
      GROUP BY
          txs.user_id,
          txs.type
    ),
    agg_data_user_wise AS (
        SELECT
            ${params.bot_user}
            t.user_id AS user_id,
            COUNT(*) OVER() AS total_count,
            MIN(u.user_name) AS user_name,
            MIN(u.id) AS id,
            MIN(u.email) AS email,
            MIN(u.phone) AS phone,
            MIN(u.vip_level) AS vip_level,
            MIN(u.affiliated_data) AS affiliated_data,
            MIN((u.created_at::timestamp AT TIME ZONE 'UTC' at time zone '${params.timezone}')) AS created_at,
            MIN(u.tenant_id) AS tenant_id,
            MIN(w.currency_id) AS currency_id,
            MIN(u.parent_id) AS agent_id,
            MIN(au.agent_name) AS agent_name,
            MIN(c.code) AS code,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'cash_deposit_by_admin'), 0) AS total_cash_deposit_by_admin,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'royalty_cash_bonus'), 0) AS loyalty_cash_bonus,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'royalty_non_cash_bonus'), 0) AS loyalty_non_cash_bonus,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'non_cash_granted_by_admin'), 0) AS total_non_cash_deposit_by_admin,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'manual_cash_deposit_by_user'), 0) AS total_manual_cash_deposit_by_user,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'gateway_cash_deposit_by_user'), 0) AS total_cash_deposit_gateway_by_user,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'non_cash_withdraw_by_admin'), 0) AS total_non_cash_withdraw_by_admin,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'manual_cash_withdraw_by_user'), 0) AS total_manual_cash_withdraw_by_user,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'gateway_cash_withdraw_by_user'), 0) AS total_cash_gateway_withdraw_by_user,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'cash_withdraw_by_admin'), 0) AS total_cash_withdraw_by_admin,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'total_withdraw'), 0) AS total_withdraws,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'one_time_bonus_deposit'), 0) AS total_one_time_bonus_deposit,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'one_time_bonus_withdraw'), 0) AS total_one_time_bonus_withdraw,
            -- COALESCE(MAX(t.count) FILTER (WHERE type = 'total_withdraw'), 0) AS withdraw_count,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'commission'), 0) AS commission,
            -- COALESCE(MIN(t.amount) FILTER (WHERE type = 'bet'), 0) AS bet,
            -- COALESCE(MIN(t.amount) FILTER (WHERE type = 'bonus_claim'), 0) AS bonus_claim,
            -- COALESCE(MIN(t.amount) FILTER (WHERE type = 'bonus_withdraw'), 0) AS bonus_withdraw,
            -- COALESCE(MAX(t.count) FILTER (WHERE type = 'bet'), 0) AS total_bet_count_only,
            -- COALESCE(MIN(t.amount) FILTER (WHERE type = 'win'), 0) AS win,
            -- COALESCE(MIN(t.count) FILTER (WHERE type = 'win'), 0) AS win_count,
            -- COALESCE(MIN(t.amount) FILTER (WHERE type = 'win_refund'), 0) AS win_refund,
            -- COALESCE(MIN(t.amount) FILTER (WHERE type = 'bet_refund'), 0) AS bet_refund,
            COALESCE(MIN(t.amount) FILTER (WHERE type = 'transaction_type_deposit'), 0) AS transaction_type_deposit,
            COALESCE(SUM(t.amount) FILTER (WHERE type IN ('transaction_type_deposit', 'non_cash_granted_by_admin', 'one_time_bonus_deposit', 'sports_free_bet_deposit')), 0) AS total_deposit,
            COALESCE(SUM(t.count) FILTER (WHERE type IN ('transaction_type_deposit', 'non_cash_granted_by_admin', 'one_time_bonus_deposit', 'sports_free_bet_deposit')), 0) AS deposit_count,
            COALESCE(SUM(CASE WHEN type = 'win' THEN t.amount ELSE 0 END), 0) - COALESCE(SUM(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END), 0) AS win_after_refund,
            -- COALESCE(SUM(CASE WHEN type = 'win' THEN t.count ELSE 0 END), 0) - COALESCE(SUM(CASE WHEN type = 'win_refund' THEN t.count ELSE 0 END), 0) AS win_after_refund_count,
            COALESCE(SUM(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END), 0) - COALESCE(SUM(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END), 0) AS bet_after_refund,
            COALESCE(SUM(CASE WHEN type = 'bet' THEN t.count ELSE 0 END), 0) - COALESCE(SUM(CASE WHEN type = 'bet_refund' THEN t.count ELSE 0 END), 0) AS bet_after_refund_count,
            COALESCE(SUM(CASE WHEN type = 'bonus_claim' THEN t.amount ELSE 0 END), 0) - COALESCE(SUM(CASE WHEN type = 'bonus_withdraw' THEN t.amount ELSE 0 END), 0) AS bonus,
            ${params.ggr_cal},
            ${params.ngr_cal}
        FROM agg_data t
        JOIN users u ON t.user_id = u.id
        JOIN admin_users au ON au.id = u.parent_id
        JOIN wallets w ON (
            w.owner_type = 'User' AND
            w.owner_id = t.user_id
        )
        JOIN currencies c ON w.currency_id = c.id
        GROUP BY t.user_id
        ${params.sports_filter}
        ${params.ggr_ngr_filter}

        ORDER BY ${params.sort_by} ${params.order}
    )
    SELECT
        s.*
    FROM
        agg_data_user_wise s
  `;

  return query;
}


export function calculateNGRForTenantRealtime (tenantId, date, tenantFilter, actionFilter, gameProvider, gameType) {
const isProduction = process.env.NODE_ENV === 'production'
const tenantIds = isProduction ? PROD_TENANTS_USING_NEW_NGR_FORMULA : STAGE_TENANTS_USING_NEW_NGR_FORMULA
const id =  Number(Array.isArray(tenantId) ? tenantId[0] : tenantId);;
  const newNGR = {
    ngrCal: `((COALESCE(SUM(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END), 0)
                - COALESCE(SUM(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END), 0))
                - (COALESCE(SUM(CASE WHEN type = 'win' THEN t.amount ELSE 0 END), 0)
                - COALESCE(SUM(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END), 0)))
                - ((COALESCE(SUM(CASE WHEN type = 'bet_non_cash' THEN t.amount ELSE 0 END), 0)
                - COALESCE(SUM(CASE WHEN type = 'bet_refund_non_cash' THEN t.amount ELSE 0 END), 0))
                - (COALESCE(SUM(CASE WHEN type = 'win_non_cash' THEN t.amount ELSE 0 END), 0)
                - COALESCE(SUM(CASE WHEN type = 'win_refund_non_cash' THEN t.amount ELSE 0 END), 0))) as ngr`,

    ngrSuperCal: `((COALESCE(SUM(CASE WHEN type = 'bet' THEN t.amount_euro ELSE 0 END), 0)
                     - COALESCE(SUM(CASE WHEN type = 'bet_refund' THEN t.amount_euro ELSE 0 END), 0))
                     - (COALESCE(SUM(CASE WHEN type = 'win' THEN t.amount_euro ELSE 0 END), 0)
                     - COALESCE(SUM(CASE WHEN type = 'win_refund' THEN t.amount_euro ELSE 0 END), 0)))
                     - ((COALESCE(SUM(CASE WHEN type = 'bet_non_cash' THEN t.amount ELSE 0 END), 0)
                     - COALESCE(SUM(CASE WHEN type = 'bet_refund_non_cash' THEN t.amount ELSE 0 END), 0))
                     - (COALESCE(SUM(CASE WHEN type = 'win_non_cash' THEN t.amount ELSE 0 END), 0)
                     - COALESCE(SUM(CASE WHEN type = 'win_refund_non_cash' THEN t.amount ELSE 0 END), 0))) as ngr_in_euro`,

    addSql: `UNION ALL
              SELECT
                  amount,
                  CASE
                      WHEN transaction_type IN (8,20,23,27,46,55,61,62,64,66) THEN 'bet_non_cash'
                      WHEN transaction_type IN (9,25,29,57,63,65,67) THEN 'win_non_cash'
                      WHEN transaction_type IN (10,47) AND target_wallet_id IS NOT NULL THEN 'bet_refund_non_cash'
                      WHEN transaction_type IN (10,47) AND source_wallet_id IS NOT NULL THEN 'win_refund_non_cash'
                  END AS type,
                  CASE
                      WHEN transaction_type IN (8,20,23,27,46,55,61,62,64,66) THEN source_wallet_id
                      WHEN transaction_type IN (9,25,29,57,63,65,67) THEN target_wallet_id
                      WHEN transaction_type IN (10,47) AND target_wallet_id IS NOT NULL THEN target_wallet_id
                      WHEN transaction_type IN (10,47) AND source_wallet_id IS NOT NULL THEN source_wallet_id
                  END AS user_wallet_id
              FROM transactions
              WHERE ${date}
              ${tenantFilter}
              AND transaction_type IN (8,9,10,20,23,25,27,29,46,47,55,57,61,62,63,64,65,66,67)
              ${actionFilter}
              ${gameProvider}
              ${gameType}`,

    addSuperNgrSql: `UNION ALL
              SELECT
                  (other_currency_amount::jsonb ->> 'EUR')::numeric(20,5) AS amount_euro,
                  amount,
                  CASE
                      WHEN transaction_type IN (8,20,23,27,46,55,61,62,64,66) THEN 'bet_non_cash'
                      WHEN transaction_type IN (9,25,29,57,63,65,67) THEN 'win_non_cash'
                      WHEN transaction_type IN (10,47) AND target_wallet_id IS NOT NULL THEN 'bet_refund_non_cash'
                      WHEN transaction_type IN (10,47) AND source_wallet_id IS NOT NULL THEN 'win_refund_non_cash'
                  END AS type,
                  CASE
                      WHEN transaction_type IN (8,20,23,27,46,55,61,62,64,66) THEN source_wallet_id
                      WHEN transaction_type IN (9,25,29,57,63,65,67) THEN target_wallet_id
                      WHEN transaction_type IN (10,47) AND target_wallet_id IS NOT NULL THEN target_wallet_id
                      WHEN transaction_type IN (10,47) AND source_wallet_id IS NOT NULL THEN source_wallet_id
                  END AS user_wallet_id
              FROM transactions
              WHERE ${date}
              ${tenantFilter}
              AND transaction_type IN (8,9,10,20,23,25,27,29,46,47,55,57,61,62,63,64,65,66,67)
              ${actionFilter}
              ${gameProvider}
              ${gameType}`
  };

  const oldNGR = {
    ngrCal: `((COALESCE(SUM(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END),0)
                - COALESCE(SUM(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END),0))
                - (COALESCE(SUM(CASE WHEN type = 'win' THEN t.amount ELSE 0 END),0)
                - COALESCE(SUM(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END),0)))
                - (COALESCE(SUM(CASE WHEN type = 'bonus_claim' THEN t.amount ELSE 0 END),0)
                - COALESCE(SUM(CASE WHEN type = 'bonus_withdraw' THEN t.amount ELSE 0 END),0)) as ngr`,

    ngrSuperCal: `((COALESCE(SUM(CASE WHEN type = 'bet' THEN t.amount_euro ELSE 0 END),0)
                     - COALESCE(SUM(CASE WHEN type = 'bet_refund' THEN t.amount_euro ELSE 0 END),0))
                     - (COALESCE(SUM(CASE WHEN type = 'win' THEN t.amount_euro ELSE 0 END),0)
                     - COALESCE(SUM(CASE WHEN type = 'win_refund' THEN t.amount_euro ELSE 0 END),0)))
                     - (COALESCE(SUM(CASE WHEN type = 'bonus_claim' THEN t.amount_euro ELSE 0 END),0)
                     - COALESCE(SUM(CASE WHEN type = 'bonus_withdraw' THEN t.amount_euro ELSE 0 END),0)) as ngr_in_euro`
  };

  return tenantIds?.includes(id) ? newNGR : oldNGR;
}
