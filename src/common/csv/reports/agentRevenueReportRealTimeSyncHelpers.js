import { PROD_TENANTS_USING_NEW_NGR_FORMULA, STAGE_TENANTS_USING_NEW_NGR_FORMULA } from "../../constants";

export function getTenantWiseRealTimeQuery (params) {
  let query = `
    with recursive direct_agents as (
      select
        au.id as id, au.tenant_id as tenant_id, au.id as main_parent_id
      from admin_users au
      join admin_users_admin_roles auar on (au.id = auar.admin_user_id)
      join admin_roles aro on (auar.admin_role_id = aro.id)
      where aro.name = 'agent'
        ${params.agent_type}
        ${params.agent_action}
        and au.parent_id in (
          select id from admin_users where id = parent_id ${params.tenant_filter}
        )
      union all
      select child.id, child.tenant_id, parent.main_parent_id as main_parent_id
      from admin_users child join direct_agents parent on (parent.id = child.parent_id and parent.tenant_id = child.tenant_id)
      where child.parent_type = 'AdminUser' and parent.id != child.id
    ),
    -- add owners
    parents as (
      select * from direct_agents
      union all
      select id, tenant_id, id as main_parent_id from admin_users
        where  id = parent_id
          ${params.tenant_filter}
          ${params.agent_type}
          ${params.agent_action}
    ),
    IST_converted_timestamp AS (
      select
        u.id as id,
        parent_id,
        w.currency_id as currency_id
      from users u
      join wallets w on (w.owner_id = u.id and w.owner_type = 'User')
      where parent_id is not null ${params.tenant_filter} ${params.reg_date}
    ),
    txs as (
      select
        amount,
        (other_currency_amount::jsonb ->> 'EUR')::numeric(20,5) as amount_euro,
        case
          when transaction_type in (0,8,20,21,23,24,27,28,32,34,36,46,54,55,56,61,62,64,66) then 'bet'
          when transaction_type in (1,9,22,33,35,31,29,30,25,26,53,57,58,63,65,67) then 'win'
          when transaction_type in (2,10,47) and target_wallet_id is not null then 'bet_refund'
          when transaction_type in (2,10,47) and source_wallet_id is not null then 'win_refund'
          when transaction_type in (5,11,12,15,17,37,41,44,48,52,59,68,69,70,71,72,74,75,76) then 'bonus_claim'
          when transaction_type in (6,39,40,42,43,49,50,51,60,73) then 'bonus_withdraw'
          else 'unknown'
        end as type,
        case
          when transaction_type in (0,6,8,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) then source_wallet_id
          when transaction_type in (1,5,9,11,12,15,17,22,33,35,31,29,30,25,26,37,44,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) then target_wallet_id
          when transaction_type in (2,10,47) and target_wallet_id is not null then target_wallet_id
          when transaction_type in (2,10,47) and source_wallet_id is not null then source_wallet_id
          else coalesce(target_wallet_id, source_wallet_id)
        end as user_wallet_id
      from transactions
      where
        ${params.date}
        ${params.tenant_filter}
        and (transaction_type not in (5,11,12,15,17,37,41,44,48,52,59,68,69,70,71,72,74,75,76) or status = 'success') -- success status for bonus claim type
        ${params.action_filter}
        ${params.game_provider}
        ${params.game_type}

      union all

      -- different condition for total deposit.
      select
        amount,
        (other_currency_amount::jsonb ->> 'EUR')::numeric(20,5) as amount,
        'total_deposit' as type,
        target_wallet_id as user_wallet_id
      from transactions
      where
        ${params.date}
        ${params.tenant_filter}
        and transaction_type in (3,5,48,59)

      union all

      -- different condition for total withdraw.
      select
        amount,
        (other_currency_amount::jsonb ->> 'EUR')::numeric(20,5) as amount,
        'total_withdraw' as type,
        source_wallet_id as user_wallet_id
      from transactions
      where
        ${params.date}
        ${params.tenant_filter}
        and transaction_type in (4,6,49,60)
        and status = 'success'
          and comments not in ('cancelled by admin', 'cancelled by the player', 'Pending confirmation from admin')
      ${params.add_noncash_sql}
    ),
    agg_data as (
      select
        p.main_parent_id as agent_id,
        txs.type as type,
        w.currency_id as currency_id,
        sum(txs.amount)::numeric(20,5) as amount,
        sum(txs.amount_euro)::numeric(20,5) as amount_euro,
        count(*) as count
      from txs
        join wallets w on (
          w.owner_type = 'User' and
          w.id = txs.user_wallet_id
        )
        join users u on (u.id = w.owner_id)
        join parents p on (u.parent_id = p.id)
        ${params.bot_sql}
      where txs.type != 'unknown'
        ${params.currency_filter}
        ${params.bot_user_id_filter}
      group by
        p.main_parent_id,
        txs.type,
        w.currency_id

      union all

      -- Add registration count agent wise
      select
        p.main_parent_id as agent_id,
        'registration_count' as type,
        u.currency_id as currency_id,
        null as amount,
        null as amount_euro,
        count(*) as count
      from IST_converted_timestamp u
        join parents p on (u.parent_id = p.id)
        ${params.currency_reg_filter}
      group by
        p.main_parent_id,
        u.currency_id
      ${params.player_type !== 'bot_players'?
        `
        union all
        -- Add all admins and their currencies as need to show all agents even if there is no activity in them.
        select
          au.id as agent_id,
          null as type,
          w.currency_id as currency_id,
          null as amount,
          null as amount_euro,
          null as count
        from admin_users au
          join wallets w on (au.id = w.owner_id and w.owner_type = 'AdminUser')
          join admin_users_admin_roles auar on (au.id = auar.admin_user_id)
          join admin_roles aro on (auar.admin_role_id = aro.id)
        where
          aro.name in (
            'agent',
            'owner'
          )
          and au.parent_id in (
            select id from admin_users where id = parent_id ${params.tenant_filter}
          )
          ${params.currency_filter}
          ${params.agent_type}
          ${params.agent_action}
        `
        : ' '}
    ),
    -- Use this CTE when you need paginated data.
    agg_data_user_wise as (
      select
        count(*) over() as total_count,
        t.agent_id as agent_id,
        c.code as currency_code,
        min(u.agent_name) as agent_name,
        min(100 - adus.value::numeric(5,2))::numeric(5,2) as my_commission,
        coalesce(min(count) filter (where type = 'registration_count'),0) as player_count,
        coalesce(min(amount) filter (where type = 'total_withdraw'),0) as total_withdraw,
        coalesce(max(count) filter (where type = 'total_withdraw'),0) as withdraw_count,
        coalesce(min(amount) filter (where type = 'total_deposit'),0) as total_deposit,
        coalesce(max(count) filter (where type = 'total_deposit'),0) as deposit_count,
        coalesce(sum(case when type = 'win' then amount else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount else 0 end),0) as win, --win after refund
        coalesce(sum(case when type = 'bet' then amount else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount else 0 end),0) as bet,
        coalesce(sum(case when type = 'bonus_claim' then amount else 0 end),0) - coalesce(sum(case when type = 'bonus_withdraw' then amount else 0 end),0) as bonus,
        (coalesce(sum(case when type = 'bet' then amount else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount else 0 end),0)) as ggr,
        ${params.ngr_formula.ngr}
        coalesce(sum(case when type = 'win' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount_euro else 0 end),0) as win_euro,
        coalesce(sum(case when type = 'bet' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount_euro else 0 end),0) as bet_after_refund_euro,
        (coalesce(sum(case when type = 'bet' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount_euro else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount_euro else 0 end),0)) as ggr_euro,
        ${params.ngr_formula.ngr_in_eur}
        ${params.ngr_formula.my_revenue_amount}
        ${params.ngr_formula.my_revenue_euro}
      from
        agg_data t
        join admin_users u on t.agent_id = u.id
        join admin_user_settings adus on (adus.admin_user_id = u.id and adus.key = 'commission_percentage')
        join currencies c on (t.currency_id = c.id)
      group by t.agent_id, c.code
      ${params.sports_filter}

      ORDER BY ${params.sort_by} ${params.order}
    )
    select * from agg_data_user_wise;
  `;

  return query;
}

export function getAgentWiseRealTimeQuery (params) {
  let query = `
    with recursive parents as (
      select
        au.id as id, au.tenant_id as tenant_id, au.id as main_parent_id
      from admin_users au
      join admin_users_admin_roles auar on (au.id = auar.admin_user_id)
      join admin_roles aro on (auar.admin_role_id = aro.id)
      where aro.name = 'agent'
        ${params.agent_ids}
        ${params.agent_type}
        ${params.agent_action}
    ),
    IST_converted_timestamp AS (
      select
        u.id as id,
        parent_id,
        w.currency_id as currency_id
      from users u
      join wallets w on (w.owner_id = u.id and w.owner_type = 'User')
      ${params.parent_id}
      ${params.tenant_filter}
      ${params.reg_date}
    ),
    -- wallet_parents as (
    -- 	select p.id, p.main_parent_id, w.currency_id
    -- 	from parents p
    -- 		join wallets w on (p.id = w.owner_id and w.owner_type = 'AdminUser')
    -- ),
    txs as (
      select
        amount,
        (other_currency_amount::jsonb ->> 'EUR')::numeric(20,5) as amount_euro,
        case
          when transaction_type in (0,8,20,21,23,24,27,28,32,34,36,46,54,55,56,61,62,64,66) then 'bet'
          when transaction_type in (1,9,22,33,35,31,29,30,25,26,53,57,58,63,65,67) then 'win'
          when transaction_type in (2,10,47) and target_wallet_id is not null then 'bet_refund'
          when transaction_type in (2,10,47) and source_wallet_id is not null then 'win_refund'
          when transaction_type in (5,11,12,15,17,37,41,44,48,52,59,68,69,70,71,72,74,75,76) then 'bonus_claim'
          when transaction_type in (6,39,40,42,43,49,50,51,60,73) then 'bonus_withdraw'
          else 'unknown'
        end as type,
        case
          when transaction_type in (0,6,8,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) then source_wallet_id
          when transaction_type in (1,5,9,11,12,15,17,22,33,35,31,29,30,25,26,37,44,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) then target_wallet_id
          when transaction_type in (2,10,47) and target_wallet_id is not null then target_wallet_id
          when transaction_type in (2,10,47) and source_wallet_id is not null then source_wallet_id
          else coalesce(target_wallet_id, source_wallet_id)
        end as user_wallet_id
      from transactions
      where
        ${params.date}
        ${params.tenant_filter}
        and (transaction_type not in (5,11,12,15,17,37,41,44,48,52,59,68,69,70,71,72,74,75,76) or status = 'success') -- success status for bonus claim type
        ${params.action_filter}
        ${params.game_provider}
        ${params.game_type}
      union all

      -- different condition for total deposit.
      select
        amount,
        (other_currency_amount::jsonb ->> 'EUR')::numeric(20,5) as amount,
        'total_deposit' as type,
        target_wallet_id as user_wallet_id
      from transactions
      where
        ${params.date}
        ${params.tenant_filter}
        and transaction_type in (3,5,48,59)

      union all

      -- different condition for total withdraw.
      select
        amount,
        (other_currency_amount::jsonb ->> 'EUR')::numeric(20,5) as amount,
        'total_withdraw' as type,
        source_wallet_id as user_wallet_id
      from transactions
      where
        ${params.date}
        ${params.tenant_filter}
        and transaction_type in (4,6,49,60)
        and status = 'success'
          and comments not in ('cancelled by admin', 'cancelled by the player', 'Pending confirmation from admin')
        ${params.add_noncash_sql}
    ),
    agg_data as (
      select
        p.main_parent_id as agent_id,
        txs.type as type,
        w.currency_id as currency_id,
        sum(txs.amount)::numeric(20,5) as amount,
        sum(txs.amount_euro)::numeric(20,5) as amount_euro,
        count(*) as count
      from txs
        join wallets w on (
          w.owner_type = 'User' and
          w.id = txs.user_wallet_id
        )
        join users u on (u.id = w.owner_id)
        join parents p on (u.parent_id = p.id)
        ${params.bot_sql}
      where txs.type != 'unknown'
        ${params.currency_filter}
        ${params.bot_user_id_filter}
      group by
        p.main_parent_id,
        txs.type,
        w.currency_id

      union all

      -- Add registration count agent wise
      select
        p.main_parent_id as agent_id,
        'registration_count' as type,
        u.currency_id as currency_id,
        null as amount,
        null as amount_euro,
        count(*) as count
      from IST_converted_timestamp u
        join parents p on (u.parent_id = p.id)
        ${params.currency_reg_filter}
      group by
        p.main_parent_id,
        u.currency_id

      union all

      -- Add all admins and their currencies as need to show all agents even if there is no activity in them.
      select
        au.id as agent_id,
        null as type,
        w.currency_id as currency_id,
        null as amount,
        null as amount_euro,
        null as count
      from admin_users au
        join wallets w on (au.id = w.owner_id and w.owner_type = 'AdminUser')
        join admin_users_admin_roles auar on (au.id = auar.admin_user_id)
        join admin_roles aro on (auar.admin_role_id = aro.id)
      where
        aro.name in ('agent')
          ${params.agent_ids}
          ${params.currency_filter}
          ${params.agent_type}
          ${params.agent_action}
    ),
    -- Use this CTE when you need paginated data.
    agg_data_user_wise as (
      select
        count(*) over() as total_count,
        t.agent_id as agent_id,
        c.code as currency_code,
        min(u.agent_name) as agent_name,
        ${params.commission_row} as my_commission,
        coalesce(min(count) filter (where type = 'registration_count'),0) as player_count,
        coalesce(min(amount) filter (where type = 'total_withdraw'),0) as total_withdraw,
        coalesce(max(count) filter (where type = 'total_withdraw'),0) as withdraw_count,
        coalesce(min(amount) filter (where type = 'total_deposit'),0) as total_deposit,
        coalesce(max(count) filter (where type = 'total_deposit'),0) as deposit_count,
        coalesce(sum(case when type = 'win' then amount else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount else 0 end),0) as win,
        coalesce(sum(case when type = 'bet' then amount else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount else 0 end),0) as bet,
        coalesce(sum(case when type = 'bonus_claim' then amount else 0 end),0) - coalesce(sum(case when type = 'bonus_withdraw' then amount else 0 end),0) as bonus,
        (coalesce(sum(case when type = 'bet' then amount else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount else 0 end),0)) as ggr,
        ${params.ngr_formula.ngr}
        coalesce(sum(case when type = 'win' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount_euro else 0 end),0) as win_euro,
        coalesce(sum(case when type = 'bet' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount_euro else 0 end),0) as bet_after_refund_euro,
        (coalesce(sum(case when type = 'bet' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount_euro else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount_euro else 0 end),0)) as ggr_euro,
        ${params.ngr_formula.ngr_in_eur}
        ${params.ngr_formula.agent_my_revenue_amount}
         ${params.ngr_formula.agent_my_revenue_euro}
      from
        agg_data t
        join admin_users u on t.agent_id = u.id
        join admin_user_settings adus on (adus.admin_user_id = u.id and adus.key = 'commission_percentage')
        join currencies c on (t.currency_id = c.id)
      group by t.agent_id, c.code
        ${params.sports_filter}
      ORDER BY ${params.sort_by} ${params.order}
    )
    select * from agg_data_user_wise;
  `;

  return query;
}


export function calculateNGRForTenantRealtime (tenantId, date, tenantFilter, actionFilter, gameProvider, gameType, commissionRow) {
const isProduction = process.env.NODE_ENV === 'production'
const tenantIds = isProduction ? PROD_TENANTS_USING_NEW_NGR_FORMULA : STAGE_TENANTS_USING_NEW_NGR_FORMULA
const id =  Number(Array.isArray(tenantId) ? tenantId[0] : tenantId);;
  const newNGR = {
    // New NGR logic
    addSql: `union all
            select
                amount,
                (other_currency_amount::jsonb ->> 'EUR')::numeric(20,5) as amount_euro,
                CASE
                    WHEN transaction_type IN (8,20,23,27,46,55,61,62,64,66) THEN 'bet_non_cash'
                    WHEN transaction_type IN (9,25,29,57,63,65,67) THEN 'win_non_cash'
                    WHEN transaction_type IN (10,47) AND target_wallet_id IS NOT NULL THEN 'bet_refund_non_cash'
                    WHEN transaction_type IN (10,47) AND source_wallet_id IS NOT NULL THEN 'win_refund_non_cash'
                END AS type,
                CASE
                    WHEN transaction_type IN (8,20,23,27,46,55,61,62,64,66) THEN source_wallet_id
                    WHEN transaction_type IN (9,25,29,57,63,65,67) THEN target_wallet_id
                    WHEN transaction_type in (10,47) AND target_wallet_id IS NOT NULL then target_wallet_id
                    WHEN transaction_type in (10,47) AND source_wallet_id IS NOT NULL then source_wallet_id
                END AS user_wallet_id
            from transactions
            WHERE ${date}
            ${tenantFilter}
            AND transaction_type IN (8,9,10,20,23,25,27,29,46,47,55,57,61,62,63,64,65,66,67)
            ${actionFilter}
            ${gameProvider}
            ${gameType}`,
    ngr :`((coalesce(sum(case when type = 'bet' then amount else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount else 0 end),0))) - ((coalesce(sum(case when type = 'bet_non_cash' then amount else 0 end), 0) - coalesce(sum(case when type = 'bet_refund_non_cash' then amount else 0 end), 0)) - (coalesce(sum(case when type = 'win_non_cash' then amount else 0 end), 0) - coalesce(sum(case when type = 'win_refund_non_cash' then amount else 0 end), 0))) as ngr,`,
    ngr_in_eur :`((coalesce(sum(case when type = 'bet' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount_euro else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount_euro else 0 end),0))) - ((coalesce(sum(case when type = 'bet_non_cash' then amount_euro else 0 end), 0) - coalesce(sum(case when type = 'bet_refund_non_cash' then amount_euro else 0 end), 0)) - (coalesce(sum(case when type = 'win_non_cash' then amount_euro else 0 end), 0) - coalesce(sum(case when type = 'win_refund_non_cash' then amount_euro else 0 end), 0))) as ngr_in_eur,`,
    my_revenue_amount :`(min(100 - adus.value::numeric(5,2))::numeric(5,2) / 100 * (((coalesce(sum(case when type = 'bet' then amount else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount else 0 end),0))) - ((coalesce(sum(case when type = 'bet_non_cash' then amount else 0 end), 0) - coalesce(sum(case when type = 'bet_refund_non_cash' then amount else 0 end), 0)) - (coalesce(sum(case when type = 'win_non_cash' then amount else 0 end), 0) - coalesce(sum(case when type = 'win_refund_non_cash' then amount else 0 end), 0)))))::numeric(20,5) as my_revenue_amount,`,
    my_revenue_euro :`(min(100 - adus.value::numeric(5,2))::numeric(5,2) / 100 * (((coalesce(sum(case when type = 'bet' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount_euro else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount_euro else 0 end),0))) - ((coalesce(sum(case when type = 'bet_non_cash' then amount_euro else 0 end), 0) - coalesce(sum(case when type = 'bet_refund_non_cash' then amount_euro else 0 end), 0)) - (coalesce(sum(case when type = 'win_non_cash' then amount_euro else 0 end), 0) - coalesce(sum(case when type = 'win_refund_non_cash' then amount_euro else 0 end), 0)))))::numeric(20,5) as my_revenue_euro`,
    agent_my_revenue_amount: `( ${commissionRow} / 100 * (((coalesce(sum(case when type = 'bet' then amount else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount else 0 end),0))) -  ((coalesce(sum(case when type = 'bet_non_cash' then amount else 0 end), 0) - coalesce(sum(case when type = 'bet_refund_non_cash' then amount else 0 end), 0)) -  (coalesce(sum(case when type = 'win_non_cash' then amount else 0 end), 0) - coalesce(sum(case when type = 'win_refund_non_cash' then amount else 0 end), 0)))))::numeric(20,5) as my_revenue_amount,`,
    agent_my_revenue_euro:  `( ${commissionRow} / 100 * (((coalesce(sum(case when type = 'bet' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount_euro else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount_euro else 0 end),0))) -  ((coalesce(sum(case when type = 'bet_non_cash' then amount_euro else 0 end), 0) - coalesce(sum(case when type = 'bet_refund_non_cash' then amount_euro else 0 end), 0)) - (coalesce(sum(case when type = 'win_non_cash' then amount_euro else 0 end), 0) - coalesce(sum(case when type = 'win_refund_non_cash' then amount_euro else 0 end), 0)))))::numeric(20,5) as my_revenue_euro`,
  };
// Default (old) NGR logic
  const oldNGR = {
    ngr : `((coalesce(sum(case when type = 'bet' then amount else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount else 0 end),0))) - (coalesce(sum(case when type = 'bonus_claim' then amount else 0 end),0) - coalesce(sum(case when type = 'bonus_withdraw' then amount else 0 end),0)) as ngr,`,
    ngr_in_eur : `((coalesce(sum(case when type = 'bet' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount_euro else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount_euro else 0 end),0))) - (coalesce(sum(case when type = 'bonus_claim' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bonus_withdraw' then amount_euro else 0 end),0)) as ngr_in_eur,`,
    my_revenue_amount : `(min(100 - adus.value::numeric(5,2))::numeric(5,2) / 100 * (((coalesce(sum(case when type = 'bet' then amount else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount else 0 end),0))) - (coalesce(sum(case when type = 'bonus_claim' then amount else 0 end),0) - coalesce(sum(case when type = 'bonus_withdraw' then amount else 0 end),0))))::numeric(20,5) as my_revenue_amount,`,
    my_revenue_euro : `(min(100 - adus.value::numeric(5,2))::numeric(5,2) / 100 * (((coalesce(sum(case when type = 'bet' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount_euro else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount_euro else 0 end),0))) - (coalesce(sum(case when type = 'bonus_claim' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bonus_withdraw' then amount_euro else 0 end),0))))::numeric(20,5) as my_revenue_euro`,
    agent_my_revenue_amount:`( ${commissionRow} / 100 * (((coalesce(sum(case when type = 'bet' then amount else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount else 0 end),0))) - (coalesce(sum(case when type = 'bonus_claim' then amount else 0 end),0) - coalesce(sum(case when type = 'bonus_withdraw' then amount else 0 end),0))))::numeric(20,5) as my_revenue_amount,`,
    agent_my_revenue_euro: `( ${commissionRow} / 100 * (((coalesce(sum(case when type = 'bet' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bet_refund' then amount_euro else 0 end),0)) - (coalesce(sum(case when type = 'win' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'win_refund' then amount_euro else 0 end),0))) - (coalesce(sum(case when type = 'bonus_claim' then amount_euro else 0 end),0) - coalesce(sum(case when type = 'bonus_withdraw' then amount_euro else 0 end),0))))::numeric(20,5) as my_revenue_euro`,
  };

  return tenantIds?.includes(id) ? newNGR : oldNGR;
}
