import { v4 as uuidv4 } from 'uuid'
import { EXPORT_CSV_STATUS } from '../../../common/constants'
import config from '../../../configs/app.config'
import db, { sequelize } from '../../../db/models'
import { s3 } from '../../../libs/awsS3ConfigV2'
import { getDateInStringFormat } from '../../helpers'
import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import getRolesDetails from '../getAdminRolesDetail';
import { getDateRange } from './getStartEndDates'
import { fetchAgentIds } from '../../helpers'
import { ROLE } from '../../../utils/constants/constant'
import { Readable } from 'stream'
import moment from 'moment-timezone'


const getPermissionsForAdminUser = async (adminUserId, tenantId) => {
  try {
    const ownerRole = await sequelize.query(
      `SELECT admin_user_id FROM admin_users_admin_roles 
       WHERE admin_user_id = :adminUserId AND admin_role_id = 1 LIMIT 1`,
      {
        replacements: { adminUserId },
        type: sequelize.QueryTypes.SELECT
      }
    )

    if (ownerRole && ownerRole.length > 0) {
      const permissions = await sequelize.query(
        'SELECT permission FROM tenant_permissions WHERE tenant_id = :tenantId LIMIT 1',
        {
          replacements: { tenantId },
          type: sequelize.QueryTypes.SELECT
        }
      )

      return permissions[0]
        ? (typeof permissions[0].permission === 'string'
            ? JSON.parse(permissions[0].permission)
            : permissions[0].permission)
        : {}
    }

    const permissionRole = await sequelize.query(
      `SELECT permission_role_id FROM admin_users_permission_roles 
       WHERE admin_user_id = :adminUserId LIMIT 1`,
      {
        replacements: { adminUserId },
        type: sequelize.QueryTypes.SELECT
      }
    )

    if (permissionRole && permissionRole.length > 0) {
      const permissions = await sequelize.query(
        'SELECT permission FROM permission_role WHERE id = :roleId LIMIT 1',
        {
          replacements: { roleId: permissionRole[0].permission_role_id },
          type: sequelize.QueryTypes.SELECT
        }
      )
      return permissions[0]
        ? (typeof permissions[0].permission === 'string'
            ? JSON.parse(permissions[0].permission)
            : permissions[0].permission)
        : {}
    }

    return {}
  } catch (error) {
    console.error('Error fetching permissions:', error)
    return {}
  }
}

const getColumnPermissions = async (attributePermissions, adminUserId, tenantId, userType) => {

  if (userType === 'SuperAdminUser') {
    return Object.keys(attributePermissions)
  }

  const permissions = await getPermissionsForAdminUser(adminUserId, tenantId)

  const allowedAttributes = []

  Object.entries(attributePermissions).forEach(([attribute, permissionData]) => {
    if (permissionData === null) {
      allowedAttributes.push(attribute)
      return
    }
    const attributeKey = attribute.split('.').pop()

    if (Array.isArray(permissionData)) {
      const permissionCategory = permissionData[0]
      const categoryPermissions = permissions[permissionCategory] || []
      
      const isAttributeAllowed = 
        categoryPermissions.includes(permissionCategory) || 
        categoryPermissions.includes(attributeKey)
      
      if (isAttributeAllowed) {
        allowedAttributes.push(attribute)
      }
    } 
    else if (typeof permissionData === 'string') {
      const permissionCategory = permissionData
      const categoryPermissions = permissions[permissionCategory] || []
      const attributeKey = attribute.split('.').pop()
      
      const isAttributeAllowed =
        categoryPermissions.includes(permissionCategory) ||
        categoryPermissions.includes(attributeKey)
      
      if (isAttributeAllowed) {
        allowedAttributes.push(attribute)
      }
    }
  })

  return allowedAttributes
}

const constructQueryForCountAndData = (params) => {
  const {
    tenantIdArray,
    botUserJoin,
    loginHistoryJoin,
    timeCondition,
    userDetailsCondition,
    loginDetailsCondition,
    bankDetailsCondition,
    casinoDetailsCondition,
    exchangeDetailsCondition,
    playerTypeCondition,
    agentCondition,
    limit,
    offset,
    orderCondition,
    isCountOnly,
    hasExchangeDetails,
    hasCasinoDetails,
    hasBankDetails,
    hasLoginDetails,
    skipTenantFilter,
    queryReplacements: existingReplacements
  } = params;

  const queryReplacements = {
    ...(existingReplacements || {}),
  };
  
  if (!skipTenantFilter) {
    queryReplacements.tenantIdArray = tenantIdArray.length > 0 ? tenantIdArray : [0]
  }
  if (!isCountOnly) {
    queryReplacements.limit = parseInt(limit, 10);
    queryReplacements.offset = parseInt(offset, 10);
  }
  
  const tenantCondition = !skipTenantFilter ? 'u.tenant_id = ANY(ARRAY[:tenantIdArray])' : '';
  
  if (isCountOnly && hasLoginDetails) {
    const isDateFormat = /^\d{4}-\d{2}-\d{2}$/.test(loginDetailsCondition);
    
    let loginFilter;
    if (isDateFormat) {
      loginFilter = `DATE(last_login_date) = DATE(:loginDate)`;
      queryReplacements.loginDate = loginDetailsCondition;
    } else {
      loginFilter = `(
        ip ILIKE :loginDetailsPattern OR 
        network ILIKE :loginDetailsPattern OR 
        device_id ILIKE :loginDetailsPattern OR 
        device_type ILIKE :loginDetailsPattern OR 
        device_model ILIKE :loginDetailsPattern OR
        data::text ILIKE :loginDetailsPattern
      )`;
      queryReplacements.loginDetailsPattern = `%${loginDetailsCondition}%`;
    }
    
    const baseConditions = [];
    if (playerTypeCondition) baseConditions.push(playerTypeCondition);
    if (timeCondition) baseConditions.push(timeCondition);
    if (agentCondition) baseConditions.push(agentCondition);
    if (tenantCondition) baseConditions.push(tenantCondition);  // Add tenant condition conditionally
    
    const loginHistoryWhereClause = !skipTenantFilter 
      ? 'tenant_id = ANY(ARRAY[:tenantIdArray]) AND' 
      : '';
    
    const query = `
      WITH filtered_user_login_history AS (
        SELECT user_id
        FROM user_login_history
        WHERE ${loginHistoryWhereClause} ${loginFilter}
        GROUP BY user_id
      )
      SELECT COUNT(*) as total
      FROM users u
      JOIN filtered_user_login_history flh ON (u.id = flh.user_id)
      LEFT JOIN bot_users bu ON bu.user_id = u.id
      ${baseConditions.length > 0 ? 'WHERE ' + baseConditions.join(' AND ') : ''}
    `;
    
    return { query, queryReplacements };
  }

  const baseConditions = [
    tenantCondition, // Use dynamic tenant condition
    timeCondition,
    playerTypeCondition,
    agentCondition
  ].filter(Boolean);

  let query;
  
  if (isCountOnly) {
    if (hasLoginDetails) {
      return { query, queryReplacements };
    }
    else if (hasExchangeDetails) {
      const betsTransactionsTenantCondition = !skipTenantFilter 
        ? 'AND bt.tenant_id = ANY(ARRAY[:tenantIdArray])' 
        : '';
      query = `
        SELECT COUNT(*) as total
        FROM users u
        LEFT JOIN bot_users bu ON bu.user_id = u.id
        WHERE ${baseConditions.join(' AND ')}
        AND EXISTS (
          SELECT 1 FROM bets_transactions bt 
          WHERE bt.user_id = u.id 
          AND (
            bt.market_id = :exchangeId OR 
            bt.market_id::text ILIKE :exchangeDetailsPattern
          )
          ${betsTransactionsTenantCondition}
          LIMIT 1
        )
      `;
      queryReplacements.exchangeDetailsPattern = `%${exchangeDetailsCondition}%`;
      queryReplacements.exchangeId = exchangeDetailsCondition;
    } 
    else if (hasCasinoDetails) {
      const transactionsTenantCondition = !skipTenantFilter 
        ? 'tenant_id = ANY(ARRAY[:tenantIdArray]) AND' 
        : '';
      query = `
        WITH filtered_transactions_details AS (
          SELECT distinct actionee_id AS user_id from
          transactions
          WHERE ${transactionsTenantCondition}
            round_id LIKE :casinoDetailsPattern
        )
        SELECT COUNT(*) as total
        FROM users u
        JOIN filtered_transactions_details fed ON (u.id = fed.user_id)
        LEFT JOIN bot_users bu ON bu.user_id = u.id
        WHERE ${baseConditions.join(' AND ')}
      `;
      queryReplacements.casinoDetailsPattern = `%${casinoDetailsCondition}%`;
    }

    else if (hasBankDetails) {
      const bankDetailsTenantCondition = !skipTenantFilter 
        ? 'tenant_id = ANY(ARRAY[:tenantIdArray]) AND' 
        : '';
      query = `
        WITH filtered_user_bank_details AS (
          SELECT distinct user_id
          FROM user_bank_details
          WHERE ${bankDetailsTenantCondition}
            (
              account_number ILIKE :bankDetailsPattern OR 
              bank_ifsc_code ILIKE :bankDetailsPattern OR
              bank_name ILIKE :bankDetailsPattern
            )
        )
        SELECT COUNT(*) as total
        FROM users u
        JOIN filtered_user_bank_details fbd ON (u.id = fbd.user_id)
        LEFT JOIN bot_users bu ON bu.user_id = u.id
        WHERE ${baseConditions.join(' AND ')}
      `;
      queryReplacements.bankDetailsPattern = `%${bankDetailsCondition}%`;
    }

    else {
      const conditions = [...baseConditions];
      if (userDetailsCondition) {
        conditions.push(`(
          u.user_name ILIKE :userDetailsPattern OR 
          u.email ILIKE :userDetailsPattern OR 
          u.first_name ILIKE :userDetailsPattern OR 
          u.last_name ILIKE :userDetailsPattern OR 
          u.city ILIKE :userDetailsPattern OR 
          u.zip_code ILIKE :userDetailsPattern OR
          CAST(u.id AS TEXT) ILIKE :userDetailsPattern
        )`);
        queryReplacements.userDetailsPattern = `%${userDetailsCondition}%`;
      }
      
      query = `
        SELECT COUNT(*) as total
        FROM users u
        LEFT JOIN bot_users bu ON bu.user_id = u.id
        WHERE ${conditions.join(' AND ')}
      `;
    }
    
    return { query, queryReplacements };
  }
  

  let filteredUsersCTE;
  
  if (hasExchangeDetails) {
    const betsTransactionsTenantCondition = !skipTenantFilter 
      ? 'AND bt.tenant_id = ANY(ARRAY[:tenantIdArray])' 
      : '';
    filteredUsersCTE = `
      filtered_users AS (
        SELECT
          u.id, u.user_name, u.email, u.first_name, u.last_name, u.phone, u.city, u.zip_code,
          u.country_code, u.last_login_date, u.created_at, u.updated_at, u.active, u.tenant_id,
          u.vip_level, u.gender, u.parent_id, ${botUserJoin ? 'bu.id as bot_user_id' : 'NULL as bot_user_id'}
        FROM users u
        LEFT JOIN bot_users bu ON bu.user_id = u.id
        WHERE ${baseConditions.join(' AND ')}
        AND EXISTS (
          SELECT 1 FROM bets_transactions bt 
          WHERE bt.user_id = u.id 
          AND (
            bt.market_id = :exchangeId OR 
            bt.market_id::text ILIKE :exchangeDetailsPattern
          )
          ${betsTransactionsTenantCondition}
          LIMIT 1
        )
        ${orderCondition}
        ${!isCountOnly ? 'LIMIT :limit OFFSET :offset' : ''}
      )
    `;
    queryReplacements.exchangeDetailsPattern = `%${exchangeDetailsCondition}%`;
    queryReplacements.exchangeId = exchangeDetailsCondition;
  }

  else if (hasCasinoDetails) {
    const transactionsTenantCondition = !skipTenantFilter 
      ? 'tenant_id = ANY(ARRAY[:tenantIdArray]) AND' 
      : '';
    filteredUsersCTE = `
      filtered_transactions_details AS (
        SELECT distinct actionee_id AS user_id from
        transactions
        WHERE ${transactionsTenantCondition}
          round_id LIKE :casinoDetailsPattern
      ),
      filtered_users AS (
        SELECT
          u.id, u.user_name, u.email, u.first_name, u.last_name, u.phone, u.city, u.zip_code,
          u.country_code, u.last_login_date, u.created_at, u.updated_at, u.active, u.tenant_id,
          u.vip_level, u.gender, u.parent_id, ${botUserJoin ? 'bu.id as bot_user_id' : 'NULL as bot_user_id'}
        FROM users u
        JOIN filtered_transactions_details fbd ON (u.id = fbd.user_id)
        LEFT JOIN bot_users bu ON (bu.user_id = u.id)
        WHERE ${baseConditions.join(' AND ')}
        ${orderCondition}
        ${!isCountOnly ? 'LIMIT :limit OFFSET :offset' : ''}
      )
    `;
    queryReplacements.casinoDetailsPattern = `%${casinoDetailsCondition}%`;
  }

  else if (hasBankDetails) {
    const bankDetailsTenantCondition = !skipTenantFilter 
      ? 'tenant_id = ANY(ARRAY[:tenantIdArray]) AND' 
      : '';
    filteredUsersCTE = `
      filtered_user_bank_details AS (
        SELECT distinct user_id
        FROM user_bank_details
        WHERE ${bankDetailsTenantCondition}
          (
            account_number ILIKE :bankDetailsPattern OR 
            bank_ifsc_code ILIKE :bankDetailsPattern OR
            bank_name ILIKE :bankDetailsPattern
          )
      ),
      filtered_users AS (
        SELECT
          u.id, u.user_name, u.email, u.first_name, u.last_name, u.phone, u.city, u.zip_code,
          u.country_code, u.last_login_date, u.created_at, u.updated_at, u.active, u.tenant_id,
          u.vip_level, u.gender, u.parent_id, ${botUserJoin ? 'bu.id as bot_user_id' : 'NULL as bot_user_id'}
        FROM users u
        JOIN filtered_user_bank_details fbd ON (u.id = fbd.user_id)
        LEFT JOIN bot_users bu ON (bu.user_id = u.id)
        WHERE ${baseConditions.join(' AND ')}
        ${orderCondition}
        ${!isCountOnly ? 'LIMIT :limit OFFSET :offset' : ''}
      )
    `;
    queryReplacements.bankDetailsPattern = `%${bankDetailsCondition}%`;
  }

  else if (hasLoginDetails) {
    const loginHistoryWhereClause = !skipTenantFilter 
      ? 'tenant_id = ANY(ARRAY[:tenantIdArray]) AND' 
      : '';
    
    // Check if loginDetails is in date format (YYYY-MM-DD)
    const isDateFormat = /^\d{4}-\d{2}-\d{2}$/.test(loginDetailsCondition);
    
    let loginFilter;
    if (isDateFormat) {
      loginFilter = `DATE(last_login_date) = DATE(:loginDate)`;
      queryReplacements.loginDate = loginDetailsCondition;
    } else {
      loginFilter = `(
        ip ILIKE :loginDetailsPattern OR 
        network ILIKE :loginDetailsPattern OR 
        device_id ILIKE :loginDetailsPattern OR 
        device_type ILIKE :loginDetailsPattern OR 
        device_model ILIKE :loginDetailsPattern OR
        data::text ILIKE :loginDetailsPattern
      )`;
      queryReplacements.loginDetailsPattern = `%${loginDetailsCondition}%`;
    }
    
    filteredUsersCTE = `
      filtered_user_login_history AS (
        SELECT user_id, MAX(last_login_date) AS max_login_date
        FROM user_login_history
        WHERE ${loginHistoryWhereClause} ${loginFilter}
        GROUP BY user_id
      ),
      filtered_users AS (
        SELECT
          u.id, u.user_name, u.email, u.first_name, u.last_name, u.phone, u.city, u.zip_code,
          u.country_code, u.last_login_date, u.created_at, u.updated_at, u.active, u.tenant_id,
          u.vip_level, u.gender, u.parent_id, ${botUserJoin ? 'bu.id as bot_user_id' : 'NULL as bot_user_id'}
        FROM users u
        JOIN filtered_user_login_history flh ON (u.id = flh.user_id)
        LEFT JOIN bot_users bu ON (bu.user_id = u.id)
        WHERE ${baseConditions.join(' AND ')}
        ${orderCondition}
        LIMIT :limit OFFSET :offset
      )
    `;
  }
  else {
    const conditions = [...baseConditions];
    if (userDetailsCondition) {
      conditions.push(`(
        u.user_name ILIKE :userDetailsPattern OR 
        u.email ILIKE :userDetailsPattern OR 
        u.first_name ILIKE :userDetailsPattern OR 
        u.last_name ILIKE :userDetailsPattern OR 
        u.city ILIKE :userDetailsPattern OR 
        u.zip_code ILIKE :userDetailsPattern OR
        CAST(u.id AS TEXT) ILIKE :userDetailsPattern
      )`);
      queryReplacements.userDetailsPattern = `%${userDetailsCondition}%`;
    }
    
    filteredUsersCTE = `
      filtered_users AS (
        SELECT
          u.id, u.user_name, u.email, u.first_name, u.last_name, u.phone, u.city, u.zip_code,
          u.country_code, u.last_login_date, u.created_at, u.updated_at, u.active, u.tenant_id,
          u.vip_level, u.gender, u.parent_id, ${botUserJoin ? 'bu.id as bot_user_id' : 'NULL as bot_user_id'}
        FROM users u
        LEFT JOIN bot_users bu ON (bu.user_id = u.id)
        WHERE ${conditions.join(' AND ')}
        ${orderCondition}
        ${!isCountOnly ? 'LIMIT :limit OFFSET :offset' : ''}
      )
    `;
  }
  
  const bankDetailsTenantCondition = !skipTenantFilter 
    ? 'tenant_id = ANY(ARRAY[:tenantIdArray]) AND' 
    : '';


  const bankDetailsCTE = `
    bank_details AS (
      SELECT 
        user_id,
        COUNT(*) as bank_count,
        json_agg(
          json_build_object(
            'bank_name', bank_name,
            'bank_ifsc_code', bank_ifsc_code,
            'account_number', CASE 
              WHEN account_number IS NULL THEN NULL
              WHEN LENGTH(account_number) <= 4 THEN account_number
              ELSE CONCAT('XXXXXXXXXXXX', RIGHT(account_number, 4))
            END,
            'created_at', created_at
          ) ORDER BY created_at DESC
        ) as all_bank_details,
        json_build_object(
          'bank_name', MAX(bank_name) FILTER (WHERE rn = 1),
          'bank_ifsc_code', MAX(bank_ifsc_code) FILTER (WHERE rn = 1),
          'account_number', CASE 
            WHEN MAX(account_number) FILTER (WHERE rn = 1) IS NULL THEN NULL
            WHEN LENGTH(MAX(account_number) FILTER (WHERE rn = 1)) <= 4 THEN MAX(account_number) FILTER (WHERE rn = 1)
            ELSE CONCAT('XXXXXXXXXXXX', RIGHT(MAX(account_number) FILTER (WHERE rn = 1), 4))
          END
        ) as latest_bank_detail
      FROM (
        SELECT 
          user_id, bank_name, bank_ifsc_code, account_number, created_at,
          ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn
        FROM user_bank_details
        WHERE ${bankDetailsTenantCondition} user_id IN (SELECT id FROM filtered_users)
      ) ranked_banks
      GROUP BY user_id
    )
  `;
  
  const loginHistoryTenantCondition = !skipTenantFilter 
    ? 'tenant_id = ANY(ARRAY[:tenantIdArray]) AND' 
    : '';

  const loginHistoryCTE = `
    login_history AS (
      SELECT 
        user_id,
        COUNT(*) as login_count,
        json_agg(
          json_build_object(
            'ip', ip,
            'device_id', device_id,
            'device_type', device_type,
            'device_model', device_model,
            'last_login_date', last_login_date,
            'network', network
          ) ORDER BY last_login_date DESC
        ) as all_logins,
        json_build_object(
          'ip', MAX(ip) FILTER (WHERE rn = 1),
          'device_id', MAX(device_id) FILTER (WHERE rn = 1),
          'device_type', MAX(device_type) FILTER (WHERE rn = 1),
          'device_model', MAX(device_model) FILTER (WHERE rn = 1),
          'last_login_date', MAX(last_login_date) FILTER (WHERE rn = 1),
          'network', MAX(network) FILTER (WHERE rn = 1)
        ) as latest_login
      FROM (
        SELECT 
          user_id, ip, device_id, device_type, device_model, last_login_date, network,
          ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY last_login_date DESC) as rn
        FROM user_login_history
        WHERE ${loginHistoryTenantCondition} user_id IN (SELECT id FROM filtered_users)
      ) ranked_logins
      GROUP BY user_id
    )
  `;
  
  const finalSelect = `
    SELECT
      u.*,
      t.name as tenant_name, c.code as currency_code, c.name as currency_name,
      COALESCE(bd.bank_count, 0) as bank_count, bd.latest_bank_detail, bd.all_bank_details,
      COALESCE(lh.login_count, 0) as login_count, lh.latest_login, lh.all_logins
    FROM
      filtered_users u
      LEFT JOIN bank_details bd ON bd.user_id = u.id
      LEFT JOIN login_history lh ON lh.user_id = u.id
      LEFT JOIN wallets w ON (w.owner_id = u.id AND w.owner_type = 'User')
      LEFT JOIN currencies c ON (c.id = w.currency_id)
      LEFT JOIN tenants t ON (t.id = u.tenant_id)
    ${orderCondition}
  `;
  
  query = `
    WITH ${filteredUsersCTE},
    ${bankDetailsCTE},
    ${loginHistoryCTE}
    ${finalSelect}
  `;
  
  return { query, queryReplacements };
};

const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

export default async (data) => {
  try {
    // Update status to in progress
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.IN_PROGRESS
    }, {
      where: { id: data.id }
    })

    const payload = data.payload
    let tenantId = payload.tenantId
    let tenantIds = payload.tenantIds
    let agentId = null
    let effectiveTenantId = tenantId
    let skipTenantFilter = ((tenantId === '0' || tenantId === '' || tenantId === 0) && data.adminType === 'SuperAdminUser') ? true : false

    // Handle tenant ID logic based on admin type
    if (data.adminType === 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: { id: data.adminId },
        attributes: ['tenantId']
      })
      
      tenantId = adminData.tenantId
      effectiveTenantId = tenantId
      const roles = await getRolesDetails(data.adminId)
      
      // Determine agent ID based on roles
      if (!roles.includes('owner')) {
        if (roles.includes('sub-admin')) {
          agentId = payload?.agent_id ? payload.agent_id : null
        } else {
          agentId = payload?.agent_id ? payload.agent_id : data.adminId
        }
      } else {
        agentId = payload?.agent_id ? payload.agent_id : null
      }
    } else if (data.adminType === 'SuperAdminUser') {
      const adminData = await db.SuperAdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['parentType', 'tenantIds']
      })
      if ( (!tenantId === 0 || !tenantId === '0')) {
        effectiveTenantId = tenantId;
      }
      else if (adminData.parentType === 'Manager' && (tenantId === 0 || tenantId === '0')){
        skipTenantFilter = false
        effectiveTenantId = adminData.tenantIds.map(id => parseInt(id));
      }
    }
  

    // Convert effectiveTenantId to array format
    let tenantIdArray = []
    if (Array.isArray(effectiveTenantId)) {
      tenantIdArray = effectiveTenantId.map(id => Number(id))
    } else if (effectiveTenantId && effectiveTenantId !== '0') {
      tenantIdArray = [Number(effectiveTenantId)]
    } else if (tenantIds && Array.isArray(tenantIds)) {
      tenantIdArray = tenantIds.map(id => Number(id))
    }
    

    // Determine user role and relevant agent IDs
    let tempAgentId = null
    let agentIds = []
    
    if (data.adminType === 'AdminUser') {
      const currentUserRole = await getRolesDetails(data.adminId)
      
      if (currentUserRole[0] === 'agent') {
        agentIds = await fetchAgentIds(data.adminId)
      } else {
        const roles = await getRolesDetails(data.adminId)
        if (!(roles.includes('sub-admin') || roles.includes('owner'))) {
          tempAgentId = data.adminId
        }
      }
    }


    const attributesWithPermissions = {
      'id': null,
      'user_name': ['players_key', 'user_name'],
      'email': ['players_key', 'email'],
      'first_name': ['players_key', 'first_name'],
      'last_name': ['players_key', 'last_name'],
      'phone': ['players_key', 'phone'],
      'city': ['players_key', 'city'],
      'zip_code': ['players_key', 'zip_code'],
      'country_code': ['players_key', 'country_code'],
      'gender': ['players_key', 'gender'],
      'created_at': ['players_key', 'created_at'],
      'last_login_date': ['players_key', 'last_login_date'],
      'currency': ['report_attributes', 'currency'],
      'vip_level': ['players_key', 'vip_level'],
      'status': ['players_key', 'status'],
      'ip_address': ['players_key', 'ip_address'],     
      'device_id': ['players_key', 'ip_address'],      
      'device_type': ['players_key', 'ip_address'],    
      'device_model': ['players_key', 'ip_address'],   
      'network': ['players_key', 'ip_address'], 
      'bank_name': ['players_key', 'bank_name'],
      'ifsc_code': ['players_key', 'ifsc_code'],
      'account_number': ['players_key', 'account_number'],
      'bank_count': ['financial_attributes', 'account_number'],
    };


    if (payload.casinoDetails) {
      attributesWithPermissions['round_id'] = ['transaction_attributes', 'round_id'];
      attributesWithPermissions['casino_count'] = ['transaction_attributes', 'round_id'];
    }
    
    if (payload.exchangeDetails) {
      attributesWithPermissions['market'] = ['sports_attributes', 'market_name'];
      attributesWithPermissions['market_id'] = ['sports_attributes', 'market_id'];
      attributesWithPermissions['exchange_count'] = ['sports_attributes', 'market_name'];
    }

    // Get allowed columns based on user permissions
    const allowedAttributes = await getColumnPermissions(
      attributesWithPermissions,
      data.adminId,
      effectiveTenantId,
      data.adminType
    );

    if (allowedAttributes.length === 0) {
      await db.ExportCsvCenter.update({
        status: EXPORT_CSV_STATUS.DONE,
        message: 'No data found or permission denied'
      }, {
        where: { id: data.id }
      });
      return true;
    }

    let botUserJoin = '';
    let playerTypeCondition = '';
    const playerType = payload.playerType;
    const playerCategory = payload.playerCategory;
    
    if (data.adminType === 'SuperAdminUser' && payload.sbo_request) {
      botUserJoin = 'bu.id';
      
      if (playerCategory) {
        if (playerCategory === 'bot_players') {
          playerTypeCondition = 'bu.id IS NOT NULL';
        } else if (playerCategory === 'real_players') {
          playerTypeCondition = 'bu.id IS NULL';
        }
      } else if (playerType) {
        if (playerType === 'bot_players') {
          playerTypeCondition = 'bu.id IS NOT NULL';
        } else if (playerType === 'real_players') {
          playerTypeCondition = 'bu.id IS NULL';
        }
      }
    } else {
      botUserJoin = 'bu.id';
      playerTypeCondition = 'bu.id IS NULL';
    }

    let timeCondition = '';
    if (payload.timeType) {
      const { start, end } = getDateRange(
        payload.timeType, 
        payload.timeZoneName, 
        payload.startDate, 
        payload.endDate, 
        payload.timeZoneName
      );
      
      if (start && end) {
        timeCondition = 'u.created_at BETWEEN :startDate AND :endDate';
      }
    }

    let agentCondition = '';
    if (tempAgentId) {
      agentCondition = 'u.parent_id = :tempAgentId';
    }

    if (agentIds.length > 0) {
      agentCondition = 'u.parent_id = ANY(ARRAY[:agentIds]::bigint[])';
    }

    let orderCondition = '';
    if (payload.sortBy && payload.order) {
      const columnMapping = {
        id: 'u.id',
        user_id: 'u.id',
        user_name: 'u.user_name',
        email: 'u.email',
        first_name: 'u.first_name',
        last_name: 'u.last_name',
        full_name: 'u.first_name',
        city: 'u.city',
        zip_code: 'u.zip_code',
        country_code: 'u.country_code',
        last_login_date: 'u.last_login_date',
        created_at: 'u.created_at',
        updated_at: 'u.updated_at',
        tenant_id: 'u.tenant_id',
        active: 'u.active',
        vip_level: 'u.vip_level',
        phone: 'u.phone'
      };

      if (columnMapping[payload.sortBy]) {
        orderCondition = `ORDER BY ${columnMapping[payload.sortBy]} ${payload.order}`;
      } else {
        orderCondition = `ORDER BY u.created_at ${payload.order || 'desc'}`;
      }
    } else {
      orderCondition = `ORDER BY u.created_at ${payload.order || 'desc'}`;
    }
    
    const queryParams = {
      tenantIdArray,
      botUserJoin,
      loginHistoryJoin: '',
      timeCondition,
      userDetailsCondition: payload.userDetails || null,
      loginDetailsCondition: payload.loginDetails || null,
      bankDetailsCondition: payload.bankDetails || null,
      casinoDetailsCondition: payload.casinoDetails || null,
      exchangeDetailsCondition: payload.exchangeDetails || null,
      playerTypeCondition,
      agentCondition,
      limit: payload.size || 5000,
      offset: 0,
      orderCondition,
      isCountOnly: false,
      hasExchangeDetails: !!payload.exchangeDetails,
      hasCasinoDetails: !!payload.casinoDetails,
      hasBankDetails: !!payload.bankDetails,
      hasLoginDetails: !!payload.loginDetails,
      skipTenantFilter,
      queryReplacements: {}
    };
    if (!skipTenantFilter) {
      queryParams.queryReplacements.tenantIdArray = tenantIdArray.length > 0 ? tenantIdArray : [0];
    }

    if (timeCondition) {
      const { start, end } = getDateRange(
        payload.timeType,
        payload.timeZoneName,
        payload.startDate, 
        payload.endDate, 
        payload.timeZoneName
      );
      
      queryParams.queryReplacements.startDate = start.utc().format('YYYY-MM-DD HH:mm:ss').concat('.000000');
      queryParams.queryReplacements.endDate = end.utc().format('YYYY-MM-DD HH:mm:ss').concat('.999999');
    }
    
    if (tempAgentId) {
      queryParams.queryReplacements.tempAgentId = tempAgentId;
    }
    
    if (agentIds.length > 0) {
      queryParams.queryReplacements.agentIds = agentIds;
    }
    
    if (payload.userDetails) {
      queryParams.queryReplacements.userDetailsPattern = `%${payload.userDetails}%`;
    }
    
    if (payload.bankDetails) {
      queryParams.queryReplacements.bankDetailsPattern = `%${payload.bankDetails}%`;
    }
    
    if (payload.casinoDetails) {
      queryParams.queryReplacements.casinoDetailsPattern = `%${payload.casinoDetails}%`;
      queryParams.queryReplacements.casinoPattern = `%${payload.casinoDetails}%`;
    }
    
    if (payload.exchangeDetails) {
      queryParams.queryReplacements.exchangeDetailsPattern = `%${payload.exchangeDetails}%`;
      queryParams.queryReplacements.exchangeId = payload.exchangeDetails;
      queryParams.queryReplacements.exchangePattern = `%${payload.exchangeDetails}%`;
    }
    
    if (payload.loginDetails) {
      const isDateFormat = /^\d{4}-\d{2}-\d{2}$/.test(payload.loginDetails);
      if (isDateFormat) {
        queryParams.queryReplacements.loginDate = payload.loginDetails;
      } else {
        queryParams.queryReplacements.loginDetailsPattern = `%${payload.loginDetails}%`;
      }
    }

    const countParams = { ...queryParams, isCountOnly: true };
    const { query: countQuery, queryReplacements: countReplacements } = constructQueryForCountAndData(countParams);

    const countResult = await sequelize.query(countQuery, {
      replacements: countReplacements,
      type: sequelize.QueryTypes.SELECT
    });

    const totalRecords = parseInt(countResult[0]?.total || 0);
    
    if (totalRecords === 0) {
      await db.ExportCsvCenter.update({
        status: EXPORT_CSV_STATUS.DONE,
        message: 'No data found'
      }, {
        where: { id: data.id }
      });
      return true;
    }

    function createCsvHeaders(allowedAttributes) {
      const includeLoginFields = allowedAttributes.includes('ip_address');
      const includeCasinoFields = allowedAttributes.includes('round_id');
      const includeExchangeFields = allowedAttributes.includes('market_id');
      const headers = [];

      allowedAttributes.forEach(attribute => {
        switch (attribute) {
          case 'id': headers.push({ id: 'id', title: 'User ID' }); break;
          case 'user_name': headers.push({ id: 'user_name', title: 'Username' }); break;
          case 'email': headers.push({ id: 'email', title: 'Email' }); break;
          case 'first_name': headers.push({ id: 'first_name', title: 'First Name' }); break;
          case 'last_name': headers.push({ id: 'last_name', title: 'Last Name' }); break;
          case 'phone': headers.push({ id: 'phone', title: 'Phone' }); break;
          case 'city': headers.push({ id: 'city', title: 'City' }); break;
          case 'zip_code': headers.push({ id: 'zip_code', title: 'Zip Code' }); break;
          case 'country_code': headers.push({ id: 'country_code', title: 'Country' }); break;
          case 'created_at': headers.push({ id: 'created_at', title: 'Registration Date' }); break;
          case 'last_login_date': headers.push({ id: 'last_login_date', title: 'Last Login Date' }); break;
          case 'currency': headers.push({ id: 'currency_code', title: 'Currency' }); break;
          case 'vip_level': headers.push({ id: 'vip_level', title: 'VIP Level' }); break;
          case 'status': headers.push({ id: 'active', title: 'Status' }); break;
          case 'gender': headers.push({ id: 'gender', title: 'Gender' }); break;

          case 'ip_address': 
            headers.push({ id: 'ip', title: 'Last Login IP' });
            if (includeLoginFields) {
              headers.push({ id: 'device_id', title: 'Device ID' });
              headers.push({ id: 'device_type', title: 'Device Type' });
              headers.push({ id: 'device_model', title: 'Device Model' });
              headers.push({ id: 'network', title: 'Network' });
              headers.push({ id: 'login_count', title: 'Login Count' });
            }
            break;
          
          
          case 'bank_name': 
            headers.push({ id: 'bank_name', title: 'Bank Name' });
            headers.push({ id: 'bank_count', title: 'Bank Account Count' });
            break;
          case 'ifsc_code': headers.push({ id: 'bank_ifsc_code', title: 'Bank IFSC Code' }); break;
          case 'account_number': headers.push({ id: 'account_number', title: 'Account Number' }); break;
          
          case 'round_id': 
            if (includeCasinoFields) {
              headers.push({ id: 'round_id', title: 'Round ID' });
              // headers.push({ id: 'casino_count', title: 'Casino count' });
            }
            break;
          case 'market_id': 
            if (includeExchangeFields) {
              // headers.push({ id: 'market', title: 'Latest Market' });
              headers.push({ id: 'market_id', title: 'Market ID' });
              // headers.push({ id: 'exchange_count', title: 'Exchange Transactions Count' });
            }
            break;
          
          default: break;
        }
      });
      
      return headers;
    }

    const csvHeaders = createCsvHeaders(allowedAttributes);
    

    const csvStringifier = createObjectCsvStringifier({ header: csvHeaders });
    const chunkSize = 5000;
    const totalChunks = Math.ceil(totalRecords / chunkSize);
    const uuid = uuidv4().replace(/-/g, '');

    const key = `tenants/${tenantId}/csv/risk_evaluation/risk_evaluation_${uuid}.csv`;

    // Get AWS S3 config
    const s3Config = config.getProperties().s3;


    // Process data in chunks
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const offset = chunkIndex * chunkSize;
      
      // Update query params for pagination
      queryParams.offset = offset;
      queryParams.limit = chunkSize;
      
      // Get the data query for this chunk using the optimized query builder
      const { query: dataQuery, queryReplacements: dataReplacements } = constructQueryForCountAndData(queryParams);
      
      const results = await sequelize.query(dataQuery, {
        replacements: dataReplacements,
        type: sequelize.QueryTypes.SELECT
      })

      if (results.length === 0) continue;

      const formatDate = (dateValue, timeZoneName) => {
        if (!dateValue) return '';
        return timeZoneName 
          ? moment.utc(dateValue).tz(timeZoneName).format('YYYY-MM-DD HH:mm:ss')
          : moment.utc(dateValue).format('YYYY-MM-DD HH:mm:ss');
      };

      const csvData = results.map(user => {
        const rowData = {};
        
        if (allowedAttributes.includes('id')) rowData['id'] = user.id;
        if (allowedAttributes.includes('user_name')) rowData['user_name'] = user.user_name;
        if (allowedAttributes.includes('email')) rowData['email'] = user.email;
        if (allowedAttributes.includes('first_name')) rowData['first_name'] = user.first_name;
        if (allowedAttributes.includes('last_name')) rowData['last_name'] = user.last_name;

        if (allowedAttributes.includes('phone')) rowData['phone'] = user.phone;
        if (allowedAttributes.includes('city')) rowData['city'] = user.city;
        if (allowedAttributes.includes('zip_code')) rowData['zip_code'] = user.zip_code;
        if (allowedAttributes.includes('country_code')) rowData['country_code'] = user.country_code;
        
        if (allowedAttributes.includes('created_at')) {
          rowData['created_at'] = formatDate(user.created_at, payload.timeZoneName);
        }
        
        if (allowedAttributes.includes('last_login_date')) {
          rowData['last_login_date'] = formatDate(user.latest_login?.last_login_date, payload.timeZoneName);
        }
        
        if (allowedAttributes.includes('currency')) rowData['currency_code'] = user.currency_code;
        if (allowedAttributes.includes('vip_level')) rowData['vip_level'] = user.vip_level;
        if (allowedAttributes.includes('status')) rowData['active'] = user.active ? 'Active' : 'Inactive';
        if (allowedAttributes.includes('gender')) rowData['gender'] = user.gender;

        
        if (allowedAttributes.includes('ip_address')) {
          rowData['ip'] = user.latest_login?.ip || '';
          rowData['device_id'] = user.latest_login?.device_id || '';
          rowData['device_type'] = user.latest_login?.device_type || '';
          rowData['device_model'] = user.latest_login?.device_model || '';
          rowData['network'] = user.latest_login?.network || '';
          rowData['login_count'] = user.login_count || 0;

        }
        
        if (allowedAttributes.includes('bank_name')){
           rowData['bank_name'] = user.latest_bank_detail?.bank_name || '';
           rowData['bank_count'] = user.bank_count || 0;
        }

        if (allowedAttributes.includes('ifsc_code')) rowData['bank_ifsc_code'] = user.latest_bank_detail?.bank_ifsc_code || '';
        if (allowedAttributes.includes('account_number')) rowData['account_number'] = user.latest_bank_detail?.account_number || '';
        
        if (allowedAttributes.includes('round_id')) {
          rowData['round_id'] = payload.casinoDetails ? payload.casinoDetails : '';
          // rowData['casino_count'] = user.casino_count || 0;
        }

        if (allowedAttributes.includes('market_id')) {
          // rowData['market'] = user.latest_exchange?.market || '';
          rowData['market_id'] = payload.exchangeDetails ? payload.exchangeDetails : '';
          // rowData['exchange_count'] = user.exchange_count || 0;
        }

        return rowData;
      });

      const csvChunk = csvStringifier.stringifyRecords(csvData);

      try {

        if (chunkIndex === 0) {
          const initialContent = csvStringifier.getHeaderString() + csvChunk;
          
          await s3.send(new PutObjectCommand({
            ACL: 'public-read',
            Bucket: s3Config.bucket,
            Key: key,
            Body: initialContent,
            ContentType: 'text/csv'
          }));
        } else {
          let existingContent = '';
          try {
            const getObjectResponse = await s3.send(new GetObjectCommand({
              Bucket: s3Config.bucket,
              Key: key
            }));
            
            existingContent = await new Promise((resolve, reject) => {
              const chunks = [];
              getObjectResponse.Body.on('data', (chunk) => chunks.push(chunk));
              getObjectResponse.Body.on('end', () => resolve(Buffer.concat(chunks).toString('utf8')));
              getObjectResponse.Body.on('error', reject);
            });
          } catch (err) {
            if (err.name !== 'NoSuchKey') throw err;
            existingContent = csvStringifier.getHeaderString();
          }
          
          const combinedContent = existingContent + csvChunk;
  
          await s3.send(new PutObjectCommand({
            ACL: 'public-read',
            Bucket: s3Config.bucket,
            Key: key,
            Body: combinedContent,
            ContentType: 'text/csv'
          }));
        }
        
        await delay(500);
        
      } catch (error) {
        console.error(`Error uploading chunk ${chunkIndex} to S3:`, error);
        throw error;
      }
    }

    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    }, {
      where: { id: data.id }
    });

    return true
  } catch (error) {
    console.error("Error in risk evaluation export:", error);

    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.ERROR
    }, {
      where: { id: data.id }
    })
    
    throw error
  }
}