import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import moment from 'moment';
import { QueryTypes } from 'sequelize';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { CHECK_SBO_DOMAIN } from '../../../utils/constants/constant';
import { BOT_ALLOWED_TENANTS, CASINO_PROVIDER_IDS_ARRAY, EXPORT_CSV_STATUS, GAME_ID_PROVIDER_IDS_ARRAY, SPORT_PROVIDER_IDS_ARRAY, ST8_PROVIDER_ID, ST8_SPORTS_SEAT_IDS_ARRAY, TABLE_ID_PROVIDERS_ARRAY } from '../../constants';
import { fetchAgentIds, formatAmount, getCurrencyById, getCurrencyByValue, getSportsMarkets, getTransactionTypeByValueOrId } from '../../helpers';
import getRolesDetails from '../getAdminRolesDetail';
import { getColumnPermissions } from '../getPermissionsForPlayerCsv';
import { getDateRange } from './getStartEndDates';

export default async (data) => {
  try {
    const payload = data.payload
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.IN_PROGRESS
    },
      {
        where: { id: data.id }
      }
    )

    let currentUser = { id: data.adminId, parentType: data.adminType }
    let isGameProviderAndTypeFilterApplied = false
    let userFilterApplied = false
    const checkSboRequest = payload.sbo_request
    let isSuperAdmin = false;
    if (payload.timeZoneName === 'UTC +00:00') {
      payload.timeZoneName = 'UTC'
    }

    const inputParams = {
      tenantIds: [],
      startDate: '',
      endDate: '',
      agentIds: [],
      subAgentIds: [],
      currency: payload.currency ? getCurrencyByValue(payload.currency)?.id : payload.currency,
      actionType: [],
      actionCategory: payload.actionCategory,
      gameProvider: payload.gameProvider,
      gameType: '',
      tenantBaseCurrency: '',
      transactionId: payload.transactionId,
      roundId: payload.roundId,
      userId: payload.userId,
      sportProviders: SPORT_PROVIDER_IDS_ARRAY,
      casinoProviders: CASINO_PROVIDER_IDS_ARRAY,
      gameIdProviders: GAME_ID_PROVIDER_IDS_ARRAY,
      st8ProviderId: ST8_PROVIDER_ID,
      st8SportsProviderSeatIds: ST8_SPORTS_SEAT_IDS_ARRAY,
      tableIdProviders: TABLE_ID_PROVIDERS_ARRAY,
      gameFilterTxTypes: [0, 1, 2, 7, 8, 9, 10, 13, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 46, 47, 53, 54, 55, 56, 57, 58, 61, 62, 63, 64, 65, 66, 67],
      financialFilterTxTypes: [3, 4, 5, 6, 11, 12, 14, 15, 16, 17, 19, 37, 38, 39, 40, 41, 42, 43, 44, 45, 48, 49, 50, 51, 52, 59, 60, 68, 69,70, 71, 72, 73, 74,75,76]
    }

    // ====================filters here ========================

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      })
      payload.tenantId = adminData.tenantId
    }

    // Tenant Filter Gathering
    const tenantId = Number(payload.tenantId)
    if (tenantId) {
      inputParams.tenantIds = [tenantId]
    } else if (data.adminType == 'SuperAdminUser') {
      const adminData = await db.SuperAdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['parentType', 'tenantIds']
      })
      if (adminData.parentType === 'Manager') {
        inputParams.tenantIds = adminData.tenantIds.map(id => parseInt(id));
      }
    }

    // Agent Filter Gathering
    payload.agentId = isNaN(payload.agentId) ? '' : Number(payload.agentId)
    let searchAgentId
    if (payload.agentId) {
      searchAgentId = payload.agentId
    } else if (data.adminType === 'AdminUser') {
      const roles = await getRolesDetails(currentUser.id)
      if (roles.includes('agent')) {
        searchAgentId = currentUser.id
      }
    }
    if (searchAgentId) {
      inputParams.agentIds = await fetchAgentIds(searchAgentId)
    }

    if (data.adminType == "AdminUser") {
      const currentUserRole = await getRolesDetails(data.adminId);
      if (currentUserRole.includes("agent")) {
        inputParams.subAgentIds = await fetchAgentIds(data.adminId);
      }
    }

    // Time Filter Gathering
    if (payload.timeType === 'custom') {
      payload.timePeriod = JSON.parse(payload.timePeriod)
    }
    const { start, end } = getDateRange(payload.timeType, payload.timeZoneName, payload.timePeriod.startDate, payload.timePeriod.endDate)
    inputParams.startDate = start.utc().format('YYYY-MM-DD HH:mm:ss').concat('.000000')
    inputParams.endDate = end.utc().format('YYYY-MM-DD HH:mm:ss').concat('.999999')

    // Game Type Filter Gathering
    if (['Jetfair', 'Powerplay', 'Bti Sportsbook', 'Sap Exchange', 'Saba Sportsbook'].includes(payload.gameProvider)) {
      payload.gameType = ''
    }

    // game_txs CTE Filters
    const gameTxsCteFilters = []
    const txsUserDataCteFilters = []

    // Action Type Filter Gathering
    const actionTypeArr = payload.actionType ? JSON.parse(payload.actionType) : []
    if (actionTypeArr.length && payload.type) {
      inputParams.actionType = await getTransactionTypeByValueOrId(actionTypeArr)
      gameTxsCteFilters.push(`transaction_type IN (${payload.type === 'game' ? ':gameFilterTxTypes' : ':financialFilterTxTypes'})`)
    } else {
      if (actionTypeArr.length) {
        inputParams.actionType = await getTransactionTypeByValueOrId(actionTypeArr)
      } else if (payload.type === 'game') {
        inputParams.actionType = inputParams.gameFilterTxTypes
      } else if (payload.type === 'financial') {
        inputParams.actionType = inputParams.financialFilterTxTypes
      } else if (payload.totals) {
        inputParams.actionType = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 46, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67]
      } else if (!payload.actionCategory && !payload.gameProvider && !payload.gameType) {
        inputParams.actionType = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41, 42, 43, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74]
      }
    }

    // Check SBO User Logic
    isSuperAdmin = currentUser.parentType === 'SuperAdminUser'
    let botUsersFilterType = 'all'
    let botWalletIds = []
    if (
      (tenantId && BOT_ALLOWED_TENANTS.
        includes(String(tenantId))) ||
      isSuperAdmin
    ) {
      // In case of super admin, do not check SBO request flag. Just check player category flag.
      if (
        isSuperAdmin ||
        (checkSboRequest === CHECK_SBO_DOMAIN[1] && checkSboRequest !== CHECK_SBO_DOMAIN[2])
      ) {
        if (payload.playerCategory === 'bot_players') {
          botUsersFilterType = 'bot'
        } else if (payload.playerCategory === 'real_players') {
          botUsersFilterType = 'real'
        }
      } else if (!checkSboRequest) {
        botUsersFilterType = 'real'
      }

      if (botUsersFilterType !== 'all') {
        // Find all bot users wallet Id
        botWalletIds = await sequelize.query(`
          SELECT
            w.id AS wallet_id
          FROM
            bot_users bu
            JOIN wallets w ON (w.owner_id = bu.user_id AND w.owner_type = 'User')
          ${tenantId ? 'WHERE bu.tenant_id = :tenantId' : ''}
        `, {
          type: QueryTypes.SELECT,
          replacements: { tenantId }
        })
        botWalletIds = botWalletIds.map(rec => rec.wallet_id)

        if (botWalletIds.length > 0) {
          if (botUsersFilterType === 'bot') {
            gameTxsCteFilters.push(`(source_wallet_id IN (${botWalletIds}) OR target_wallet_id IN (${botWalletIds}))`)
          } else if (botUsersFilterType === 'real') {
            gameTxsCteFilters.push(`COALESCE(source_wallet_id, 0) NOT IN (${botWalletIds}) AND COALESCE(target_wallet_id, 0) NOT IN (${botWalletIds})`)
          }
        }
      }
    }

    // User Id Filter
    if (payload.userId && !isNaN(payload.userId)) {
      let agentCond = ''
      if (inputParams.agentIds.length > 0) {
        agentCond = 'u.parent_id IN (:agentIds)'
      }
      if (inputParams.subAgentIds.length > 0) {
        agentCond = 'u.parent_id IN (:subAgentIds)' // Extra check in case of agent login to validate if the payload agent Id should be from his sub-agents
      }
      const botUserCondMap = {
        bot: 'bu.id IS NOT NULL',
        real: 'bu.id IS NULL',
        all: ''
      }
      const userData = await sequelize.query(`
        SELECT
          w.id AS wallet_id
        FROM
          wallets w
          JOIN users u ON (w.owner_id = u.id)
          LEFT JOIN bot_users bu ON (bu.user_id = w.owner_id)
        WHERE
          w.owner_id = :userId
          AND w.owner_type = 'User'
          ${agentCond ? 'AND ' + agentCond : ''}
          ${botUserCondMap[botUsersFilterType] ? 'AND ' + botUserCondMap[botUsersFilterType] : ''}
      `, {
        type: QueryTypes.SELECT,
        replacements: { userId: payload.userId, agentIds: inputParams.agentIds, subAgentIds: inputParams.subAgentIds }
      })
      if (!userData || userData.length <= 0) {
        throw new Error('Data not found!');
      }
      gameTxsCteFilters.push(`(source_wallet_id = '${userData[0].wallet_id}' OR target_wallet_id = '${userData[0].wallet_id}')`)
      userFilterApplied = true
    }

    if (inputParams.startDate && inputParams.endDate) {
      gameTxsCteFilters.push('created_at BETWEEN :startDate AND :endDate')
    }
    if (inputParams.actionType.length > 0) {
      gameTxsCteFilters.push('transaction_type IN (:actionType)')
    }
    if (payload.transactionId) {
      gameTxsCteFilters.push('transaction_id = :transactionId')
    }
    if (payload.roundId) {
      gameTxsCteFilters.push('round_id = :roundId')
    }
    if (inputParams.currency) {
      gameTxsCteFilters.push('(source_currency_id = :currency OR target_currency_id = :currency)')
    }

    if (!userFilterApplied && inputParams.agentIds.length > 0) {
      let agentFilter
      if (inputParams.subAgentIds.length > 0) {
        agentFilter = '(u.parent_id IN (:agentIds) AND u.parent_id IN (:subAgentIds))' // Extra check in case of agent login to validate if the payload agent Id should be from his sub-agents
      } else {
        agentFilter = 'u.parent_id IN (:agentIds)'
      }
      txsUserDataCteFilters.push(agentFilter)
    } else if (!userFilterApplied && inputParams.subAgentIds.length > 0) {
      const agentFilter = 'u.parent_id IN (:subAgentIds)'
      txsUserDataCteFilters.push(agentFilter)
    }

    if (inputParams.gameProvider) {
      // Query to get provider IDs
      let providerIds = await sequelize.query(`
        SELECT id FROM casino_providers WHERE name = :gameProvider
      `, { type: QueryTypes.SELECT, replacements: { gameProvider: inputParams.gameProvider } })

      providerIds = providerIds.map((row) => row.id)

      let providerCondition = ''
      if (providerIds.length > 0) {
        providerCondition = `provider_id IN (${providerIds})`
      }

      // Query to get transaction seat IDs
      let transactionSeatIds = await sequelize.query(`
        SELECT seat_ids FROM transactions_providers_list WHERE title = :gameProvider
      `, { type: QueryTypes.SELECT, replacements: { gameProvider: inputParams.gameProvider } })

      transactionSeatIds = transactionSeatIds[0]?.seat_ids || []

      let gameCondition = ''
      if (transactionSeatIds.length > 0) {
        const quotedSeatIds = transactionSeatIds.map(id => id.replace(/'/g, "''")).map(id => `'${id}'`).join(',')
        gameCondition = `seat_id IN (${quotedSeatIds})`
      }

      let finalProviderCondition = ''
      if (providerCondition && gameCondition) {
        finalProviderCondition = `(${providerCondition} OR ${gameCondition})`
      } else if (providerCondition) {
        finalProviderCondition = `${providerCondition}`
      } else if (gameCondition) {
        finalProviderCondition = `${gameCondition}`
      }
      if (finalProviderCondition) {
        isGameProviderAndTypeFilterApplied = true
        gameTxsCteFilters.push(finalProviderCondition)
      } else {
        throw new Error('Data not found!');
      }
    }

    if (payload.gameType) {
      // Fetch Provider IDs by Provider Name
      let providerIds = await sequelize.query(`
        SELECT id FROM casino_providers WHERE name = :gameProvider

        UNION ALL

        SELECT id FROM casino_providers
        WHERE
        name IN ('st8' , 'Whitecliff')
          AND id IN (
              SELECT provider_id::bigint FROM transactions_providers_list WHERE title = :gameProvider
          )
      `, {
        type: QueryTypes.SELECT,
        replacements: { gameProvider: inputParams.gameProvider }
      })
      providerIds = providerIds.map(obj => obj.id)

      // Fetch all UUIDs by Provider ID and Game Name
      const uuidsDbData = await sequelize.query(`
        SELECT DISTINCT uuid FROM casino_items WHERE name = :gameType AND provider IN (:providerIds)
      `, {
        type: QueryTypes.SELECT,
        replacements: { gameType: payload.gameType, providerIds: providerIds }
      })

      const uuids = uuidsDbData.map(obj => obj.uuid.replace(/'/g, "''")).map(id => `'${id}'`)

      if (uuids.length) {
        // Case for Red Tiger and NetEnt Games
        if (providerIds.length > 1) {
          const uuidsFromDb = uuidsDbData.map(obj => obj.uuid)
          const tableIds = uuidsFromDb.filter(id => !isNaN(id))
          const seatIds = uuidsFromDb.filter(id => isNaN(id)).map(id => `'${id}'`)
          if (tableIds.length && seatIds.length) {
            gameTxsCteFilters.push(`(table_id IN (${tableIds}) OR seat_id IN (${seatIds}))`)
          } else if (tableIds.length) {
            gameTxsCteFilters.push(`table_id IN (${tableIds})`)
          } else {
            gameTxsCteFilters.push(`seat_id IN (${seatIds})`)
          }
        } else if (GAME_ID_PROVIDER_IDS_ARRAY.includes(providerIds[0])) {
          gameTxsCteFilters.push(`game_id IN (${uuids})`)
        } else if (TABLE_ID_PROVIDERS_ARRAY.includes(providerIds[0])) {
          gameTxsCteFilters.push(`table_id IN (${uuids})`)
        } else {
          gameTxsCteFilters.push(`seat_id IN (${uuids})`)
        }
        isGameProviderAndTypeFilterApplied = true
      }
    }
    if (inputParams.actionCategory && !isGameProviderAndTypeFilterApplied) {
      if (inputParams.actionCategory === 'sports') {
        gameTxsCteFilters.push('provider_id IN (:sportProviders)')
      } else if (inputParams.actionCategory === 'casino') {
        gameTxsCteFilters.push('provider_id IN (:casinoProviders)')
      }
    }

    if (inputParams.tenantIds.length > 0) {
      if (!userFilterApplied) {
        gameTxsCteFilters.push('tenant_id IN (:tenantIds)')
      }
    }
    const txsUserDataCteWhereCondition = txsUserDataCteFilters.length ? `WHERE ${txsUserDataCteFilters.join(' AND ')}` : ''
    const gameTxsCteWhereCondition = gameTxsCteFilters.length ? `WHERE ${gameTxsCteFilters.join(' AND ')}` : ''

    const sort = payload.sortBy || 'created_at'
    const sortOrder = payload.order || 'DESC'
    let sortCondition = ''
    let sortConditionWithAlias = ''
    if (sort && sortOrder) {
      sortCondition = 'ORDER BY ' + sort + ' ' + sortOrder
      sortConditionWithAlias = 'ORDER BY t.' + sort + ' ' + sortOrder
    }

    // CSV column permissions
    const columnKetNameMap = {
      player_id: 'Player Id',
      user_name: 'Username',
      agent_name: 'Agent Name',
      transaction_id: 'Transaction ID',
      type: 'Type',
      game_type: 'Game Type',
      table_id: 'Table Id',
      creation_date: 'TIMESTAMP',
      action_type: "Action Type",
      balance_before: 'INITIAL BALANCE',
      bet_withdraw: 'Bet/Withdraw',
      win_deposit: 'Win/Deposit',
      after_balance: 'ENDING Balance',
      revenue: 'Revenue',
      status: 'Status',
      internal_tracking_id: 'Internal Tracking ID',
      round_id: 'Round ID',
      currency: 'Currency',
    };

    const columnKeyPermissions = {
      player_id: ["report_attributes", "player_id"],
      user_name: ["players_key", "user_name"],
      agent_name: ["players_key", "agent_details"],
      transaction_id: ["transaction_attributes", "transaction_id"],
      type: null,
      game_type: ['report_attributes', 'game_type'],
      table_id: ['report_attributes', 'table_id'],
      creation_date: ['report_attributes', 'activity_timestamp'],
      action_type: ["transaction_attributes", "action_type"],
      balance_before: ['players_key', 'before_balance'],
      bet_withdraw: ['transaction_attributes', 'bet_withdraw'],
      win_deposit: ['transaction_attributes', 'win_deposit'],
      after_balance: ['players_key', 'after_balance'],
      revenue: ['transaction_attributes', 'revenue'],
      status: ['report_attributes', 'status'],
      internal_tracking_id: ['report_attributes', 'internal_tracking_id'],
      round_id: ['transaction_attributes', 'round_id'],
      currency: ["report_attributes", "currency"],
    };

    const allowedColumns = await getColumnPermissions(columnKeyPermissions, data.adminId, inputParams.tenantIds[0], data.adminType);
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    // S3 Upload
    let awsTenantFolderName = 0;
    if (inputParams.tenantIds.length <= 1) {
      awsTenantFolderName = inputParams.tenantIds[0];
    }


    //new code
    const chunkSize = 5000
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3

    const uuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${awsTenantFolderName}/csv/unfied_trn/transaction_${uuid}.csv`

    const csvStringifier = createObjectCsvStringifier({
      header: allowedColumns.map(columnKey => ({ id: columnKey, title: columnKetNameMap[columnKey] }))
    })

    const allowedColumnsObj = {};
    allowedColumns.forEach(c => allowedColumnsObj[c] = true);

    // Get count
    const gameTransactionReportCntQuery = prepareGameTransactionReportCountQuery({
      gameTxsCteWhereCondition,
      txsUserDataCteWhereCondition,
      userFilterApplied
    })
    const totalRecords = await sequelize.query(gameTransactionReportCntQuery, { type: sequelize.QueryTypes.SELECT, useMaster: false, replacements: inputParams });
    const totalRecordsCount = totalRecords[0]?.total_count || 0
    const totalChunks = Math.ceil(totalRecordsCount / chunkSize)


    // Loop through each chunk
    for (let i = 0; i < totalChunks; i++) {
      const offset = i * chunkSize
      const paginationCondition = 'LIMIT ' + chunkSize + ' OFFSET ' + offset;

      const gameTransactionReportQuery = prepareGameTransactionReportQuery({
        gameTxsCteWhereCondition,
        txsUserDataCteWhereCondition,
        sortCondition,
        paginationCondition,
        sortConditionWithAlias,
        userFilterApplied
      })
      const reportData = await sequelize.query(gameTransactionReportQuery, { type: sequelize.QueryTypes.SELECT, userMaster: false, replacements: inputParams });

      // Set Game Name
      let casinoItemUUIDs = reportData
        .filter(reportRec => (
          reportRec.provider_id &&
          reportRec.table_id &&
          !SPORT_PROVIDER_IDS_ARRAY.includes(String(reportRec.provider_id)) &&
          !ST8_SPORTS_SEAT_IDS_ARRAY.includes(reportRec.table_id)
        ))
        .map(reportRec => reportRec.table_id)
      casinoItemUUIDs = [...new Set(casinoItemUUIDs)]

      let uuidProviderNameMap = {}
      if (casinoItemUUIDs.length) {
        const uuidProviderNameMapData = await sequelize.query(`
          SELECT json_object_agg(uuid, provider_name_map) AS uuid_provider_name_map FROM (
            SELECT uuid, json_object_agg(provider, name) AS provider_name_map FROM (
              SELECT DISTINCT ON (uuid, provider) uuid, provider, name FROM casino_items WHERE uuid IN (:casinoItemUUIDs)
            ) S1 GROUP BY uuid
          ) S2;
        `, {
          type: QueryTypes.SELECT,
          replacements: { casinoItemUUIDs }
        })
        uuidProviderNameMap = uuidProviderNameMapData[0]?.uuid_provider_name_map
      }

      if (uuidProviderNameMap) {
        reportData.forEach(reportRec => {
          if (
            reportRec.provider_id &&
            reportRec.table_id &&
            !SPORT_PROVIDER_IDS_ARRAY.includes(String(reportRec.provider_id)) &&
            !ST8_SPORTS_SEAT_IDS_ARRAY.includes(reportRec.table_id)
          ) {
            reportRec.game_name = uuidProviderNameMap[reportRec.table_id]?.[reportRec.provider_id] || '-';
          } else {
            reportRec.game_name = reportRec.seat_id
          }
        })
      }

      // Get Market Name
      await fetchSportMarketNames(reportData);

      let mainArr = []
      if (reportData.length > 0) {
        for (const report of reportData) {
          if (payload.timeZoneName) {
            report.created_at = moment.utc(report.created_at).tz(payload.timeZoneName).format('DD-MM-YYYY HH:mm:ss')
          }
          if (report.currency_id) {
            const id = parseInt(report.currency_id || 0)
            report.currency = getCurrencyById(id)?.value || null
          }
          if (report.transaction_type != null) {
            report.transaction_type = await getTransactionTypeByValueOrId(report.transaction_type)
          }

          const object = {
            ...(allowedColumnsObj.player_id && { player_id: report.user_id }),
            ...(allowedColumnsObj.user_name && { user_name: report.user_name }),
            ...(allowedColumnsObj.agent_name && { agent_name: report.agent_name }),
            ...(allowedColumnsObj.transaction_id && { transaction_id: report.transaction_id }),
            ...(allowedColumnsObj.type && { type: report.type }),
            ...(allowedColumnsObj.game_type && { game_type: report.game_type || report.game_name || '' }),
            ...(allowedColumnsObj.table_id && { table_id: report.table_id }),
            ...(allowedColumnsObj.creation_date && { creation_date: report.created_at }),
            ...(allowedColumnsObj.action_type && { action_type: report.transaction_type }),
            ...(allowedColumnsObj.balance_before && { balance_before: formatAmount(report.initial_balance,tenantId,report.currency) || '0.00' }),
            ...(allowedColumnsObj.bet_withdraw && { bet_withdraw: formatAmount(report.deducted_amount,tenantId,report.currency) || '0.00' }),
            ...(allowedColumnsObj.win_deposit && { win_deposit: formatAmount(report.added_amount,tenantId,report.currency)|| '0.00' }),
            ...(allowedColumnsObj.after_balance && { after_balance: formatAmount(report.ending_balance,tenantId,report.currency) || '0.00' }),
            ...(allowedColumnsObj.revenue && { revenue: formatAmount(report.revenue,tenantId,report.currency) || '0.00' }),
            ...(allowedColumnsObj.status && { status: report.status }),
            ...(allowedColumnsObj.internal_tracking_id && { internal_tracking_id: report.internal_tracking_id }),
            ...(allowedColumnsObj.round_id && { round_id: report.round_id }),
            ...(allowedColumnsObj.currency && { currency: report.currency }),
          }
          mainArr = [...mainArr, object]

        }
      }

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(mainArr)


      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }
      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      await delay(500)
    }
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )
    return true
  } catch (error) {
    // Update export status to failed
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.FAILED
    },
      {
        where: { id: data.id }
      }
    )
    throw error
  }
}

function prepareGameTransactionReportQuery ({
  gameTxsCteWhereCondition,
  txsUserDataCteWhereCondition,
  sortCondition,
  paginationCondition,
  sortConditionWithAlias,
  userFilterApplied
}) {
  let finalQ = ''
  if (userFilterApplied) {
    // Add the UNION ALL for the User Filter so that Postgres Query Planner uses the Source and Target Wallet ID related Indexes for Fast Result Across the Multiple Partitions.
    const userFilterIndexHelper = INDEXING_HELPER_USER_FILTER_QUERY + '\n' + 'UNION ALL' + '\n';
    finalQ = `
      SET enable_seqscan = OFF;

      WITH game_txs AS (
        ${userFilterIndexHelper}
        SELECT
          CASE
            WHEN transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN source_wallet_id
            WHEN transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_wallet_id
            ELSE COALESCE(target_wallet_id, source_wallet_id) -- For transaction type (2,10,47)
          END AS user_wallet_id,
          id,
          status,
          round_id,
          transaction_id,
          debit_transaction_id,
          seat_id,
          table_id,
          game_id,
          provider_id,
          tenant_id,
          created_at,
          CASE
            WHEN transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN amount
            WHEN transaction_type IN (2,10,47) AND target_wallet_id IS NOT NULL THEN amount
            ELSE 0
          END AS added_amount,
          CASE
            WHEN transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN amount
            WHEN transaction_type IN (2,10,47) AND source_wallet_id IS NOT NULL THEN amount
            ELSE 0
          END AS deducted_amount,

          source_before_balance,
          target_before_balance,
          source_after_balance,
          target_after_balance,
          source_wallet_id,
          target_wallet_id,
          transaction_type,
          source_currency_id,
          target_currency_id
        FROM transactions t
        ${gameTxsCteWhereCondition}
        ${sortCondition}
        ${paginationCondition}
      )
      SELECT
        w.owner_id AS user_id,
        u.user_name,
        adu.agent_name,
        t.round_id,
        t.transaction_id,
        CASE
          WHEN transaction_type IN (46,47) THEN 'Financial/Game'
          WHEN transaction_type IN (:gameFilterTxTypes) THEN 'Game'
          ELSE 'Financial'
        END AS type,
        CASE
          WHEN t.provider_id IN (:gameIdProviders) THEN game_id
          WHEN t.provider_id IN (:tableIdProviders) THEN table_id::text
          ELSE t.seat_id
        END AS table_id,
        t.created_at::text AS created_at,
        CASE
          WHEN t.transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN source_before_balance::numeric(20,2)
          WHEN t.transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_before_balance::numeric(20,2)
          WHEN source_wallet_id IS NULL THEN target_before_balance::numeric(20,2) -- For transaction type (2,10,47)
          ELSE source_before_balance::numeric(20,2) -- For transaction type (2,10,47)
        END AS initial_balance,
        CASE
          WHEN t.transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN source_after_balance::numeric(20,2)
          WHEN t.transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_after_balance::numeric(20,2)
          WHEN source_wallet_id IS NULL THEN target_after_balance::numeric(20,2) -- For transaction type (2,10,47)
          ELSE source_after_balance::numeric(20,2) -- For transaction type (2,10,47)
        END AS ending_balance,
        CASE
          WHEN t.transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN (source_after_balance - source_before_balance)::numeric(20,2)
          WHEN t.transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN (target_after_balance - target_before_balance)::numeric(20,2)
          WHEN source_wallet_id IS NULL THEN (target_after_balance - target_before_balance)::numeric(20,2) -- For transaction type (2,10,47)
          ELSE (source_after_balance - source_before_balance)::numeric(20,2) -- For transaction type (2,10,47)
        END AS revenue,
        t.added_amount::numeric(20,2) AS added_amount,
        t.deducted_amount::numeric(20,2) AS deducted_amount,
        t.status,
        t.id AS internal_tracking_id,
        t.debit_transaction_id,
        t.provider_id,
        w.currency_id,
        t.tenant_id,
        t.seat_id,
        t.transaction_type,
        t.source_currency_id,
        t.target_currency_id,
        bu.id AS bot_user_id
      FROM game_txs t
        LEFT JOIN wallets w ON (w.id = t.user_wallet_id)
        LEFT JOIN users u ON (u.id = w.owner_id)
        LEFT JOIN bot_users bu ON (bu.id = w.owner_id)
        LEFT JOIN admin_users adu ON (adu.id = u.parent_id)
        -- Get Provider data
        LEFT JOIN casino_providers cp ON (cp.id = t.provider_id)
        LEFT JOIN transactions_providers_list tp ON (cp.name IN ('st8', 'Whitecliff') AND t.provider_id= cp.id AND tp.seat_ids ? t.seat_id)
      ${sortConditionWithAlias};

      SET enable_seqscan = ON;
    `
  } else {
    finalQ = `
      SET enable_seqscan = OFF;

      WITH game_txs AS (
        SELECT
          CASE
            WHEN transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN source_wallet_id
            WHEN transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_wallet_id
            ELSE COALESCE(target_wallet_id, source_wallet_id) -- For transaction type (2,10,47)
          END AS user_wallet_id,
          id,
          status,
          round_id,
          transaction_id,
          debit_transaction_id,
          seat_id,
          table_id,
          game_id,
          provider_id,
          tenant_id,
          created_at,
          CASE
            WHEN transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN amount
            WHEN transaction_type IN (2,10,47) AND target_wallet_id IS NOT NULL THEN amount
            ELSE 0
          END AS added_amount,
          CASE
            WHEN transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN amount
            WHEN transaction_type IN (2,10,47) AND source_wallet_id IS NOT NULL THEN amount
            ELSE 0
          END AS deducted_amount,
          source_before_balance,
          target_before_balance,
          source_after_balance,
          target_after_balance,
          source_wallet_id,
          target_wallet_id,
          transaction_type,
          source_currency_id,
          target_currency_id
        FROM transactions t
        ${gameTxsCteWhereCondition}
      ),
      txs_user_data AS (
        SELECT
          t.*
        FROM
          game_txs t
          JOIN wallets w ON (w.id = t.user_wallet_id AND w.owner_type = 'User')
          ${txsUserDataCteWhereCondition ? 'JOIN users u ON (w.owner_id = u.id)' : ''}
        ${txsUserDataCteWhereCondition}
        ${sortConditionWithAlias}
        ${paginationCondition}
      )
      SELECT
        w.owner_id AS user_id,
        u.user_name,
        adu.agent_name,
        t.round_id,
        t.transaction_id,
        CASE
          WHEN transaction_type IN (46,47) THEN 'Financial/Game'
          WHEN transaction_type IN (:gameFilterTxTypes) THEN 'Game'
          ELSE 'Financial'
        END AS type,
        CASE
          WHEN t.provider_id IN (:gameIdProviders) THEN game_id
          WHEN t.provider_id IN (:tableIdProviders) THEN table_id::text
          ELSE t.seat_id
        END AS table_id,
        t.created_at::text AS created_at,
        CASE
          WHEN t.transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN source_before_balance::numeric(20,2)
          WHEN t.transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_before_balance::numeric(20,2)
          WHEN source_wallet_id IS NULL THEN target_before_balance::numeric(20,2) -- For transaction type (2,10,47)
          ELSE source_before_balance::numeric(20,2) -- For transaction type (2,10,47)
        END AS initial_balance,
        CASE
          WHEN t.transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN source_after_balance::numeric(20,2)
          WHEN t.transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_after_balance::numeric(20,2)
          WHEN source_wallet_id IS NULL THEN target_after_balance::numeric(20,2) -- For transaction type (2,10,47)
          ELSE source_after_balance::numeric(20,2) -- For transaction type (2,10,47)
        END AS ending_balance,
        CASE
          WHEN t.transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN (source_after_balance - source_before_balance)::numeric(20,2)
          WHEN t.transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN (target_after_balance - target_before_balance)::numeric(20,2)
          WHEN source_wallet_id IS NULL THEN (target_after_balance - target_before_balance)::numeric(20,2) -- For transaction type (2,10,47)
          ELSE (source_after_balance - source_before_balance)::numeric(20,2) -- For transaction type (2,10,47)
        END AS revenue,
        t.added_amount::numeric(20,2) AS added_amount,
        t.deducted_amount::numeric(20,2) AS deducted_amount,
        t.status,
        t.id AS internal_tracking_id,
        t.debit_transaction_id,
        t.provider_id,
        w.currency_id,
        t.tenant_id,
        t.seat_id,
        t.transaction_type,
        t.source_currency_id,
        t.target_currency_id,
        bu.id AS bot_user_id
      FROM txs_user_data t
        LEFT JOIN wallets w ON (w.id = t.user_wallet_id)
        LEFT JOIN users u ON (u.id = w.owner_id)
        LEFT JOIN bot_users bu ON (bu.id = w.owner_id)
        LEFT JOIN admin_users adu ON (adu.id = u.parent_id)
        -- Get Provider data
        LEFT JOIN casino_providers cp ON (cp.id = t.provider_id)
        LEFT JOIN transactions_providers_list tp ON (cp.name IN ('st8', 'Whitecliff') AND t.provider_id= cp.id AND tp.seat_ids ? t.seat_id)
      ${sortConditionWithAlias};

      SET enable_seqscan = ON;
    `
  }
  return finalQ
}

function prepareGameTransactionReportCountQuery ({
  gameTxsCteWhereCondition,
  txsUserDataCteWhereCondition,
  userFilterApplied
}) {
  let finalQ = ''
  if (userFilterApplied) {
    finalQ = `
      SET enable_seqscan = OFF;

      SELECT COUNT(*) AS total_count
      FROM transactions
      ${gameTxsCteWhereCondition};

      SET enable_seqscan = ON;
    `
  } else {
    finalQ = `
      SET enable_seqscan = OFF;

      WITH game_txs AS (
        SELECT
          CASE
            WHEN transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN source_wallet_id
            WHEN transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_wallet_id
            ELSE COALESCE(target_wallet_id, source_wallet_id) -- For transaction type (2,10,47)
          END AS user_wallet_id
        FROM transactions
        ${gameTxsCteWhereCondition}
      )
      SELECT
        COUNT(*) AS total_count
      FROM game_txs t
        JOIN wallets w ON (w.id = t.user_wallet_id AND owner_type = 'User')
        ${txsUserDataCteWhereCondition ? 'JOIN users u ON (w.owner_id = u.id)' : ''}
      ${txsUserDataCteWhereCondition};

      SET enable_seqscan = ON;`
  }
  return finalQ
}

async function fetchSportMarketNames(reportData) {
  let debitTxIds = [];
  reportData.forEach(report => {
    if (SPORT_PROVIDER_IDS_ARRAY.includes(String(report.provider_id))) {
      debitTxIds.push(report.debit_transaction_id);
    }
  });

  if (debitTxIds.length <= 0) return;

  let marketNames = await getSportsMarkets(debitTxIds);

  if (!marketNames) return;

  reportData.forEach(report => {
    if (SPORT_PROVIDER_IDS_ARRAY.includes(String(report.provider_id))) {
      report.game_type = (marketNames[report.debit_transaction_id] ? String(marketNames[report.debit_transaction_id]) : report.seat_id);
    }
  });
}

const INDEXING_HELPER_USER_FILTER_QUERY =
`
SELECT
  1 AS user_wallet_id,
  id,
  status,
  round_id,
  transaction_id,
  debit_transaction_id,
  seat_id,
  table_id,
  game_id,
  provider_id,
  tenant_id,
  created_at,
  1 AS added_amount,
  1 AS deducted_amount,
  source_before_balance,
  target_before_balance,
  source_after_balance,
  target_after_balance,
  source_wallet_id,
  target_wallet_id,
  transaction_type,
  source_currency_id,
  target_currency_id
FROM
  transactions
WHERE false
`;
