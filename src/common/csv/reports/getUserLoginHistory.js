import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import moment from 'moment';
import { Op, fn, literal, where as sequelizeWhere } from 'sequelize';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { EXPORT_CSV_STATUS } from '../../constants';

const CHUNK_SIZE = 1000;

export default async (data) => {

  const payload = data.payload;

  await db.ExportCsvCenter.update(
    { status: EXPORT_CSV_STATUS.IN_PROGRESS },
    { where: { id: data.id } }
  );

  let params = {
    tenantId: 0,
    adminId: data.adminId,
    startDate: moment.utc(payload.startDate).startOf('day').toISOString(),
    endDate: moment.utc(payload.endDate).endOf('day').toISOString(),
    playerId: payload.playerId,
    deviceType: payload.deviceType,
    location: payload.location,
  };

  if (data.adminType === 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: { id: data.adminId },
      attributes: ['tenantId'],
    });
    params.tenantId = adminData.tenantId;
  }

  try {
    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'userName', title: 'Username' },
        { id: 'ip', title: 'IP Address' },
        { id: 'location', title: 'Location' },
        { id: 'network', title: 'Network' },
        { id: 'version', title: 'Version' },
        { id: 'deviceId', title: 'Device Id' },
        { id: 'deviceType', title: 'Device Type' },
        { id: 'deviceModel', title: 'Device Model' },
        { id: 'signInCount', title: 'Signin count' },
        { id: 'lastLoginDate', title: 'Last Login Date' }
      ]
    })

    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3
    const csvUuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${params.tenantId}/csv/user_login_history/user_login_history_${csvUuid}.csv`
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    let where = {
      tenantId: params.tenantId
    }

    if (!params.playerId) {
      where.lastLoginDate = {
        [Op.between]: [params.startDate, params.endDate
        ]
      }
    }

    where.userId = {
      [Op.in]: sequelize.literal(`(
            SELECT id FROM users WHERE parent_id = ${params.adminId}
            ${params.playerId ? `AND id = ${params.playerId}` : ''}
          )`)
    }

    if (params.deviceType) {
      where.deviceType = { [Op.iLike]: `%${params.deviceType.replace(/'/g, '')}%` }
    }

    if (params.location) {
      where[Op.and] = [
        sequelizeWhere(
          fn('LOWER', literal(`data->>'city'`)),
          { [Op.iLike]: `%${params.location.toLowerCase()}%` }
        )
      ];
    }

    let offset = 0;
    let totalRecords = 0;
    let hasMoreRecords = true;

    while (hasMoreRecords) {

      const userLoginHistory = await db.UserLoginHistory.findAll({
        where,
        include: [
          {
            model: db.User,
            raw: true,
            attributes: ['userName']
          }
        ],
        order: [['lastLoginDate', 'DESC']],
        offset,
        limit: CHUNK_SIZE,
        raw: true,
        nest: true
      })

      if (userLoginHistory.length < CHUNK_SIZE) {
        hasMoreRecords = false;
      }

      const resultData = userLoginHistory.reduce((acc, curr) => {
        const newObj = {
          ...curr,
          userName: curr.User?.userName,
          location: JSON.stringify(curr?.data || {}),
          lastLoginDate: moment(curr.lastLoginDate)
        }
        acc.push(newObj)
        return acc
      }, [])

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(resultData)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name != 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }

      // Append data or create new file
      const finalCsvContent = existingCsvContent ? existingCsvContent + csvData : csvStringifier.getHeaderString() + csvData;

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      totalRecords += csvData.length
      offset += CHUNK_SIZE
      await delay(500)
    }

    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    return {
      totalRecords,
      csvUrl: key,
    };
  } catch (error) {
    console.log("Error in exporting User Login History", error);
    throw error;
  }
};
