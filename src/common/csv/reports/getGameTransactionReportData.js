import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { EXPORT_CSV_STATUS, OTB_TENANTS_CONFIG } from '../../../common/constants'
import config from '../../../configs/app.config'
import db from '../../../db/models'
import { s3 } from '../../../libs/awsS3Config'
import getESclient from '../../../libs/getEsClient'
import getRolesDetails from '../getAdminRolesDetail'
import getStartEndDates from './getStartEndDates'
import gameTransactionReportSearchKey from './searchByKey/gameTransactionReportSearchKey'
import { getDateInStringFormat } from '../../helpers'
const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')


export default async (data) => {
  let esClient = getESclient();
  const payload = data.payload
  await db.ExportCsvCenter.update({
    status: EXPORT_CSV_STATUS.IN_PROGRESS
  },
    {
      where: { id: data.id }
    }
  )
  let filter = []
  let mustArray = []
  let query
  let params = {
    tenantId: 0,
    agentId: 0
  }

  let time_period
  if (payload.time_type != 'custom') {
    time_period = await getStartEndDates(payload.time_type)
  } else {
    time_period = payload.datetime
  }

  // ====================filters here ========================

  if (data.adminType == 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: {
        id: data.adminId
      },
      attributes: ['tenantId']
    }
    )
    params.tenantId = adminData.tenantId
    const roles = await getRolesDetails(data.adminId)
    if (!roles.includes('owner')) {
      //condition for new sub-admin role
      if (roles.includes('owner') || roles.includes('sub-admin')) {
        params.agentId = payload?.agent_id ? payload.agent_id : null
      } else {
        params.agentId = payload?.agent_id ? payload.agent_id : data.adminId
      }
    } else {
      params.agentId = payload?.agent_id ? payload.agent_id : null
    }

  } else {
    params.tenantId = payload?.tenant_id ? payload.tenant_id : null
    params.agentId = payload?.agent_id ? payload.agent_id : null
  }

  if (params.tenantId) {
    filter = [...filter, { term: { tenant_id: params.tenantId } }]
  }

  if (payload?.time_type) {
    if (time_period.start_date && time_period.end_date) {
      if (payload.time_zone_name) {
        time_period.start_date = moment.tz(time_period.start_date, payload.time_zone_name).utc().format()
        time_period.end_date = moment.tz(time_period.end_date, payload.time_zone_name).utc().format()
      }

      let endDate = time_period.end_date
      filter = [...filter, {
        range: {
          created_at: {
            from: time_period.start_date,
            include_lower: true,
            to: endDate.replace('000000', '999999'),
            include_upper: true
          }
        }
      }]
    }
  }

  const otbTenants = config.get('env') === 'production' ? OTB_TENANTS_CONFIG.PROD : OTB_TENANTS_CONFIG.STAGE
  const isOTBEnabled = otbTenants[parseInt(params.tenantId)] ? true : false

  filter.push({ 'bool': { 'must_not': { 'bool': { 'must_not': { 'exists': { 'field': 'player_details' } } } } } })

  payload.owner_id = (payload.owner_id || null)
  payload.player_type = (payload.player_type || 'all')
  payload.game_provider = (payload?.game_provider || null)
  payload.game_type = (payload?.game_type || null)
  payload.currency = (payload?.currency || null)
  payload.internal_error_code = (payload?.internal_error_code || null)
  payload.action_type = (payload?.action_type || null)



  if (payload?.player_type == 'direct' && params.agentId != '') {
    filter.push({ 'term': { 'player_details.parent_id': { 'value': params.agentId } } })
  }
  if (payload?.owner_id) {
    filter.push({ 'term': { 'player_details.parent_chain_ids': { 'value': payload.owner_id } } })
  }

  if (params.agentId) {
    filter.push({ 'term': { 'player_details.parent_chain_ids': { 'value': params.agentId } } })
  }

  if (payload.currency) {
    filter.push({ 'term': { 'player_details.currency': { 'value': payload.currency } } })
  }

  if (payload.internal_error_code) {
    filter = [...filter, { term: { internal_error_code: payload.internal_error_code } }]
  }

  if (payload.game_provider) {
    filter.push({"bool":{"should":[{"term":{"custom_3":{"value":payload.game_provider}}},{"term":{"game_provider":{"value":payload.game_provider}}},{"term":{"custom_2":{"value":payload.game_provider}}}]}})
  }

  if (payload.action_category != '' && payload.game_provider == '') {
    if (payload.action_category == 'casino') {
      filter.push({ "bool": { "must_not": { "terms": { "game_provider": ["Jetfair"] } } } })

      filter.push({ "bool": { "must_not": { "terms": { "custom_2": ["Betting Exchange", "Sportsbook", "sbs_sportsbook", "bti_sportsbook", "sap_lobby"] } } } })
    } else {
      filter.push({ "bool": { "should": [{ "bool": { "must": { "terms": { "game_provider": ["Jetfair"] } } } }, { "bool": { "must": { "terms": { "game_provider": ["st8"] } }, "should": [{ "bool": { "must": { "terms": { "custom_2": ["Betting Exchange", "Sportsbook", "sbs_sportsbook", "bti_sportsbook", "sap_lobby"] } } } }], "minimum_should_match": 1 } }] } })
    }
  }

  if (payload.game_type) {
    filter = [...filter, { term: { game_type: payload.game_type } }]
  }


  if (payload.action_type) {
    filter = [...filter, { terms: { transaction_type: payload.action_type } }]
  }
  else {
    filter.push({
      terms: {
        transaction_type: [
          'bet',
          'win',
          'refund',
          'tip',
          'bet_non_cash',
          'win_non_cash',
          'tip_non_cash',
          'refund_non_cash',
          'exchange_place_bet_non_cash_debit',
          'exchange_place_bet_cash_debit',
          'exchange_place_bet_cash_credit',
          'exchange_refund_cancel_bet_non_cash_debit',
          'exchange_refund_cancel_bet_cash_debit',
          'exchange_refund_cancel_bet_non_cash_credit',
          'exchange_refund_cancel_bet_cash_credit',
          'exchange_refund_market_cancel_non_cash_debit',
          'exchange_refund_market_cancel_cash_debit',
          'exchange_refund_market_cancel_non_cash_credit',
          'exchange_refund_market_cancel_cash_credit',
          'exchange_settle_market_cash_credit',
          'exchange_settle_market_cash_debit',
          'exchange_resettle_market_cash_credit',
          'exchange_resettle_market_cash_debit',
          'exchange_cancel_settled_market_cash_credit',
          'exchange_cancel_settled_market_cash_debit',
          ...(isOTBEnabled ? ['refund_one_time_bonus', 'bet_one_time_bonus'] : [])
          // 'exchange_deposit_bonus_claim'
        ]
      }
    })
  }

  if (payload.search != '') {
    filter.push(gameTransactionReportSearchKey(payload.search))
  }
  // ========================filter ends here================================

  if (mustArray.length > 0) {
    query = mustArray
  } else {
    query = {
      match_all: {}
    }
  }

  if (filter.length > 0) {
    filter = {
      bool: {
        filter: filter
      }
    }
  }

  let sortData = {}
  let sortOrder = payload?.order ? payload.order : 'desc'
  if (payload?.sort_by) {
    let sortKey = payload.sort_by
    sortData[sortKey] = sortOrder
  }

  try {
    let searchDetails
    let limit = 1000
    let page = 0
    let offset = page * limit

    searchDetails = await esClient.search({
      index: config.getProperties().es_index.transactions_index_name,
      body: {
        query: {
          bool: {
            must: query,
            filter: filter
          }
        },
        sort: sortData,
        timeout: '3000s',
        track_total_hits: true,
        size: limit,
        from: offset
      }
    })


    const uuid = uuidv4().replace(/-/g, '')
    const filePath = `/tmp/transaction_${uuid}.csv`

    const csvWriter = createCsvWriter({
      path: filePath,
      header: [
        { id: 'player_id', title: 'Player ID' },
        { id: 'player_name', title: 'Player Name' },
        { id: 'agent_name', title: 'Agent Name' },
        { id: 'game_provider', title: 'Game Provider' },
        { id: 'game_name', title: 'Game Name' },
        { id: 'table_id', title: 'Table Id' },
        { id: 'round_id', title: 'RoundID' },
        { id: 'creation_date', title: 'Activity Timestamp' },
        { id: 'action_type', title: 'Action Type' },
        { id: 'currency', title: 'Currency' },
        { id: 'balance_before', title: 'Initial Balance' },
        { id: 'amount', title: 'Played Amount' },
        { id: 'after_balance', title: 'Ending Balance' },
        { id: 'transaction_id', title: 'Transaction ID' },
        { id: 'gp_error_code', title: 'GP Error Code' },
        { id: 'internal_error_code', title: 'Internal Error Code' },
        { id: 'internal_error_description', title: 'Internal Error Description' }
      ]
    });
    const csvData = searchDetails.body.hits.hits

    if (payload.time_zone_name) {
      const csvDataList = await Promise.all(
        csvData.map(async object => {
          // Formatting createdAt date with optional user timezone
          const createdDate = payload.time_zone_name ? moment.tz(object._source.created_at, payload.time_zone_name) : moment(object._source.created_at)
          object._source.created_at = createdDate.format('DD-MM-YYYY HH:mm:ss')
          return object
        })
      )
    }

    let mainArr = []
    let tenantId
    if (csvData.length > 0) {
      tenantId = csvData[0]._source.tenant_id
      for (const txn of csvData) {

        let object = {
          player_id: txn._source.player_details?.player_id || '',
          player_name: txn._source.player_details?.player_name || '',
          game_provider: (txn._source?.game_provider == 'st8' ? txn._source?.custom_3 : txn._source?.game_provider) || '',
          round_id: (txn._source?.game_provider == 'pgsoft' ? `'${txn._source?.round_id_s}'`: txn._source?.round_id_s )|| '',
          agent_name: txn._source?.player_details?.agent_name || '',
          creation_date: getDateInStringFormat(txn._source?.created_at || ''),
          game_name: txn._source?.table_name || (txn._source?.game_type || ''),
          currency: txn._source?.player_details?.currency || '',
          action_type: txn._source.transaction_type || '',
          amount: parseFloat(txn._source?.amount).toFixed(2) || '',
          balance_before: parseFloat(txn._source?.player_details?.before_balance).toFixed(2) || '',
          after_balance: parseFloat(txn._source?.player_details?.after_balance).toFixed(2) || '',
          transaction_id: txn._source.transaction_id || '',
          gp_error_code: txn._source?.internal_tracking_id || '',
          internal_error_code: txn._source?.gp_error_code || '',
          internal_error_description: txn._source?.internal_error_description || '',
          table_id: txn._source?.table_id || ((txn._source?.game_provider == 'st8' || txn._source?.game_provider == 'pgsoft') ? txn._source?.uuid : (txn._source?.game_id || ''))
        };

        mainArr = [...mainArr, object]
      }
    }
    await Promise.all([csvWriter.writeRecords(mainArr)])

    const totalData = searchDetails.body.hits.total.value
    let itval = parseFloat(totalData) / limit
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    for (let i = 1; i < itval; i++) {
      await delay(1000)
      offset = i * limit
      searchDetails =
        await esClient.search({
          index: config.getProperties().es_index.transactions_index_name,
          body: {
            query: {
              bool: {
                must: query,
                filter: filter
              }
            },
            sort: sortData,
            timeout: '3000s',
            track_total_hits: true,
            size: limit,
            from: offset
          }
        })
      let mainArr = []
      let tenantId
      const csvData = searchDetails.body.hits.hits
      if (payload.time_zone_name) {
        const csvDataList = await Promise.all(
          csvData.map(async object => {
            // Formatting createdAt date with optional user timezone
            const createdDate = payload.time_zone_name ? moment.tz(object._source.created_at, payload.time_zone_name) : moment(object._source.created_at)
            object._source.created_at = createdDate.format('DD-MM-YYYY HH:mm:ss')
            return object
          })
        )
      }
      if (csvData.length > 0) {
        tenantId = csvData[0]._source.tenant_id
        for (const txn of csvData) {

          let object = {
            player_id: txn._source.player_details?.player_id || '',
            player_name: txn._source.player_details?.player_name || '',
            game_provider: (txn._source?.game_provider == 'st8' ? txn._source?.custom_3 : txn._source?.game_provider) || '',
            round_id: txn._source?.round_id_s || '',
            agent_name: txn._source?.player_details?.agent_name || '',
            creation_date: getDateInStringFormat(txn._source?.created_at || ''),
            game_name: txn._source?.table_name || (txn._source?.game_type || ''),
            currency: txn._source?.player_details?.currency || '',
            action_type: txn._source.transaction_type || '',
            amount: parseFloat(txn._source?.amount).toFixed(2) || '',
            balance_before: parseFloat(txn._source?.player_details?.before_balance).toFixed(2) || '',
            after_balance: parseFloat(txn._source?.player_details?.after_balance).toFixed(2) || '',
            transaction_id: txn._source.transaction_id || '',
            gp_error_code: txn._source?.internal_tracking_id || '',
            internal_error_code: txn._source?.gp_error_code || '',
            internal_error_description: txn._source?.internal_error_description || '',
            table_id: txn._source?.table_id || ((txn._source?.game_provider == 'st8' || txn._source?.game_provider == 'pgsoft') ? txn._source?.uuid : (txn._source?.game_id || ''))
          };

          mainArr = [...mainArr, object]
        }

        await Promise.all([csvWriter.writeRecords(mainArr)])
      }
    }


    // upload file to s3
    const s3Config = config.getProperties().s3
    const fileContent = await fs.promises.readFile(filePath);
    const key = `tenants/${tenantId}/csv/unfied_trn/transaction_${uuid}.csv`

    const s3Params = {
      ACL: 'public-read',
      Bucket: s3Config.bucket,
      Key: key,
      Body: fileContent
    }
    const uploadedFile = await s3.upload(s3Params).promise()
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    fs.unlink(filePath, (err) => {
      if (err) {
        throw err
      }
    });

    return true
  } catch (error) {
    console.log('=======errorr', error)
    throw error
  }
}
