import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import moment from 'moment';
import { QueryTypes } from 'sequelize';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import { BOT_ALLOWED_TENANTS, EXPORT_CSV_STATUS, EXPORT_CSV_TYPE } from '../../../common/constants';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { CHECK_SBO_DOMAIN } from '../../../utils/constants/constant';
import { fetchAgentIds, formatAmount, getCurrencyById, getCurrencyByValue, getCurrentUserRole, getTransactionFilters, getTransactionTypeByValueOrId } from '../../helpers';
import { getColumnPermissions } from '../getPermissionsForPlayerCsv';
import { getDateRange } from './getStartEndDates';

export default async (data) => {
  try {
    const payload = data.payload
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.IN_PROGRESS
    },
      {
        where: { id: data.id }
      }
    )

    let {
      tenantId, agentId, timeType, timeZoneName, sortBy, order, actionType,
      currency, ownerId, playerType, status, utrNumber, timePeriod, playerCategory, sbo_request, userId,
      internalTrackingId
    } = payload

    const checkSboRequest = sbo_request
    let isSuperAdmin = false
    let isAgentLogin = false
    if (timeZoneName === 'UTC +00:00') {
      timeZoneName = 'UTC'
    }

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      })

      tenantId = adminData.tenantId
    }
    const { startDate, endDate } = (timePeriod) ? JSON.parse(timePeriod) : {}
    const { start, end } = getDateRange(timeType, timeZoneName, startDate, endDate)

    const currentUser = { id: data.adminId, parentType: data.adminType }

    agentId = isNaN(agentId) ? '' : Number(agentId)
    actionType = actionType ? JSON.parse(actionType) : []
    tenantId = Number(tenantId)

    const inputParams = {
      internalTrackingId: internalTrackingId,
      tenantIds: [],
      agentIds: [],
      subAgentIds: [],
      start: start.toISOString(),
      end: end.toISOString(),
      time_zone_name: timeZoneName,
      action_type: actionType,
      currency: (currency) ? getCurrencyByValue(currency)?.id : null,
      owner_id: ownerId,
      player_type: playerType,
      status,
      utr_number: utrNumber,
    }

    if (tenantId) {
      inputParams.tenantIds = [tenantId]
    } else if (data.adminType == 'SuperAdminUser') {
      const adminData = await db.SuperAdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['parentType', 'tenantIds']
      })
      if (adminData?.parentType === 'Manager') {
        inputParams.tenantIds = adminData.tenantIds.map(id => parseInt(id));
      }
    }
    isSuperAdmin = currentUser.parentType === 'SuperAdminUser'

    let userFilterApplied = false

    const mainWhereConditions = []
    const secMainWhereConditions = []

    // Check SBO User Logic
    let botUsersFilterType = 'all'
    let botWalletIds = []

    if (
      (tenantId && BOT_ALLOWED_TENANTS.includes(String(tenantId))) ||
      isSuperAdmin
    ) {
      // In case of super admin, do not check SBO request flag. Just check player category flag.
      if (
        isSuperAdmin ||
        (checkSboRequest === CHECK_SBO_DOMAIN[1] && checkSboRequest !== CHECK_SBO_DOMAIN[2])
      ) {
        if (playerCategory === 'bot_players') {
          botUsersFilterType = 'bot'
        } else if (playerCategory === 'real_players') {
          botUsersFilterType = 'real'
        }
      } else if (!checkSboRequest) {
        botUsersFilterType = 'real'
      }

      if (botUsersFilterType !== 'all') {
        // Find all bot users wallet Id
        botWalletIds = await sequelize.query(`
          SELECT
            w.id AS wallet_id
          FROM
            bot_users bu
            JOIN wallets w ON (w.owner_id = bu.user_id AND w.owner_type = 'User')
          ${tenantId ? 'WHERE bu.tenant_id = :tenantId' : ''}
        `, {
          type: QueryTypes.SELECT,
          replacements: { tenantId }
        })
        botWalletIds = botWalletIds.map(rec => rec.wallet_id)

        if (botWalletIds.length > 0) {
          if (botUsersFilterType === 'bot') {
            mainWhereConditions.push(`(source_wallet_id IN (${botWalletIds}) OR target_wallet_id IN (${botWalletIds}))`)
          } else if (botUsersFilterType === 'real') {
            mainWhereConditions.push(`COALESCE(source_wallet_id, 0) NOT IN (${botWalletIds}) AND COALESCE(target_wallet_id, 0) NOT IN (${botWalletIds})`)
          }
        }
      }
    }

    let searchAgentId
    if (agentId) {
      searchAgentId = agentId
    } else if (currentUser.parentType === 'AdminUser') {
      const currentUserRole = await getCurrentUserRole(currentUser.id)
      if (currentUserRole === 'agent') {
        searchAgentId = currentUser.id
        isAgentLogin = true
      }
    }
    if (searchAgentId) {
      inputParams.agentIds = await fetchAgentIds(searchAgentId)
    }
    if (currentUser.parentType === 'AdminUser') {
      const currentUserRole = await getCurrentUserRole(currentUser.id)
      if (currentUserRole === 'agent') {
        inputParams.subAgentIds = await fetchAgentIds(currentUser.id)
        isAgentLogin = true
      }
    }

    // User Id Filter
    if (userId && !isNaN(userId)) {
      let agentCond = ''
      if (inputParams.agentIds.length > 0) {
        agentCond = 'u.parent_id IN (:agentIds)'
      }
      if (inputParams.subAgentIds.length > 0) {
        agentCond = 'u.parent_id IN (:subAgentIds)' // Extra check in case of agent login to validate if the payload agent Id should be from his sub-agents
      }
      const botUserCondMap = {
        bot: 'bu.id IS NOT NULL',
        real: 'bu.id IS NULL',
        all: ''
      }
      const userData = await sequelize.query(`
        SELECT
          w.id AS wallet_id
        FROM
          wallets w
          JOIN users u ON (w.owner_id = u.id)
          LEFT JOIN bot_users bu ON (bu.user_id = w.owner_id)
        WHERE
          w.owner_id = :userId
          AND w.owner_type = 'User'
          ${agentCond ? 'AND ' + agentCond : ''}
          ${botUserCondMap[botUsersFilterType] ? 'AND ' + botUserCondMap[botUsersFilterType] : ''}
      `, {
        type: QueryTypes.SELECT,
        replacements: { userId: userId, agentIds: inputParams.agentIds, subAgentIds: inputParams.subAgentIds }
      })
      if (!userData || userData.length <= 0) {
        throw new Error('Data not found!');
      }
      mainWhereConditions.push(`(source_wallet_id = '${userData[0].wallet_id}' OR target_wallet_id = '${userData[0].wallet_id}')`)
      userFilterApplied = true
    }

    // UTR filter
    // Fetch transaction id from UTR number
    if (utrNumber) {
      const transactionIdArr = await sequelize.query(`
        SELECT
          transaction_id
        FROM
          utr_history
        WHERE
          utr_number = :utrNumber
        LIMIT 1;
      `, {
        type: QueryTypes.SELECT,
        replacements: { utrNumber: utrNumber }
      })

      const transactionId = transactionIdArr?.[0]?.transaction_id

      if (!transactionId) {
        throw new Error('Data not found!');
      }
      inputParams.transactionIdFromUtrNumber = transactionId
    }

    if (inputParams.transactionIdFromUtrNumber) {
      mainWhereConditions.push('id = :transactionIdFromUtrNumber')
    }

    if (inputParams.internalTrackingId) {
      mainWhereConditions.push('id = :internalTrackingId')
    }

    if (inputParams.start && inputParams.end) {
      mainWhereConditions.push('created_at BETWEEN :start AND :end')
    }

    if (!userFilterApplied && inputParams.agentIds.length > 0) {
      secMainWhereConditions.push('u.parent_id IN (:agentIds)')
    }
    if (!userFilterApplied && inputParams.subAgentIds.length > 0) {
      secMainWhereConditions.push('u.parent_id IN (:subAgentIds)') // Extra check in case of agent login to validate if the payload agent Id should be from his sub-agents
    }

    if (inputParams.currency) {
      mainWhereConditions.push('(source_currency_id = :currency OR target_currency_id = :currency)')
    }

    if (inputParams.tenantIds.length > 0) {
      if (!userFilterApplied) {
        mainWhereConditions.push('tenant_id IN (:tenantIds)')
      }
    }

    const transactionType = [3, 4, 6, 48, 49, 5, 14, 59, 60];
    const actionTypeArr = inputParams.action_type

    if (actionTypeArr.length > 0) {
      const conditionalFilters = []
      const transactionTypeSet = new Set()

      for (const data of actionTypeArr) {
        const res = await getTransactionFilters(data)

        const transactionList = Array.isArray(res.transactionType)
          ? res.transactionType
          : res.transactionType !== null
            ? [res.transactionType]
            : []

        if (res.filters.length > 0) {
          conditionalFilters.push(`((${res.filters.join(' AND ')}) AND transaction_type IN (${transactionList.join(',')}))`)
        } else {
          transactionList.forEach(t => transactionTypeSet.add(t))
        }
      }

      let finalCondition = ''

      if (conditionalFilters.length > 0) {
        if (transactionTypeSet.size > 0) {
          finalCondition = `(${conditionalFilters.join(' OR ')} OR transaction_type IN (${Array.from(transactionTypeSet).join(',')}))`
        } else {
          finalCondition += `(${conditionalFilters.join(' OR ')})`
        }
      } else if (transactionTypeSet.size > 0) {
        finalCondition = `transaction_type IN (${Array.from(transactionTypeSet).join(',')})`
      }

      if (finalCondition) {
        mainWhereConditions.push(finalCondition)
      }
    } else {
      mainWhereConditions.push(`transaction_type IN (${transactionType})`)
    }

    const mainWhereConditionClause = mainWhereConditions.length ? `WHERE ${mainWhereConditions.join(' AND ')}` : ''
    const secMainWhereConditionClause = secMainWhereConditions.length ? `WHERE ${secMainWhereConditions.join(' AND ')}` : ''

    const sortFieldMapping = {
      internal_tracking_id: 'id',
      transfer_method: 'payment_method',
      amount: 'amount',
      created_at: 'created_at'
    }

    const sort = sortFieldMapping[sortBy] || 'created_at'
    const sortOrder = order || 'DESC'
    let sortCondition = ''
    let sortConditionWithAlias = ''
    if (sort && sortOrder) {
      sortCondition = 'ORDER BY ' + sort + ' ' + sortOrder
      sortConditionWithAlias = 'ORDER BY t.' + sort + ' ' + sortOrder
    }

    // Check UTR permission in Allowed Modules
    let UTREnabled = false;
    if (!isSuperAdmin) {
      const UTREnabledData = await sequelize.query(`
        SELECT
          COUNT(*) AS count
        FROM
          tenant_theme_settings
        WHERE
          tenant_id = :tenantId
          AND 'utrNumber' = ANY(string_to_array(allowed_modules, ','));
      `, {
        type: QueryTypes.SELECT,
        replacements: { tenantId: inputParams.tenantIds[0] }
      })
      UTREnabled = Number(UTREnabledData[0].count) > 0
    }

    const ATTRIBUTE_PERMISSIONS = {
      user_id: ['report_attributes', 'player_id'],
      user_name: ['report_attributes', 'player_name'],
      agent_name: ['report_attributes', 'agent_name'],
      created_at: ['transaction_attributes', 'transaction_timestamp'],
      requested_timestamp: ['transaction_attributes', 'requested_timestamp'],
      transaction_type: ['transaction_attributes', 'action_type'],
      amount: ['financial_attributes', 'transaction_amount'],
      initial_balance: ['players_key', 'before_balance'],
      ending_balance: ['players_key', 'after_balance'],
      transaction_id: ['transaction_attributes', 'transaction_id'],
      id: ['report_attributes', 'internal_tracking_id'],
      comments: ['transaction_attributes', 'description'],
      status: ['report_attributes', 'status'],
      payment_method: ['report_attributes', 'transfer_method'],
      currency_id: ['report_attributes', 'currency'],
      ...((data.type === EXPORT_CSV_TYPE.PLAYER_FINANCIAL_REPORT_DB) && { utr_number: ['transaction_attributes', 'utr_number'] }),
    };

    const COLUMN_TO_HEADER_MAP = {
      user_id: 'Player ID',
      user_name: 'Player Name',
      agent_name: 'Agent Name',
      created_at: 'Transaction Timestamp',
      requested_timestamp:'Requested Timestamp',
      transaction_type: 'Action Type',
      amount: 'Amount',
      initial_balance: 'Initial Balance',
      ending_balance: 'Ending Balance',
      transaction_id: 'Transaction ID',
      id: 'Internal Tracking ID',
      comments: 'Description',
      status: 'Status',
      payment_method: 'Transfer Method',
      currency_id: 'Currency',
      utr_number: 'UTR Number',
    };

    // Manage column permissions
    let finalAttributes = await getColumnPermissions(ATTRIBUTE_PERMISSIONS, currentUser.id, tenantId, currentUser.parentType)

    // Manage excluded permissions
    let hasUTRPermission = false
    finalAttributes = finalAttributes.filter(column => {
      if (column === 'utr_number') {
        if (!isAgentLogin && (isSuperAdmin || UTREnabled)) {
          hasUTRPermission = true;
          return true;
        } else {
          return false;
        }
      }
      return true;
    })

    const allowedColumnsObj = {};
    finalAttributes.forEach(attr => allowedColumnsObj[attr] = true);

    let awsTenantFolderName = 0;
    if (inputParams.tenantIds.length === 1) {
      awsTenantFolderName = inputParams.tenantIds[0];
    }

    // New code

    const chunkSize = 5000
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3

    const uuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${awsTenantFolderName}/csv/casino_transaction/transaction_${uuid}.csv`
    const csvStringifier = createObjectCsvStringifier({
      header: finalAttributes.map(columnKey => ({ id: columnKey, title: COLUMN_TO_HEADER_MAP[columnKey] }))
    })

    // Get count
    const cntQuery = prepareCountQuery({
      mainWhereConditionClause,
      secMainWhereConditionClause,
      userFilterApplied
    })
    const countData = await sequelize.query(cntQuery, { type: sequelize.QueryTypes.SELECT, useMaster: false, replacements: inputParams })

    const totalRecordsCount = countData[0].total_count || 0;
    const totalChunks = Math.ceil(totalRecordsCount / chunkSize)
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
    // Loop through each chunk
    for (let i = 0; i < totalChunks; i++) {
      const paginationCondition = 'LIMIT ' + chunkSize + ' OFFSET ' + i * chunkSize
      // Fetch the data for the current chunk
      const reportQuery = prepareReportQuery({
        mainWhereConditionClause,
        secMainWhereConditionClause,
        sortCondition,
        paginationCondition,
        sortConditionWithAlias,
        userFilterApplied
      })
      const reportData = await sequelize.query(reportQuery, { type: sequelize.QueryTypes.SELECT, useMaster: false, replacements: inputParams })

      if (reportData.length > 0) {
        // Get UTR Number
        // Only consider for deposit type transactions
        // Defining separate block for this
        if (allowedColumnsObj.utr_number) {
          const transactionIds = []
          const internalIds = []
          const tenantIds = new Set()
          reportData.forEach(rec => {
            if (rec.transaction_type === 3) {
              tenantIds.add(rec.tenant_id)
              internalIds.push(rec.id)
              if (rec.transaction_id) { transactionIds.push(rec.transaction_id) }
            }
          })

          const [utrNumberData] = await Promise.all([
            internalIds.length && hasUTRPermission
              ? (
                  sequelize.query(`
                  SELECT
                    JSON_OBJECT_AGG(
                      transaction_id,
                      utr_number
                    ) AS transaction_id_utr_map
                  FROM
                    utr_history
                  WHERE transaction_id IN (:internalIds) AND tenant_id IN (:tenantIds);
                `, {
                    type: QueryTypes.SELECT,
                    replacements: { internalIds, tenantIds: [...new Set(tenantIds)] }
                  })
                )
              : null
          ])
          const utrNumberDataMapping = utrNumberData?.[0]?.transaction_id_utr_map

          if (utrNumberDataMapping && Object.keys(utrNumberDataMapping).length >= 1) {
            reportData.forEach(rec => {
              rec.utr_number = utrNumberDataMapping[rec.id] || null
            })
          }
        }

        for (const report of reportData) {
          if (inputParams.time_zone_name) {
            if (report.created_at) {
              const dateStr = moment(report.created_at)
                .tz(inputParams.time_zone_name)
                .format('DD-MM-YYYY HH:mm:ss');
              report.created_at = dateStr; // Escaped double quotes
            }

            if (report.requested_timestamp) {
              const timestamp = Number(report.requested_timestamp);
              const tsStr = moment.tz(timestamp, inputParams.time_zone_name)
                .format('DD-MM-YYYY HH:mm:ss');
              report.requested_timestamp = tsStr; // Escaped double quotes
            }
            else {
              report.requested_timestamp = '--';
            }
          }
          if (report.currency_id) {
            const id = parseInt(report.currency_id || 0)
            report.currency_id = getCurrencyById(id)?.value || null
          }
          if(report.amount){
            report.amount= formatAmount(report.amount,tenantId,report.currency_id)
          }
          if(report.initial_balance){
            report.initial_balance= formatAmount(report.initial_balance,tenantId,report.currency_id)
          }
          if(report.ending_balance){
            report.ending_balance= formatAmount(report.ending_balance,tenantId,report.currency_id)
          }

          if (report.transaction_type) {
            report.transaction_type = await getTransactionTypeByValueOrId(report.transaction_type)
          }
        }
      }

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(reportData)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }

      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      await delay(500)
    }
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )
    return true
  } catch (error) {
    console.log("=======errorr", error)
    throw error
  }


}

function prepareCountQuery ({
  mainWhereConditionClause,
  secMainWhereConditionClause, // Agent condition
  userFilterApplied
}) {
  let finalQ = ''
  // When agent filter is not applied
  if (userFilterApplied) {
    finalQ = `
      SET enable_seqscan = OFF;

      SELECT COUNT(*) AS total_count
      FROM transactions
      ${mainWhereConditionClause};

      SET enable_seqscan = ON;
    `
  } else {
    finalQ = `
      WITH main_cte AS (
        SELECT
          CASE
            WHEN transaction_type IN (4, 6, 49, 60) THEN source_wallet_id
            ELSE target_wallet_id
          END AS user_wallet_id
        FROM transactions
        ${mainWhereConditionClause}
      )
      SELECT
        COUNT(*) AS total_count
      FROM main_cte t
        JOIN wallets w ON (w.id = t.user_wallet_id AND w.owner_type = 'User')
        ${secMainWhereConditionClause ? 'JOIN users u ON (w.owner_id = u.id)' : ''}
      ${secMainWhereConditionClause};
    `
  }
  return finalQ
}

function prepareReportQuery ({
  mainWhereConditionClause,
  secMainWhereConditionClause,
  sortCondition,
  paginationCondition,
  sortConditionWithAlias,
  userFilterApplied
}) {
  let finalQ = ''
  if (userFilterApplied) {
    finalQ = `
      SET enable_seqscan = OFF;

      WITH main_cte AS (
        SELECT
          id,
          tenant_id,
          amount,
          transaction_id,
          transaction_type,
          created_at,
          source_wallet_id,
          target_wallet_id,
          source_currency_id,
          target_currency_id,
          payment_method,
          status,
          comments,
          source_before_balance,
          target_before_balance,
          source_after_balance,
          target_after_balance,
          CASE
            WHEN transaction_type IN (4, 6, 49, 60) THEN source_wallet_id
            ELSE target_wallet_id
          END AS user_wallet_id,
          timestamp as requested_timestamp
        FROM transactions
        ${mainWhereConditionClause}
        ${sortCondition}
        ${paginationCondition}
      )
      SELECT
        t.tenant_id,
        t.source_currency_id,
        t.target_currency_id,
        tmd.meta_data,
        u.id AS user_id,
        u.user_name,
        adu.agent_name,
        t.created_at::text AS created_at,
        t.transaction_type,
        t.amount,
        CASE
          WHEN t.transaction_type IN (4, 6, 49, 60) THEN source_before_balance::numeric(20,2)
          ELSE target_before_balance::numeric(20,2)
        END AS initial_balance,
        CASE
          WHEN t.transaction_type IN (4, 6, 49, 60) THEN source_after_balance::numeric(20,2)
          ELSE target_after_balance::numeric(20,2)
        END AS ending_balance,
        t.transaction_id,
        t.id,
        t.comments,
        t.requested_timestamp,
        t.status,
        t.payment_method,
        w.currency_id
      FROM main_cte t
        LEFT JOIN transactions_meta_data tmd ON tmd.transaction_id = t.id
        LEFT JOIN wallets w ON (w.id = t.user_wallet_id)
        LEFT JOIN users u ON (u.id = w.owner_id)
        LEFT JOIN admin_users adu ON (adu.id = u.parent_id)
      ${sortConditionWithAlias};

      SET enable_seqscan = ON;
    `
  } else {
    finalQ = `
      SET enable_seqscan = OFF;

      WITH main_cte AS (
        SELECT
          id,
          tenant_id,
          amount,
          transaction_id,
          transaction_type,
          created_at,
          source_wallet_id,
          target_wallet_id,
          source_currency_id,
          target_currency_id,
          payment_method,
          status,
          comments,
          source_before_balance,
          target_before_balance,
          source_after_balance,
          target_after_balance,
          CASE
            WHEN transaction_type IN (4, 6, 49, 60) THEN source_wallet_id
            ELSE target_wallet_id
          END AS user_wallet_id,
          timestamp as requested_timestamp
        FROM transactions
        ${mainWhereConditionClause}
      ),
      txs_user_data AS (
        SELECT
          t.*
        FROM
          main_cte t
          JOIN wallets w ON (w.id = t.user_wallet_id AND w.owner_type = 'User')
          ${secMainWhereConditionClause ? 'JOIN users u ON (w.owner_id = u.id)' : ''}
        ${secMainWhereConditionClause}
        ${sortConditionWithAlias}
        ${paginationCondition}
      )
      SELECT
        t.tenant_id,
        t.source_currency_id,
        t.target_currency_id,
        tmd.meta_data,
        u.id AS user_id,
        u.user_name,
        adu.agent_name,
        t.created_at::text AS created_at,
        t.transaction_type,
        t.amount,
        CASE
          WHEN t.transaction_type IN (4, 6, 49, 60) THEN source_before_balance::numeric(20,2)
          ELSE target_before_balance::numeric(20,2)
        END AS initial_balance,
        CASE
          WHEN t.transaction_type IN (4, 6, 49, 60) THEN source_after_balance::numeric(20,2)
          ELSE target_after_balance::numeric(20,2)
        END AS ending_balance,
        t.transaction_id,
        t.id,
        t.comments,
        t.requested_timestamp,
        t.status,
        t.payment_method,
        w.currency_id
      FROM txs_user_data t
        LEFT JOIN transactions_meta_data tmd ON tmd.transaction_id = t.id
        LEFT JOIN wallets w ON (w.id = t.user_wallet_id)
        LEFT JOIN users u ON (u.id = w.owner_id)
        LEFT JOIN admin_users adu ON (adu.id = u.parent_id)
      ${sortConditionWithAlias};

      SET enable_seqscan = ON;
    `
  }

  return finalQ
}
