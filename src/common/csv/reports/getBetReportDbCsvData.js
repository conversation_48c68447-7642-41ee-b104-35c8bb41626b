import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3'
import { createObjectCsvStringifier } from 'csv-writer'
import moment from 'moment'
import { Readable } from 'stream'
import { v4 as uuidv4 } from 'uuid'
import config from '../../../configs/app.config'
import db, { sequelize } from '../../../db/models'
import { s3 } from '../../../libs/awsS3ConfigV2'
import { EXPORT_CSV_STATUS, SPORTS_PROVIDERS_STAGE, SPORTS_PROVIDER_PROD } from '../../constants'
import { expandBonusComment, fetchAgentIds, formatAmount, getDateInStringFormat, getMarketStatusLabel, getMatchTypeDescription, isEmptyArrayOrNullOrUndefined } from '../../helpers'
import getRolesDetails from '../getAdminRolesDetail'
import { getColumnPermissions } from '../getPermissionsForPlayerCsv'
import getActionType from './getActionType'

export default async (data) => {
  try {
    const payload = data.payload;
    const isProduction = process.env.NODE_ENV === 'production'

    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.IN_PROGRESS
    },
      {
        where: { id: data.id }
      }
    )

    const jetFairId = isProduction ? SPORTS_PROVIDER_PROD.JETFAIR : SPORTS_PROVIDERS_STAGE.JETFAIR
    const powerPlayId = isProduction ? SPORTS_PROVIDER_PROD.POWERPLAY : SPORTS_PROVIDERS_STAGE.POWERPLAY
    const turboId = isProduction ? SPORTS_PROVIDER_PROD.TURBOSTARS : SPORTS_PROVIDERS_STAGE.TURBOSTARS
    // Query replacements
    const inputParams = {
      tenantId: 0,
      managerTenantIds: [],
      agentIds: [],
      subAgentIds: [],
      start: null,
      end: null,
      scheduleDateStart: null,
      scheduleDateEnd: null,
      userId: null,
      search: null,
      searchId: payload.searchId,
      searchMarketId: payload.searchMarketId,
      status: payload.status,
      journalEntry: payload.journalEntry,
      providerId: payload.providerId,
      currency: payload.currency,
      tenantBaseCurrency: null,
      module: payload.module,
    };

    let isSuperAdmin = false;
    if (payload.timeZoneName === 'UTC +00:00') {
      payload.timeZoneName = 'UTC'
    }

    // Tenant ID Gathering
    inputParams.tenantId = !isNaN(payload.tenantId) ? Number(payload.tenantId) : 0;

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({ where: { id: data.adminId }, attributes: ['tenantId'] })
      inputParams.tenantId = adminData.tenantId;
    } else {
      const adminData = await db.SuperAdminUser.findOne({ where: { id: data.adminId }, attributes: ['parentType', 'tenantIds'] })
      if (adminData.parentType === 'Manager') {
        inputParams.managerTenantIds = adminData.tenantIds.map(id => parseInt(id));
      }
      isSuperAdmin = adminData.parentType === 'SuperAdminUser';
    }

    // Agent ID Gathering
    let searchAgentId = !isNaN(payload.agentId) ? Number(payload.agentId) : 0;

    if (searchAgentId) {
      inputParams.agentIds = await fetchAgentIds(searchAgentId);
    }

    if (data.adminType == 'AdminUser') {
      const currentUserRole = await getRolesDetails(data.adminId);
      if (currentUserRole.includes('agent')) {
        inputParams.subAgentIds = await fetchAgentIds(data.adminId)
      }
    }

    // Date time Gathering
    inputParams.start = moment.tz(payload.startDate, 'YYYY-MM-DD HH:mm', payload.timeZoneName).utc().format('YYYY-MM-DD HH:mm:00.000000')
    inputParams.end = moment.tz(payload.endDate, 'YYYY-MM-DD HH:mm', payload.timeZoneName).utc().format('YYYY-MM-DD HH:mm:59.999999')

    if (payload.startDateScheduleAt && payload.endDateScheduleAt) {
      inputParams.scheduleDateStart = moment.tz(payload.startDateScheduleAt, 'YYYY-MM-DD HH:mm', payload.timeZoneName).utc().format('YYYY-MM-DD HH:mm:00.000000')
      inputParams.scheduleDateEnd = moment.tz(payload.endDateScheduleAt, 'YYYY-MM-DD HH:mm', payload.timeZoneName).utc().format('YYYY-MM-DD HH:mm:59.999999')
    }

    // Action type gathering
    payload.actionType = payload.actionType || [];
    if (!Array.isArray(payload.actionType)) payload.actionType = JSON.parse(payload.actionType)
    payload.actionType = payload.actionType.map(i => i && `'${i}'`);

    // User Id Gathering
    inputParams.userId = (payload.userId && payload.userId !== 'null') ? payload.userId : null;

    // Search Gathering
    inputParams.search = (payload.search && !isNaN(payload.search)) ? payload.search : null;

    // Filtering Starts
    const filters = []

    if (inputParams.start && inputParams.end) filters.push('bt.created_at BETWEEN :start AND :end')
    if (inputParams.scheduleDateStart && inputParams.scheduleDateEnd) filters.push('bb.start_date >= :scheduleDateStart AND bb.start_date <= :scheduleDateEnd')
    if (inputParams.tenantId) filters.push(`bt.tenant_id = :tenantId`)
    if (inputParams.managerTenantIds.length > 0) filters.push(`bt.tenant_id IN (:managerTenantIds)`)
    if (inputParams.agentIds.length > 0) filters.push(`u.parent_id IN (:agentIds)`)
    if (inputParams.subAgentIds.length > 0) filters.push(`u.parent_id IN (:subAgentIds)`)
    if (inputParams.userId) filters.push('bt.user_id = :userId')
    if (inputParams.search) filters.push('bt.transaction_id = :search')
    if (inputParams.searchId) filters.push('bt.id = :searchId')
    if (inputParams.searchMarketId) filters.push('bt.market_id = :searchMarketId')
    if (payload.searchMarket) filters.push(`bb.market ILIKE '%${payload.searchMarket}%'`)
    if (payload.searchRunnerName) filters.push(`bt.runner_name ILIKE '%${payload.searchRunnerName}%'`)

    if (payload.betType) {
      if (payload.betType === 'not_combo' || payload.betType === 'single') filters.push('bsl.bettype = 1')
      else if (payload.betType === 'multibets' || payload.betType === 'combo') filters.push('bsl.bettype = 2')
      else if (payload.betType === 'lay_back') filters.push('bsl.bettype IN (1,2)')
      else if (payload.betType === 'settled_or_bonus' || payload.betType === 'settled') filters.push('bsl.bettype IS NULL')
      else filters.push('bsl.bettype IN (1,2)')
    }

    if (inputParams.status) {
      if (inputParams.status === 'SettledMarket') filters.push('(bsl.settlement_status = :status OR bt.payment_for = 2)')
      else filters.push('bsl.settlement_status = :status')
    }

    if (inputParams.journalEntry) filters.push('bt.journal_entry = :journalEntry')
    if (payload.actionType.length > 0) filters.push(`bt.payment_for IN (${payload.actionType})`)

    // Apply module-based filtering only if no providerId is set
    if (inputParams.module === "sport-book-report" && !inputParams.providerId && turboId !== null) {
      filters.push(`bt.provider_id = ${turboId}`);
    } else {
      if (inputParams.providerId) {
        filters.push("bt.provider_id = :providerId");
      } else {
        filters.push(
          `(bt.provider_id = ${jetFairId} OR bt.provider_id = ${powerPlayId})`
        );
      }
    }
    if (inputParams.currency) filters.push('c.code = :currency')

    // Check SBO User Logic
    let botUserJoin = ''
    let botUserSelect = ''

    const checkSboRequest = payload.sbo_request;
    if (isSuperAdmin || checkSboRequest === true) {
      botUserSelect = ', bu.id as bot_user_id'
      botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
      if (payload.playerCategory === 'bot_players') {
        filters.push('bu.id IS NOT NULL')
      } else if (payload.playerCategory === 'real_players') {
        filters.push('bu.id IS NULL')
      }
    } else if (checkSboRequest === false) {
      botUserSelect = ', bu.id as bot_user_id'
      botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
      filters.push('bu.id IS NULL')
    }

    // Sorting
    let orderCondition = ''
    if (payload.sortBy && payload.order) {
      const columnMapping = {
        internal_tracking_id: 'id',
        internalTrackingId: 'id',
        created_at: 'created_at',
        createdAt: 'created_at'
      }
      const sortColumn = columnMapping[payload.sortBy];
      if (sortColumn) {
        orderCondition = `ORDER BY ${sortColumn} ${payload.order}`
      }
    }

    const whereCondition = filters.length ? `WHERE ${filters.join(' AND ')}` : ''

    // Check sports free bets permission
    const ATTRIBUTE_PERMISSIONS = {
      sports_free_bet: ['sports_free_bet', 'R'],
    };
    let permissions = await getColumnPermissions(ATTRIBUTE_PERMISSIONS, data.adminId, inputParams.tenantId, data.adminType)

    // AWS tenant folder name
    let awsTenantFolderName = inputParams.tenantId || 0;
    if (!awsTenantFolderName && inputParams.managerTenantIds.length === 1) awsTenantFolderName = inputParams.managerTenantIds[0];


    // new code
    const chunkSize = 5000
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3
    inputParams.limit = chunkSize
    inputParams.offset = 0
    const query = `
      SELECT
        bt.id, -- id
        bt.created_at::text, -- timestamp
        bt.market_type, -- Placed Bet On
        bb.start_date::text, -- event schedule,
        u.id AS user_id, -- player id
        u.user_name, -- player name
        bb.market, -- market name
        --COALESCE(
        --  bb.market,
        --  mf.market
        --) AS market, -- market name with fallback
        bt.market_id, -- Market Id
        bt.runner_name, -- team name
        bsl.run, -- run
        bt.transaction_id, -- bet id
        bsl.bettype, -- bet type 1=back else Lay
        bt.journal_entry,
        bt.amount, -- transaction amount
        bt.non_cash_amount, -- transaction amount
        bt.sports_freebet_amount, -- transaction amount
        bsl.stake, -- Stake
        bsl.multi_price, -- odds
        bsl.possible_win_amount, -- possible win amount
        bt.payment_for, -- Action Type (1 bet placement, 2 won etc)
        bt.transaction_code, -- status
        bt.status, -- transaction status
        bt.description, -- comment
        bt.net_pl, -- commission details
        bt.commission_per, -- commission details
        bt.commission_amount, -- commission details
        bt.other_currency_amount AS amount_in_currencies,
        c.code as currency,
        bsl.settlement_status,
        bt.betslip_id ${botUserSelect},
        CASE
          WHEN bsl.bettype IS NOT NULL THEN
            JSON_BUILD_OBJECT(
              'bettype', bsl.bettype,
              'betslip_id', bt.betslip_id
            )
          ELSE NULL
        END AS betslip_details, -- Conditional JSON object for betslip details
        JSON_BUILD_OBJECT(
          'first_name', u.first_name,
          'last_name', u.last_name,
          'email', u.email,
          'user_name', u.user_name,
          'id', u.id
        ) AS player_details
      FROM bets_transactions bt
       LEFT JOIN (
        SELECT
            STRING_AGG(market, ',') AS market,min(start_date) as  start_date,
            min(bet_id) as bet_id
        FROM bets_bets
        GROUP BY bet_id
     ) AS bb ON  bb.bet_id =bt.transaction_id
      LEFT JOIN bets_betslip bsl ON bt.betslip_id = bsl.id
      LEFT JOIN users u ON u.id = bt.user_id
      LEFT JOIN wallets w ON u.id = w.owner_id AND w.owner_type = 'User'
      LEFT JOIN currencies c ON c.id = w.currency_id
      ${botUserJoin}
      ${whereCondition}
      ${orderCondition}
      LIMIT :limit offset :offset
      `

    const uuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${awsTenantFolderName}/csv/bet_report/transaction_${uuid}.csv`
    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'id', title: 'Internal ID' },
        { id: 'created_at', title: 'Date & Time' },
        { id: 'player_name', title: 'Player Name' },
        { id: 'market_id', title: 'Market ID' },
        { id: 'bet_id', title: 'Bet ID' },
        { id: 'market_name', title: 'Market name' },
        { id: 'market_type', title: 'Placed Bet On' },
        { id: 'team_name', title: 'Team Placed Bet On' },
        { id: 'bet_type', title: 'Bet Type' },
        { id: 'run', title: 'Run' },
        { id: 'stake', title: 'Stake Amount' },
        { id: 'odds', title: 'Odds' },
        { id: 'action_type', title: 'Action Type' },
        { id: 'currency', title: 'Currency' },
        { id: 'transaction_amount', title: 'Transaction Amount' },
        { id: 'status', title: 'Status' },
        { id: 'possible_win_amount', title: 'Possible Win Amount' },
        { id: 'commission', title: 'Commission Details' },
        { id: 'comment', title: 'Comment' },
      ]
    })

    //count query
    const totalRecords = await sequelize.query(
      `SELECT
      COUNT(*) AS total
    FROM bets_transactions bt
     LEFT JOIN (
      SELECT
          STRING_AGG(market, ',') AS market,min(start_date) as  start_date,
          min(bet_id) as bet_id
      FROM bets_bets
      GROUP BY bet_id
   ) AS bb ON  bb.bet_id =bt.transaction_id
    LEFT JOIN bets_betslip bsl ON bt.betslip_id = bsl.id
    LEFT JOIN users u ON u.id = bt.user_id
    LEFT JOIN wallets w ON u.id = w.owner_id AND w.owner_type = 'User'
    LEFT JOIN currencies c ON c.id = w.currency_id
    ${botUserJoin}
    ${whereCondition}`,
      {
        replacements: inputParams,
        type: sequelize.QueryTypes.SELECT
      })

    const totalRecordsCount = totalRecords[0].total
    const totalChunks = Math.ceil(totalRecordsCount / chunkSize)
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
    // Loop through each chunk
    for (let i = 0; i < totalChunks; i++) {
      inputParams.offset = i * chunkSize
      // Fetch the data for the current chunk
      const reportData = await sequelize.query(query, {
        replacements: inputParams,
        type: sequelize.QueryTypes.SELECT
      })

      let mainArr = []
      if (reportData.length > 0) {
        reportData.forEach((report) => {
          if (report.created_at) {
            if (payload.timeZoneName) {
              report.created_at = moment(report.created_at).tz(payload.timeZoneName).format('DD-MM-YYYY HH:mm:ss')
            } else {
              report.created_at = moment.utc(report.created_at).format('DD-MM-YYYY HH:mm:ss')
            }
          }
        })

        for (const txn of reportData) {
          const object = {
            id: txn.id,
            created_at: getDateInStringFormat(txn.created_at),
            player_name: txn.user_name,
            market_type: getMatchTypeDescription(txn.market_type) || '',
            market_name: txn.market || '',
            market_id: txn.market_id || '',
            team_name: txn.runner_name || '',
            run: txn.run || '',
            bet_id: isEmptyArrayOrNullOrUndefined(txn.betslip_details) ? '-' : txn.transaction_id,
            bet_type: isEmptyArrayOrNullOrUndefined(txn.betslip_details) ? 'Settled' : (txn.bettype == 1) ? 'Back' : 'Lay',
            currency: txn.currency,
            transaction_amount: 'Cash: ' + (formatAmount(txn.amount,inputParams.tenantId,txn.currency) || 0) + ' ' + (txn.journal_entry === 'CR' ? 'Credited' : 'Debited') + '\n' + 'Non Cash: ' + (formatAmount(txn.non_cash_amount,inputParams.tenantId,txn.currency) || 0) + (permissions.includes('sports_free_bet') ? ('\n' + 'Sports Free Bets: ' + (formatAmount(txn.sports_freebet_amount,inputParams.tenantId,txn.currency) || 0)) : ''),
            stake: txn.stake || '',
            odds: txn.multi_price || '',
            possible_win_amount: formatAmount(txn.possible_win_amount,inputParams.tenantId,txn.currency) || '',
            action_type: getActionType(txn.payment_for) || '-',
            status: getMarketStatusLabel(txn.settlement_status) || (getMarketStatusLabel(txn.bettype) ? 'Match In Progress' : getMarketStatusLabel(txn.transaction_code) || ''),
            comment:expandBonusComment(txn.description) || '',
            commission: 'Net P/L: ' + (txn.net_pl || 0) + '\n' + 'Percent: ' + (txn.commission_per || 0) + '\n' + 'Amount' + (formatAmount(txn.commission_amount,inputParams.tenantId,txn.currency) || 0)
          }
          mainArr = [...mainArr, object]
        }
      }

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(mainArr)
      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }

      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      delay(500)
    }

    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )
    return true
  } catch (error) {
    console.log("=======errorr", error)
    throw error
  }
}
