import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import moment from 'moment';
import { QueryTypes } from 'sequelize';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { EXPORT_CSV_STATUS, OTB_TENANTS_CONFIG } from '../../constants';
import { formatAmount, getDateInStringFormat, getPlayerType } from '../../helpers';
import getPermissionsForPlayerCsv, { countTotalRecords, getParentAgent, getRoleByAdminId } from '../getPermissionsForPlayerCsv';
import getCategoryType from './getCategoryType';

export default async (data) => {
  try {
    const payload = data.payload;
    await db.ExportCsvCenter.update(
      {
        status: EXPORT_CSV_STATUS.IN_PROGRESS,
      },
      {
        where: { id: data.id },
      }
    );
    let isSuperAdmin = false;
    // ==================== params gathering starts here ====================
    let params = {
      tenantId: 0,
      agentId: 0,
      search: payload?.search ? payload?.search : '',
      search_by_id: payload?.search_by_id ? payload?.search_by_id : '',
      search_by_nic: payload?.search_by_nic ? payload?.search_by_nic : '',
      status: payload?.status,
      phone_verified: payload?.phone_verified ? payload?.phone_verified : '',
      email_verified: payload?.email_verified ? payload?.email_verified : '',
      kyc_done: payload?.kyc_done ? payload?.kyc_done : '',
      kyc_docs: payload?.kyc_docs ? payload?.kyc_docs : '',
      date_range: payload?.date_range ? payload?.date_range : [],
      min_amount: payload?.min_amount ? payload?.min_amount : '',
      max_amount: payload?.max_amount ? payload?.max_amount : '',
      sort_by: payload?.sort_by ? payload?.sort_by : 'id',
      order: payload?.order ? payload?.order : 'desc',
      vip_levels: payload?.vip_levels ? payload?.vip_levels : '',
      player_category: payload?.player_category ? payload?.player_category : '',
      players_commission: payload?.players_commission ? payload?.players_commission : '',
      affiliated_data: payload?.affiliated_data ? payload?.affiliated_data : '',
      search_promo_code: payload?.search_promo_code ? payload?.search_promo_code : '',
      currency: payload?.currency,
      time_zone_name :  payload?.time_zone_name ? payload?.time_zone_name : '',
    };

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId,
        },
        attributes: ['tenantId'],
      });
      params.tenantId = adminData.tenantId;
      //params.agentId = data.adminId;

    } else {
      params.tenantId = payload?.tenant_id ? payload.tenant_id : 0;
      //params.agentId = payload?.adminId ? payload.adminId : 0;
    }
    isSuperAdmin = data.adminType === 'SuperAdminUser';
    // ==================== params gathering ends here ====================

    const otbTenants = config.get('env') === 'production' ? OTB_TENANTS_CONFIG.PROD : OTB_TENANTS_CONFIG.STAGE
    const isOTBEnabled = otbTenants[parseInt(params.tenantId)] ? true : false
    if (payload.time_zone_name === 'UTC +00:00' || payload.time_zone_name === '' || !payload.time_zone_name) {
      payload.time_zone_name = 'UTC'
    }
    const timezone =  payload.time_zone_name;

    // Find player category enabled or not for the tenant
    const [playerCategoryEnabled] = await sequelize.query(
      `SELECT id FROM tenant_theme_settings
    WHERE tenant_id = :tenantId
    AND :allowedModule = ANY(string_to_array(allowed_modules, ','))
    LIMIT 1`,
      {
        replacements: {
          tenantId: params.tenantId,
          allowedModule: 'enablePlayerCategory'
        },
        type: sequelize.QueryTypes.SELECT,
        useMaster: false
      }
    );

    // ====================filters here ========================
    if(payload.adminId != 'direct-player'){
      params.agentId = payload?.adminId ? payload.adminId : data.adminId
    }else{
      params.agentId = data.adminId
    }
    //params.agentId = payload?.adminId ? payload.adminId : data.adminId
    let whereStr = '';
    params.agentId = parseInt(params.agentId)

    if (payload.adminId === 'direct-player') {
     whereStr = `${whereStr ? ' AND' : ''} u.parent_type = 'AdminUser' AND u.parent_id IN (
        SELECT DISTINCT admin_users.id
        FROM admin_users
        JOIN admin_users_admin_roles ON admin_users.id = admin_users_admin_roles.admin_user_id
        WHERE admin_users_admin_roles.admin_role_id IN (1, 3, 4, 5)
    )`;
    }

    if (params.agentId != 0 && params.agentId != 'direct-player') {
      let isTopLevel = true;
      const loginAgentRole = await getRoleByAdminId(params.agentId);

      if (loginAgentRole && (loginAgentRole.admin_role_id == 4 || loginAgentRole.admin_role_id == 5)) {
        params.agentId = await getParentAgent(params.agentId);
      }

      if (!([1, 3].includes(parseInt(loginAgentRole.admin_role_id)))) {
        whereStr = `${whereStr ? ' AND' : ''} u.parent_type = 'AdminUser' AND u.parent_id = ${params.agentId}`;
      }
    }


    if (params.search) {
      params.search = params.search.replace(/'/g, "");
      whereStr += `${whereStr ? ' AND' : ''} (u.email ILIKE '%${params.search}%' OR u.user_name ILIKE '%${params.search}%' OR u.first_name ILIKE '%${params.search}%' OR u.last_name ILIKE '%${params.search}%' OR u.phone ILIKE '%${params.search}%' OR u.nick_name ILIKE '%${params.search}%' OR u.zip_code LIKE '%${params.search}%' OR u.city ILIKE '%${params.search}%')`;
    }

    if (params.search_by_id) {
      params.search_by_id = params.search_by_id.replace(/'/g, "");
      whereStr += `${whereStr ? ' AND' : ''} (CAST(u.id AS TEXT) ILIKE '%${params.search_by_id}%')`;
    }

    if (params.search_by_nic) {
      params.search_by_nic = params.search_by_nic.replace(/'/g, "");
      whereStr += `${whereStr ? ' AND' : ''} (u.national_id ILIKE '%${params.search_by_nic}%')`;
    }

    if (params.status) {
      whereStr += `${whereStr ? ' AND' : ''} u.active = ${params.status}`;
    }

    if (params.phone_verified) {
      whereStr += `${whereStr ? ' AND' : ''} u.phone_verified = ${params.phone_verified}`;
    }

    if (params.affiliated_data) {
      if (params.affiliated_data === 'all') {
        whereStr += `${whereStr ? ' AND' : ''} u.affiliated_data IS NOT NULL AND u.affiliated_data <> ''`;
      } else {
        whereStr += `${whereStr ? ' AND' : ''} u.affiliated_data = '${params.affiliated_data}'`;
      }
    }

    if (params.players_commission) {
      if (params.players_commission === 'true') {
        whereStr += `${whereStr ? ' AND ' : ''} EXISTS (SELECT 1 FROM user_settings WHERE user_id = u.id AND key = 'commissionPercentage' AND CAST(value AS INTEGER) != 0)`;
      } else {
        whereStr += `${whereStr ? ' AND ' : ''} (NOT EXISTS (SELECT 1 FROM user_settings WHERE user_id = u.id AND key = 'commissionPercentage') OR (SELECT CAST(value AS INTEGER) = 0 FROM user_settings WHERE user_id = u.id AND key = 'commissionPercentage'))`;
      }
    }

    if (params.kyc_done) {
      if (params.kyc_done === 'true') {
        whereStr += `${whereStr ? ' AND' : ''} u.kyc_done = 'true'`;
      } else {
        whereStr += `${whereStr ? ' AND' : ''} (u.kyc_done = 'false' OR u.kyc_done IS NULL)`;
      }
    }

    if (params.kyc_docs) {
      if (params.kyc_docs === 'uploaded') {
        whereStr += `${whereStr ? ' AND ' : ''} EXISTS (SELECT 1 FROM user_documents ud WHERE ud.user_id = u.id)`;
      } else if (params.kyc_docs === 'not_uploaded') {
        whereStr += `${whereStr ? ' AND ' : ''} NOT EXISTS (SELECT 1 FROM user_documents ud WHERE ud.user_id = u.id)`;
      }
    }

    if (params.min_amount) {
      whereStr += `${whereStr ? ' AND' : ''} (w.amount + w.non_cash_amount ${isOTBEnabled ? ' + w.one_time_bonus_amount' : ''} ) >= ${params.min_amount}`;
    }

    if (params.max_amount) {
      whereStr += `${whereStr ? ' AND' : ''} (w.amount + w.non_cash_amount ${isOTBEnabled ? ' + w.one_time_bonus_amount' : ''} ) <= ${params.max_amount}`;
    }

    // if (params.date_range && Array.isArray(params.date_range) && params.date_range.length) {
    //   let start_date = params.date_range['start_date'];
    //   let end_date = new Date(params.date_range['end_date']);
    //   end_date = end_date.setHours(23, 59, 59, 999)
    //   whereStr += `${whereStr ? ' AND' : ''} u.created_at BETWEEN '${start_date}' AND '${end_date}'`;
    // }
    if (params?.date_range && Object.keys(params.date_range).length > 0) {

      if (payload.time_zone_name) {
        params.date_range.start_date = moment.tz(params.date_range.start_date, payload.time_zone_name).utc().format()
        params.date_range.end_date = moment.tz(params.date_range.end_date, payload.time_zone_name).utc().format()
      }

      let start_date = params.date_range.start_date
      let end_date = params.date_range.end_date
      whereStr += `${whereStr ? ' AND' : ''} u.created_at BETWEEN '${start_date}' AND '${end_date}'`;
    }

    if (params.vip_levels && !Array.isArray(params.vip_levels)) {
      whereStr += `${whereStr ? ' AND' : ''} vip_level IN (${params.vip_levels})`;
    }

    if (params.tenantId != 0) {
      whereStr += `${whereStr ? ' AND' : ''} u.tenant_id = ${params.tenantId}`;
    }

    if (params.email_verified) {
      whereStr += `${whereStr ? ' AND' : ''} u.email_verified = ${params.email_verified}`;
    }

    if (params.player_category) {
      whereStr += `${whereStr ? ' AND' : ''} u.category_type in (${params.player_category})`;
    }

    if (params.currency) {
      whereStr += `${whereStr ? ' AND' : ''} w.currency_id = ${params.currency}`;
    }

    if (params.search_promo_code) {
      const promoCode = params.search_promo_code.replace(/'/g, "");
      whereStr += `${whereStr ? ' AND ' : ''} COALESCE(
        u.id IN (
            SELECT DISTINCT user_promo_code_bonus.user_id
            FROM tenant_promo_codes
            JOIN user_promo_code_bonus ON tenant_promo_codes.id = user_promo_code_bonus.promo_code_id
            WHERE tenant_promo_codes.code ILIKE '%${promoCode}%'
            AND tenant_promo_codes.tenant_id = ${params.tenantId}
        ), false
    )`;
    }

    // Check SBO User Logic
    let botUserJoin = ''
    let botUserSelect = ''

    const checkSboRequest = payload.sbo_request;
    payload.playerCategory = getPlayerType(payload)

    if (isSuperAdmin || checkSboRequest === true) {
      botUserSelect = ', bu.id as bot_user_id'
      botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
      if (payload.playerCategory === 'bot_players') {
        whereStr += `${whereStr ? ' AND ' : ''} bu.id IS NOT NULL`
      } else if (payload.playerCategory === 'real_players') {
        whereStr += `${whereStr ? ' AND ' : ''} bu.id IS NULL`
      }
    } else if (checkSboRequest === false) {
      botUserSelect = ', bu.id as bot_user_id'
      botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
      whereStr += `${whereStr ? ' AND ' : ''} bu.id IS NULL`
    }

    // ========================filter ends here================================

    const attributesWithPermissions = {
      "u.id": null,
      "u.national_id": "national_id",
      "u.first_name": "first_name",
      "u.last_name": "last_name",
      "u.kyc_done": "kyc_done",
      "u.email": "email",
      "u.user_name": "user_name",
      "u.active": null,
      "u.phone_code": "phone",
      "u.phone": "phone",
      "ur.referral_code":  "referral_code",
      "u.enable_withdraw_requests": 'enable_withdrawal_requests',
      "u.affiliated_data": null,
      "w.currency_id": null,
      "c.code AS currency_code": null,
      "a.agent_name AS agentname": "agent_details",
      "w.amount AS total_cash_amount": "total_cash_balance",
      "w.non_cash_amount AS total_non_cash_amount": "total_non_cash_balance",
      "ulh.ip as latest_ip_address": "ip_address",
      "ulh.data as latest_location_data": null,
      "us.value": null,
      "u.demo": null,
      "u.nick_name": "nick_name",
      [`TO_CHAR(u.created_at AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}', 'YYYY-MM-DD HH24:MI:SS') AS created_at`]: null,
      [`TO_CHAR(u.last_login_date AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}', 'YYYY-MM-DD HH24:MI:SS') AS last_login_date`]: null,
      "u.zip_code": "zip_code",
      "u.city": "city",
      "u.phone_verified": "phone",
      "u.parent_id": null,
      "u.vip_level": "vip_level",
      "u.disabled_remarks": null,
      "lt.last_deposit_date": "last_deposit_date",
      "lt.last_deposit_amount": "last_deposit_amount",
      ...(playerCategoryEnabled && { "u.category_type": 'category_type' })
    };

    if (isOTBEnabled) {
      attributesWithPermissions["w.one_time_bonus_amount AS total_one_time_bonus_amount"] = "total_one_time_bonus_balance"
      attributesWithPermissions["(w.amount + w.non_cash_amount + w.one_time_bonus_amount) AS total_amount"] = "total_balance"
    }
    else {
      attributesWithPermissions["(w.amount + w.non_cash_amount) AS total_amount"] = "total_balance"
    }
    const finalAttributes = await getPermissionsForPlayerCsv(attributesWithPermissions, data.adminId, params.tenantId, data.adminType);
    const selectStatement = `${finalAttributes.join(', ')}` + botUserSelect;
    const tableUsers = 'users';
    const tableWallets = 'wallets';

    const sqlCheckV = `
          SELECT ${selectStatement}
          FROM ${tableUsers} as u
          LEFT JOIN ${tableWallets} as w on u.id = w.owner_id and w.owner_type = 'User'
          LEFT JOIN user_settings AS us ON u.id = us.user_id AND us.key = 'commissionPercentage'
          LEFT JOIN currencies AS c ON w.currency_id = c.id
          LEFT JOIN admin_users AS a ON u.parent_id = a.id AND a.parent_type = 'AdminUser'
          LEFT JOIN (
              SELECT user_id, MAX(id) AS ulh_max_id
              FROM user_login_history
              WHERE login_type = 0
              GROUP BY user_id
          ) AS ulh_max ON u.id = ulh_max.user_id
          LEFT JOIN user_login_history AS ulh ON ulh.id = ulh_max.ulh_max_id
          LEFT JOIN user_referral_code AS ur ON u.id = ur.user_id
          LEFT JOIN (SELECT DISTINCT ON (target_wallet_id)
          target_wallet_id,
          TO_CHAR(created_at AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}', 'YYYY-MM-DD HH24:MI:SS') AS last_deposit_date,
          amount as last_deposit_amount
                FROM transactions
                WHERE transaction_type = 3
                  AND tenant_id =  ${params.tenantId}
               ORDER BY target_wallet_id, created_at DESC
            ) AS lt ON lt.target_wallet_id = w.id
          ${botUserJoin}
          ${whereStr ? `WHERE ${whereStr}` : ''}
          ORDER BY ${params.sort_by} ${params.order} LIMIT ? OFFSET ?`;


    // ------------------- column process --------------------------------
    let mappedFields
    let columnsPermissions
    let columns
    const affiliateKey = 'partnerMatrixData'

    let allowedModulesData = await sequelize.query(
      'SELECT allowed_modules FROM tenant_theme_settings WHERE tenant_id = ?',
      {
        replacements: [params.tenantId],
        type: QueryTypes.SELECT
      }
    );
    if (payload?.sampleCsv && payload.sampleCsv) {
      columnsPermissions = {
        'Email': 'email',
        'Username': 'user_name',
        'Phone': 'phone',
        'Amount': 'total_balance',
        'UTR Number': null,
        'Total Cash Amount': 'total_cash_balance',
        'Total Non Cash Amount': 'total_non_cash_balance',
        ...(isOTBEnabled ? { 'Total One Time Bonus Amount': 'total_one_time_bonus_balance' } : {})
      };

      columns = await getPermissionsForPlayerCsv(columnsPermissions, data.adminId, params.tenantId, data.adminType);
      mappedFields = columns.filter(column => columnsPermissions[column] !== null)
        .map(column => ({ id: columnsPermissions[column], title: column }));
      mappedFields.push({ id: 'utr_number', title: 'UTR Number' })
    }
    else {
      columnsPermissions = {
        'ID': null,
        'First Name': 'first_name',
        'Last Name': 'last_name',
        'Email': 'email',
        'User Name': 'user_name',
        'Nick Name': 'nick_name',
        'Agent name': 'agent_details',
        'Total Balance': 'total_balance',
        'IP Address (Last Login)': 'ip_address',
        'Location': null,
        'Currency': null,
        'Phone Code': 'phone',
        'Phone Number': 'phone',
        'Player Commission (in percentage)': null,
        'Status': null,
        'KYC Done ?': 'kyc_done',
        'Referral Code': 'referral_code',
        'Withdrawal Requests': 'enable_withdrawal_requests',
        'Registered At': null,
        'Last Login Date': 'last_login_date',
        'Vip Level': 'vip_level',
        ...(playerCategoryEnabled ? { "Category": 'category_type' } : {}),
        'Disable Reason': null,
        'Nic': 'national_id',
        'Last Cashable Deposit Date': 'last_deposit_date',
        'Last Cashable Deposit Amount': 'last_deposit_amount'
      };


      allowedModulesData = allowedModulesData.length ? allowedModulesData[0].allowed_modules : null;

      if ((allowedModulesData && allowedModulesData.includes(affiliateKey)) || (data.adminType === 'SuperAdminUser' || data.adminType === 'Manager')) {
        columnsPermissions = {
          ...Object.fromEntries(Object.entries(columnsPermissions).slice(0, 3)),
          'Affiliate Code': 'affiliate_code',
          ...Object.fromEntries(Object.entries(columnsPermissions).slice(3))
        };
      }
      columns = await getPermissionsForPlayerCsv(columnsPermissions, data.adminId, params.tenantId, data.adminType);
      mappedFields = columns.filter(column => columnsPermissions[column] !== null)
        .map(column => ({ id: columnsPermissions[column], title: column }));

      mappedFields.push({ id: 'location', title: 'Location' })
      mappedFields.push({ id: 'currency', title: 'Currency' })
      mappedFields.push({ id: 'id', title: 'Id' })
      mappedFields.push({ id: 'value', title: 'Player Commission (in percentage)' })
      mappedFields.push({ id: 'active', title: 'Status' })
      // if (playerCategoryEnabled) mappedFields.push({ id: 'category_type', title: 'Category' })
      // mappedFields.push({ id: 'vip_level', title: 'Vip Level' })
      mappedFields.push({ id: 'disabled_remarks', title: 'Disable Reason' })
      mappedFields.push({ id: 'registered_at', title: 'Registered At' })
      mappedFields = mappedFields.map(item => item.title === 'Phone Code' ? { ...item, ...{ id: 'phone_code', title: 'Phone Code' } } : item);
      mappedFields = mappedFields.map(item => item.title === 'Phone Number' ? { ...item, ...{ id: 'phone_number', title: 'Phone Number' } } : item);

    }

    //new code
    const chunkSize = 5000
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3

    const uuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${params.tenantId}/csv/player_list/list_${uuid}.csv`
    const csvStringifier = createObjectCsvStringifier({
      header: mappedFields
    })

    // Get count
    const totalRecords = await countTotalRecords(whereStr, params.tenantId, botUserJoin)
    const totalRecordsCount = totalRecords || 0
    const totalChunks = Math.ceil(totalRecordsCount / chunkSize)
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    // Loop through each chunk
    for (let i = 0; i < totalChunks; i++) {
      const offset = i * chunkSize
      // Fetch the data for the current chunk
      const reportData = await sequelize.query(sqlCheckV, {
        type: QueryTypes.SELECT,
        replacements: [chunkSize, offset],
      });

      let mainArr = []
      if (reportData.length > 0) {
        for (const txn of reportData) {

          // if (txn?.last_login_date && txn.last_login_date != '') {
          //   txn.last_login_date = moment.utc(txn.last_login_date).tz(timezone).format('DD-MM-YYYY HH:mm:ss')
          // }
          // else {
          //   txn.last_login_date = ''
          // }
          if (txn.active == true) {
            txn.active = 'Active'
          }
          else {
            txn.active = 'InActive'

          }
          if (payload?.sampleCsv && payload.sampleCsv) {

            const values = {
              'email': (txn?.email || ''),
              'user_name': (txn?.user_name || ''),
              'phone': (txn.phone || ''),
              'amount': '0.00',
              'utr_number': '',
              'total_cash_balance': (txn?.total_cash_amount || '0.00'),
              'total_non_cash_balance': (txn?.total_non_cash_amount || '0.00'),
              ...(isOTBEnabled ? { 'total_one_time_bonus_balance': (txn?.total_one_time_bonus_amount || '0.00') } : {}),
            };

            let valuesWithPermission = {
              'email': 'email',
              'user_name': 'user_name',
              'phone': 'phone',
              'total_balance': 'total_balance',
              'utr_number': null,
              'total_cash_balance': 'total_cash_balance',
              'total_non_cash_balance': 'total_non_cash_balance',
              ...(isOTBEnabled ? { 'total_one_time_bonus_balance': 'total_one_time_bonus_balance' } : {})
            };

            const filteredKeys = await getPermissionsForPlayerCsv(valuesWithPermission, data.adminId, params.tenantId, data.adminType);

            const finalValues = Object.keys(values).filter(key => filteredKeys.includes(key)).reduce((obj, key) => { obj[key] = values[key]; return obj }, {});
            mainArr = [...mainArr, finalValues]

          }
          else {
            const values = {
              'id': txn?.id,
              'first_name': (txn?.first_name || ''),
              'last_name': (txn?.last_name || ''),
              'affiliate_code': (txn?.affiliated_data || ''),
              'email': (txn?.email || ''),
              'user_name': (txn?.user_name || ''),
              'nick_name': (txn?.nick_name || ''), //x
              'agent_details': (txn?.agentname || ''),
              'total_balance': txn?.total_amount ?  formatAmount(txn.total_amount, params.tenantId, txn?.currency_code) : '',
              'ip_address': (txn.latest_ip_address || ''),
              'location': (txn?.latest_location_data ? (`${txn.latest_location_data.city}, ${txn.latest_location_data.region}, ${txn.latest_location_data.countryName}`) : ''),
              'currency': (txn?.currency_code || ''),
              'phone_code': (txn?.phone_code || ''),
              'phone_number': (txn.phone || ''),
              'value': (txn.value || ''),
              'active': txn.active,
              ...(playerCategoryEnabled && { 'category_type': await getCategoryType(txn.category_type) }),
              'kyc_done': ((txn.kyc_done || '') == 1 ? 'true' : 'false'),
              'referral_code': (txn?.referral_code || ''),
              'enable_withdrawal_requests' : ((txn.enable_withdraw_requests || '') == 1 ? 'Yes' : 'No'),
              'registered_at': getDateInStringFormat((txn?.created_at || '')),
              'last_login_date': getDateInStringFormat((txn?.last_login_date || '')), //x
              'vip_level': txn.vip_level,
              'disabled_remarks': txn.disabled_remarks,
              'national_id': (txn?.national_id),
              'last_deposit_date': (txn?.last_deposit_date ? getDateInStringFormat(txn?.last_deposit_date) : 'No Deposits'), // If last_deposit_date exists, use it; otherwise, use "No Deposit"
              'last_deposit_amount': formatAmount(txn?.last_deposit_amount, params.tenantId, txn?.currency_code)
            };

            let valuesWithPermission = {
              'id': null, // Assuming 'id' does not require permission
              'first_name': 'first_name',
              'last_name': 'last_name',
              'email': 'email',
              'user_name': 'user_name',
              'nick_name': 'nick_name',
              'agent_details': 'agent_details',
              'total_balance': 'total_balance',
              'ip_address': 'ip_address',
              'location': null,
              'currency': null,
              'phone_code': 'phone',
              'phone_number': 'phone',
              'value': null,
              'active': null, // Assuming 'active' does not require permission
              ...(playerCategoryEnabled && { 'category_type': 'category_type' }),
              'kyc_done': 'kyc_done',
              'referral_code': 'referral_code',
              'enable_withdrawal_requests': 'enable_withdrawal_requests',
              'registered_at': null,
              'last_login_date': 'last_login_date',
              'vip_level': null,
              'disabled_remarks': null,
              'national_id': 'national_id',
              'last_deposit_date': 'last_deposit_date',
              'last_deposit_amount':'last_deposit_amount'
            }

            if ((allowedModulesData && allowedModulesData.includes(affiliateKey)) || (data.adminType === 'SuperAdminUser' || data.adminType === 'Manager')) {
              valuesWithPermission.affiliate_code = 'affiliate_code'
            }

            const filteredKeys = await getPermissionsForPlayerCsv(valuesWithPermission, data.adminId, params.tenantId, data.adminType);

            const finalValues = Object.keys(values).filter(key => filteredKeys.includes(key)).reduce((obj, key) => { obj[key] = values[key]; return obj }, {});

            mainArr = [...mainArr, finalValues]

          }
        }

      }

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(mainArr)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }
      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      await delay(500)
    }
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    return true;
  } catch (error) {
    console.log('=======error', error);
    throw error;
  }
};
