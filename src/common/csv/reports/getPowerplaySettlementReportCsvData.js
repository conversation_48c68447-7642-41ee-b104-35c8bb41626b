import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { EXPORT_CSV_STATUS } from '../../constants';
import getRolesDetails from '../getAdminRolesDetail';
import { getColumnPermissions } from '../getPermissionsForPlayerCsv.js';
import { getDateRange } from './getStartEndDates';

export default async (data) => {
  try {
    const payload = data.payload
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.IN_PROGRESS
    },
      {
        where: { id: data.id }
      }
    )

    let currentUser = { id: data.adminId, parentType: data.adminType }

    let isSuperAdmin = false;

    if (payload.timeZoneName === 'UTC +00:00') {
      payload.timeZoneName = 'UTC'
    }

    const inputParams = {
      tenantIds: [],
      startDate: '',
      endDate: '',
      searchAgentId: '',
      userId: payload.userId
    }

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      })
      payload.tenantId = adminData.tenantId
    }

    // Tenant Filter Gathering
    const tenantId = Number(payload.tenantId)
    if (tenantId) {
      inputParams.tenantIds = [tenantId]
    } else if (data.adminType == 'SuperAdminUser') {
      const adminData = await db.SuperAdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['parentType', 'tenantIds']
      })
      if (adminData.parentType === 'Manager') {
        inputParams.tenantIds = adminData.tenantIds.map(id => parseInt(id));
      }
      isSuperAdmin = adminData.parentType === 'SuperAdminUser';
    }

    // User filter flag
    let isUserFilter = false
    if (inputParams.userId && !isNaN(inputParams.userId)) {
      isUserFilter = true
    }

    // Agent Filter Gathering
    payload.agentId = isNaN(payload.agentId) ? '' : Number(payload.agentId)

    if (!isUserFilter) {
      if (payload.agentId) {
        inputParams.searchAgentId = payload.agentId
      } else if (data.adminType === 'AdminUser') {
        const roles = await getRolesDetails(currentUser.id)
        if (roles.includes('agent')) {
          inputParams.searchAgentId = currentUser.id
        }
      }
    }

    // Time Filter Gathering
    if (payload.timeType === 'custom') {
      payload.timePeriod = JSON.parse(payload.timePeriod)
    }
    const { start, end } = getDateRange(payload.timeType, payload.timeZoneName, payload.timePeriod?.startDate, payload.timePeriod?.endDate)
    inputParams.startDate = start.utc().format('YYYY-MM-DD HH:mm:ss').concat('.000000')
    inputParams.endDate = end.utc().format('YYYY-MM-DD HH:mm:ss').concat('.999999')

    // Check SBO User Logic
    let botUserJoin = ''
    let botUserSelect = ''
    let botUserFilter = ''
    let botGroupBy = ''

    const checkSboRequest = payload.sbo_request;
    if (isSuperAdmin || checkSboRequest === true) {
      botUserSelect = ', bu.id as bot_user_id'
      botUserJoin = 'left join bot_users bu on bu.user_id = bt.user_id'
      botGroupBy = ', bu.id'
      if (payload.playerCategory === 'bot_players') {
        botUserFilter = 'bu.id is not null'
      } else if (payload.playerCategory === 'real_players') {
        botUserFilter = 'bu.id is null'
      }
    } else if (checkSboRequest === false) {
      botUserSelect = ', bu.id as bot_user_id'
      botUserJoin = 'left join bot_users bu on bu.user_id = bt.user_id'
      botUserFilter = 'bu.id is null'
      botGroupBy = ', bu.id'
    }

    const isAgentFilter = Boolean(Number(inputParams.searchAgentId))

    // Sorting
    const sortFieldMap = {
      user_id: 'bt.user_id',
      user_name: 'u.user_name',
      net_pl: 'net_pl',
      commission_amount: 'commission_amount'
    }

    const sort = sortFieldMap[payload.sortBy] || 'u.user_name'
    const sortOrder = payload.order || 'asc'
    let sortCondition = ''
    if (sort && sortOrder) {
      sortCondition = 'order by ' + sort + ' ' + sortOrder
    }

    // CSV column permissions
    const columnKeyNameMap = {
      player_id: "Player ID",
      player_name: "Player Name",
      net_pl: "Profit/Loss",
      commission: "Commission"
    };

    const columnKeyPermissions = {
      player_id: null,
      player_name: ["players_key", "user_name"],
      net_pl: ["sports_attributes", "profit_loss"],
      commission: ["sports_attributes", "commission_details"],
    };

    const allowedColumns = await getColumnPermissions(columnKeyPermissions, data.adminId, inputParams.tenantIds[0], data.adminType);
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    //new code
    const chunkSize = 5000
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3

    let awsTenantFolderName = 0;
    if (inputParams.tenantIds.length === 1) {
      awsTenantFolderName = inputParams.tenantIds[0];
    }

    const uuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${awsTenantFolderName}/csv/powerplay_settlement_report/transaction_${uuid}.csv`

    const csvStringifier = createObjectCsvStringifier({
      header: allowedColumns.map(columnKey => ({ id: columnKey, title: columnKeyNameMap[columnKey] }))
    })

    const allowedColumnsObj = {};
    allowedColumns.forEach(c => allowedColumnsObj[c] = true);

    // Get count
    const countQuery = getCountQuery({ isUserFilter, botUserJoin, botUserFilter, isAgentFilter })

    const totalRecords = await sequelize.query(countQuery, { type: sequelize.QueryTypes.SELECT, useMaster: false, replacements: inputParams });
    const totalRecordsCount = totalRecords[0]?.total_count || 0
    const totalChunks = Math.ceil(totalRecordsCount / chunkSize)
    // Loop through each chunk
    for (let i = 0; i < totalChunks; i++) {
      const offset = i * chunkSize
      const paginationCondition = 'LIMIT ' + chunkSize + ' OFFSET ' + offset
      // Query preparation and fetch results
      const listQuery = getListQuery({
        isUserFilter,
        sortCondition,
        paginationCondition,
        botUserJoin,
        botUserFilter,
        botUserSelect,
        botGroupBy,
        isAgentFilter
      })
      const reportData = await sequelize.query(listQuery, { type: sequelize.QueryTypes.SELECT, useMaster: false, replacements: inputParams });

      let mainArr = []
      if (reportData.length > 0) {
        for (const txn of reportData) {
          let object = {
            player_id: txn.user_id || '',
            player_name: txn.user_name || '',
            net_pl: txn.net_pl ?? '',
            commission: txn.commission_amount ?? '',
          };
          mainArr = [...mainArr, object]
        }
      }

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(mainArr)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')

      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }
      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      await delay(1000)
    }
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    return true
  } catch (error) {
    console.log('=======errorr', error)
    throw error
  }
}

const agentFilterCTEs = `
  with recursive parents as (
      select
          au.id, au.tenant_id
      from
          admin_users au
      where
          au.id = :searchAgentId

      union all

      select
          child.id, child.tenant_id
      from
          admin_users child
          join parents parent on parent.id = child.parent_id and parent.tenant_id = child.tenant_id
      where
          child.parent_type like 'AdminUser' AND parent.id != child.id
  ),
  filtered_users as (
      select id from users where parent_id in ( select id from parents )
  ),
`;

function getCountQuery ({
  isUserFilter, botUserJoin, botUserFilter, isAgentFilter
}) {
  if (botUserFilter) {
    botUserFilter = 'and ' + botUserFilter
  }

  let userFilter = ''
  let userFilterByAlias = ''
  if (isUserFilter) {
    userFilter = 'and user_id = :userId'
    userFilterByAlias = 'and bt.user_id = :userId'
  }

  let agentCTE = ''
  if (isAgentFilter) {
    agentCTE = agentFilterCTEs
    userFilter = 'and user_id in ( select id from filtered_users )'
    userFilterByAlias = 'and bt.user_id in ( select id from filtered_users )'
  }

  return `
    ${isAgentFilter ? agentCTE : 'with'} settled_markets as (
        select
            distinct market_id
        from
            bets_transactions
        where
            transaction_code in ('SettledMarket', 'ResettleMarket')
            and created_at between :startDate AND :endDate
            and tenant_id IN (:tenantIds)
            ${userFilter}
    ),
    filtered_cancelled_markets as (
        select * from settled_markets sm where not exists ( select 1 from bets_transactions b where b.market_id = sm.market_id and transaction_code = 'CancelSettledMarket' )
    ),
    filtered_markets as (
        select
            market_id
        from
            bets_transactions
        where
            market_id in ( select market_id from filtered_cancelled_markets )
            and transaction_code in ('SettledMarket', 'ResettleMarket')
            and tenant_id IN (:tenantIds)
            ${userFilter}
        group by
            market_id
        having
            min(created_at) between :startDate AND :endDate
    )
    select
        count(distinct bt.user_id) as total_count
    from
        bets_transactions bt
        ${botUserJoin}
    where
        market_id in ( select market_id from filtered_cancelled_markets )
        and bt.tenant_id IN (:tenantIds)
        ${userFilterByAlias}
        ${botUserFilter}
  `
}

function getListQuery ({
  isUserFilter, isAgentFilter,
  sortCondition, paginationCondition,
  botUserJoin, botUserFilter, botUserSelect, botGroupBy
}) {
  if (botUserFilter) {
    botUserFilter = 'and ' + botUserFilter
  }

  let userFilter = ''
  if (isUserFilter) {
    userFilter = 'and user_id = :userId'
  }

  let agentCTE = ''
  if (isAgentFilter) {
    agentCTE = agentFilterCTEs
    userFilter = 'and user_id in ( select id from filtered_users )'
  }

  return `
    ${isAgentFilter ? agentCTE : 'with'} settled_markets as (
        select
            distinct market_id
        from
            bets_transactions
        where
            transaction_code in ('SettledMarket', 'ResettleMarket')
            and created_at between :startDate AND :endDate
            and tenant_id IN (:tenantIds)
            ${userFilter}
    ),
    filtered_cancelled_markets as (
        select * from settled_markets sm where not exists ( select 1 from bets_transactions b where b.market_id = sm.market_id and transaction_code = 'CancelSettledMarket' )
    ),
    filtered_markets as (
        select
            market_id
        from
            bets_transactions
        where
            market_id in ( select market_id from filtered_cancelled_markets )
            and transaction_code in ('SettledMarket', 'ResettleMarket')
            and tenant_id IN (:tenantIds)
            ${userFilter}
        group by
            market_id
        having
            min(created_at) between :startDate AND :endDate
    ),
    latest_settled_tx_ids as (
        select
            user_id, market_id, max(id) as latest_settled_tx_id
        from
            bets_transactions
        where
            market_id in ( select market_id from filtered_markets )
            and transaction_code in ('SettledMarket', 'ResettleMarket')
            and tenant_id IN (:tenantIds)
            ${userFilter}
        group by
            user_id, market_id
    )
    select
        bt.user_id,
        u.user_name,
        (sum(net_pl) * -1)::numeric(18,2) as net_pl,
        (sum(commission_amount) * -1)::numeric(18,2) as commission_amount
        ${botUserSelect}
    from
        bets_transactions bt
        join users u on (bt.user_id = u.id)
        ${botUserJoin}
    where
        bt.id in ( select latest_settled_tx_id from latest_settled_tx_ids )
        ${botUserFilter}
    group by
        bt.user_id, u.user_name ${botGroupBy}
    ${sortCondition}
    ${paginationCondition}
  `
}
