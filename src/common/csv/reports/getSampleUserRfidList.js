import { PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { EXPORT_CSV_STATUS } from '../../constants';


export default async (data) => {
  await db.ExportCsvCenter.update(
    { status: EXPORT_CSV_STATUS.IN_PROGRESS },
    { where: { id: data.id } }
  );

  let params = { tenantId: 0, adminId: data.adminId };

  if (data.adminType === 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: { id: data.adminId },
      attributes: ['tenantId'],
    });

    params.tenantId = adminData.tenantId;
  }

  try {
    const csvUuid = uuidv4().replace(/-/g, '')
    const s3Config = config.getProperties().s3
    const key = `tenants/${params.tenantId}/csv/player_list/sample_user_rfid_${csvUuid}.csv`

    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'name', title: 'Name' },
        { id: 'phoneCode', title: 'Phone Code' },
        { id: 'phone', title: 'Phone' },
        { id: 'rfidToken', title: 'Rfid' }
      ]
    })

    const sampleUsers = [
      { name: 'Test User 1', phoneCode: '91', phone: '000000024511', rfidToken: 'RFID_1241' },
      { name: 'Test User 2', phoneCode: '91', phone: '000000024512', rfidToken: 'RFID_1242' },
      { name: 'Test User 3', phoneCode: '94', phone: '000000024513', rfidToken: 'RFID_1243' },
      { name: 'Test User 4', phoneCode: '91', phone: '000000024514', rfidToken: 'RFID_1244' },
      { name: 'Test User 5', phoneCode: '94', phone: '000000024515', rfidToken: 'RFID_1245' }
    ]

    const csvData = csvStringifier.stringifyRecords(sampleUsers)
    const finalCsvContent = csvStringifier.getHeaderString() + csvData

    const stream = Readable.from([finalCsvContent])
    const contentLength = Buffer.byteLength(finalCsvContent)

    // Upload the CSV file to S3
    const uploadFileParams = {
      Bucket: s3Config.bucket,
      Key: key,
      Body: stream,
      ContentType: 'text/csv',
      ContentLength: contentLength,
    };

    // Upload to S3
    await s3.send(new PutObjectCommand(uploadFileParams))

    // Update CSV status in DB
    await db.ExportCsvCenter.update(
      {
        csvUrl: key,
        status: EXPORT_CSV_STATUS.DONE
      },
      {
        where: { id: data.id }
      }
    )

    return { csvUrl: key }
  } catch (error) {
    throw error
  }
}
