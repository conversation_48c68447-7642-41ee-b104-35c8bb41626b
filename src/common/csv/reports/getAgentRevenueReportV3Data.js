import moment from 'moment-timezone'
import { literal, QueryTypes } from 'sequelize'
import { v4 as uuidv4 } from 'uuid'
import config from '../../../configs/app.config'
import db, { sequelize } from '../../../db/models'
import { s3 } from '../../../libs/awsS3Config'
import { CASINO_PROVIDER_IDS_ARRAY, EXPORT_CSV_STATUS, GET_ST8_SPORTS_ACTIONS, PROD_TENANTS_USING_NEW_NGR_FORMULA, SPORT_PROVIDER_IDS_ARRAY, STAGE_TENANTS_USING_NEW_NGR_FORMULA } from '../../constants'
import { formatAmount, getPlayerType } from '../../helpers'
import getRolesDetails from '../getAdminRolesDetail'
import getPermissionsForPlayerCsv from '../getPermissionsForPlayerCsv'
import getAgentRevenueReportV3DataRealTimeSync from './getAgentRevenueReportV3DataRealTimeSync'
import { getDateRange } from './getStartEndDates'

const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')

export default async (data) => {
  try {
    const payload = data.payload
    await db.ExportCsvCenter.update(
      {
        status: EXPORT_CSV_STATUS.IN_PROGRESS
      },
      {
        where: { id: data.id }
      }
    )

    const checkSboRequest = payload.sbo_request
    let isSuperAdmin = false;
    let isManager = false;
    // ==================== params gathering starts here ====================
    const params = {
      tenantId: 0,
      currencyId: payload?.currency || null,
      dateRange: payload?.datetime || {},
      timeType: payload?.time_type || 'yesterday',
      timeZone: payload?.time_zone_name || 'UTC',
      limit: payload?.limit || 1000,
      offset: payload?.offset || 0,
      sortBy: payload?.sort_by || 'agent_name',
      sortOrder: payload?.order || 'asc',
      agentId: 0,
      agentAction: payload?.agent_action || '',
      actionType: payload?.action_type || '',
      provider_id: payload?.game_provider,
      action_category: payload?.action_category,
      game_type: payload?.game_type,
      agentType: payload?.agent_type,
      isAgentLoggedIn: false,
      loggedInUserId: data.adminId,
      isAgentType: payload?.agent_type === '0' || payload?.agent_type === '1' ? true : false,

    }
    if (Array.isArray(payload.agent_id) && payload.agent_id.length <= 0) {
      payload.agent_id = 0;
    }

    params.agentId = payload?.agent_id || data.adminId;


    payload.tenant_id = parseInt(payload.tenant_id) || "";

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      }
      )
      params.tenantId = adminData.tenantId || payload.tenant_id;
      const roles = await getRolesDetails(data.adminId)
      params.isAgentLoggedIn = roles.includes('agent');
      if (!roles.includes('owner')) {
        //condition for new sub-admin role
        if (roles.includes('owner') || roles.includes('sub-admin')) {
          params.agentId = payload?.agent_id ? payload.agent_id : ''
        } else {
          params.agentId = payload?.agent_id ? payload.agent_id : data.adminId
        }
      } else {
        params.agentId = payload?.agent_id ? payload.agent_id : ''
      }
    } else {
      params.tenantId = payload?.tenant_id || '';
      params.agentId = payload?.agent_id || '';
    }
    if (data.adminType == 'SuperAdminUser' && !params.tenantId) {
      const adminData = await db.SuperAdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['parentType', 'tenantIds']
      })
      if (adminData?.parentType === 'Manager') {
        params.tenantId = adminData.tenantIds.map(id => parseInt(id));
        isManager = true;
      }
    }
    isSuperAdmin = data.adminType === 'SuperAdminUser';
    payload.isSuperAdmin = isSuperAdmin
    payload.playerCategory = getPlayerType(payload)
    payload.checkSboRequest = checkSboRequest

    let agentReports = [];
    if (payload.time_type === 'real-time-sync') {
      agentReports = await getAgentRevenueReportV3DataRealTimeSync(payload, params);
    } else {
      // ==================== Date processing ====================
      let startDate, endDate

      if (params.timeType !== 'custom' || !Object.keys(params.dateRange).length) {
        const { start, end } = getDateRange(params.timeType, params.timeZone);
        startDate = start.format('YYYY-MM-DD')
        endDate = end.format('YYYY-MM-DD')
      } else {
        startDate = moment(params.dateRange.start_date).utc().format()
        endDate = moment(params.dateRange.end_date).utc().format()
      }
      const isProduction = process.env.NODE_ENV === 'production';

      const tenantNgrIds = isProduction ? PROD_TENANTS_USING_NEW_NGR_FORMULA : STAGE_TENANTS_USING_NEW_NGR_FORMULA;

      // Initialize variables
      let loginCheck = ''
      let playerType = ''
      let currencyFilter = ''
      let currencyFilterar = ''
      let selectCountClause = ''
      let providerConditions = ''
      let gameType = ''
      let dynamicWhereClause = ''
      let ngrNewformulaSelect = ''
      let ngrWhereCondition = ''
      let ngrCalculation = ''
      let agentAction = ''
      let agentType = ''
      let actionFilter = ''
      let agentRecursiveParents = ''
      let agentRecursiveParentsLists = ''
      let ngrCalculationList = ''
      let ngrTypesSelect = ''
      let defaultPlayerType = ''
      let ngrSuperCalculation = ''
      let ngrSuperTypesSelect = ''
      let orderBy = ''
      let otherOrder = ''
      let defaultLimit = ''

      const agentLoggedInId = params.agentId ? params.agentId : null;

      let agentIds = '';
      if (params.agentId && params.agentId !== 0) {
        if (Array.isArray(params.agentId)) {
          agentIds = params.agentId.map(id => parseInt(id)).join(',');
        } else {
          agentIds = params.agentId.toString();
        }
      }

      if (params.agentAction && params.agentAction !== '') {
        agentAction = `AND adu.agent_action = '${params.agentAction}'`;
      }
      if (params.isAgentType) {
        agentType = `AND adu.agent_type=${parseInt(params.agentType)}`;
      }


      // NGR Formula logic
      if (!tenantNgrIds.includes(Number(params.tenantId))) {
        ngrNewformulaSelect = `sum(ar.amount) filter(where ar.type = 31) as bonus_currency_wise,
              sum(a2.amount) filter(where ar.type = 31) as bonus_in_base_currency`;
        ngrWhereCondition = 'AND ar.type in (21,31)';
        ngrCalculation = `(coalesce(td.ggr_currency_wise, 0) - coalesce(td.bonus_currency_wise, 0)) as ngr,
              (select coalesce(sum(ggr_in_base_currency), 0) - coalesce(sum(bonus_in_base_currency), 0) from totals_data) as ngr_in_euro`;
        ngrTypesSelect = 'ar.type in (29, 20, 21)';
        ngrSuperTypesSelect = 'ar.type in (29, 20, 21, 31)';
      } else {
        ngrNewformulaSelect = `sum(ar.amount) filter(where ar.type = 37) as bet_non_cash_currency_wise,
              sum(a2.amount) filter(where ar.type = 37) as bet_non_cash_in_base_currency,
              sum(ar.amount) filter(where ar.type = 35) as win_non_cash_currency_wise,
              sum(a2.amount) filter(where ar.type = 35) as win_non_cash_in_base_currency`;
        ngrWhereCondition = 'AND ar.type in (21,37,35)';
        ngrCalculation = `(coalesce(td.ggr_currency_wise, 0) - coalesce(td.bet_non_cash_currency_wise, 0) + coalesce(td.win_non_cash_currency_wise, 0)) as ngr,
              (select coalesce(sum(ggr_in_base_currency), 0) - coalesce(sum(bet_non_cash_in_base_currency), 0) + coalesce(sum(win_non_cash_in_base_currency), 0) from totals_data) as ngr_in_euro`;
        ngrTypesSelect = 'ar.type in (29, 20, 21, 35, 37)';
        ngrSuperTypesSelect = 'ar.type in (29, 20, 21, 35, 37)';
      }


      // Action category filter
      if (params.action_category) {
        const sportProviderIds = SPORT_PROVIDER_IDS_ARRAY
        const casinoProviderIds = CASINO_PROVIDER_IDS_ARRAY
        const actionCategory = params.action_category.toLowerCase();
        if (actionCategory === 'casino') {
          const ids = casinoProviderIds.join(',');
          actionFilter = `AND ar.provider_id IN(${ids})`;
        } else if (actionCategory === 'sports') {
          const ids = sportProviderIds.join(',');
          actionFilter = `AND ar.provider_id IN(${ids})`;
        }
      }

      //game provider id filter
      if (params.provider_id && Array.isArray(params.provider_id) && params.provider_id.length > 0) {
        const providerIdsLower = params.provider_id.map(val => val.toLowerCase().replace(/'/g, "''")); // Handle apostrophes
        const providerIdsStr = providerIdsLower.map((id) => `'${id}'`).join(",");

        let providerCondition, gameCondition;

        // Fetch provider IDs from database
        const providerList = await db.CasinoProvider.findAll({
          where: literal(`lower(name) in (${providerIdsStr})`),
          attributes: ['id'],
          raw: true
        });

        let providerIds = providerList.map(provider => provider.id);
        if (providerIds.length > 0) {
          const id = providerIds.join(',');
          providerCondition = `provider_id IN (${id})`;
        }

        // Fetch transaction seat IDs from database
        const transactionSeatIds = await sequelize.query(
          `SELECT seat_ids
          FROM transactions_providers_list
          WHERE LOWER(title) IN (${providerIdsStr})
          `,
          {
            type: QueryTypes.SELECT,
          }
        )

        // Flatten and process seat IDs
        const seatIds = transactionSeatIds.flatMap((row) =>
          row.seat_ids.map((id) => id.replace(/'/g, "''").toLowerCase())
        );

        const providerToGameId = GET_ST8_SPORTS_ACTIONS;
        for (const [provider, gameId] of Object.entries(providerToGameId)) {
          if (providerIdsLower.includes(provider.toLowerCase())) {
            seatIds.push(gameId.toLowerCase())
          }
        }

        if (seatIds.length > 0) {
          const quotedSeatIds = seatIds.map((id) => `'${id}'`).join(",")
          gameCondition = `game_id IN (${quotedSeatIds})`;
        }

        if (providerCondition && gameCondition) {
          // Both conditions are present, use OR
          providerConditions = `AND (${providerCondition} OR ${gameCondition})`

        } else if (providerCondition) {
          // Only provider condition, use AND
          providerConditions = `AND ${providerCondition}`
        } else if (gameCondition) {
          // Only game condition, use AND
          providerConditions = `AND ${gameCondition}`
        }

      }

      // Game type filter
      if (params.game_type && params.game_type.length > 0) {
        params.game_type = params.game_type.map(x => "'" + x.trim().replace(/'/g, "''").toLowerCase() + "'");
        gameType = `AND game_id in(${params.game_type})`
      }

      payload.checkSboRequest = checkSboRequest

      // Check SBO User Logic
      if (!params.agentId || params.agentId === 0) {
        if (isSuperAdmin || checkSboRequest === true) {
          if (payload.playerCategory === 'bot_players') {
            if (providerConditions || gameType || actionFilter) {
              playerType = "AND bu.id IS NOT NULL"
            } else {
              playerType = "AND is_real_player = false"
            }
            defaultPlayerType = "AND is_real_player = false"
          } else if (payload.playerCategory === 'real_players') {
            if (providerConditions || gameType || actionFilter) {
              playerType = "AND bu.id IS NULL"
            } else {
              playerType = "AND is_real_player = true"
            }
            defaultPlayerType = "AND is_real_player = true"
          }
        } else if (checkSboRequest === false) {
          if (providerConditions || gameType || actionFilter) {
            playerType = "AND bu.id IS  NULL"
          } else {
            playerType = "AND is_real_player = true"
          }
          defaultPlayerType = "AND is_real_player = true"
        }
      }

      if (agentLoggedInId || agentIds) {

        agentRecursiveParentsLists = `WITH direct_and_child_agent_list as (
          WITH RECURSIVE parents AS (
              SELECT
              au.id, au.tenant_id, au.parent_id, au.parent_type, au.agent_type, au.agent_action
              FROM admin_users au
              WHERE au.id in (${agentIds})
              UNION ALL
              SELECT
              child.id, child.tenant_id, child.parent_id, child.parent_type, child.agent_type, child.agent_action
              FROM admin_users child
              JOIN parents parent ON parent.id = child.parent_id AND parent.tenant_id = child.tenant_id
              WHERE child.parent_type LIKE 'AdminUser' AND parent.id != child.id
          )
          SELECT id as agent_id, agent_type, agent_action from admin_users WHERE id IN (SELECT id FROM parents)
      )`;
        loginCheck = `(aro.name = 'agent' and (adu.parent_id in (${agentIds}) or adu.id in (${agentIds})))`
        selectCountClause = 'distinct on (agent_id, currency_id) agent_id, currency_id'
      } else {
        loginCheck = `(aro.name = 'owner' or (aro.name = 'agent' and adu.parent_id in (
          select id from admin_users where id = parent_id )))`
        selectCountClause = 'distinct on (top_parent_id, currency_id) top_parent_id as agent_id, currency_id'
      }


      // Build dynamic WHERE conditions
      const whereConditions = [];

      if (params.currencyId) {
        currencyFilter = `and ar.currency_id = ${params.currencyId}`;
        whereConditions.push(`(w.currency_id = ${params.currencyId} OR ar.currency_id = ${params.currencyId})`);
      }

      // Agent type and action filter
      if (params.isAgentType || params.agentAction) {
        const agentFilterConditions = [];

        if ((agentLoggedInId && params.isAgentType) || (agentIds && params.isAgentType)) {
          if (params.isAgentType) {
            agentFilterConditions.push(`agent_type = ${parseInt(params.agentType)}`);
          }
        }

        if ((agentLoggedInId && params.agentAction) || (agentIds && params.agentAction)) {
          if (params.agentAction) {
            agentFilterConditions.push(`agent_action = '${params.agentAction}'`);
          }
        }

        if (agentFilterConditions.length > 0) {
          whereConditions.push(`COALESCE(al.agent_id, ar.agent_id) IN (
                          SELECT agent_id FROM direct_and_child_agent_list
                          WHERE ${agentFilterConditions.join(' AND ')}
                      )`);
        }
      }

      // Add provider condition filter
      if (providerConditions) {
        whereConditions.push('ar.agent_id IS NOT NULL');
      }


      // NGR calculation logic
      if (!tenantNgrIds.includes(Number(params.tenantId))) {
        if ((params.provider_id && params.provider_id.length > 0) || (params.game_type && params.game_type.length > 0)) {
          ngrCalculationList = 'sum(amount) filter (where type = 21) as ngr';
          ngrSuperCalculation = 'sum(amount_euro) filter (where type = 21) as ngr_euro';
        } else {
          ngrCalculationList = '(coalesce(sum(amount) filter (where type = 21), 0) - coalesce(sum(amount) filter (where type = 31), 0)) as ngr';
          ngrSuperCalculation = '(coalesce(sum(amount_euro) filter (where type = 21), 0) - coalesce(sum(amount_euro) filter (where type = 31), 0)) as ngr_euro';
        }
      } else {
        ngrCalculationList = '(coalesce(sum(amount) filter (where type = 21), 0) - coalesce(sum(amount) filter (where type = 37), 0) + coalesce(sum(amount) filter (where type = 35), 0)) as ngr';
        ngrSuperCalculation = '(coalesce(sum(amount_euro) filter (where type = 21), 0) - coalesce(sum(amount_euro) filter (where type = 37), 0) + coalesce(sum(amount_euro) filter (where type = 35), 0)) as ngr_euro';
      }

      // Parent type check (implement your user authentication)
      let parentType = false;
      if (isSuperAdmin || isManager) {
        parentType = true;
      }
      // Build final WHERE clause dynamically
      dynamicWhereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : '';

      if ((!agentLoggedInId && (!agentIds || agentIds.length === 0)) && (params.sortBy === 'agent_name' || params.sortBy === 'agent_id')) {
        orderBy = `order by ${params.sortBy} ${params.sortOrder}, currency_id LIMIT ${params.limit} OFFSET ${params.offset}`;
      } else {
        orderBy = '';
      }

      if (params.sortBy !== 'agent_name' && params.sortBy !== 'agent_id' && params.sortBy !== 'my_commission') {
        otherOrder = `order by ${params.sortBy} ${params.sortOrder}, currency_id LIMIT ${params.limit} OFFSET ${params.offset}`;
      } else {
        otherOrder = '';
      }

      if (!orderBy && !otherOrder) {
        defaultLimit = `LIMIT ${params.limit} OFFSET ${params.offset}`;
      }

      const queryParams = {
        params: params,
        sort_by: params.sortBy,
        order: params.sortOrder,
        tenant_id: params.tenantId,
        start_date: startDate,
        end_date: endDate,
        agent_ids: agentIds,
        currency_id: params.currencyId,
        login_check: loginCheck,
        agent_login_check: agentLoggedInId,
        is_agent_logged_in: params.isAgentLoggedIn,
        player_type: playerType,
        currency_filter: currencyFilter,
        currency_filter_ar: currencyFilterar,
        select_count_clause: selectCountClause,
        provider_filter: providerConditions,
        game_filter: gameType,
        dynamic_where_clause: dynamicWhereClause,
        ngr_newformula_select: ngrNewformulaSelect,
        ngr_where_condition: ngrWhereCondition,
        ngr_calculation: ngrCalculation,
        action_filter: actionFilter,
        agent_action: agentAction,
        agent_type: agentType,
        agent_recursive_parents: agentRecursiveParents,
        agent_recursive_parents_lists: agentRecursiveParentsLists,
        ngr_calculation_list: ngrCalculationList,
        ngr_types_select: ngrTypesSelect,
        default_player_type: defaultPlayerType,
        parent_type: parentType,
        ngr_super_calculation: ngrSuperCalculation,
        ngr_super_types_select: ngrSuperTypesSelect,
        order_by: orderBy,
        other_order: otherOrder,
        default_limit: defaultLimit
      };
      const agentqueryResults = await getCustomAgentListQuery(queryParams)
      agentReports = await sequelize.query(agentqueryResults, {
        type: QueryTypes.SELECT
      })

    }

    const csvData = agentReports
    // Prepare CSV
    const uuid = uuidv4().replace(/-/g, '')
    const filePath = `/tmp/agent_revenue_${uuid}.csv`

    const columnsPermissions = {
      'Agent ID': null,
      'Agent Name': null,
      'Currency Code': null,
      'My Commission': null,
      'Total Deposit': null,
      'Total Deposit (EUR)': null,
      'Deposit Count': null,
      'Total Deposit Count (EUR)': null,
      'Total Withdraw': null,
      'Total Withdraw (EUR)': null,
      'Withdraw Count': null,
      'Total Withdraw Count (EUR)': null,
      'Bonus': null,
      'Bonus (EUR)': null,
      'Bet': null,
      'Bet After Refund (EUR)': null,
      'Win': null,
      'Win (EUR)': null,
      'GGR': null,
      'GGR (EUR)': null,
      'Player Count': null,
      'Ngr': null,
      'Ngr (EUR)': null,
      'My Revenue': null,
      'My Revenue (EUR)': null
    }


    const columns = await getPermissionsForPlayerCsv(columnsPermissions, data.adminId, params.tenantId, data.adminType)
    const mappedFields = columns.filter(column => columnsPermissions[column] !== null)
      .map(column => ({ id: columnsPermissions[column], title: column }))

    mappedFields.push({ id: 'agent_id', title: 'Agent ID' })
    mappedFields.push({ id: 'agent_name', title: 'Agent Name' })
    mappedFields.push({ id: 'currency_code', title: 'Currency Code' })
    mappedFields.push({ id: 'bet', title: 'Bet' })
    mappedFields.push({ id: 'win', title: 'Win' })
    mappedFields.push({ id: 'ggr', title: 'GGR' })
    mappedFields.push({ id: 'bonus', title: 'Bonus' })
    mappedFields.push({ id: 'ngr', title: 'Ngr' })
    mappedFields.push({ id: 'my_commission', title: 'My Commission' })
    mappedFields.push({ id: 'my_revenue', title: 'My Revenue' })
    mappedFields.push({ id: 'deposit_count', title: 'No. of Deposits' })
    mappedFields.push({ id: 'total_deposit', title: 'Total Deposit' })
    mappedFields.push({ id: 'withdraw_count', title: 'No. of Withdrawals' })
    mappedFields.push({ id: 'total_withdraw', title: 'Total Withdrawals' })
    mappedFields.push({ id: 'player_count', title: 'Registration' })
    // mappedFields.push({ id: 'currency_id', title: 'Currency ID' })
    // mappedFields.push({ id: 'total_deposit_euro', title: 'Total Deposit (EUR)' })
    // mappedFields.push({ id: 'deposit_count_euro', title: 'Total Deposit Count (EUR)' })
    // mappedFields.push({ id: 'total_withdraw_euro', title: 'Total Withdraw (EUR)' })
    // mappedFields.push({ id: 'withdraw_count_euro', title: 'Total Withdraw Count (EUR)' })
    // mappedFields.push({ id: 'bonus_euro', title: 'Bonus (EUR)' })
    // mappedFields.push({ id: 'bet_after_refund', title: 'Bet After Refund' })
    // mappedFields.push({ id: 'bet_after_refund_euro', title: 'Bet After Refund (EUR)' })
    // mappedFields.push({ id: 'win_euro', title: 'Win (EUR)' })
    // mappedFields.push({ id: 'ggr_euro', title: 'GGR (EUR)' })
    // mappedFields.push({ id: 'ngr_euro', title: 'Ngr (EUR)' })
    // mappedFields.push({ id: 'my_revenue_euro', title: 'My Revenue (EUR)' })



    const csvWriter = createCsvWriter({
      path: filePath,
      header: mappedFields
    })

    // ------------------- End: column process --------------------------------

    let mainArr = []
    if (csvData.length > 0) {
      for (const txn of csvData) {

        // Define the values from the transaction data based on the available fields
        const values = {
          agent_id: txn.agent_id,
          agent_name: txn.agent_name,
          currency_code: txn.currency_code,
          my_commission: txn.my_commission,
          total_deposit: formatAmount(txn.total_deposit, params.tenantId, txn.currency_code),
          total_deposit_euro: formatAmount(txn.total_deposit_euro, params.tenantId, txn.currency_code),
          deposit_count: txn.deposit_count,
          deposit_count_euro: txn.deposit_count_euro,
          total_withdraw: formatAmount(txn.total_withdraw, params.tenantId, txn.currency_code),
          total_withdraw_euro: formatAmount(txn.total_withdraw_euro, params.tenantId, txn.currency_code),
          withdraw_count: txn.withdraw_count,
          withdraw_count_euro: txn.withdraw_count_euro,
          bonus: formatAmount(txn.bonus, params.tenantId, txn.currency_code),
          bonus_euro: formatAmount(txn.bonus_euro, params.tenantId, txn.currency_code),
          bet: formatAmount(txn.bet_after_refund, params.tenantId, txn.currency_code),
          bet_after_refund_euro: formatAmount(txn.bet_after_refund_euro, params.tenantId, txn.currency_code),
          win: formatAmount(txn.win_after_refund, params.tenantId, txn.currency_code),
          win_euro: formatAmount(txn.win_after_refund_euro, params.tenantId, txn.currency_code),
          ggr: formatAmount(txn.ggr, params.tenantId, txn.currency_code),
          ggr_euro: formatAmount(txn.ggr_euro, params.tenantId, txn.currency_code),
          player_count: txn.player_count,
          ngr: formatAmount(txn.ngr, params.tenantId, txn.currency_code),
          ngr_euro: formatAmount(txn.ngr_in_eur, params.tenantId, txn.currency_code),
          my_revenue: formatAmount(txn.my_revenue_amount, params.tenantId, txn.currency_code),
          my_revenue_euro: formatAmount(txn.my_revenue_euro, params.tenantId, txn.currency_code)
        }
        // Use columnsPermissions to set up permission checks for the fields
        const columnsPermissions = {
          agent_id: null,
          agent_name: null,
          currency_code: null,
          my_commission: null,
          total_deposit: null,
          total_deposit_euro: null,
          deposit_count: null,
          deposit_count_euro: null,
          total_withdraw: null,
          total_withdraw_euro: null,
          withdraw_count: null,
          withdraw_count_euro: null,
          bonus: null,
          bonus_euro: null,
          bet: null,
          bet_after_refund_euro: null,
          win: null,
          win_euro: null,
          ggr: null,
          ggr_euro: null,
          player_count: null,
          ngr: null,
          ngr_euro: null,
          my_revenue: null,
          my_revenue_euro: null
        }

        // Get permissions for each of the columns
        const filteredKeys = await getPermissionsForPlayerCsv(columnsPermissions, data.adminId, params.tenantId, data.adminType)
        // Filter the values based on the permissions
        const finalValues = Object.keys(values)
          .filter(key => filteredKeys.includes(key)) // Only include permitted keys
          .reduce((obj, key) => {
            obj[key] = values[key]
            return obj
          }, {})
        // Add the filtered final values to mainArr
        mainArr = [...mainArr, finalValues]
      }
    }
    await Promise.all([csvWriter.writeRecords(mainArr)])

    let awsTenantFolderName = params.tenantId || 0;
    if (data.adminType == 'SuperAdminUser' && Array.isArray(params.tenantId)) {
      if (params.tenantId.length === 1) awsTenantFolderName = params.tenantId[0];
      else awsTenantFolderName = 0;
    }

    // Upload to S3
    const s3Config = config.getProperties().s3
    const fileContent = await fs.promises.readFile(filePath)
    const key = `tenants/${awsTenantFolderName}/csv/agent/agent_revenue_${uuid}.csv`

    const s3Params = {
      ACL: 'public-read',
      Bucket: s3Config.bucket,
      Key: key,
      Body: fileContent
    }
    const uploadedFile = await s3.upload(s3Params).promise()

    // Update export status
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    // Remove temporary file
    fs.unlink(filePath, (err) => {
      if (err) {
        console.error('Error deleting temporary file:', err)
      }
    })

    return true
  } catch (error) {
    console.error('Agent Revenue Report Error:', error)

    // Update export status to failed
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.FAILED
    },
      {
        where: { id: data.id }
      }
    )

    throw error
  }
}


async function getCustomAgentListQuery (params) {
  const agentRecursiveQuery = params.agent_ids && params.agent_ids.length > 0
    ? `${params.agent_recursive_parents_lists},`
    : `WITH RECURSIVE direct_and_child_agent_list AS (
          -- Owner + All Its Direct Agents
          SELECT
              adu.id AS agent_id,
              adu.tenant_id,
              adu.id AS top_parent_id,
              adu.agent_type,
              adu.agent_action
          FROM
              admin_users adu
              JOIN admin_users_admin_roles auar ON (adu.id = auar.admin_user_id)
              JOIN admin_roles aro ON (auar.admin_role_id = aro.id)
          WHERE
              (aro.name = 'owner' OR (aro.name = 'agent' AND adu.parent_id IN (
                  SELECT id FROM admin_users WHERE id = parent_id
              )))
              AND adu.tenant_id = ${params.tenant_id}
          UNION ALL

          -- Finding all the child agents of the Direct Agents
          SELECT
              child.id AS agent_id,
              child.tenant_id,
              parent.top_parent_id,
              child.agent_type,
              child.agent_action
          FROM
              direct_and_child_agent_list parent
              JOIN admin_users child ON (
                  parent.agent_id = child.parent_id
                  AND parent.tenant_id = child.tenant_id
              )
          WHERE
              child.parent_type = 'AdminUser'
              AND parent.agent_id != child.id
              AND parent.agent_id NOT IN (
                  SELECT id FROM admin_users
                  WHERE tenant_id = ${params.tenant_id} AND id = parent_id
              )
      ),`;

  const topParentIdSelect = !params.agent_ids || params.agent_ids.length === 0
    ? `, COALESCE(top_parent_id, (SELECT top_parent_id FROM direct_and_child_agent_list WHERE agent_id = ar.agent_id LIMIT 1)) AS top_parent_id`
    : '';

  const dataSource = (params.provider_filter || params.game_filter || params.action_filter)
    ? `player_summary_provider_wise ar
         LEFT JOIN bot_users bu ON (bu.user_id = ar.user_id)`
    : `agent_revenue_report ar
         JOIN agent_revenue_report_currency a2 ON (ar.id = a2.agent_revenue_report_id)`;

  const agentIdsCondition = params.agent_ids && params.agent_ids.length > 0
    ? `AND ar.agent_id IN (SELECT agent_id FROM direct_and_child_agent_list)`
    : '';

  const bonusCalculation = (params.provider_filter || params.game_filter)
    ? '0 AS bonus,'
    : 'SUM(amount) FILTER (WHERE type = 31) AS bonus,';

  const superAdminCalculations = params.parent_type === true
    ? `,
      SUM(amount_euro) FILTER (WHERE type = 21) AS ggr_euro,
      SUM(amount_euro) FILTER (WHERE type = 31) AS bonus_euro,
      SUM(amount_euro) FILTER (WHERE type = 29) AS bet_after_refund_euro,
      SUM(amount_euro) FILTER (WHERE type = 20) AS win_after_refund_euro,
      ${params.ngr_super_calculation || ''}`
    : '';

  const commissionCalculation = params.is_agent_logged_in
    ? 'adus.value::numeric(5,2) AS my_commission,'
    : '(100 - adus.value::numeric(5,2))::numeric(5,2) AS my_commission,';

  const revenueCalculation = params.is_agent_logged_in
    ? 'adus.value::numeric(5,2)'
    : '(100 - adus.value::numeric(5,2))';

  const superAdminSelectFields = params.parent_type === true
    ? `,
      COALESCE(awd.bet_after_refund_euro, 0) AS bet_after_refund_euro,
      COALESCE(awd.win_after_refund_euro, 0) AS win_after_refund_euro,
      COALESCE(awd.ggr_euro, 0) AS ggr_euro,
      COALESCE(awd.bonus_euro, 0) AS bonus_euro,
      COALESCE(awd.ngr_euro, 0) AS ngr_in_eur,
      (COALESCE(awd.ngr_euro, 0) * (100 - adus.value::numeric(5,2))::numeric(5,2) / 100)::numeric(20,2) AS my_revenue_euro`
    : '';

  const query = `
      ${agentRecursiveQuery}
      agent_list_included_invalid_currencies AS (
          SELECT
              COALESCE(al.agent_id, ar.agent_id) AS agent_id,
              COALESCE(w.currency_id, ar.currency_id) AS currency_id
              ${topParentIdSelect} -- Admin Login
          FROM
              direct_and_child_agent_list al
              JOIN wallets w ON (al.agent_id = w.owner_id AND w.owner_type = 'AdminUser')
              FULL OUTER JOIN (
                  SELECT DISTINCT ON (ar.agent_id, ar.currency_id) ar.agent_id, ar.currency_id
                  FROM
                      ${dataSource}
                  WHERE
                      date BETWEEN '${params.start_date}' AND '${params.end_date}'
                      ${params.player_type || ''}
                      AND ar.tenant_id = ${params.tenant_id}
                      ${agentIdsCondition}
                      ${params.provider_filter || ''}
                      ${params.game_filter || ''}
              ) ar ON (
                  al.agent_id = ar.agent_id
                  AND w.currency_id = ar.currency_id
                  ${params.currency_filter || ''}
              )
          ${params.dynamic_where_clause || ''}
      ),
      limit_agent_list_included_invalid_currencies AS (
          WITH agent_list AS (
              SELECT
                  ${params.select_count_clause || 'agent_id, currency_id'}
              FROM agent_list_included_invalid_currencies
          )
          SELECT
              al.agent_id,
              au.agent_name,
              al.currency_id
          FROM agent_list al
          JOIN admin_users au ON (al.agent_id = au.id)
          ${params.order_by }
      ),
      ${filterByAgentQuery(params)}
      agent_wise_data AS (
          SELECT
              atd.agent_id,
              atd.currency_id,
              SUM(amount) FILTER (WHERE type = 2) AS total_deposit,
              SUM(amount) FILTER (WHERE type = 16) AS deposit_count,
              SUM(amount) FILTER (WHERE type = 12) AS total_withdraw,
              SUM(amount) FILTER (WHERE type = 17) AS withdraw_count,
              ${bonusCalculation}
              SUM(amount) FILTER (WHERE type = 29) AS bet_after_refund,
              SUM(amount) FILTER (WHERE type = 20) AS win_after_refund,
              SUM(amount) FILTER (WHERE type = 21) AS ggr,
              COALESCE(SUM(amount) FILTER (WHERE type = 25), 0) AS player_count,
              ${params.ngr_calculation_list || '0 AS ngr'}
              -- SuperAdmin...
              ${superAdminCalculations}
          FROM agent_and_type_wise_data atd
          GROUP BY atd.agent_id, atd.currency_id
          ${params.other_order }
      )
      SELECT
          al.agent_name,
          al.agent_id,
          al.currency_id,
          c.code AS currency_code,
          COALESCE(awd.bet_after_refund, 0) AS bet_after_refund,
          COALESCE(awd.win_after_refund, 0) AS win_after_refund,
          COALESCE(awd.ggr, 0) AS ggr,
          COALESCE(awd.bonus, 0) AS bonus,
          COALESCE(awd.ngr, 0) AS ngr,
          COALESCE(awd.deposit_count, 0) AS deposit_count,
          COALESCE(awd.total_deposit, 0) AS total_deposit,
          COALESCE(awd.withdraw_count, 0) AS withdraw_count,
          COALESCE(awd.total_withdraw, 0) AS total_withdraw,
          COALESCE(awd.player_count, 0) AS player_count,
          ${commissionCalculation}
          (COALESCE(awd.ngr, 0) * ${revenueCalculation} / 100)::numeric(20,2) AS my_revenue_amount
          -- SuperAdmin...
          ${superAdminSelectFields}
      FROM limit_agent_list_included_invalid_currencies al
      LEFT JOIN agent_wise_data awd ON (al.agent_id = awd.agent_id AND al.currency_id = awd.currency_id)
      LEFT JOIN admin_user_settings adus ON (adus.admin_user_id = al.agent_id AND adus.key = 'commission_percentage')
      LEFT JOIN currencies c ON (al.currency_id = c.id)
      ORDER BY  ${params.sort_by} ${params.order}, currency_id
      ${params.default_limit}
  `;

  return query
}

/**
* Filter by agent query
* @param {Object} params - Query parameters
* @returns {string} - SQL query string
*/
function filterByAgentQuery (params) {

  const agentIdSelect = params.agent_ids && params.agent_ids.length > 0
    ? 'al.agent_id,'
    : 'al.top_parent_id AS agent_id,';

  const whereCondition = params.agent_ids && params.agent_ids.length > 0
    ? ''
    : 'WHERE al.top_parent_id IN (SELECT DISTINCT agent_id FROM limit_agent_list_included_invalid_currencies)';


  const groupByClause = params.agent_ids && params.agent_ids.length > 0
    ? 'al.agent_id, ar.currency_id, ar.type'
    : 'al.top_parent_id, ar.currency_id, ar.type';

  const superAdminAmount = params.parent_type === true
    ? ', SUM(arc.amount) AS amount_euro'
    : '';

  if (params.provider_filter || params.game_filter || params.action_filter) {
    const adminUserJoin = (params.agent_action || params.agent_type)
      ? `JOIN admin_users adu ON (adu.id = al.agent_id ${params.agent_action || ''} ${params.agent_type || ''})`
      : '';

    const superAdminJoin = params.parent_type === true
      ? `
          -- SuperAdmin Old Formula and new formula dynamic
          LEFT JOIN player_provider_other_currency arc ON (arc.player_summary_provider_id = ar.id AND arc.currency_id = '1' AND ${params.ngr_super_types_select || 'true'})`
      : '';

    const unionSuperAdminAmount = params.parent_type === true
      ? `
          -- SuperAdmin...
          , 0 AS amount_euro`
      : '';

    return `agent_and_type_wise_data AS (
          SELECT
              ${agentIdSelect}
              ar.currency_id,
              type,
              SUM(ar.amount) AS amount
              -- SuperAdmin...
              ${superAdminAmount}
          FROM
              direct_and_child_agent_list al
              ${adminUserJoin}
              JOIN player_summary_provider_wise ar ON (
                  al.agent_id = ar.agent_id
                  AND ar.tenant_id = ${params.tenant_id}
                  AND ar.date BETWEEN '${params.start_date}' AND '${params.end_date}'
                  AND (
                      (ar.type IN (2, 16, 12, 17, 31))
                      OR
                      (${params.ngr_types_select} ${params.action_filter } ${params.provider_filter} ${params.game_filter})
                  )
                  ${params.currency_filter}
              )
              LEFT JOIN bot_users bu ON (bu.user_id = ar.user_id)
              ${superAdminJoin}
              ${whereCondition}
              ${params.player_type}
          GROUP BY
              ${groupByClause}
          UNION ALL

          SELECT
              ${agentIdSelect}
              ar.currency_id,
              25 AS type,
              SUM(ar.amount) AS amount
              ${unionSuperAdminAmount}
          FROM
              direct_and_child_agent_list al
              JOIN agent_revenue_report ar ON (
                  al.agent_id = ar.agent_id
                  AND ar.tenant_id = ${params.tenant_id}
                  ${params.default_player_type}
                  AND ar.date BETWEEN '${params.start_date}' AND '${params.end_date}'
                  AND type = 25
                  ${params.currency_filter}
              )
              ${whereCondition}
          GROUP BY
              ${groupByClause}
      ),`;
  } else {
    const superAdminJoin = params.parent_type === true
      ? `
          -- SuperAdmin Old and new Formula..
          LEFT JOIN agent_revenue_report_currency arc ON (arc.agent_revenue_report_id = ar.id AND arc.currency_id = '1' AND ${params.ngr_super_types_select || 'true'})`
      : '';

    return `agent_and_type_wise_data AS (
          SELECT
              ${agentIdSelect}
              ar.currency_id,
              type,
              SUM(ar.amount) AS amount
              ${superAdminAmount}
          FROM
              direct_and_child_agent_list al
              JOIN admin_users adu ON (adu.id = al.agent_id ${params.agent_action} ${params.agent_type})
              JOIN agent_revenue_report ar ON (
                  al.agent_id = ar.agent_id
                  AND ar.tenant_id = ${params.tenant_id}
                  AND ar.is_real_player = true
                  AND ar.date BETWEEN '${params.start_date}' AND '${params.end_date}'
                  ${params.currency_filter} -- Currency Filter...
              )
              ${superAdminJoin}
              ${whereCondition}
          GROUP BY
              ${groupByClause}
      ),`;
  }
}
