import moment from 'moment'
import { literal, QueryTypes } from 'sequelize'
import { v4 as uuidv4 } from 'uuid'
import config from '../../../configs/app.config'
import db, { sequelize, Sequelize } from '../../../db/models'
import { s3 } from '../../../libs/awsS3Config'
import { EXPORT_CSV_STATUS, GET_ST8_SPORTS_ACTIONS, PROD_TENANTS_USING_NEW_NGR_FORMULA, STAGE_TENANTS_USING_NEW_NGR_FORMULA } from '../../constants'
import { fetchAgentIds, formatAmount, getDateInStringFormat, getPlayerType } from '../../helpers'
import getRolesDetails from '../getAdminRolesDetail'
import { getColumnPermissions } from '../getPermissionsForPlayerCsv'
import getPlayerRevenueReportDataRealTimeSync from './getPlayerRevenueReportDataRealTimeSync'
import { getDateRange } from './getStartEndDates'

const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')

export default async (data) => {
  try {
    const payload = data.payload
    await db.ExportCsvCenter.update(
      {
        status: EXPORT_CSV_STATUS.IN_PROGRESS
      },
      {
        where: { id: data.id }
      }
    )

    if (!payload.time_zone_name || payload.time_zone_name === 'UTC +00:00') {
      payload.time_zone_name = 'UTC';
    }

    let botUserJoin = ''
    let botUserSelect = ''
    const checkSboRequest = payload.sbo_request
    let isSuperAdmin = false;
    // ==================== params gathering starts here ====================
    const params = {
      tenantId: 0,
      search: payload?.search ? payload?.search : '',
      // search_by_id: payload?.search_by_id ? payload?.search_by_id : '',
      currency_id: payload?.currency ? payload?.currency : '',
      date_range: payload?.date_range ? payload?.date_range : [],
      sort_by: payload?.sort_by ? payload?.sort_by : 'id',
      order: payload?.order ? payload?.order : 'desc',
      action_category: payload?.action_category,
      // sports_items: payload?.sports_items,
      game_type: payload?.game_type,
      // providers: payload?.providers,
      game_provider: payload?.game_provider,
      ggr_ngr: payload?.ggr_ngr
    }

    // Manage Sort Fields
    const sortMap = {
      player_registration_date: 'created_at',
      bet: 'bet_after_refund',
      bet_count: 'bet_after_refund_count',
      win: 'win_after_refund'
    };
    params.sort_by = sortMap[params.sort_by] || params.sort_by;

    // Tenant Filter Gathering
    const tenantId = Number(payload.tenant_id)

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      })
      params.tenantId = [adminData.tenantId];
    } else {
      if (tenantId) {
        params.tenantId = [tenantId]
      } else {
        const adminData = await db.SuperAdminUser.findOne({
          where: {
            id: data.adminId
          },
          attributes: ['parentType', 'tenantIds']
        })
        if (adminData.parentType === 'Manager') {
          params.tenantId = adminData.tenantIds.map(id => parseInt(id));
        }
      }
    }
    isSuperAdmin = data.adminType === 'SuperAdminUser';

    // Agent Filter Gathering
    params.agentId = 0;
    const agentId = Number(payload.agent_id)

    let searchAgentId
    if (agentId) {
      searchAgentId = agentId
    } else if (data.adminType == 'AdminUser') {
      const roles = await getRolesDetails(data.adminId)
      if (roles.includes('agent')) {
        searchAgentId = data.adminId
      }
    }
    if (searchAgentId) {
      params.agentId = await fetchAgentIds(searchAgentId)
    }

    // ==================== params gathering ends here ====================

    // ====================filters here ========================
    let users = [];
    isSuperAdmin = data.adminType === 'SuperAdminUser';
    payload.isSuperAdmin = isSuperAdmin
    payload.playerCategory = getPlayerType(payload)
    payload.checkSboRequest = checkSboRequest

    if (payload.time_type === 'real-time-sync') {
      users = await getPlayerRevenueReportDataRealTimeSync(payload, params, data);
    } else {
      let whereStr = ''
      let havingQuery = ''

      if (params.agentId != 0) {
        let agentIds;
        if (Array.isArray(params.agentId)) {
          agentIds = params.agentId.map(id => parseInt(id)).join(',')
        } else {
          agentIds = params.agentId;
        }
        whereStr += `${whereStr ? ' AND' : ''} pw.agent_id IN (${agentIds})`
      }

      let providerCondition, gameCondition;
      if (params.game_provider && params.game_provider.length > 0) {
        // Normalize provider names: trim, escape, and convert to lowercase
        const providerNames = params.game_provider.map(provider => provider.trim().replace(/'/g, "''").toLowerCase());
        const providerNamesStr = providerNames.map(name => `'${name}'`).join(',');

        // Fetch provider IDs
        const providerList = await db.CasinoProvider.findAll({
          where: literal(`lower(name) in (${providerNamesStr})`),
          attributes: ['id'],
          raw: true
        });
        const providerIds = providerList.map(provider => provider.id);
        if (providerIds.length > 0) {
          const id = providerIds.join(',');
          providerCondition = `pw.provider_id IN (${id})`;
        }

        // Fetch transaction seat IDs
        const transactionSeatIds = await sequelize.query(
          `SELECT seat_ids
            FROM transactions_providers_list
            WHERE LOWER(title) IN (${providerNamesStr});
            `,
          {
            type: Sequelize.QueryTypes.SELECT,
          }
        )

        let seatIds = transactionSeatIds.flatMap((row) =>
          row.seat_ids.map((id) => id.replace(/'/g, "''").toLowerCase())
        );

        const providerToGameId = GET_ST8_SPORTS_ACTIONS;
        for (const [provider, gameId] of Object.entries(providerToGameId)) {
          if (providerNames.includes(provider.toLowerCase())) {
            seatIds.push(gameId.toLowerCase());
          }
        }

        if (seatIds.length > 0) {
          const quotedSeatIds = seatIds.map((id) => `'${id}'`).join(",");
          gameCondition = `lower(pw.game_id) IN (${quotedSeatIds})`;
        }

        // Construct `whereStr`
        if (providerCondition && gameCondition) {
          // If both conditions are present, use OR
          whereStr += (whereStr ? ' AND ' : '') + `(${providerCondition} OR ${gameCondition})`;
        } else if (providerCondition) {
          // Only provider condition, use AND
          whereStr += (whereStr ? ' AND ' : '') + providerCondition;
        } else if (gameCondition) {
          // Only game condition, use AND
          whereStr += (whereStr ? ' AND ' : '') + gameCondition;
        }
      }

      if (params.action_category) {
        const actionCategory = params.action_category.toLowerCase();

        if (actionCategory === 'casino') {
          whereStr += `
            ${whereStr ? ' AND' : ''} (
              pw.provider_id NOT IN (
                SELECT id
                FROM casino_providers
                WHERE LOWER(name) IN ('jetfair', 'powerplay','turbostars')
              )
              AND (pw.game_id NOT IN ('bti_sportsbook', 'sbs_sportsbook', 'sap_lobby') OR pw.game_id IS NULL)
            )
          `;
        } else if (actionCategory === 'sports') {
          whereStr += `
            ${whereStr ? ' AND' : ''} (
              pw.provider_id IN (
                SELECT id
                FROM casino_providers
                WHERE LOWER(name) IN ('jetfair', 'powerplay','turbostars')
              )
              OR pw.game_id IN ('bti_sportsbook', 'sbs_sportsbook', 'sap_lobby')
                OR ( (pw.provider_id IS NULL OR pw.provider_id = 0) AND pw.game_id IS NULL )
            )
          `;
        }
      }

      if (params.game_type && params.game_type.length > 0) {
        params.game_type = params.game_type.map(x => "'" + x.trim().replace(/'/g, "''").toLowerCase() + "'");
        whereStr += `${whereStr ? ' AND' : ''} pw.game_id IN (${params.game_type})`
      }

      if (params.search) {
        params.search = params.search.replace(/'/g, '')
        whereStr += `${whereStr ? ' AND' : ''} (u.id = '${params.search}')`
      }

      if (params.currency_id) {
        whereStr += `${whereStr ? ' AND' : ''} (pw.currency_id = '${params.currency_id}')`
      }

      let timePeriod = payload.datetime || {}
      const timeType = payload.time_type || 'yesterday'

      if (timeType != 'custom' || !Object.keys(timePeriod).length) {
        const { start, end } = getDateRange(timeType, payload.time_zone_name);
        const startDate = start.format('YYYY-MM-DD')
        const endDate = end.format('YYYY-MM-DD')
        whereStr += `${whereStr ? ' AND' : ''} pw.date BETWEEN '${startDate}' AND '${endDate}'`
      } else {
        const startDate = moment(timePeriod.start_date).utc().format()
        const endDate = moment(timePeriod.end_date).utc().format()
        whereStr += `${whereStr ? ' AND' : ''} pw.date BETWEEN '${startDate}' AND '${endDate}'`
      }

      if (params.tenantId != 0) {
        if (Array.isArray(params.tenantId)) {
          const tenantIds = params.tenantId.map(id => parseInt(id)).join(',')
          whereStr += `${whereStr ? ' AND' : ''} pw.tenant_id in (${tenantIds})`
        } else {
          whereStr += `${whereStr ? ' AND' : ''} pw.tenant_id = ${params.tenantId}`
        }
      }

      // Check SBO User Logic
      // payload.playerCategory = payload.playerCategory || payload.playerType
      payload.playerCategory = getPlayerType(payload)

      if (isSuperAdmin || checkSboRequest === true) {
        botUserSelect = ', bu.id as bot_user_id'
        botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
        if (payload.playerCategory === 'bot_players') {
          whereStr +=  `${whereStr ? ' AND' : ''} bu.id IS NOT NULL`
        } else if (payload.playerCategory === 'real_players') {
          whereStr +=  `${whereStr ? ' AND' : ''} bu.id IS NULL`
        }
      } else if (checkSboRequest === false) {
        botUserSelect = ', bu.id as bot_user_id'
        botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
        whereStr +=  `${whereStr ? ' AND' : ''} bu.id IS NULL`
      }

      if(params.ggr_ngr == 'ggr_profit'){
        havingQuery = 'HAVING SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) > 0'
      }
      if(params.ggr_ngr == 'ggr_loss'){
        havingQuery = 'HAVING SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) <  0'
      }
      if(params.ggr_ngr == 'ngr_profit'){
        havingQuery = 'HAVING (SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) - SUM(CASE WHEN pw.type = 31 THEN pw.amount ELSE 0 END)) > 0'
      }
      if(params.ggr_ngr == 'ngr_loss'){
        havingQuery = 'HAVING (SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) - SUM(CASE WHEN pw.type = 31 THEN pw.amount ELSE 0 END)) < 0  '
      }

      const isProduction = process.env.NODE_ENV === 'production'
      const tenantIds = isProduction ? PROD_TENANTS_USING_NEW_NGR_FORMULA : STAGE_TENANTS_USING_NEW_NGR_FORMULA
      const id =  Number(Array.isArray(params.tenantId) ? params.tenantId[0] : params.tenantId);;
      let dynamicGgrAttr = '';
      let dynamicNgrAttr = '';
      if (params.ggr_ngr === 'ggr_profit' || params.ggr_ngr === 'ggr_loss') {
        dynamicGgrAttr = 'SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) AS ggr';
        dynamicNgrAttr = '0 AS ngr';
      } else if (params.ggr_ngr === 'ngr_profit' || params.ggr_ngr === 'ngr_loss') {
        dynamicGgrAttr = '0 AS ggr';
        if ((providerCondition || gameCondition) &&  !tenantIds?.includes(id)) {
          dynamicNgrAttr = 'SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) AS ngr';
        } else {
          dynamicNgrAttr = tenantIds?.includes(id)
            ? "(SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) - SUM(CASE WHEN pw.type = 37 THEN pw.amount ELSE 0 END) + SUM(CASE WHEN pw.type = 35 THEN pw.amount ELSE 0 END)) AS ngr"
            : "(SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) - SUM(CASE WHEN pw.type = 31 THEN pw.amount ELSE 0 END)) AS ngr";

        }
      } else {
        dynamicGgrAttr = 'SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) AS ggr';
        if ((providerCondition || gameCondition) &&  !tenantIds?.includes(id)) {
          dynamicNgrAttr = 'SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) AS ngr';
        } else {
          dynamicNgrAttr = tenantIds?.includes(id)
            ? "(SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) - SUM(CASE WHEN pw.type = 37 THEN pw.amount ELSE 0 END) + SUM(CASE WHEN pw.type = 35 THEN pw.amount ELSE 0 END)) AS ngr"
            : "(SUM(CASE WHEN pw.type = 21 THEN pw.amount ELSE 0 END) - SUM(CASE WHEN pw.type = 31 THEN pw.amount ELSE 0 END)) AS ngr";
        }
      }

    // ========================filter ends here================================
      const attributesWithPermissions = {
        'u.id': ["players_key", "user_name"],
        'u.user_name': ["players_key", "user_name"],
        'u.email': ["players_key", "email"],
        'u.phone': ["players_key", "phone"],
        'u.vip_level': ["players_key", "vip_level"],
        'u.affiliated_data': ["players_key", "affiliate_code"],
        [`(u.created_at::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${payload.time_zone_name}') AS created_at`]: ["players_key", "player_registration_date"],
        'admin_users.agent_name AS agent_name': ["players_key", "agent_details"],
        'pw.currency_id': null,
        'c.code': null,
        // Deposit types
        'SUM(CASE WHEN pw.type = 1 THEN pw.amount ELSE 0 END) AS total_cash_deposit_by_admin': ["financial_attributes", "total_cash_deposit_by_admin"],
        'SUM(CASE WHEN pw.type = 2 THEN pw.amount ELSE 0 END) AS total_deposit': ["financial_attributes", "total_deposit"],
        'SUM(CASE WHEN pw.type = 3 THEN pw.amount ELSE 0 END) AS loyalty_cash_bonus': ["report_attributes", "royalty_cash_bonus"],
        'SUM(CASE WHEN pw.type = 4 THEN pw.amount ELSE 0 END) AS loyalty_non_cash_bonus': ["report_attributes", "royalty_non_cash_bonus"],
        'SUM(CASE WHEN pw.type = 5 THEN pw.amount ELSE 0 END) AS total_non_cash_deposit_by_admin': ["financial_attributes", "total_non_cash_deposit_by_admin"],
        'SUM(CASE WHEN pw.type = 6 THEN pw.amount ELSE 0 END) AS total_manual_cash_deposit_by_user': ["financial_attributes", "total_manual_cash_deposit_by_user"],
        'SUM(CASE WHEN pw.type = 7 THEN pw.amount ELSE 0 END) AS total_cash_deposit_gateway_by_user': ["financial_attributes", "total_cash_deposit_by_user"],
        // Withdraw types
        'SUM(CASE WHEN pw.type = 8 THEN pw.amount ELSE 0 END) AS total_non_cash_withdraw_by_admin': ["financial_attributes", "total_non_cash_withdraw_by_admin"],
        'SUM(CASE WHEN pw.type = 9 THEN pw.amount ELSE 0 END) AS total_manual_cash_withdraw_by_user': ["financial_attributes", "total_manual_cash_withdraw_by_user"],
        'SUM(CASE WHEN pw.type = 10 THEN pw.amount ELSE 0 END) AS total_cash_gateway_withdraw_by_user': ["financial_attributes", "total_cash_gateway_withdraw_by_user"],
        'SUM(CASE WHEN pw.type = 11 THEN pw.amount ELSE 0 END) AS total_cash_withdraw_by_admin': ["financial_attributes", "total_cash_withdraw_by_admin"],
        'SUM(CASE WHEN pw.type = 12 THEN pw.amount ELSE 0 END) AS total_withdraws': ["financial_attributes", "total_withdrawals"],
        // Other financial metrics
        'SUM(CASE WHEN pw.type = 14 THEN pw.amount ELSE 0 END) AS total_one_time_bonus_deposit': ["one_time_bonus_deposit", "R"],
        'SUM(CASE WHEN pw.type = 15 THEN pw.amount ELSE 0 END) AS total_one_time_bonus_withdraw': ["one_time_bonus_withdrawal", "R"],
        'SUM(CASE WHEN pw.type = 18 THEN pw.amount ELSE 0 END) AS commission': null,
        'SUM(CASE WHEN pw.type = 20 THEN pw.amount ELSE 0 END) AS win_after_refund': ["report_attributes", "win"],
        [dynamicGgrAttr]: ["report_attributes", "ggr"],
        [dynamicNgrAttr]: ["report_attributes", "ngr"],
        'SUM(CASE WHEN pw.type = 29 THEN pw.amount ELSE 0 END) AS bet_after_refund': ["report_attributes", "bet"],
        'SUM(CASE WHEN pw.type = 30 THEN pw.amount ELSE 0 END) AS bet_after_refund_count': ["report_attributes", "total_bet"],
        'SUM(CASE WHEN pw.type = 31 THEN pw.amount ELSE 0 END) AS bonus': ["report_attributes", "bonus"]
      }

      const finalAttributes = await getColumnPermissions(
        attributesWithPermissions,
        data.adminId,
        params.tenantId,
        data.adminType
      )
      const selectStatement = `${finalAttributes.join(', ')}` + `${botUserJoin ? botUserSelect : ''}`
      // const limit = params.limit || 1000 // Default to 1000 if limit is not provided
      // const page = params.page || 0 // Default to 0 if page is not provided
      // // const offset = page * limit

      // Dynamically append LIMIT and OFFSET
      // const paginationStr = limit ? `LIMIT ${limit} OFFSET ${offset}` : ''
      const paginationStr = ''
      const sqlQuery = `
        SELECT ${selectStatement}
        FROM player_summary_provider_wise AS pw
        LEFT JOIN users AS u ON u.id = pw.user_id
        LEFT JOIN currencies AS c ON pw.currency_id = c.id
        LEFT JOIN admin_users ON u.parent_id = admin_users.id
        LEFT JOIN casino_providers AS cp ON pw.provider_id = cp.id
        ${botUserJoin}
        ${whereStr ? `WHERE ${whereStr}` : ''}
        GROUP BY u.id, u.user_name, u.email, u.phone, u.vip_level, u.affiliated_data,
                 u.created_at, admin_users.agent_name, pw.currency_id, c.code ${botUserJoin? ', bu.id' : ''}
        ${params?.ggr_ngr ? `${havingQuery}` : ''}
        ORDER BY ${params.sort_by} ${params.order} ${paginationStr}
      `

      users = await sequelize.query(sqlQuery, {
        type: QueryTypes.SELECT
      })
    }

    const csvData = users
    // const getTotal = await countTotalRecords(whereStr, params.tenantId)

    const uuid = uuidv4().replace(/-/g, '')
    const filePath = `/tmp/player_summary_${uuid}.csv`


    // ------------------- Start: column process --------------------------------
    const columnKeyNameMap = {
      player_id: 'Player ID',
      user_name: 'Player Name',
      email: 'Email',
      phone: 'Phone Number',
      vip_level: 'Vip Level',
      affiliate_code: 'Affiliated Data',
      created_at: 'Created At',
      agent_name: 'Agent Name',
      code: 'Currency',
      total_cash_deposit_by_admin: 'Total Cash Deposit By Admin',
      total_deposit: 'Total Deposit',
      loyalty_cash_bonus: 'Loyalty Cash Bonus',
      loyalty_non_cash_bonus: 'Loyalty Non Cash Bonus',
      total_non_cash_deposit_by_admin: 'Total Non Cash Deposit By Admin',
      total_manual_cash_deposit_by_user: 'Total Manual Cash Deposit By User',
      total_cash_deposit_gateway_by_user: 'Total Cash Deposit Gateway By User',
      total_non_cash_withdraw_by_admin: 'Total Non Cash Withdraw By Admin',
      total_manual_cash_withdraw_by_user: 'Total Manual Cash Withdraw By User',
      total_cash_gateway_withdraw_by_user: 'Total Cash Gateway Withdraw By User',
      total_cash_withdraw_by_admin: 'Total Cash Withdraw By Admin',
      total_withdraws: 'Total Withdraws',
      total_one_time_bonus_deposit: 'Total One Time Bonus Deposit',
      total_one_time_bonus_withdraw: 'Total One Time Bonus Withdraw',
      ...(params.action_category === 'sports' && { commission: 'Commission' }),
      ...(params.action_category === 'sports' && { commission_minus_ggr: '(Total Commission - GGR)' }),
      bet: 'Bet',
      win: 'Win',
      ggr: 'GGR',
      ngr: 'NGR',
      bet_count: 'Total Bet Count',
      bonus: 'Bonus'
    };

    const columnKeyPermissions = {
      player_id: ["players_key", "user_name"],
      user_name: ["players_key", "user_name"],
      email: ["players_key", "email"],
      phone: ["players_key", "phone"],
      vip_level: ["players_key", "vip_level"],
      affiliate_code: ["players_key", "affiliate_code"],
      created_at: ["players_key", "player_registration_date"],
      agent_name: ["players_key", "agent_details"],
      code: null,
      total_cash_deposit_by_admin: ["financial_attributes", "total_cash_deposit_by_admin"],
      total_deposit: ["financial_attributes", "total_deposit"],
      loyalty_cash_bonus: ["report_attributes", "royalty_cash_bonus"],
      loyalty_non_cash_bonus: ["report_attributes", "royalty_non_cash_bonus"],
      total_non_cash_deposit_by_admin: ["financial_attributes", "total_non_cash_deposit_by_admin"],
      total_manual_cash_deposit_by_user: ["financial_attributes", "total_manual_cash_deposit_by_user"],
      total_cash_deposit_gateway_by_user: ["financial_attributes", "total_cash_deposit_by_user"],
      total_non_cash_withdraw_by_admin: ["financial_attributes", "total_non_cash_withdraw_by_admin"],
      total_manual_cash_withdraw_by_user: ["financial_attributes", "total_manual_cash_withdraw_by_user"],
      total_cash_gateway_withdraw_by_user: ["financial_attributes", "total_cash_gateway_withdraw_by_user"],
      total_cash_withdraw_by_admin: ["financial_attributes", "total_cash_withdraw_by_admin"],
      total_withdraws: ["financial_attributes", "total_withdrawals"],
      total_one_time_bonus_deposit: ["one_time_bonus_deposit", "R"],
      total_one_time_bonus_withdraw: ["one_time_bonus_withdrawal", "R"],
      ...(params.action_category === 'sports' && { commission: null }),
      ...(params.action_category === 'sports' && { commission_minus_ggr: null }),
      bet: ["report_attributes", "bet"],
      win: ["report_attributes", "win"],
      ggr: ["report_attributes", "ggr"],
      ngr: ["report_attributes", "ngr"],
      bet_count: ["report_attributes", "total_bet"],
      bonus: ["report_attributes", "bonus"]
    };

    const allowedColumns = await getColumnPermissions(columnKeyPermissions, data.adminId, params.tenantId, data.adminType);

    const csvWriter = createCsvWriter({
      path: filePath,
      header: allowedColumns.map(columnKey => ({ id: columnKey, title: columnKeyNameMap[columnKey] }))
    })

    // ------------------- End: column process --------------------------------

    let mainArr = []

    if (csvData.length > 0) {
      for (const txn of csvData) {
        // Define the values from the transaction data based on the available fields
        const values = {
          player_id: txn.id,
          user_name: txn.user_name,
          email: txn.email,
          phone: txn.phone ? String(txn.phone) : '',
          vip_level: txn.vip_level,
          // created_at: moment(txn.created_at).toISOString(),
          created_at: getDateInStringFormat(moment(txn.created_at).format('DD-MM-YYYY HH:mm:ss')),
          agent_name: txn.agent_name,
          code: txn.code,
          total_cash_deposit_by_admin:  (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.total_cash_deposit_by_admin, params.tenantId, txn.code),
          total_deposit:  (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.total_deposit, params.tenantId, txn.code),
          loyalty_cash_bonus:  (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.loyalty_cash_bonus, params.tenantId, txn.code),
          loyalty_non_cash_bonus:  (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.loyalty_non_cash_bonus, params.tenantId, txn.code),
          total_non_cash_deposit_by_admin:  (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.total_non_cash_deposit_by_admin, params.tenantId, txn.code),
          total_manual_cash_deposit_by_user: (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.total_manual_cash_deposit_by_user, params.tenantId, txn.code),
          total_cash_deposit_gateway_by_user: (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.total_cash_deposit_gateway_by_user, params.tenantId, txn.code),
          total_non_cash_withdraw_by_admin:  (params.game_provider && params.game_provider.length > 0)? '-' :formatAmount(txn.total_non_cash_withdraw_by_admin, params.tenantId, txn.code),
          total_manual_cash_withdraw_by_user:  (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.total_manual_cash_withdraw_by_user, params.tenantId, txn.code),
          total_cash_gateway_withdraw_by_user:  (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.total_cash_gateway_withdraw_by_user, params.tenantId, txn.code),
          total_cash_withdraw_by_admin:  (params.game_provider && params.game_provider.length > 0)? '-' :formatAmount(txn.total_cash_withdraw_by_admin, params.tenantId, txn.code),
          total_withdraws:  (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.total_withdraws, params.tenantId, txn.code),
          affiliate_code: txn.affiliated_data,
          total_one_time_bonus_deposit:  (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.total_one_time_bonus_deposit, params.tenantId, txn.code),
          total_one_time_bonus_withdraw:  (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.total_one_time_bonus_withdraw, params.tenantId, txn.code),
          ...(params.action_category === 'sports' && { commission: parseFloat(txn.commission).toFixed(5) }),
          ...(params.action_category === 'sports' && { commission_minus_ggr: parseFloat(txn.commission - txn.ggr).toFixed(5) }),
          bet: formatAmount(txn.bet_after_refund, params.tenantId, txn.code),
          win: formatAmount(txn.win_after_refund, params.tenantId, txn.code),
          ggr: formatAmount(txn.ggr, params.tenantId, txn.code),
          ngr: formatAmount(txn.ngr, params.tenantId, txn.code),
          bet_count: txn.bet_after_refund_count,
          bonus:  (params.game_provider && params.game_provider.length > 0) ? '-' :formatAmount(txn.bonus, params.tenantId, txn.code)
        }
        // Add the filtered final values to mainArr
        mainArr = [...mainArr, values]
      }
    }

    let awsTenantFolderName = params.tenantId || 0;
    if (data.adminType == 'SuperAdminUser' && Array.isArray(params.tenantId)) {
      if (params.tenantId.length === 1) awsTenantFolderName = params.tenantId[0];
      else awsTenantFolderName = 0;
    }

    await Promise.all([csvWriter.writeRecords(mainArr)])
    // upload file to s3
    const s3Config = config.getProperties().s3
    const fileContent = await fs.promises.readFile(filePath)
    const key = `tenants/${awsTenantFolderName}/csv/player/player_revenue_${uuid}.csv`

    const s3Params = {
      ACL: 'public-read',
      Bucket: s3Config.bucket,
      Key: key,
      Body: fileContent
    }
    const uploadedFile = await s3.upload(s3Params).promise()
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
    {
      where: { id: data.id }
    }
    )
    fs.unlink(filePath, (err) => {
      if (err) {
        throw err
      }
    })

    return true
  } catch (error) {
    console.log('=======error', error)
    throw error
  }
}
