import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import moment from 'moment';
import { QueryTypes } from 'sequelize';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { CHECK_SBO_DOMAIN } from '../../../utils/constants/constant.js';
import { BOT_ALLOWED_TENANTS, CASINO_PROVIDER_IDS_ARRAY, EXPORT_CSV_STATUS, GAME_ID_PROVIDER_IDS_ARRAY, SPORT_PROVIDER, SPORT_PROVIDER_IDS_ARRAY, ST8_PROVIDER_ID, ST8_SPORTS_SEAT_IDS_ARRAY, TABLE_ID_PROVIDERS_ARRAY } from '../../constants';
import { fetchAgentIds, formatAmount, getCurrencyById, getCurrencyByValue, getSportsMarkets, getTransactionTypeByValueOrId } from '../../helpers';
import getRolesDetails from '../getAdminRolesDetail';
import { getColumnPermissions } from '../getPermissionsForPlayerCsv.js';
import { getDateRange } from './getStartEndDates';

export default async (data) => {
  try {
    const payload = data.payload
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.IN_PROGRESS
    },
      {
        where: { id: data.id }
      }
    )

    let currentUser = { id: data.adminId, parentType: data.adminType }
    let isGameProviderAndTypeFilterApplied = false
    let userFilterApplied = false
    const checkSboRequest = payload.sbo_request
    let isSuperAdmin = false;
    if (payload.timeZoneName === 'UTC +00:00') {
      payload.timeZoneName = 'UTC'
    }

    const inputParams = {
      tenantIds: [],
      startDate: '',
      endDate: '',
      agentIds: [],
      subAgentIds: [],
      currency: payload.currency ? getCurrencyByValue(payload.currency)?.id : payload.currency,
      actionType: [],
      actionCategory: payload.actionCategory,
      gameProvider: payload.gameProvider,
      transactionId: payload.transactionId,
      roundId: payload.roundId,
      userId: payload.userId,
      sportProviders: SPORT_PROVIDER_IDS_ARRAY,
      casinoProviders: CASINO_PROVIDER_IDS_ARRAY,
      gameIdProviders: GAME_ID_PROVIDER_IDS_ARRAY,
      st8ProviderId: ST8_PROVIDER_ID,
      tableIdProviders: TABLE_ID_PROVIDERS_ARRAY
    }

    // ====================filters here ========================

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      })
      payload.tenantId = adminData.tenantId
    }

    // Tenant Filter Gathering
    const tenantId = Number(payload.tenantId)
    if (tenantId) {
      inputParams.tenantIds = [tenantId]
    } else if (data.adminType == 'SuperAdminUser') {
      const adminData = await db.SuperAdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['parentType', 'tenantIds']
      })
      if (adminData.parentType === 'Manager') {
        inputParams.tenantIds = adminData.tenantIds.map(id => parseInt(id));
      }
    }

    // Agent Filter Gathering
    payload.agentId = isNaN(payload.agentId) ? '' : Number(payload.agentId)

    let searchAgentId
    if (payload.agentId) {
      searchAgentId = payload.agentId
    } else if (data.adminType === 'AdminUser') {
      const roles = await getRolesDetails(currentUser.id)
      if (roles.includes('agent')) {
        searchAgentId = currentUser.id
      }
    }
    if (searchAgentId) {
      inputParams.agentIds = await fetchAgentIds(searchAgentId)
    }

    if (data.adminType == "AdminUser") {
      const currentUserRole = await getRolesDetails(data.adminId);
      if (currentUserRole.includes("agent")) {
        inputParams.subAgentIds = await fetchAgentIds(data.adminId);
      }
    }

    // Time Filter Gathering
    if (payload.timeType === 'custom') {
      payload.timePeriod = JSON.parse(payload.timePeriod)
    }
    const { start, end } = getDateRange(payload.timeType, payload.timeZoneName, payload.timePeriod.startDate, payload.timePeriod.endDate)
    inputParams.startDate = start.utc().format('YYYY-MM-DD HH:mm:ss').concat('.000000')
    inputParams.endDate = end.utc().format('YYYY-MM-DD HH:mm:ss').concat('.999999')

    // Action Type Filter Gathering
    const actionTypeArr = payload.actionType ? JSON.parse(payload.actionType) : []
    if (actionTypeArr.length) {
      inputParams.actionType = await getTransactionTypeByValueOrId(actionTypeArr)
    } else {
      inputParams.actionType = [0, 1, 2, 7, 8, 9, 10, 13, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 46, 47, 53, 54, 55, 56, 57, 58, 61, 62, 63, 64, 65, 66, 67]
    }

    // Game Type Filter Gathering
    if (['Jetfair', 'Powerplay', 'Bti Sportsbook', 'Sap Exchange', 'Saba Sportsbook', 'TurboStars'].includes(payload.gameProvider)) {
      payload.gameType = ''
    }

    const txsUserDataCteFilters = []
    const gameTxsCteFilters = []

    // Check SBO User Logic
    isSuperAdmin = currentUser.parentType === 'SuperAdminUser'
    let botUsersFilterType = 'all'
    let botWalletIds = []
    if (
      (tenantId && BOT_ALLOWED_TENANTS.includes(String(tenantId))) ||
      isSuperAdmin
    ) {
      // In case of super admin, do not check SBO request flag. Just check player category flag.
      if (
        isSuperAdmin ||
        (checkSboRequest === CHECK_SBO_DOMAIN[1] && checkSboRequest !== CHECK_SBO_DOMAIN[2])
      ) {
        if (payload.playerCategory === 'bot_players') {
          botUsersFilterType = 'bot'
        } else if (payload.playerCategory === 'real_players') {
          botUsersFilterType = 'real'
        }
      } else if (!checkSboRequest) {
        botUsersFilterType = 'real'
      }

      if (botUsersFilterType !== 'all') {
        // Find all bot users wallet Id
        botWalletIds = await sequelize.query(`
          SELECT
            w.id AS wallet_id
          FROM
            bot_users bu
            JOIN wallets w ON (w.owner_id = bu.user_id AND w.owner_type = 'User')
          ${tenantId ? 'WHERE bu.tenant_id = :tenantId' : ''}
        `, {
          type: QueryTypes.SELECT,
          replacements: { tenantId }
        })
        botWalletIds = botWalletIds.map(rec => rec.wallet_id)

        if (botWalletIds.length > 0) {
          if (botUsersFilterType === 'bot') {
            gameTxsCteFilters.push(`(source_wallet_id IN (${botWalletIds}) OR target_wallet_id IN (${botWalletIds}))`)
          } else if (botUsersFilterType === 'real') {
            gameTxsCteFilters.push(`COALESCE(source_wallet_id, 0) NOT IN (${botWalletIds}) AND COALESCE(target_wallet_id, 0) NOT IN (${botWalletIds})`)
          }
        }
      }
    }

    // User Id Filter
    if (payload.userId && !isNaN(payload.userId)) {
      let agentCond = ''
      if (inputParams.agentIds.length > 0) {
        agentCond = 'u.parent_id IN (:agentIds)'
      }
      if (inputParams.subAgentIds.length > 0) {
        agentCond = 'u.parent_id IN (:subAgentIds)' // Extra check in case of agent login to validate if the payload agent Id should be from his sub-agents
      }
      const botUserCondMap = {
        bot: 'bu.id IS NOT NULL',
        real: 'bu.id IS NULL',
        all: ''
      }
      const userData = await sequelize.query(`
        SELECT
          w.id AS wallet_id
        FROM
          wallets w
          JOIN users u ON (w.owner_id = u.id)
          LEFT JOIN bot_users bu ON (bu.user_id = w.owner_id)
        WHERE
          w.owner_id = :userId
          AND w.owner_type = 'User'
          ${agentCond ? 'AND ' + agentCond : ''}
          ${botUserCondMap[botUsersFilterType] ? 'AND ' + botUserCondMap[botUsersFilterType] : ''}
      `, {
        type: QueryTypes.SELECT,
        replacements: { userId: payload.userId, agentIds: inputParams.agentIds, subAgentIds: inputParams.subAgentIds }
      })
      if (!userData || userData.length <= 0) {
        throw new Error('Data not found!');
      }
      gameTxsCteFilters.push(`(source_wallet_id = '${userData[0].wallet_id}' OR target_wallet_id = '${userData[0].wallet_id}')`)
      userFilterApplied = true
    }

    if (inputParams.startDate && inputParams.endDate) {
      gameTxsCteFilters.push('created_at BETWEEN :startDate AND :endDate')
    }
    if (inputParams.actionType.length > 0) {
      gameTxsCteFilters.push('transaction_type IN (:actionType)')
    }
    if (payload.transactionId) {
      gameTxsCteFilters.push('transaction_id = :transactionId')
    }
    if (payload.roundId) {
      gameTxsCteFilters.push('round_id = :roundId')
    }
    if (inputParams.currency) {
      gameTxsCteFilters.push('(source_currency_id = :currency OR target_currency_id = :currency)')
    }

    if (!userFilterApplied && inputParams.agentIds.length > 0) {
      let agentFilter
      if (inputParams.subAgentIds.length > 0) {
        agentFilter = '(u.parent_id IN (:agentIds) AND u.parent_id IN (:subAgentIds))' // Extra check in case of agent login to validate if the payload agent Id should be from his sub-agents
      } else {
        agentFilter = 'u.parent_id IN (:agentIds)'
      }
      txsUserDataCteFilters.push(agentFilter)
    } else if (!userFilterApplied && inputParams.subAgentIds.length > 0) {
      const agentFilter = 'u.parent_id IN (:subAgentIds)'
      txsUserDataCteFilters.push(agentFilter)
    }

    if (inputParams.gameProvider) {
      // Query to get provider IDs
      let providerIds = await sequelize.query(`
        SELECT id FROM casino_providers WHERE name = :gameProvider
      `, { type: QueryTypes.SELECT, replacements: { gameProvider: inputParams.gameProvider } })

      providerIds = providerIds.map((row) => row.id)

      let providerCondition = ''
      if (providerIds.length > 0) {
        providerCondition = `provider_id IN (${providerIds})`
      }

      // Query to get transaction seat IDs
      let transactionSeatIds = await sequelize.query(`
        SELECT seat_ids FROM transactions_providers_list WHERE title = :gameProvider
      `, { type: QueryTypes.SELECT, replacements: { gameProvider: inputParams.gameProvider } })

      transactionSeatIds = transactionSeatIds[0]?.seat_ids || []

      let gameCondition = ''
      if (transactionSeatIds.length > 0) {
        const quotedSeatIds = transactionSeatIds.map(id => id.replace(/'/g, "''")).map(id => `'${id}'`).join(',')
        gameCondition = `seat_id IN (${quotedSeatIds})`
      }

      let finalProviderCondition = ''
      if (providerCondition && gameCondition) {
        finalProviderCondition = `(${providerCondition} OR ${gameCondition})`
      } else if (providerCondition) {
        finalProviderCondition = `${providerCondition}`
      } else if (gameCondition) {
        finalProviderCondition = `${gameCondition}`
      }
      if (finalProviderCondition) {
        isGameProviderAndTypeFilterApplied = true
        gameTxsCteFilters.push(finalProviderCondition)
      } else {
        throw new Error('Data not found!');
      }
    }
    if (payload.gameType) {
      // Fetch Provider IDs by Provider Name
      let providerIds = await sequelize.query(`
        SELECT id FROM casino_providers WHERE name = :gameProvider

        UNION ALL

        SELECT id FROM casino_providers
        WHERE
          name IN ('st8', 'Whitecliff')
          AND id IN (
            SELECT provider_id::bigint FROM transactions_providers_list WHERE title = :gameProvider
          )
      `, {
        type: QueryTypes.SELECT,
        replacements: { gameProvider: inputParams.gameProvider }
      })
      providerIds = providerIds.map(obj => obj.id)

      // Fetch all UUIDs by Provider ID and Game Name
      const uuidsDbData = await sequelize.query(`
        SELECT DISTINCT uuid FROM casino_items WHERE name = :gameType AND provider IN (:providerIds)
      `, {
        type: QueryTypes.SELECT,
        replacements: { gameType: payload.gameType, providerIds: providerIds }
      })
      const uuids = uuidsDbData.map(obj => obj.uuid.replace(/'/g, "''")).map(id => `'${id}'`)

      if (uuids.length) {
        // Case for Red Tiger and NetEnt Games
        if (providerIds.length > 1) {
          const uuidsFromDb = uuidsDbData.map(obj => obj.uuid)
          const tableIds = uuidsFromDb.filter(id => !isNaN(id))
          const seatIds = uuidsFromDb.filter(id => isNaN(id)).map(id => `'${id}'`)
          if (tableIds.length && seatIds.length) {
            gameTxsCteFilters.push(`(table_id IN (${tableIds}) OR seat_id IN (${seatIds}))`)
          } else if (tableIds.length) {
            gameTxsCteFilters.push(`table_id IN (${tableIds})`)
          } else {
            gameTxsCteFilters.push(`seat_id IN (${seatIds})`)
          }
        } else if (GAME_ID_PROVIDER_IDS_ARRAY.includes(providerIds[0])) {
          gameTxsCteFilters.push(`game_id IN (${uuids})`)
        } else if (TABLE_ID_PROVIDERS_ARRAY.includes(providerIds[0])) {
          gameTxsCteFilters.push(`table_id IN (${uuids})`)
        } else {
          gameTxsCteFilters.push(`seat_id IN (${uuids})`)
        }
        isGameProviderAndTypeFilterApplied = true
      }
    }

    if (inputParams.actionCategory && !isGameProviderAndTypeFilterApplied) {
      if (inputParams.actionCategory === 'sports') {
        gameTxsCteFilters.push('provider_id IN (:sportProviders)')
      } else if (inputParams.actionCategory === 'casino') {
        gameTxsCteFilters.push('provider_id IN (:casinoProviders)')
      }
    }
    if (inputParams.tenantIds.length > 0) {
      if (!userFilterApplied) {
        gameTxsCteFilters.push('tenant_id IN (:tenantIds)')
      }
    }

    const txsUserDataCteWhereCondition = txsUserDataCteFilters.length ? `WHERE ${txsUserDataCteFilters.join(' AND ')}` : ''
    const gameTxsCteWhereCondition = gameTxsCteFilters.length ? `WHERE ${gameTxsCteFilters.join(' AND ')}` : ''

    const sort = payload.sortBy || 'created_at'
    const sortOrder = payload.order || 'DESC'
    let sortCondition = ''
    let sortConditionWithAlias = ''
    if (sort && sortOrder) {
      sortCondition = 'ORDER BY ' + sort + ' ' + sortOrder
      sortConditionWithAlias = 'ORDER BY t.' + sort + ' ' + sortOrder
    }

    // CSV column permissions
    const columnKeyNameMap = {
      player_id: "Player ID",
      player_name: "Player Name",
      agent_name: "Agent Name",
      game_provider: "Game Provider",
      game_name: "Game Name",
      table_id: "Table ID",
      round_id: "Round ID",
      creation_date: "Activity Timestamp",
      action_type: "Action Type",
      currency: "Currency",
      balance_before: "Initial Balance",
      amount: "Played Amount",
      after_balance: "Ending Balance",
      transaction_id: "Transaction ID",
      internal_tracking_id: "Internal Tracking ID",
      debit_transaction_id: "Debit Transaction ID",
    };

    const columnKeyPermissions = {
      player_id: ["report_attributes", "player_id"],
      player_name: ["players_key", "user_name"],
      agent_name: ["players_key", "agent_details"],
      game_provider: ["report_attributes", "game_provider"],
      game_name: ["report_attributes", "game_name"],
      table_id: ["report_attributes", "table_id"],
      round_id: ["transaction_attributes", "round_id"],
      creation_date: ["report_attributes", "activity_timestamp"],
      action_type: ["transaction_attributes", "action_type"],
      currency: ["report_attributes", "currency"],
      balance_before: ["players_key", "before_balance"],
      amount: ["players_key", "total_balance"],
      after_balance: ["players_key", "after_balance"],
      transaction_id: ["transaction_attributes", "transaction_id"],
      internal_tracking_id: ["report_attributes", "internal_tracking_id"],
      debit_transaction_id: ["transaction_attributes", "debit_transaction_id"],
    };

    const allowedColumns = await getColumnPermissions(columnKeyPermissions, data.adminId, inputParams.tenantIds[0], data.adminType);
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    //new code
    const chunkSize = 5000
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3

    let awsTenantFolderName = 0;
    if (inputParams.tenantIds.length === 1) {
      awsTenantFolderName = inputParams.tenantIds[0];
    }

    const uuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${awsTenantFolderName}/csv/game_transaction/transaction_${uuid}.csv`

    const csvStringifier = createObjectCsvStringifier({
      header: allowedColumns.map(columnKey => ({ id: columnKey, title: columnKeyNameMap[columnKey] }))
    })

    const allowedColumnsObj = {};
    allowedColumns.forEach(c => allowedColumnsObj[c] = true);

    // Get count
    const gameTransactionReportCntQuery = prepareGameTransactionReportCountQuery({
      gameTxsCteWhereCondition,
      txsUserDataCteWhereCondition
    })
    const totalRecords = await sequelize.query(gameTransactionReportCntQuery, { type: sequelize.QueryTypes.SELECT, useMaster: false, replacements: inputParams });
    const totalRecordsCount = totalRecords[0]?.total_count || 0
    const totalChunks = Math.ceil(totalRecordsCount / chunkSize)
    // Loop through each chunk
    for (let i = 0; i < totalChunks; i++) {
      const offset = i * chunkSize
      const paginationCondition = 'LIMIT ' + chunkSize + ' OFFSET ' + offset
      // Query preparation and fetch results
      const gameTransactionReportQuery = prepareGameTransactionReportQuery({
        gameTxsCteWhereCondition,
        txsUserDataCteWhereCondition,
        sortCondition,
        paginationCondition,
        sortConditionWithAlias,
        userFilterApplied
      })
      const reportData = await sequelize.query(gameTransactionReportQuery, { type: sequelize.QueryTypes.SELECT, useMaster: false, replacements: inputParams });

      await fetchGameNames(reportData);
      await fetchSportMarketNames(reportData);
      await ProcessReportData(reportData, payload);

      let mainArr = []
      if (reportData.length > 0) {
        for (const txn of reportData) {
          let object = {
            ...(allowedColumnsObj.player_id && { player_id: txn.user_id || '' }),
            ...(allowedColumnsObj.player_name && { player_name: txn.user_name || '' }),
            ...(allowedColumnsObj.agent_name && { agent_name: txn.agent_name || '' }),
            ...(allowedColumnsObj.game_provider && { game_provider: txn.game_provider || '' }),
            ...(allowedColumnsObj.game_name && { game_name: txn.game_type || txn.game_name || '' }),
            ...(allowedColumnsObj.table_id && { table_id: txn.table_id || '' }),
            ...(allowedColumnsObj.round_id && { round_id: txn.round_id || '' }),
            ...(allowedColumnsObj.creation_date && { creation_date: txn.created_at }),
            ...(allowedColumnsObj.action_type && { action_type: txn.transaction_type || '' }),
            ...(allowedColumnsObj.currency && { currency: txn.currency || '' }),
            ...(allowedColumnsObj.balance_before && { balance_before: formatAmount(txn.initial_balance,tenantId,txn.currency) || '' }),
            ...(allowedColumnsObj.amount && { amount: formatAmount(txn.amount,tenantId,txn.currency) || '' }),
            ...(allowedColumnsObj.after_balance && { after_balance: formatAmount(txn.ending_balance,tenantId,txn.currency) || '' }),
            ...(allowedColumnsObj.transaction_id && { transaction_id: txn.transaction_id || '' }),
            ...(allowedColumnsObj.internal_tracking_id && { internal_tracking_id: txn.internal_tracking_id || '' }),
            ...(allowedColumnsObj.debit_transaction_id && { debit_transaction_id: txn.debit_transaction_id || '' }),

          };
          mainArr = [...mainArr, object]

        }
      }

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(mainArr)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')

      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }
      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      await delay(1000)
    }
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    return true
  } catch (error) {
    console.log('=======errorr', error)
    throw error
  }
}


function prepareGameTransactionReportQuery ({
  gameTxsCteWhereCondition,
  txsUserDataCteWhereCondition,
  sortCondition,
  paginationCondition,
  sortConditionWithAlias,
  userFilterApplied
}) {
  let txsUserDataCte = ''
  let finalCte = 'game_txs'

  let walletsJoin = "LEFT JOIN wallets w ON (w.id = t.user_wallet_id AND owner_type = 'User')"
  let usersJoin = 'LEFT JOIN users u ON (w.owner_id = u.id)'
  let userIdAttr = 'u.id AS user_id,'
  let userNameAttr = 'u.user_name AS user_name,'

  // Add the UNION ALL for the User Filter so that Postgres Query Planner uses the Source and Target Wallet ID related Indexes for Fast Result Across the Multiple Partitions.
  let userFilterIndexHelper = '';
  if (userFilterApplied) {
    userFilterIndexHelper = INDEXING_HELPER_USER_FILTER_QUERY + '\n' + 'UNION ALL' + '\n';
  }

  if (txsUserDataCteWhereCondition) {
    finalCte = 'txs_user_data'

    walletsJoin = ''
    usersJoin = ''
    userIdAttr = 't.user_id,'
    userNameAttr = 't.user_name,'

    txsUserDataCte = `
      , txs_user_data AS (
        SELECT
          t.*,
          u.id AS user_id,
          u.user_name AS user_name,
          u.parent_id AS parent_id,
          w.owner_id
        FROM
          game_txs t
          LEFT JOIN wallets w ON (w.id = t.user_wallet_id AND owner_type = 'User')
          LEFT JOIN users u ON (w.owner_id = u.id)
        ${txsUserDataCteWhereCondition}
        ${sortConditionWithAlias}
        ${paginationCondition}
      )
    `
    paginationCondition = ''
    sortCondition = ''
  }

  return `
  SET enable_seqscan = OFF;

  WITH game_txs AS (
    ${userFilterIndexHelper}
    SELECT
      CASE
        WHEN transaction_type IN (0,7,8,13,20,21,23,24,27,28,32,34,36,46,54,55,56,61,62,64,66) THEN source_wallet_id
        WHEN transaction_type IN (1,9,22,25,26,29,30,31,33,35,53,57,58,63,65,67) THEN target_wallet_id
        ELSE COALESCE(target_wallet_id, source_wallet_id) -- For transaction type (2,10,47)
      END AS user_wallet_id,
      t.seat_id,
      t.game_id,
      t.table_id,
      t.provider_id,
      t.round_id,
      t.created_at,
      t.transaction_type,
      t.amount,
      t.transaction_id,
      t.tenant_id,
      t.source_before_balance,
      t.target_before_balance,
      t.source_after_balance,
      t.target_after_balance,
      t.source_currency_id,
      t.target_currency_id,
      t.source_wallet_id,
      t.target_wallet_id,
      t.error_code,
      t.id AS internal_tracking_id,
      t.payment_method AS transfer_method,
      t.debit_transaction_id
      FROM transactions t
      ${gameTxsCteWhereCondition}
      ${sortCondition}
      ${paginationCondition}
  )
  ${txsUserDataCte}
    SELECT
      ${userIdAttr}
      ${userNameAttr}
      adu.agent_name,
      t.seat_id,
      CASE WHEN t.seat_id = 'bti_sportsbook' THEN 'Bti Sportsbook'
        WHEN t.seat_id = 'sap_lobby' THEN 'Sap Exchange'
        WHEN t.seat_id = 'sbs_sportsbook' THEN 'Saba Sportsbook'
        WHEN cp.name IN ('st8','Whitecliff') THEN tp.title
        ELSE cp.name
      END AS game_provider,
      CASE
        WHEN t.provider_id IN (:gameIdProviders) THEN game_id
        WHEN t.provider_id IN (:tableIdProviders) THEN table_id::text
        ELSE t.seat_id
      END AS table_id,
      t.provider_id,
      t.round_id,
      t.created_at::text AS created_at,
      t.transaction_type,
      t.amount::numeric(20,2) AS amount,
      CASE
        WHEN t.transaction_type IN (0,7,8,13,20,21,23,24,27,28,32,34,36,46,54,55,56,61,62,64,66) THEN source_before_balance::numeric(20,2)
        WHEN t.transaction_type IN (1,9,22,25,26,29,30,31,33,35,53,57,58,63,65,67) THEN target_before_balance::numeric(20,2)
        WHEN source_wallet_id IS NULL THEN target_before_balance::numeric(20,2) -- For transaction type (2,10,47)
        ELSE source_before_balance::numeric(20,2) -- For transaction type (2,10,47)
      END AS initial_balance,
      CASE
        WHEN t.transaction_type IN (0,7,8,13,20,21,23,24,27,28,32,34,36,46,54,55,56,61,62,64,66) THEN source_after_balance::numeric(20,2)
        WHEN t.transaction_type IN (1,9,22,25,26,29,30,31,33,35,53,57,58,63,65,67) THEN target_after_balance::numeric(20,2)
        WHEN source_wallet_id IS NULL THEN target_after_balance::numeric(20,2) -- For transaction type (2,10,47)
        ELSE source_after_balance::numeric(20,2) -- For transaction type (2,10,47)
      END AS ending_balance,
      CASE
        WHEN transaction_type IN (0,7,8,13,20,21,23,24,27,28,32,34,36,46,54,55,56,61,62,64,66) THEN source_currency_id
        WHEN transaction_type IN (1,9,22,25,26,29,30,31,33,35,53,57,58,63,65,67) THEN target_currency_id
        ELSE COALESCE(source_currency_id, target_currency_id) -- For transaction type (2,10,47)
      END AS currency_id,
      t.transaction_id,
      t.tenant_id,
      t.source_currency_id,
      t.target_currency_id,
      t.error_code,
      t.internal_tracking_id,
      CASE WHEN cp.name IN ('st8', 'Whitecliff') THEN cp.name
        ELSE NULL
      END AS aggregator_name,
      t.transfer_method,
      t.debit_transaction_id,
      bu.id AS bot_user_id
    FROM ${finalCte} t
      ${walletsJoin}
      ${usersJoin}
      LEFT JOIN bot_users bu ON (bu.user_id = owner_id)
      LEFT JOIN admin_users adu ON (adu.id = ${usersJoin ? 'u.parent_id' : 't.parent_id'})
      -- Get Provider data
      LEFT JOIN casino_providers cp ON (cp.id = t.provider_id)
      LEFT JOIN transactions_providers_list tp ON (cp.name IN ('st8', 'Whitecliff') AND t.provider_id= cp.id AND tp.seat_ids ? t.seat_id)
    ${sortConditionWithAlias};

    SET enable_seqscan = ON;
  `
}


function prepareGameTransactionReportCountQuery ({
  gameTxsCteWhereCondition,
  txsUserDataCteWhereCondition
}) {
  let finalQ = ''
  // When agent filter is not applied
  if (!txsUserDataCteWhereCondition) {
    finalQ = `
      SET enable_seqscan = OFF;

      SELECT COUNT(*) AS total_count
      FROM transactions
      ${gameTxsCteWhereCondition};

      SET enable_seqscan = ON;
    `
  } else {
    finalQ = `
      SET enable_seqscan = OFF;

      WITH game_txs AS (
        SELECT
          CASE
            WHEN transaction_type IN (0,7,8,13,20,21,23,24,27,28,32,34,36,46,54,55,56,61,62,64,66) THEN source_wallet_id
            WHEN transaction_type IN (1,9,22,25,26,29,30,31,33,35,53,57,58,63,65,67) THEN target_wallet_id
            ELSE COALESCE(target_wallet_id, source_wallet_id) -- For transaction type (2,10,47)
          END AS user_wallet_id
        FROM transactions
        ${gameTxsCteWhereCondition}
      )
      SELECT
        COUNT(*) AS total_count
      FROM game_txs t
        LEFT JOIN wallets w ON (w.id = t.user_wallet_id AND owner_type = 'User')
        LEFT JOIN users u ON (w.owner_id = u.id)
      ${txsUserDataCteWhereCondition};

      SET enable_seqscan = ON;`
  }
  return finalQ
}

async function fetchGameNames (reportData) {
  let casinoItemUUIDs = reportData
    .filter(reportRec => (
      reportRec.provider_id &&
      reportRec.table_id &&
      !SPORT_PROVIDER_IDS_ARRAY.includes(String(reportRec.provider_id)) &&
      !ST8_SPORTS_SEAT_IDS_ARRAY.includes(reportRec.table_id)
    ))
    .map(reportRec => reportRec.table_id);
  casinoItemUUIDs = [...new Set(casinoItemUUIDs)];

  let uuidProviderNameMap = {};
  if (casinoItemUUIDs.length) {
    const uuidProviderNameMapData = await sequelize.query(`
      SELECT json_object_agg(uuid, provider_name_map) AS uuid_provider_name_map FROM (
        SELECT uuid, json_object_agg(provider, name) AS provider_name_map FROM (
          SELECT DISTINCT ON (uuid, provider) uuid, provider, name FROM casino_items WHERE uuid IN (:casinoItemUUIDs)
        ) S1 GROUP BY uuid
      ) S2;
    `, {
      type: QueryTypes.SELECT,
      replacements: { casinoItemUUIDs }
    })
    uuidProviderNameMap = uuidProviderNameMapData[0]?.uuid_provider_name_map
  }

  if (uuidProviderNameMap) {
    reportData.forEach(reportRec => {
      if (
        reportRec.provider_id &&
        reportRec.table_id &&
        !SPORT_PROVIDER_IDS_ARRAY.includes(String(reportRec.provider_id)) &&
        !ST8_SPORTS_SEAT_IDS_ARRAY.includes(reportRec.table_id)
      ) {
        reportRec.game_name = uuidProviderNameMap[reportRec.table_id]?.[reportRec.provider_id] || '-';
      } else {
        reportRec.game_name = reportRec.seat_id
      }
    })
  }
}

async function fetchSportMarketNames(reportData) {
  let debitTxIds = [];
  reportData.forEach(report => {
    if (report.game_provider === SPORT_PROVIDER.JETFAIR || report.game_provider === SPORT_PROVIDER.POWERPLAY || report.game_provider === SPORT_PROVIDER.TURBOSTARS) {
      debitTxIds.push(report.debit_transaction_id);
    }
  });

  if (debitTxIds.length <= 0) return;

  let marketNames = await getSportsMarkets(debitTxIds);

  if (!marketNames) return;

  reportData.forEach(report => {
    if (report.game_provider === SPORT_PROVIDER.JETFAIR || report.game_provider === SPORT_PROVIDER.POWERPLAY || report.game_provider === SPORT_PROVIDER.TURBOSTARS) {
      report.game_type = (marketNames[report.debit_transaction_id] ? String(marketNames[report.debit_transaction_id]) : report.seat_id);
    }
  });
}


async function ProcessReportData (reportData, payload) {
  // Convert UTC date to provided timezone
  for (const report of reportData) {
    if (payload.timeZoneName) {
      report.created_at = moment
        .utc(report.created_at)
        .tz(payload.timeZoneName)
        .format("YYYY-MM-DD HH:mm:ss");
    }
    if (report.transaction_type != null) {
      report.transaction_type = await getTransactionTypeByValueOrId(report.transaction_type)
    }
    if (report.source_currency_id) {
      const id = parseInt(report.source_currency_id || 0)
      report.source_currency = getCurrencyById(id)?.value || null
    }
    if (report.target_currency_id) {
      const id = parseInt(report.target_currency_id || 0)
      report.target_currency = getCurrencyById(id)?.value || null
    }
    if (report.currency_id) {
      const id = parseInt(report.currency_id || 0)
      report.currency = getCurrencyById(id)?.value || null
    }
  }
}

const INDEXING_HELPER_USER_FILTER_QUERY =
`
SELECT
  source_wallet_id AS user_wallet_id,
  seat_id,
  game_id,
  table_id,
  provider_id,
  round_id,
  created_at,
  transaction_type,
  amount,
  transaction_id,
  tenant_id,
  source_before_balance,
  target_before_balance,
  source_after_balance,
  target_after_balance,
  source_currency_id,
  target_currency_id,
  source_wallet_id,
  target_wallet_id,
  error_code,
  id AS internal_tracking_id,
  payment_method AS transfer_method,
  debit_transaction_id
FROM
  transactions
WHERE false
`;
