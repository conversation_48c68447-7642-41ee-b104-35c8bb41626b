import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import moment from 'moment';
import { QueryTypes } from 'sequelize';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { BET_PAYOUT_SETTLEMENT_STATUS_CODES, EXPORT_CSV_STATUS, SPORTS_PROVIDER_PROD, SPORTS_PROVIDERS_STAGE } from '../../constants';

const CHUNK_SIZE = 1000;

export default async (data) => {

  const payload = data.payload;

  await db.ExportCsvCenter.update(
    { status: EXPORT_CSV_STATUS.IN_PROGRESS },
    { where: { id: data.id } }
  );

  let params = {
    tenantId: 0,
    adminId: data.adminId,
    startDate: moment.utc(payload.startDate).startOf('day').toISOString(),
    endDate: moment.utc(payload.endDate).endOf('day').toISOString(),
    transactionId: payload.transaction_id,
    playerId: payload.playerId,
    marketId: payload.marketId,
    payoutStatus: payload.payoutStatus,
    betStatus: payload.betStatus,
  };

  if (data.adminType === 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: { id: data.adminId },
      attributes: ['tenantId'],
    });
    params.tenantId = adminData.tenantId;
  }

  try {
    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'userName', title: 'Username' },
        { id: 'betId', title: 'Bet Id' },
        { id: 'marketId', title: 'Market Id' },
        { id: 'betType', title: 'Bet Type' },
        { id: 'status', title: 'Status' },
        { id: 'betSlipId', title: 'Bet Slip Id' },
        { id: 'betAmount', title: 'Bet Amount' },
        { id: 'winAmount', title: 'Win Amount' },
        { id: 'payoutStatus', title: 'Payout Status' },
        { id: 'betList', title: 'Bet List' },
        { id: 'createdAt', title: 'Created At' }
      ]
    })

    const providerName = 'TurboStars'
    const providerId = config.get('env') === 'production' ? SPORTS_PROVIDER_PROD[providerName?.toUpperCase()] : SPORTS_PROVIDERS_STAGE[providerName?.toUpperCase()]

    let offset = 0;
    let totalRecords = 0;
    let hasMoreRecords = true;

    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3
    const csvUuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${params.tenantId}/csv/cashier_bet_reports/bet_report_${csvUuid}.csv`
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    while (hasMoreRecords) {

      let sql = `with bets_data as (
          select
            bt.transaction_id as bet_id,
            min(bt.betslip_id) as betslip_id,
            sum(coalesce(bt.amount, 0) + coalesce(bt.non_cash_amount, 0) + coalesce(bt.sports_freebet_amount, 0))
              filter (where bt.transaction_code = 'PlaceMatchedBet') as bet_amount,
            min(bt.created_at) filter (where bt.transaction_code = 'PlaceMatchedBet') as bet_created_at,
            min(bt.id) filter (where bt.transaction_code = 'PlaceMatchedBet') as bets_transaction_id,
            max(bt.id) filter (where bt.transaction_code = 'SettledMarket') as latest_win_id,
            min(bt.id) filter (where bt.transaction_code = 'CancelMatchedBet') as cancel_bet_id,
            min(bt.market_id) filter (where bt.transaction_code = 'PlaceMatchedBet') as market_id,
            min(bt.user_id) as user_id
          from bets_transactions bt
          join users u on bt.user_id = u.id
          where
            u.parent_id = :adminId
            and bt.tenant_id = :tenantId
            and bt.provider_id = :providerId
            ${params.playerId ? 'and bt.user_id = :playerId' : ''}
            ${params.transactionId ? 'and bt.transaction_id = :transactionId' : ''}
            ${params.startDate && params.endDate ? 'and bt.created_at between :startDate and :endDate' : ''}
            ${params.marketId ? 'and bt.market_id = :marketId' : ''}
          group by bt.transaction_id
        ),
        bet_win_data as (
          select
            bd.*,
            u.user_name,
            bps.status as bt_payout_status,
            (coalesce(bt.amount, 0) + coalesce(bt.non_cash_amount, 0) + coalesce(bt.sports_freebet_amount, 0)) as latest_win_amt,

            -- window aggregates
            sum(coalesce(bd.bet_amount, 0)) over () as total_bet_amount,
            sum(coalesce(bt.amount, 0) + coalesce(bt.non_cash_amount, 0) + coalesce(bt.sports_freebet_amount, 0)) over () as total_win_amount,
            sum(coalesce(bd.bet_amount, 0) - (coalesce(bt.amount, 0) + coalesce(bt.non_cash_amount, 0) + coalesce(bt.sports_freebet_amount, 0))) over () as ggr

          from bets_data bd
            left join bets_transactions bt on bd.latest_win_id = bt.id
            left join users u on bd.user_id = u.id
            left join bet_payout_status bps on bd.bets_transaction_id = bps.bet_id
          where
            bd.bets_transaction_id is not null
            ${params.payoutStatus ? params.payoutStatus != '3' ? 'and bps.status = :payoutStatus' : 'and bps.bet_id is null' : ''}
          order by bd.bet_created_at desc
        ),
        bets_bets_data as (
          select
            bet_id,
            json_agg(json_build_object(
              'price', price,
              'market', market,
              'match', match,
              'market_id', market_id
            )) as bet_list
          from bets_bets
          where
            bet_id in (select bet_id from bet_win_data)
          group by bet_id
        )
        select
          bwd.*,
          case
            when bwd.cancel_bet_id is not null then 'Cancelled'
            when bwd.latest_win_id is null and bwd.cancel_bet_id is null then 'Pending'
            when bwd.latest_win_amt >= bwd.bet_amount then 'Win'
            when bwd.latest_win_amt < bwd.bet_amount then 'Loss'
          end as status,
          bbd.bet_list
        from
          bet_win_data bwd
          join bets_bets_data bbd on (bwd.bet_id = bbd.bet_id)
        where
          1 = 1
          ${params.betStatus === 'Win' ? 'and (bwd.latest_win_amt >= bwd.bet_amount and bwd.latest_win_id is not null and bwd.cancel_bet_id is null)' : ''}
          ${params.betStatus === 'Loss' ? 'and (bwd.latest_win_amt < bwd.bet_amount and bwd.latest_win_id is not null and bwd.cancel_bet_id is null)' : ''}
          ${params.betStatus === 'Pending' ? 'and (bwd.latest_win_id is null and bwd.cancel_bet_id is null)' : ''}
          ${params.betStatus === 'Cancelled' ? 'and bwd.cancel_bet_id is not null' : ''}
          limit :limit offset :offset`

      const betWinTransactions = await sequelize.query(sql, {
        type: QueryTypes.SELECT,
        replacements: {
          adminId: params.adminId || null,
          providerId: providerId || null,
          playerId: params.playerId || null,
          tenantId: params.tenantId || null,
          startDate: params.startDate || null,
          endDate: params.endDate || null,
          transactionId: params.transactionId || null,
          marketId: params.marketId || null,
          payoutStatus: +params.payoutStatus || null,
          betStatus: +params.betStatus || null,
          limit: CHUNK_SIZE,
          offset: offset,
        }
      });

      if (betWinTransactions.length < CHUNK_SIZE) {
        hasMoreRecords = false;
      }

      const resultData = betWinTransactions.reduce((acc, curr) => {
        const payoutStatus = curr.bt_payout_status ? curr.bt_payout_status : 3
        const newObj = {}
        newObj.betId = curr.bet_id
        newObj.userName = curr.user_name
        newObj.marketId = curr.market_id
        newObj.betType = curr.bet_list.length > 1 ? 'COMBO' : 'SINGLE'
        newObj.status = curr.status
        newObj.betSlipId = curr.betslip_id
        newObj.createdAt = moment(curr.bet_created_at)
        newObj.betAmount = curr.bet_amount
        newObj.winAmount = curr.latest_win_amt
        newObj.betList = curr.bet_list ? JSON.stringify(curr.bet_list) : ""
        newObj.payoutStatus = BET_PAYOUT_SETTLEMENT_STATUS_CODES[payoutStatus]
        acc.push(newObj)
        return acc
      }, [])

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(resultData)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name != 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }

      // Append data or create new file
      const finalCsvContent = existingCsvContent ? existingCsvContent + csvData : csvStringifier.getHeaderString() + csvData;

      // // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      totalRecords += csvData.length
      offset += CHUNK_SIZE
      await delay(500)
    }

    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    return {
      totalRecords,
      csvUrl: key,
    };
  } catch (error) {
    console.log("Error in exporting Cashier Bet Report", error);
    throw error;
  }
};
