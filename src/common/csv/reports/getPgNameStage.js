export default (id) => {
  let str = '';
  switch (id) {
    case 41:
      str = 'sambhavPay';
      break;
    case 40:
      str = 'RPAYOUT';
      break;
    case 39:
      str = 'Paywings Withdraw Provider';
      break;
    case 38:
      str = 'paywings';
      break;
    case 12:
      str = 'saspay';
      break;
    case 11:
      str = 'nexa';
      break;
    case 10:
      str = 'wizpay';
      break;

    case 9:
      str = 'Sky Withdraw Provider';
      break;

    case 8:
      str = 'sky';
      break;

    case 7:
      str = 'runpay2';
      break;

    case 6:
      str = 'Indigrit Withdrawal Provider';
      break;

    case 5:
      str = 'indigrit';
      break;

    case 4:
      str = 'Payout Withdrawal Provider';
      break;

    case 3:
      str = 'payIn';
      break;

    case 2:
      str = 'M paisa';
      break;

    case 1:
      str = 'Stripe Payment';
      break;

    default:
      str = 'Na';
      break;
  }
  return str;
}
