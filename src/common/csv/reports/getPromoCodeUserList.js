import moment from 'moment';
import { QueryTypes } from 'sequelize';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3Config';
import { EXPORT_CSV_STATUS } from '../../constants';
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const fs = require('fs');



export default async (data) => {
  const payload = data.payload;
  await db.ExportCsvCenter.update(
    {
      status: EXPORT_CSV_STATUS.IN_PROGRESS,
    },
    {
      where: { id: data.id },
    }
  );

  // ==================== params gathering starts here ====================
  let params = {
    tenantId: 0,
    adminId: data.adminId,
    promoCodeId: payload.id,
  };

  if (data.adminType == 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: {
        id: data.adminId,
      },
      attributes: ['tenantId'],
    });
    params.tenantId = adminData.tenantId;
  }
  // ==================== params gathering ends here ====================

  try {

    let limit = 1000;
    const page = 0;
    let offset = page * limit;
    const sql = `SELECT
    upcb.id,
    upcb.status,
    upcb.bonus_amount,
    upcb.claimed_at,
    u.user_name,
    tpc.code,
    tpc.valid_from,
    tpc.valid_till
    FROM
    user_promo_code_bonus AS upcb
    INNER JOIN tenant_promo_codes AS tpc ON upcb.promo_code_id = tpc.id
    INNER JOIN users AS u ON upcb.user_id = u.id
     WHERE
    upcb.promo_code_id = ${params.promoCodeId}
    and tpc.tenant_id = ${params.tenantId}  ORDER BY upcb.id desc LIMIT ? OFFSET ?`;

    const users = await sequelize.query(sql, {
      type: QueryTypes.SELECT,
      replacements: [limit, offset],
    });

    const countSql = `SELECT
    count(upcb.id) as users_count
  FROM
    user_promo_code_bonus AS upcb
    INNER JOIN tenant_promo_codes AS tpc ON upcb.promo_code_id = tpc.id
  WHERE
    upcb.promo_code_id = ${params.promoCodeId}
    and tpc.tenant_id = ${params.tenantId}
  `
    const countTotalRecord = await sequelize.query(countSql, {
      type: QueryTypes.SELECT
    });

    const getTotal = parseInt(countTotalRecord[0].users_count);

    const uuid = uuidv4().replace(/-/g, '')
    const filePath = `/tmp/transaction_${uuid}.csv`
    // ------------------- column process --------------------------------

    const csvWriter = createCsvWriter({
      path: filePath,
      header: [
        { id: 'id', title: 'ID' },
        { id: 'user_name', title: 'Username' },
        { id: 'status', title: 'Status' },
        { id: 'bonus_amount', title: 'Bonus Amount' },
        { id: 'used_at', title: 'Used At' },
        { id: 'promo_code', title: 'Promo Code' },
        { id: 'valid_from', title: 'Valid From' },
        { id: 'valid_till', title: 'Valid Till' },

      ]
    });

    // ------------------- column process --------------------------------
    const csvData = users
    if (payload.time_zone_name) {
      const csvDataList = await Promise.all(
        csvData.map(async object => {
          // Formatting createdAt date with optional user timezone
          const createdDate = payload.time_zone_name ? moment.tz(object.claimed_at, payload.time_zone_name) : moment(object.claimed_at)
          object.claimed_at = createdDate.format('DD-MM-YYYY HH:mm:ss')
          const validFrom = payload.time_zone_name ? moment.tz(object.valid_from, payload.time_zone_name) : moment(object.valid_from)
          object.valid_from = validFrom.format('DD-MM-YYYY HH:mm:ss')
          const validTill = payload.time_zone_name ? moment.tz(object.valid_till, payload.time_zone_name) : moment(object.valid_till)
          object.valid_till = validTill.format('DD-MM-YYYY HH:mm:ss')
          return object
        })
      )
    }
    let mainArr = []
    if (csvData.length > 0) {
      for (const txn of csvData) {
        const values = {
          'id': txn?.id,
          'user_name': (txn?.user_name || ''),
          'status': (txn?.status || ''),
          'bonus_amount': (txn?.bonus_amount || ''),
          'used_at': (txn?.claimed_at || ''),
          'promo_code': (txn?.code || ''),
          'valid_from': (txn?.valid_from || ''),
          'valid_till': (txn?.valid_till || ''),
        };

        mainArr = [...mainArr, values]
      }
    }
    await Promise.all([csvWriter.writeRecords(mainArr)])

    let itval = parseFloat(getTotal) / limit
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    for (let i = 1; i < itval; i++) {
      await delay(1000)
      offset = i * limit
      const users = await sequelize.query(sql, {
        type: QueryTypes.SELECT,
        replacements: [limit, offset],
      });


      let mainArr = []
      const csvData = users
      if (payload.time_zone_name) {
        const csvDataList = await Promise.all(
          csvData.map(async object => {
            // Formatting createdAt date with optional user timezone
            const createdDate = payload.time_zone_name ? moment.tz(object.claimed_at, payload.time_zone_name) : moment(object.claimed_at)
            object.claimed_at = createdDate.format('DD-MM-YYYY HH:mm:ss')
            const validFrom = payload.time_zone_name ? moment.tz(object.valid_from, payload.time_zone_name) : moment(object.valid_from)
            object.valid_from = validFrom.format('DD-MM-YYYY HH:mm:ss')
            const validTill = payload.time_zone_name ? moment.tz(object.valid_till, payload.time_zone_name) : moment(object.valid_till)
            object.valid_till = validTill.format('DD-MM-YYYY HH:mm:ss')
            return object
          })
        )
      }
      if (csvData.length > 0) {
        for (const txn of csvData) {
          const values = {
            'id': txn?.id,
            'user_name': (txn?.user_name || ''),
            'status': (txn?.status || ''),
            'bonus_amount': (txn?.bonus_amount || ''),
            'used_at': (txn?.claimed_at || ''),
            'promo_code': (txn?.code || ''),
            'valid_from': (txn?.valid_from || ''),
            'valid_till': (txn?.valid_till || ''),
          };

          mainArr = [...mainArr, values]
        }

        await Promise.all([csvWriter.writeRecords(mainArr)])
      }
    }

    // upload file to s3
    const s3Config = config.getProperties().s3
    const fileContent = await fs.promises.readFile(filePath);
    const key = `tenants/${params.tenantId}/csv/player/list_${uuid}.csv`

    const s3Params = {
      ACL: 'public-read',
      Bucket: s3Config.bucket,
      Key: key,
      Body: fileContent
    }
    const uploadedFile = await s3.upload(s3Params).promise()
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    fs.unlink(filePath, (err) => {
      if (err) {
        throw err
      }
    });

    return true;
  } catch (error) {
    console.log('=======error', error);
    throw error;
  }
};
