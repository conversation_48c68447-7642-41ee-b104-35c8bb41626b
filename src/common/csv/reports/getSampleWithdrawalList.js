import { PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import { QueryTypes } from 'sequelize';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { EXPORT_CSV_STATUS } from '../../constants';


// Function to fill empty fields with 'N/A'
const fillEmptyFields = (record) => {
  const fields = [
    "id",
    "user_name",
    "transaction_id",
    "amount",
    "remark",
    "processed_bank_name",
    "processed_account_number",
    "process_utr_number",
    "status",
  ]

  fields.forEach(field => {
    if (record[field] === null || record[field] === undefined || record[field] === '') {
      record[field] = 'N/A';
    }
  });

  return record;
};

export default async (data) => {
  await db.ExportCsvCenter.update(
    { status: EXPORT_CSV_STATUS.IN_PROGRESS },
    { where: { id: data.id } }
  );

  let params = { tenantId: 0, adminId: data.adminId };

  if (data.adminType === 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: { id: data.adminId },
      attributes: ['tenantId'],
    });

    params.tenantId = adminData.tenantId;
  }

  try {
    const csvUuid = uuidv4().replace(/-/g, '');
    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'id', title: 'ID' },
        { id: 'user_name', title: 'Username' },
        { id: 'transaction_id', title: 'Tracking Transaction Id' },
        { id: 'amount', title: 'Amount' },
        { id: 'remark', title: 'Remark' },
        { id: 'processed_bank_name', title: 'Processed Bank Name' },
        { id: 'processed_account_number', title: 'Processed Account Number' },
        { id: 'processed_utr_number', title: 'Processed UTR Number' },
        { id: 'status', title: 'Status' },
      ]
    })

    let sql = `
       SELECT
        wr.id,
        users.user_name,
        wr.transaction_id,
        wr.amount,
        wr.remark,
        wr.processed_bank_details->>'process_bank_name' AS "processed_bank_name",
        wr.processed_bank_details->>'process_account_number' AS "processed_account_number",
        wr.processed_bank_details->>'process_utr_number' AS "processed_utr_number",
        wr.status
        FROM
            withdraw_requests wr
        LEFT JOIN
          users ON users.id = wr.user_id AND users.demo = 'false'
              WHERE 1 = 1 and wr.tenant_id = ${params.tenantId} and wr.status = 'pending_by_bank'
      `;

    sql += ` ORDER BY wr.id DESC LIMIT 10`;

    const withdrawRequests = await sequelize.query(sql, {
      type: QueryTypes.SELECT,
      replacements: { tenant_id: params.tenantId || null }
    });

    // Fill empty fields with 'N/A'
    const processedWithdrawRequests = withdrawRequests.map(fillEmptyFields);

    const s3Config = config.getProperties().s3;
    const key = `tenants/${params.tenantId}/csv/withdraw_requests/withdraw_requests_${csvUuid}.csv`;

     // Convert data to CSV format
     const csvData = csvStringifier.stringifyRecords(processedWithdrawRequests)

     const finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with data

     // Create a readable stream of the final CSV content
     const stream = Readable.from([finalCsvContent])
     const contentLength = Buffer.byteLength(finalCsvContent)

     // Upload the CSV file to S3
     const uploadFileParams = {
       Bucket: s3Config.bucket,
       Key: key,
       Body: stream,
       ContentType: 'text/csv',
       ContentLength: contentLength,
     };

     await s3.send(new PutObjectCommand(uploadFileParams))

     await db.ExportCsvCenter.update(
       {
         csvUrl: key,
         status: EXPORT_CSV_STATUS.DONE,
       },
       {
         where: { id: data.id },
       }
     );

    return { csvUrl: key };

  } catch (error) {
    throw error;
  }
};
