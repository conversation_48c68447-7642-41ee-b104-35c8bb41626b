import moment from 'moment';
import { QueryTypes } from 'sequelize';
import { GET_ST8_SPORTS_ACTIONS, SPORTS_PROVIDERS_STAGE, SPORTS_PROVIDER_PROD, ZAMBIA_TENANT_ID } from '../../../common/constants';
import config from '../../../configs/app.config';
import { sequelize } from '../../../db/models';
import getRolesDetails from '../getAdminRolesDetail';
import getStartEndDates from './getStartEndDates';
import { calculateNGRForTenantRealtime, getTenantWiseRealTimeQuery } from './playerRevenueReportRealTimeSyncHelpers';

export default async (payload, params, data) => {
  const roles = await getRolesDetails(data.adminId);

  let timePeriod = payload.datetime || {}
  const timeZone = payload.time_zone_name;
  const dateFormat = 'YYYY-MM-DDTHH:mm:ss';
  let startDate;
  let endDate;
  if (!Object.keys(timePeriod).length) {
    timePeriod = await getStartEndDates(timeType)
    startDate = moment.tz(timePeriod.start_date, timeZone).utc().format(dateFormat) + '.000000Z'
    endDate = moment.tz(timePeriod.end_date, timeZone).utc().format(dateFormat) + '.999999Z'
  } else {
    startDate = moment.utc(timePeriod.start_date).format(dateFormat) + '.000000Z'
    endDate = moment.utc(timePeriod.end_date).format(dateFormat) + '.999999Z'
  }

  let whereStr = ''

  let tenantFilter, currencyFilter, date, userFilter, agentIdList, actionFilter,
  sportsFilter, actionFilterComission, ggrFilter, ngrCal, ggrCal, ggrNgrFilter,
  ngrSuperCal, ggrSuperCal, gameType, ggrNgrFilterTotal;

  tenantFilter = currencyFilter = date = userFilter = agentIdList = actionFilter =
  sportsFilter = actionFilterComission = ggrFilter = ngrCal = ggrCal = ggrNgrFilter =
  ngrSuperCal = ggrSuperCal = gameType = ggrNgrFilterTotal = '';

  let gameProvider = '';

  let providerIds = Object.values(config.get('env') === 'production' ? SPORTS_PROVIDER_PROD : SPORTS_PROVIDERS_STAGE).join(',');

  let botUsersSql, botUseIdCheck, botSelect, botUser, botUserIdTotalCheck, botsql;
  if (params.isSuperAdmin || payload.checkSboRequest === true) {
    // params.botUserSelect = ', bu.id as bot_user_id'
    botsql = " LEFT JOIN bot_users as bu on bu.user_id =  txs.user_id"; //bot listing for player
    botUsersSql = " LEFT JOIN bot_users as bu on bu.user_id =  w.owner_id";//bot total for player
    botSelect = "min(bu.id) AS bot_id,";
    botUser = "min(bot_id) as bot_id,";
    if (payload.playerCategory === 'bot_players') {
      botUseIdCheck = " where  bu.id is not null";
      botUserIdTotalCheck = " and  bu.id is not null";
      botSelect = "min(bu.id) AS bot_id,";
      botUser = "min(bot_id) as bot_id,";
    } else if (payload.playerCategory === 'real_players') {
      botUseIdCheck = " where bu.id is null";
      botUserIdTotalCheck = " and  bu.id is null";
    }
  } else if (payload.checkSboRequest === false) {
    botsql = " LEFT JOIN bot_users as bu on bu.user_id =  txs.user_id"; //bot listing for player
    botUsersSql = " LEFT JOIN bot_users as bu on bu.user_id =  w.owner_id";//bot total for player
    botUserIdTotalCheck = " and bu.id is null";
    botUseIdCheck = " where bu.id is null";
  }
  params.playerCategory = payload.playerCategory

  if (params.tenantId != 0) {
    if (Array.isArray(params.tenantId)) {
      const tenantIds = params.tenantId.map(id => parseInt(id)).join(',')
      tenantFilter = `and tenant_id IN (${tenantIds})`;
    } else {
      tenantFilter = `and tenant_id = ${params.tenantId}`;
    }
  }
  if (params.agentId != 0) {
    let agentIds;
    if (Array.isArray(params.agentId)) {
      agentIds = params.agentId.map(id => parseInt(id)).join(',')
    } else {
      agentIds = params.agentId;
    }
    agentIdList = `and u.parent_id IN (${agentIds})`;
  }
  if (params.currency_id && !isNaN(params.currency_id)) {
    currencyFilter = `and w.currency_id = ${parseInt(params.currency_id, 10)}`;
  }
  if (params.search && !isNaN(params.search)) {
    userFilter = `and w.owner_id = ${parseInt(params.search, 10)}`;
  }
  if (params.action_category) {
    const actionCategory = params.action_category.toLowerCase();

    if (actionCategory === "casino") {
      actionFilter = `
        and (
          (coalesce(provider_id, 0) not in (${providerIds})
          and coalesce(seat_id, '') not in ('bti_sportsbook', 'sbs_sportsbook', 'sap_lobby') and transaction_type not in (69,76))
          or transaction_type in (5, 11, 12, 15, 17, 37, 41, 44, 48, 52, 6, 39, 40, 42, 43, 49, 50, 51, 45, 3, 4, 68,70, 71 ,72 ,73, 74,75)
        )`;

      actionFilterComission = `
        and (
          (coalesce(provider_id, 0) not in (${providerIds})
          and coalesce(seat_id, '') not in ('bti_sportsbook', 'sbs_sportsbook', 'sap_lobby'))
          or transaction_type in (5, 6, 48, 49, 44)
        )`;
    } else if (actionCategory === "sports") {
      actionFilter = `
        and (
          (provider_id in (${providerIds})
          or seat_id in ('bti_sportsbook', 'sbs_sportsbook', 'sap_lobby'))
          or transaction_type in (5, 11, 12, 15, 17, 37, 41, 44, 48, 52, 6, 39, 40, 42, 43, 49, 50, 51, 45, 3, 4,69,70,71,72,72,73,74,76)
        )`;

      actionFilterComission = `
        and (
          (provider_id in (${providerIds})
          or seat_id in ('bti_sportsbook', 'sbs_sportsbook', 'sap_lobby'))
          or transaction_type in (5, 6, 48, 49, 44)
        )`;
      // sportsFilter = `
      //   having coalesce(
      //     sum(case when type = 'bet' then t.count else 0 end), 0
      //   ) - coalesce(
      //     sum(case when type = 'bet_refund' then t.count else 0 end), 0
      //   ) > 0`;
    }
  }
  if (params.game_provider && params.game_provider.length) {
    const providerIdsLower = params.game_provider.map((provider) => provider.replace(/'/g, "''").toLowerCase());
    const providerIdsStr = providerIdsLower.map((id) => `'${id}'`).join(",");

    // Query to get provider IDs
    const providerList = await sequelize.query(
      `SELECT id FROM casino_providers WHERE lower(name) IN (${providerIdsStr})`,
      { type: QueryTypes.SELECT }
    );
    const providerIdsArray = providerList.map((row) => row.id);

    let providerCondition = "";
    if (providerIdsArray.length > 0) {
      const id = providerIdsArray.join(",");
      providerCondition = `provider_id IN (${id})`;
    }

    // Query to get transaction seat IDs
    const transactionSeatIds = await sequelize.query(
      `SELECT seat_ids FROM transactions_providers_list WHERE lower(title) IN (${providerIdsStr})`,
      { type: QueryTypes.SELECT }
    );

    // Flatten and process seat IDs
    const seatIds = transactionSeatIds.flatMap((row) =>
      row.seat_ids.map((id) => id.replace(/'/g, "''").toLowerCase())
    );

    const providerToGameId = GET_ST8_SPORTS_ACTIONS;
    for (const [provider, gameId] of Object.entries(providerToGameId)) {
      if (providerIdsLower.includes(provider.toLowerCase())) {
        seatIds.push(gameId.toLowerCase());
      }
    }

    let gameCondition = "";
    if (seatIds.length > 0) {
      const quotedSeatIds = seatIds.map((id) => `'${id}'`).join(",");
      gameCondition = `lower(seat_id::text) IN (${quotedSeatIds})`;
    }

    gameProvider = "";
    if (providerCondition && gameCondition) {
      gameProvider += ` and (${providerCondition} OR ${gameCondition})`;
    } else if (providerCondition) {
      gameProvider += ` and ${providerCondition}`;
    } else if (gameCondition) {
      gameProvider += ` and ${gameCondition}`;
    }

    // sportsFilter = `
    //   having coalesce(
    //     sum(case when type = 'bet' then t.count else 0 end), 0
    //   ) - coalesce(
    //     sum(case when type = 'bet_refund' then t.count else 0 end), 0
    //   ) > 0`;
  }
  if (startDate && endDate) {
    date = `${whereStr ? " AND " : ""}created_at BETWEEN '${startDate}' AND '${endDate}'`;
  }

  if (params.game_type && typeof params.game_type !== "undefined" && params.game_type.length > 0) {
    params.game_type = params.game_type.map(x => "'" + x.trim().replace(/'/g, "''").toLowerCase() + "'");
    gameType = `
      and (
        seat_id::text IN (${params.game_type})
        OR game_id::text IN (${params.game_type})
        OR table_id::text IN (${params.game_type})
      )`;
  }
  const ngrnewFormula = calculateNGRForTenantRealtime(params.tenantId, date, tenantFilter, actionFilter, gameProvider, gameType);
  // Filter logic for GGR and NGR profit/loss
  ggrCal = `
    (
      coalesce(sum(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END), 0)
      - coalesce(sum(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END), 0)
    )
    - (
      coalesce(sum(CASE WHEN type = 'win' THEN t.amount ELSE 0 END), 0)
      - coalesce(sum(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END), 0)
    ) AS ggr`;

  ngrCal = ngrnewFormula.ngrCal;

  if (params.ggr_ngr && ["ggr_profit", "ggr_loss"].includes(params.ggr_ngr)) {
   // ggrNgrFilter = sportsFilter ? "and" : "having";
   ggrNgrFilter = having;
    if (["SuperAdminUser", "Manager"].includes(roles)) {
      ngrSuperCal = "0 as ngr_in_euro";
      ngrCal = "0 as ngr";
      ggrSuperCal = `
        (
          coalesce(sum(CASE WHEN type = 'bet' THEN t.amount_euro ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'bet_refund' THEN t.amount_euro ELSE 0 END), 0)
        )
        - (
          coalesce(sum(CASE WHEN type = 'win' THEN t.amount_euro ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'win_refund' THEN t.amount_euro ELSE 0 END), 0)
        ) as ggr_in_euro`;

      ggrNgrFilterTotal = `
        having (
          coalesce(sum(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END), 0)
        )
        - (
          coalesce(sum(CASE WHEN type = 'win' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END), 0)
        )`;

      ggrNgrFilter += `
        (
          coalesce(sum(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END), 0)
        )
        - (
          coalesce(sum(CASE WHEN type = 'win' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END), 0)
        )`;
    } else {
      ngrCal = "0 as ngr";
      ggrCal = ggrCal;

      ggrNgrFilterTotal = `
        having (
          coalesce(sum(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END), 0)
        )
        - (
          coalesce(sum(CASE WHEN type = 'win' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END), 0)
        )`;

      ggrNgrFilter += `
        (
          coalesce(sum(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END), 0)
        )
        - (
          coalesce(sum(CASE WHEN type = 'win' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END), 0)
        )`;
    }

    if (params.ggr_ngr === "ggr_profit") {
      ggrNgrFilter += " > 0"; // for ggr_profit, the condition is greater than 0
      ggrNgrFilterTotal += " > 0";
    } else {
      ggrNgrFilter += " < 0"; // for ggr_loss, the condition is less than 0
      ggrNgrFilterTotal += " < 0";
    }
  }
  if (params.ggr_ngr && ["ngr_profit", "ngr_loss"].includes(params.ggr_ngr)) {
    // ggrNgrFilter = sportsFilter ? "and" : "having";
      ggrNgrFilter = "having";
    if (["SuperAdminUser", "Manager"].includes(roles)) {
      ggrCal = "0 as ggr";
      ngrSuperCal = ngrnewFormula.ngrSuperCal;

      ggrSuperCal = "0 as ggr_in_euro";

      ggrNgrFilterTotal = `
        having (
          (
            coalesce(sum(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END), 0)
            - coalesce(sum(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END), 0)
          )
          - (
            coalesce(sum(CASE WHEN type = 'win' THEN t.amount ELSE 0 END), 0)
            - coalesce(sum(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END), 0)
          )
        )
        - (
          coalesce(sum(CASE WHEN type = 'bonus_claim' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'bonus_withdraw' THEN t.amount ELSE 0 END), 0)
        )`;

      ggrNgrFilter += `
        (
          (
            coalesce(sum(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END), 0)
            - coalesce(sum(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END), 0)
          )
          - (
            coalesce(sum(CASE WHEN type = 'win' THEN t.amount ELSE 0 END), 0)
            - coalesce(sum(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END), 0)
          )
        )
        - (
          coalesce(sum(CASE WHEN type = 'bonus_claim' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'bonus_withdraw' THEN t.amount ELSE 0 END), 0)
        )`;
    } else {
      ngrCal = ngrCal;
      ggrCal = "0 as ggr";

      ggrNgrFilterTotal = `
        having (
          (
            coalesce(sum(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END), 0)
            - coalesce(sum(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END), 0)
          )
          - (
            coalesce(sum(CASE WHEN type = 'win' THEN t.amount ELSE 0 END), 0)
            - coalesce(sum(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END), 0)
          )
        )
        - (
          coalesce(sum(CASE WHEN type = 'bonus_claim' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'bonus_withdraw' THEN t.amount ELSE 0 END), 0)
        )`;

      ggrNgrFilter += `
        (
          (
            coalesce(sum(CASE WHEN type = 'bet' THEN t.amount ELSE 0 END), 0)
            - coalesce(sum(CASE WHEN type = 'bet_refund' THEN t.amount ELSE 0 END), 0)
          )
            - (
              coalesce(sum(CASE WHEN type = 'win' THEN t.amount ELSE 0 END), 0)
              - coalesce(sum(CASE WHEN type = 'win_refund' THEN t.amount ELSE 0 END), 0)
          )
        )
        - (
          coalesce(sum(CASE WHEN type = 'bonus_claim' THEN t.amount ELSE 0 END), 0)
          - coalesce(sum(CASE WHEN type = 'bonus_withdraw' THEN t.amount ELSE 0 END), 0)
        )`;
    }

    if (params.ggr_ngr === "ngr_profit") {
      ggrNgrFilter += " > 0"; // For ngr_profit, the condition is greater than 0
      ggrNgrFilterTotal += " > 0";
    } else {
      ggrNgrFilter += " < 0"; // For ngr_loss, the condition is less than 0
      ggrNgrFilterTotal += " < 0";
    }
  }

  const qParams = {
    timezone: (params.tenantId && String(params.tenantId) === ZAMBIA_TENANT_ID) ? 'Africa/Lusaka' : 'Asia/kolkata',
    tenant_filter: tenantFilter,
    currency_filter: currencyFilter,
    date: date,
    user_filter: userFilter,
    ngr_cal: ngrCal,
    ggr_cal: ggrCal,
    ggr_ngr_filter: ggrNgrFilter,
    ggr_ngr_filter_total: ggrNgrFilterTotal,
    ngr_super_cal: ngrSuperCal,
    ggr_super_cal: ggrSuperCal,
    agent_ids: agentIdList,
    action_filter: actionFilter,
    action_filter_comission: actionFilterComission,
    sort_by: params.sort_by,
    order: params.order,
    sports_filter: sportsFilter,
    game_provider: gameProvider,
    game_type: gameType,
    bot_users_sql: botUsersSql || "", // bot for total
    bot_user_id_check: botUseIdCheck || "", // bot user id check for listing
    bot_select: botSelect || "",
    bot_user: botUser || "",
    bot_user_id_total: botUserIdTotalCheck || "", // bot user id check for total
    bot_sql: botsql || "", //joining bot user for listing,
    add_noncash_sql: ngrnewFormula.addSql ?? "",
    add_super_noncash_sql: ngrnewFormula.addSuperNgrSql ?? "",
    ngr_formula: ngrnewFormula ?? "",
  };

  const mainQ = getTenantWiseRealTimeQuery(qParams);

  return await sequelize.query(mainQ, {
    type: QueryTypes.SELECT
  })

};
