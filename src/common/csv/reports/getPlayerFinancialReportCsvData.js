import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { EXPORT_CSV_STATUS, OTB_TENANTS_CONFIG } from '../../../common/constants'
import config from '../../../configs/app.config'
import db from '../../../db/models'
import { s3 } from '../../../libs/awsS3Config'
import getESclient from '../../../libs/getEsClient'
import getRolesDetails from '../getAdminRolesDetail'
import getMakerCheckerData from '../getMakerCheckerData'
import searchByKeyword from '../reports/searchByKey/playerFinancialReportSearchKey'
import getPgNameProd from './getPgNameProd'
import getPgNameStage from './getPgNameStage'
import getStartEndDates from './getStartEndDates'
import { getDateInStringFormat } from '../../helpers'
const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')


export default async (data) => {
  // try{
  let esClient = getESclient();
  const payload = data.payload
  await db.ExportCsvCenter.update({
    status: EXPORT_CSV_STATUS.IN_PROGRESS
  },
    {
      where: { id: data.id }
    }
  )
  let filter = []
  let mustArray = []
  let query
  let agentId
  let tenantId
  let isDirectPlayer = payload?.isDirectPlayer ? parseInt(payload.isDirectPlayer) : 'all'

  if (data.adminType == 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: {
        id: data.adminId
      },
      attributes: ['tenantId']
    }
    )
    tenantId = adminData.tenantId
    const roles = await getRolesDetails(data.adminId)
    if (!roles.includes('owner')) {
      //condition for new sub-admin role
      if (roles.includes('owner') || roles.includes('sub-admin')) {
        agentId = payload?.agent_id ? payload.agent_id : null
      } else {
        agentId = payload?.agent_id ? payload.agent_id : data.adminId
      }
    } else {
      agentId = payload?.agent_id ? payload.agent_id : null
    }

  } else {
    tenantId = payload?.tenant_id ? payload.tenant_id : null
    agentId = payload?.agent_id ? payload.agent_id : null
  }

  const owner_id = payload?.owner_id ? payload.owner_id : null
  let time_period
  if (payload.time_type != 'custom') {
    time_period = await getStartEndDates(payload.time_type)
  } else {
    time_period = payload.time_period
  }

  const otbTenants = config.get('env') === 'production' ? OTB_TENANTS_CONFIG.PROD : OTB_TENANTS_CONFIG.STAGE


  const params = {
    tenantId: tenantId,
    timePeriod: time_period,
    agentIdTop: agentId,
    isDirectPlayer: isDirectPlayer,
    searchKeyword: payload.search,
    actionType: payload.action_type,
    currency: payload.currency,
    ownerId: owner_id,
    status: payload.status,
    timeType: payload?.time_type ? payload?.time_type : 'today'
  }
  const isOTBEnabled = otbTenants[parseInt(params.tenantId)] ? true : false

  filter = [...filter, {
    bool: {
      must_not: {
        bool: {
          must_not: {
            exists: {
              field: "player_details"
            }
          }
        }
      }
    }
  }]

  if (params.tenantId) {
    filter = [...filter, { term: { tenant_id: params.tenantId } }]
  }

  if (payload.player_id) {
    filter = [...filter, { term: { 'player_details.player_id_s': payload.player_id } }]
  }
  if (params.isDirectPlayer == 'direct' && params.agentId && params.agentIdTop != params.agentId) {
    filter = [...filter, { term: { parent_id: { value: params.agent_id } } }]
  }


  if (params?.ownerId && params?.agentIdTop) {
    filter = [...filter, { term: { 'player_details.parent_chain_ids': { value: params.ownerId } } }]
  } else if (params?.agentIdTop) {
    filter = [...filter, { term: { 'player_details.parent_chain_ids': { value: params.agentIdTop } } }]
  }

  if (params.status) {
    filter = [...filter, { term: { status: params.status } }]
  }

  if (payload.utr_number) {
    filter = [...filter, { term: { custom_4: payload.utr_number } }]
  }

  if (params.searchKeyword) {
    const searchData = searchByKeyword(params.searchKeyword)
    filter = [...filter, searchData]
  }

  if (params.currency) {
    mustArray = [...mustArray, {
      multi_match: {
        query: params.currency,
        fields: [
          'player_details.currency'
        ],
        type: "best_fields",
        operator: "OR"
      }
    }
    ]
  }

  if (params.actionType === 'deposit_cash_admin') {
    filter = [...filter, {
      bool: {
        must: [
          {
            terms: {
              'actionee.type': [
                "AdminUser"
              ]
            }
          },
          {
            term: {
              transfer_method: "manual"
            }
          },
          {
            term: {
              transaction_type: "deposit"
            }
          }
        ],
        must_not: {
          terms: {
            description: [
              "Deposit Request Approved"
            ]
          }
        }
      }
    }
    ]
  } else if (params.actionType === 'deposit_non_cash_admin') {
    filter = [...filter, {
      term: {
        transaction_type: "non_cash_granted_by_admin"
      }
    }
    ]
  }
  else if (isOTBEnabled && params.actionType === 'one_time_bonus_deposit') {
    filter = [...filter, {
      term: {
        transaction_type: "one_time_bonus_deposit"
      }
    }
    ]
  }
   else if (params.actionType === 'deposit_manual_user') {
    filter = [...filter, {
      bool: {
        filter: [
          {
            terms: {
              description: [
                "Deposit Request Approved"
              ]
            }
          },
          {
            term: {
              transaction_type: "deposit"
            }
          },
          {
            term: {
              transfer_method: "manual"
            }
          }
        ]
      }
    }
    ]
  } else if (params.actionType === 'deposit_gateway_user') {
    filter = [...filter, {
      bool: {
        filter: [
          {
            terms: {
              description: [
                "Deposit Request"
              ]
            }
          },
          {
            term: {
              transaction_type: "deposit"
            }
          }
        ]
      }
    }
    ]
  } else if (params.actionType === 'withdraw_gateway_user') {
    filter = [...filter, {
      bool: {
        filter: [
          {
            terms: {
              description: [
                "Approved By Payment gateway"
              ]
            }
          },
          {
            term: {
              transaction_type: "withdraw"
            }
          }
        ]
      }
    }
    ]
  } else if (params.actionType === 'withdraw_cash_manual_user') {
    filter = [...filter, {
      bool: {
        filter: [
          {
            terms: {
              description: [
                "approved by admin"
              ]
            }
          },
          {
            term: {
              transaction_type: "withdraw"
            }
          }
        ]
      }
    }
    ]
  } else if (params.actionType === 'withdraw_non_cash_admin') {
    filter = [...filter, {
      term: {
        transaction_type: "non_cash_withdraw_by_admin"
      }
    }
    ]
  } else if (isOTBEnabled && params.actionType === 'one_time_bonus_withdraw') {
    filter = [...filter, {
      term: {
        transaction_type: "one_time_bonus_withdraw"
      }
    }
    ]
  } else if (params.actionType === 'withdraw_cash_admin') {
    filter = [...filter, {
      bool: {
        must: [
          {
            terms: {
              'actionee.type': [
                "AdminUser"
              ]
            }
          },
          {
            term: {
              transaction_type: "withdraw"
            }
          },
          {
            term: {
              status: "success"
            }
          }
        ],
        must_not: {
          terms: {
            description: [
              "approved by admin",
              "cancelled by admin",
              "Approved By Payment gateway"
            ]
          }
        }
      }
    }
    ]
  } else if (params.actionType === 'deposit') {
    filter = [...filter, {
      terms: {
        transaction_type: [
          "deposit",
          "non_cash_granted_by_admin"
        ]
      }
    }
    ]
  } else if (params.actionType === 'withdraw') {
    filter = [...filter, {
      bool: {
        must: [
          {
            terms: {
              transaction_type: [
                "withdraw",
                "non_cash_withdraw_by_admin"
              ]
            }
          },
          {
            term: {
              status: "success"
            }
          }
        ],
        must_not: {
          terms: {
            description: [
              "Pending confirmation from admin",
              "cancelled by admin"
            ]
          }
        }
      }
    }
    ]
  } else if (params.actionType) {
    filter = [...filter, {
      term: {
        transaction_type: params.actionType
      }
    }
    ]
  } else {
    filter = [...filter, {
      terms: {
        transaction_type: [
          "deposit",
          "withdraw",
          "withdraw_cancel",
          "deposit_bonus_claim",
          "non_cash_granted_by_admin",
          "non_cash_withdraw_by_admin",
          ...(isOTBEnabled ? ["one_time_bonus_deposit", "one_time_bonus_withdraw"] : [])
        ]
      }
    }
    ]
  }

  if (params?.timeType) {
    if (params?.timePeriod?.start_date && params?.timePeriod?.end_date) {
      if (payload.time_zone_name) {
        params.timePeriod.start_date = moment.tz(params.timePeriod.start_date, payload.time_zone_name).utc().format()
        params.timePeriod.end_date = moment.tz(params.timePeriod.end_date, payload.time_zone_name).utc().format()
      }

      let endDate = params.timePeriod.end_date
      filter = [...filter, {
        range: {
          created_at: {
            from: params.timePeriod.start_date,
            include_lower: true,
            to: endDate.replace("000000", "999999"),
            include_upper: true
          }
        }
      }]
    }
  }


  if (mustArray.length > 0) {
    query = mustArray
  } else {
    query = {
      match_all: {}
    }
  }

  if (filter.length > 0) {
    filter = {
      bool: {
        filter: filter
      }
    }
  }

  let sortData = {}
  let sortOrder = payload?.order ? payload.order : 'ASC'
  if (payload?.sort_by) {
    let sortKey = payload.sort_by
    sortData[sortKey] = sortOrder
  }

  try {
    let searchDetails
    let limit = 1000
    let page = 0
    let offset = page * limit

    searchDetails = await esClient.search({
      index: config.getProperties().es_index.transactions_index_name,
      body: {
        query: {
          bool: {
            must: query,
            filter: filter
          }
        },
        sort: sortData,
        timeout: '3000s',
        track_total_hits: true,
        size: limit,
        from: offset
      }
    })
    // console.log("=========", searchDetails.body.hits.hits.length)
    // return

    const uuid = uuidv4().replace(/-/g, '')
    const filePath = `/tmp/transaction_${uuid}.csv`
    const csvWriter = createCsvWriter({
      path: filePath,
      header: [
        { id: 'player_id', title: 'Player Id' },
        { id: 'player_name', title: 'Player Name' },
        { id: 'agent_name', title: 'Agent Name' },
        { id: 'created_at', title: 'TIMESTAMP' },
        { id: 'action_type', title: 'Action Type' },
        { id: 'utr_number', title: 'UTR Number' },
        { id: 'balance_before', title: 'INITIAL BALANCE' },
        { id: 'amount', title: 'Amount' },
        { id: 'balance_after', title: 'ENDING BALANCE' },
        { id: 'transfer_method', title: 'Transfer Method' },
        { id: 'status', title: 'Status' },
        { id: 'description', title: 'Description' },
        { id: 'transaction_id', title: 'Transaction ID' },
        { id: 'maker_email', title: 'Maker Email' },
        { id: 'maker_username', title: 'Maker Username' },
        { id: 'maker_time_stamp', title: 'Maker Time Stamp' },
        { id: 'maker_remark', title: 'Maker Remark' },
        { id: 'checker_email', title: 'Checker Email' },
        { id: 'checker_username', title: 'Checker Username' },
        { id: 'checker_time_stamp', title: 'Checker Time Stamp' },
        { id: 'checker_remark', title: 'Checker Remark' },
        { id: 'internal_tracking_id', title: 'Internal Tracking ID' },
        { id: 'currency', title: 'Currency' },
        { id: 'payment_gateway', title: 'Payment Gateway' },

      ]
    });
    const csvData = searchDetails.body.hits.hits

    if (payload.time_zone_name) {
      const csvDataList = await Promise.all(
        csvData.map(async object => {
          // Formatting createdAt date with optional user timezone
          const createdDate = payload.time_zone_name ? moment.tz(object._source.created_at, payload.time_zone_name) : moment(object._source.created_at)
          object._source.created_at = createdDate.format('DD-MM-YYYY HH:mm:ss')
          return object
        })
      )
    }

    let mainArr = []
    let tenantId
    if (csvData.length > 0) {
      tenantId = csvData[0]._source.tenant_id
      for (const txn of csvData) {
        let playerDetails = txn._source.player_details
        let object = {
          player_id: playerDetails.player_id_s,
          player_name: playerDetails.player_name,
          agent_name: playerDetails.agent_name,
          created_at: getDateInStringFormat(txn._source.created_at),
          action_type: txn._source.transaction_type,
          utr_number: txn._source.custom_4 || '',
          amount: txn?._source?.amount ? parseFloat(txn._source.amount).toFixed(2) : 0,
          currency: playerDetails.currency,
          balance_before: playerDetails?.before_balance ? parseFloat(playerDetails.before_balance).toFixed(2) : 0,
          balance_after: playerDetails?.after_balance ? parseFloat(playerDetails.after_balance).toFixed(2) : 0,
          transfer_method: txn._source.transfer_method,
          status: txn._source.status,
          description: txn._source.description,
          payment_gateway: config.get('env') === 'production' ? getPgNameProd(txn._source.payment_provider_id) : getPgNameStage(txn._source.payment_provider_id),
          transaction_id: txn._source.transaction_id,
          internal_tracking_id: txn._source.internal_tracking_id,
          maker_email: txn._source?.maker_data ? getMakerCheckerData(txn, 'email', 'maker',payload.time_zone_name) : ' ',
          maker_username: txn._source?.maker_data ? getMakerCheckerData(txn, 'user_name', 'maker',payload.time_zone_name) : ' ',
          maker_time_stamp: txn._source?.maker_data ? getMakerCheckerData(txn, 'time_stamp', 'maker',payload.time_zone_name) : ' ',
          maker_remark: txn._source?.maker_data ? getMakerCheckerData(txn, 'remark', 'maker',payload.time_zone_name) : ' ',
          checker_email:txn._source?.checker_data ? getMakerCheckerData(txn, 'email', 'checker',payload.time_zone_name) : ' ',
          checker_username: txn._source?.checker_data ? getMakerCheckerData(txn, 'user_name', 'checker',payload.time_zone_name) : ' ',
          checker_time_stamp:txn._source?.checker_data ? getMakerCheckerData(txn, 'time_stamp', 'checker',payload.time_zone_name) : ' ',
          checker_remark:txn._source?.checker_data ? getMakerCheckerData(txn, 'remark', 'checker',payload.time_zone_name) : ' ',
        }
        mainArr = [...mainArr, object]
        playerDetails = null
        object = null
      }
    }
    await Promise.all([csvWriter.writeRecords(mainArr)])

    const totalData = searchDetails.body.hits.total.value
    let itval = parseFloat(totalData) / limit
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    for (let i = 1; i < itval; i++) {
      await delay(1000)
      offset = i * limit
      searchDetails = //await Promise.all([
        await esClient.search({
          index: config.getProperties().es_index.transactions_index_name,
          body: {
            query: {
              bool: {
                must: query,
                filter: filter
              }
            },
            sort: sortData,
            timeout: '3000s',
            track_total_hits: true,
            size: limit,
            from: offset
          }
        })
      //])
      let mainArr = []
      let tenantId
      const csvData = searchDetails.body.hits.hits
      if (payload.time_zone_name) {
        const csvDataList = await Promise.all(
          csvData.map(async object => {
            // Formatting createdAt date with optional user timezone
            const createdDate = payload.time_zone_name ? moment.tz(object._source.created_at, payload.time_zone_name) : moment(object._source.created_at)
            object._source.created_at = createdDate.format('YYYY-MM-DD HH:mm:ss')
            return object
          })
        )
      }
      if (csvData.length > 0) {
        tenantId = csvData[0]._source.tenant_id
        for (const txn of csvData) {
          let playerDetails = txn._source.player_details
          let object = {
            player_id: playerDetails.player_id_s,
            player_name: playerDetails.player_name,
            agent_name: playerDetails.agent_name,
            created_at: getDateInStringFormat(txn._source.created_at),
            action_type: txn._source.transaction_type,
            utr_number: txn._source.custom_4 || '',
            amount: txn?._source?.amount ? parseFloat(txn._source.amount).toFixed(2) : 0,
            currency: playerDetails.currency,
            balance_before: playerDetails?.before_balance ? parseFloat(playerDetails.before_balance).toFixed(2) : 0,
            balance_after: playerDetails?.after_balance ? parseFloat(playerDetails.after_balance).toFixed(2) : 0,
            transfer_method: txn._source.transfer_method,
            status: txn._source.status,
            description: txn._source.description,
            payment_gateway: config.get('env') === 'production' ? getPgNameProd(txn._source.payment_provider_id) : getPgNameStage(txn._source.payment_provider_id),
            transaction_id: txn._source.transaction_id,
            internal_tracking_id: txn._source.internal_tracking_id,
            maker_email: txn._source?.maker_data ? getMakerCheckerData(txn, 'email', 'maker',payload.time_zone_name) : ' ',
            maker_username: txn._source?.maker_data ? getMakerCheckerData(txn, 'user_name', 'maker',payload.time_zone_name) : ' ',
            maker_time_stamp: txn._source?.maker_data ? getMakerCheckerData(txn, 'time_stamp', 'maker',payload.time_zone_name) : ' ',
            maker_remark: txn._source?.maker_data ? getMakerCheckerData(txn, 'remark', 'maker',payload.time_zone_name) : ' ',
            checker_email:txn._source?.checker_data ? getMakerCheckerData(txn, 'email', 'checker',payload.time_zone_name) : ' ',
            checker_username: txn._source?.checker_data ? getMakerCheckerData(txn, 'user_name', 'checker',payload.time_zone_name) : ' ',
            checker_time_stamp:txn._source?.checker_data ? getMakerCheckerData(txn, 'time_stamp', 'checker',payload.time_zone_name) : ' ',
            checker_remark:txn._source?.checker_data ? getMakerCheckerData(txn, 'remark', 'checker',payload.time_zone_name) : ' ',
            }
          mainArr = [...mainArr, object]
          playerDetails = null
          object = null
        }

        await Promise.all([csvWriter.writeRecords(mainArr)])
      }
    }


    // upload file to s3
    const s3Config = config.getProperties().s3
    const fileContent = await fs.promises.readFile(filePath);
    const key = `tenants/${tenantId}/csv/casino_transaction/transaction_${uuid}.csv`

    const s3Params = {
      ACL: 'public-read',
      Bucket: s3Config.bucket,
      Key: key,
      Body: fileContent
    }
    const uploadedFile = await s3.upload(s3Params).promise()
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    fs.unlink(filePath, (err) => {
      if (err) {
        throw err
      }
    });

    return true
  } catch (error) {
    console.log("=======errorr", error)
    throw error
  }
}
