import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import moment from 'moment';
import { QueryTypes } from 'sequelize';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { CURRENCY_LIST_PROD, CURRENCY_LIST_STAGE, EXPORT_CSV_STATUS, EXPORT_CSV_TYPE } from '../../constants';
import { formatAmount } from '../../helpers';


const CHUNK_SIZE = 1000;

// Function to fill empty fields with 'N/A'
const fillEmptyFields = (record) => {
  const fields = [
    'id', 'user_name', 'bank_name', 'account_holder_name', 'transaction_id', 'process_utr_number',
    'account_number', 'ifsc_code', 'amount', 'remark',
    'withdrawal_type', 'payment_provider_name',
    'processed_bank_name', 'processed_account_number',
    'created_at', 'actioned_at', 'status', 'transaction_id', 'fd_transaction_id'
  ];

  fields.forEach(field => {
    if (record[field] === null || record[field] === undefined || record[field] === '' || record[field] === 'null') {
      record[field] = 'N/A';
    }
  });

  return record;
};

export default async (data) => {
  const payload = data.payload;

  await db.ExportCsvCenter.update(
    { status: EXPORT_CSV_STATUS.IN_PROGRESS },
    { where: { id: data.id } }
  );

  let params = {
    tenantId: 0,
    adminId: data.adminId,
    transactionId: payload.transaction_id,
    depositType: payload.deposit_type,
  };
  let isSuperAdmin = false;

  if (data.adminType === 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: { id: data.adminId },
      attributes: ['tenantId'],
    });
    params.tenantId = adminData.tenantId;
  }
  isSuperAdmin = data.adminType === 'SuperAdminUser';
  // Check SBO User Logic
  let botUserJoin = ''
  let botUserSelect = ''
  let whereStr = ''

  const checkSboRequest = payload.sbo_request;
  if (isSuperAdmin || checkSboRequest === true) {
    botUserSelect = ', bu.id as bot_user_id'
    botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = users.id'
    if (payload.playerCategory === 'bot_players') {
      whereStr += `${whereStr ? ' AND ' : ''} bu.id IS NOT NULL`
    } else if (payload.playerCategory === 'real_players') {
      whereStr += `${whereStr ? ' AND ' : ''} bu.id IS NULL`
    }
  } else if (checkSboRequest === false) {
    botUserSelect = ', bu.id as bot_user_id'
    botUserJoin = 'LEFT JOIN bot_users bu ON bu.user_id = users.id'
    whereStr += `${whereStr ? ' AND ' : ''} bu.id IS NULL`
  }

  try {
    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'id', title: 'ID' },
        { id: 'user_name', title: 'Username' },
        { id: 'bank_name', title: 'Bank Name' },
        { id: 'name', title: 'Account Holder Name' },
        { id: 'transaction_id', title: 'Tracking Transaction Id' },
        { id: 'account_number', title: 'Account Number' },

        { id: 'agent_name', title: 'Agent Name' },
        { id: 'ifsc_code', title: 'IFSC Code' },
        { id: 'amount', title: 'Amount' },
        { id: 'remark', title: 'Remark' },
        { id: 'withdrawal_type', title: 'Withdrawal Type' },
        { id: 'payment_provider_name', title: 'Payment Gateway' },
        { id: 'processed_bank_name', title: 'Processed Bank Name' },
        { id: 'processed_account_number', title: 'Processed Account Number' },
        { id: 'process_utr_number', title: 'Processed UTR Number' },

        { id: 'created_at', title: 'Requested At' },
        ...((data.type !== EXPORT_CSV_TYPE.WITHDRAW_UNVERIFIED_REQUEST) ? [{ id: 'actioned_at', title: 'Actioned At' }] : []),
        { id: 'status', title: 'Status' },
        { id: 'fd_transaction_id', title: 'Fd Transaction Id' },
        { id: 'payment_transaction_id', title: 'Transaction Id' }
      ]
    })

    let offset = 0;
    let totalRecords = 0;
    let hasMoreRecords = true;
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3
    const csvUuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${params.tenantId}/csv/withdraw_requests/withdraw_${csvUuid}.csv`
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    while (hasMoreRecords) {
      let sql = `
       SELECT
    withdraw_requests.*,
    withdraw_requests.processed_bank_details->>'process_bank_name' AS "processed_bank_name",
    withdraw_requests.processed_bank_details->>'process_account_number' AS "processed_account_number",
        withdraw_requests.processed_bank_details->>'process_utr_number' AS "process_utr_number",

    wallets.id as wallet_id,
     wallets.currency_id,
    users.vip_level ,
     users.user_name,
    parent_agent.agent_name
    ${botUserSelect}
FROM
    withdraw_requests
LEFT JOIN
    users ON users.id = withdraw_requests.user_id AND users.demo = 'false'
LEFT JOIN
    admin_users AS parent_agent ON parent_agent.id = users.parent_id AND parent_agent.parent_type = 'AdminUser'
LEFT JOIN
    wallets ON wallets.owner_id = withdraw_requests.user_id AND wallets.owner_type = 'User'
${botUserJoin}
        WHERE 1 = 1
        -- and withdraw_requests.tenant_id = ${params.tenantId}
      `;

      if (params.tenantId) {
        sql += ` AND withdraw_requests.tenant_id = ${params.tenantId}`
      }

      if (payload.search) {
        sql += `
          AND (
            users.user_name ILIKE :search
            OR withdraw_requests.phone_number ILIKE :search
            OR CAST(withdraw_requests.id AS TEXT) ILIKE :search
            OR users.first_name ILIKE :search
          )
        `;
      }

      if (payload.account_ifsc) {
        sql += `
          AND (
            withdraw_requests.account_number = :account_ifsc
            OR withdraw_requests.ifsc_code = :account_ifsc
          )
        `;
      }

      if (payload?.time_period && payload?.time_period?.start_date && payload?.time_period?.end_date) {
        if (payload?.time_zone_name) {
          payload.time_period.start_date = moment.tz(payload.time_period.start_date, payload.time_zone_name).utc().format()
          payload.time_period.end_date = moment.tz(payload.time_period.end_date, payload.time_zone_name).utc().format()
        }

        sql += ` AND withdraw_requests.created_at BETWEEN :startDate AND :endDate`;
      }
      if (payload.transaction_id) {
        sql += ` AND withdraw_requests.transaction_id = :transaction_id`;
      }
      if (payload.fd_transaction_id) {
        sql += ` AND withdraw_requests.fd_transaction_id = :fd_transaction_id`;
      }
      if (payload.status) {
        sql += ` AND withdraw_requests.status = :status`;
      }
      if (payload.withdrawal_type && payload.withdrawal_type !== 'all') {
        sql += ` AND withdraw_requests.withdrawal_type = :withdrawal_type`;
      }

      if (payload.p_bank_name) {
        sql += ` AND withdraw_requests.processed_bank_details->>'process_bank_name' = :p_bank_name`;
      }

      if (payload.p_account_number) {
        sql += ` AND withdraw_requests.processed_bank_details->>'process_account_number' = :p_account_number`;
      }

      if (payload.adminId) {
        sql += ` AND admin_users.id  = :adminId`;
      }

      if (payload.type == 'unverified' && payload.module == 'unverified_withdrawal_request') {
        sql += ` AND withdraw_requests.verify_status = 'unverified' AND withdraw_requests.status NOT IN ('rejected', 'rejected_by_gateway', 'cancelled') `;
      }

      if (payload.type == 'verified' && payload.module == 'verified_withdrawal_request') {
        sql += ` AND withdraw_requests.verify_status = 'verified' AND withdraw_requests.status NOT IN ('rejected', 'rejected_by_gateway', 'cancelled') `;
      }

      if (payload.type == 'rejected' && payload.module == 'rejected_withdrawal_request') {
        sql += ` AND withdraw_requests.status  IN ('rejected', 'rejected_by_gateway', 'cancelled') `;
      }

      // bot user
      if (sql && botUserJoin && whereStr) {
        sql += ` AND ${whereStr}`
      }

      sql += ` ORDER BY withdraw_requests.id DESC LIMIT :limit OFFSET :offset`;

      const users = await sequelize.query(sql, {
        type: QueryTypes.SELECT,
        replacements: {
          search: `%${payload.search}%` || null,
          account_ifsc: payload.account_ifsc || null,
          ifsc_code: payload.ifsc_code || null,
          transaction_id: payload.transaction_id || null,
          fd_transaction_id: payload.fd_transaction_id || null,
          status: payload.status || null,
          withdrawal_type: payload.withdrawal_type || null,
          p_bank_name: payload.p_bank_name || null,
          p_account_number: payload.p_account_number || null,
          adminId: payload.adminId || null,
          startDate: payload.time_period ? payload.time_period.start_date : null,
          endDate: payload.time_period ? payload.time_period.end_date : null,
          limit: CHUNK_SIZE,
          offset: offset,
        },
      });


      if (users.length < CHUNK_SIZE) {
        hasMoreRecords = false;
      }

      if (payload.time_zone_name) {
        users.forEach((user) => {
          const createdDate = payload.time_zone_name ? moment.tz(user.created_at, payload.time_zone_name) : moment(user.created_at);
          user.created_at = createdDate.format('DD-MM-YYYY HH:mm:ss');
          if (data.type !== EXPORT_CSV_TYPE.WITHDRAW_UNVERIFIED_REQUEST && user.actioned_at) {
            const actionedDate = payload.time_zone_name ? moment.tz(user.actioned_at, payload.time_zone_name) : moment(user.actioned_at);
            user.actioned_at = actionedDate.format('DD-MM-YYYY HH:mm:ss');
          }
          const currencyList = (config.get('env') === 'production') ? CURRENCY_LIST_PROD : CURRENCY_LIST_STAGE;
          const currencyEntry = currencyList.find(currency => currency.id === Number(user.currency_id));
          const currencyLabel = currencyEntry ? currencyEntry.value : '';
          user.amount = formatAmount(user.amount, params.tenantId, currencyLabel)
        });
      }

      // Fill empty fields with 'N/A'
      const processedUsers = users.map(fillEmptyFields);

      // Convert data to CSV format
      const csvData = csvStringifier.stringifyRecords(processedUsers)

      // S3 upload parameters
      const uploadParams = {
        Bucket: s3Config.bucket,
        Key: key,
      }

      // Try to fetch the existing CSV file to append data
      let existingCsvContent = ''
      try {
        const data = await s3.send(new GetObjectCommand(uploadParams))
        const chunks = [];
        for await (const chunk of data.Body) {
          chunks.push(chunk)
        }
        existingCsvContent = Buffer.concat(chunks).toString('utf-8')
      } catch (err) {
        if (err.name !== 'NoSuchKey') throw err;  // Ignore if file doesn't exist
      }
      // Append data or create new file
      let finalCsvContent = existingCsvContent
      if (existingCsvContent) {
        finalCsvContent += csvData // Append the new data (excluding the header)
      } else {
        finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
      }

      // Create a readable stream of the final CSV content
      const stream = Readable.from([finalCsvContent])
      const contentLength = Buffer.byteLength(finalCsvContent)

      // Upload the CSV file to S3
      const uploadFileParams = {
        Bucket: s3Config.bucket,
        Key: key,
        Body: stream,
        ContentType: 'text/csv',
        ContentLength: contentLength,
      };

      await s3.send(new PutObjectCommand(uploadFileParams))
      totalRecords += users.length
      offset += CHUNK_SIZE
      await delay(500)
    }

    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    return {
      totalRecords,
      csvUrl: key,
    };
  } catch (error) {
    throw error;
  }
};
