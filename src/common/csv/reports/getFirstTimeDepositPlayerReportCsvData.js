import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import config from '../../../configs/app.config'
import db from '../../../db/models'
import { s3 } from '../../../libs/awsS3Config'
import getESclient from '../../../libs/getEsClient'
import { EXPORT_CSV_STATUS } from '../../constants'
import getRolesDetails from '../getAdminRolesDetail'
import getStartEndDates from './getStartEndDates'
import searchByKeyword from './searchByKey/playerReportSearchKey'
import { getDateInStringFormat } from '../../helpers'
const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')


export default async (data) => {
  // try{
  let esClient = getESclient();
  const payload = data.payload
  await db.ExportCsvCenter.update({
    status: EXPORT_CSV_STATUS.IN_PROGRESS
  },
    {
      where: { id: data.id }
    }
  )
  let filter = []
  let mustArray = []
  let query
  let agentId
  let tenantId
  let isDirectPlayer = payload?.isDirectPlayer ? payload.isDirectPlayer : "0"

  if (data.adminType == 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: {
        id: data.adminId
      },
      attributes: ['tenantId']
    }
    )
    tenantId = adminData.tenantId
    const roles = await getRolesDetails(data.adminId)
    if (!roles.includes('owner')) {
      //condition for new sub-admin role
      if (roles.includes('owner') || roles.includes('sub-admin')) {
        agentId = payload?.agent_id ? payload.agent_id : null
      } else {
        agentId = payload?.agent_id ? payload.agent_id : data.adminId
      }
    } else {
      agentId = payload?.agent_id ? payload.agent_id : null
    }

  } else {
    tenantId = payload?.tenant_id ? payload.tenant_id : null
    agentId = payload?.agent_id ? payload.agent_id : null
  }

  const owner_id = payload?.owner_id ? payload.owner_id : null
  let time_period
  if (payload.time_type != 'custom') {
    time_period = await getStartEndDates(payload.time_type)
  } else {
    time_period = payload.time_period
  }
  const params = {
    tenant_id: tenantId,
    time_period: time_period,
    agentId: parseInt(agentId),
    isDirectPlayer: isDirectPlayer,
    searchKeyword: payload.search,
    actionType: payload.action_type,
    currency: payload.currency,
    owner_id: owner_id,
    vip_levels: payload.vip_levels,
    time_type: payload?.time_type ? payload?.time_type : 'today'
  }

  if (isDirectPlayer === "0" && params.agentId) {
    params.agentIdTop = params.agentId
  } else if (isDirectPlayer == "0" && owner_id) {
    params.agentIdTop = owner_id
  }


  if (params.tenant_id) {
    filter = [...filter, { term: { tenant_id: params.tenant_id } }]
  }

  if (params.agentId && params.agentIdTop != params.agentId) {
    filter = [...filter, { term: { parent_id: { value: params.agentId } } }]
  }

  if (params.agentIdTop) {
    filter = [...filter, { term: { parent_chain_ids: { value: params.agentIdTop } } }]
  }

  if (params.currency) {
    mustArray = [...mustArray, {
      match: {
        currency: params.currency
      }
    }
    ]
  }

  if (params.searchKeyword) {
    const searchData = searchByKeyword(params.searchKeyword)
    filter = [...filter, searchData]
  }

  if (params.actionType === 'first-deposit') {
    filter = [...filter, {
      bool: {
        filter: {
          bool: {
            must_not: {
              terms: {
                first_deposit_amount: [
                  "0.0"
                ]
              }
            }
          }
        }
      }
    }
    ]
  }
  if (params.actionType === 'no-deposit-amount') {
    filter = [...filter, {
      bool: {
        filter: {
          bool: {
            must: {
              terms: {
                first_deposit_amount: [
                  "0.0"
                ]
              }
            }
          }
        }
      }
    }
    ]
  }

  if (params.vip_levels) {
    filter = [...filter, {
      bool: {
        filter: {
          bool: {
            must: {
              terms: {
                vip_level: [
                  params.vip_levels
                ]
              }
            }
          }
        }
      }
    }
    ]
  }



  if (params?.time_type) {
    if (params?.time_period?.start_date && params?.time_period?.end_date) {
      if (payload.time_zone_name) {
        params.time_period.start_date = moment.tz(params.time_period.start_date, payload.time_zone_name).utc().format()
        params.time_period.end_date = moment.tz(params.time_period.end_date, payload.time_zone_name).utc().format()
      }
      let endDate = params.time_period.end_date
      filter = [...filter, {
        range: {
          first_deposit_amount_date_time: {
            from: params.time_period.start_date,
            include_lower: true,
            to: endDate.replace("000000", "999999"),
            include_upper: true
          }
        }
      }]
    }
  }
// console.log("===========",filter)
// return

  if (mustArray.length > 0) {
    query = mustArray
  } else {
    query = {
      match_all: {}
    }
  }

  if (filter.length > 0) {
    filter = {
      bool: {
        filter: filter
      }
    }
  }
  let sortData = {}
  let sortOrder = payload?.order ? payload.order : 'ASC'
  if (payload?.sort_by) {
    let sortKey = payload.sort_by
    sortData[sortKey] = sortOrder
  }


  try {
    let searchDetails
    let limit = 1000
    let page = 0
    let offset = page * limit

    searchDetails = await esClient.search({
      index: config.getProperties().es_index.users_index_name,
      body: {
        query: {
          bool: {
            must: query,
            filter: filter
          }
        },
        sort: sortData,
        timeout: '3000s',
        track_total_hits: true,
        size: limit,
        from: offset
      }
    })
    // console.log("=========", searchDetails.body.hits.hits)
    // return
    const uuid = uuidv4().replace(/-/g, '')
    const filePath = `/tmp/transaction_${uuid}.csv`
    const csvWriter = createCsvWriter({
      path: filePath,
      header: [
        { id: 'player_id', title: 'Player Id' },
        { id: 'user_name', title: 'User Name' },
        { id: 'status', title: 'Status' },
        { id: 'agent_name', title: 'Agent Name' },
        { id: 'country', title: 'Country' },
        { id: 'vip_level', title: 'Vip Level' },
        { id: 'total_balance', title: 'Current Total Balance' },
        { id: 'first_deposit_amount', title: 'First Deposit Amount' },
        { id: 'first_deposit_amount_txn_id', title: 'First Deposit Amount Trx Id' },
        { id: 'first_deposit_date_time', title: 'First Deposit Date Time' },
        { id: 'last_login_date', title: 'Last Login Date' },
        { id: 'currency', title: 'Currency' },
        { id: 'phone', title: 'Phone' },
      ]
    });
    const csvData = searchDetails.body.hits.hits

    if (payload.time_zone_name) {
      const csvDataList = await Promise.all(
        csvData.map(async object => {
          // Formatting createdAt date with optional user timezone
          const createdDate = payload.time_zone_name ? moment.tz(object._source.creation_date, payload.time_zone_name) : moment(object._source.creation_date)
          if(object._source.last_login){
            const lastLoginDate = payload.time_zone_name ? moment.tz(object._source.last_login, payload.time_zone_name) : moment(object._source.last_login)
            object._source.last_login = lastLoginDate.format('DD-MM-YYYY HH:mm:ss')
          }
          if ('first_deposit_amount_date_time' in object._source && object._source.first_deposit_amount_date_time) {
            const firstDepositDate = payload.time_zone_name ? moment.tz(object._source.first_deposit_amount_date_time, payload.time_zone_name) : moment(object._source.first_deposit_amount_date_time)
            object._source.first_deposit_amount_date_time = firstDepositDate.format('DD-MM-YYYY HH:mm:ss')
          }
          object._source.creation_date = createdDate.format('DD-MM-YYYY HH:mm:ss')
          return object
        })
      )
    }

    let mainArr = []
    let tenantId
    if (csvData.length > 0) {
      tenantId = csvData[0]._source.tenant_id
      for (const txn of csvData) {
        const object = {
          player_id: txn._source.player_id_s,
          user_name: txn._source.user_name,
          agent_name: txn._source.agent_name,
          vip_level: txn._source.vip_level,
          total_balance: txn?._source?.total_balance ? parseFloat(txn._source.total_balance).toFixed(2) : 0,
          currency: txn._source.currency,
          total_bets: parseInt(txn._source.total_bets) + parseInt(txn._source.total_sport_bets),
          country: txn._source.country,
          last_login_date: getDateInStringFormat(txn._source.last_login),
          first_deposit_amount: txn._source.first_deposit_amount,
          first_deposit_amount_txn_id: txn._source.first_deposit_amount_transaction_id,
          first_deposit_date_time: getDateInStringFormat(txn._source.first_deposit_amount_date_time),
          status: txn._source.status,
          phone: txn._source.phone,
        }
        mainArr = [...mainArr, object]
      }
    }
    await Promise.all([csvWriter.writeRecords(mainArr)])

    const totalData = searchDetails.body.hits.total.value
    let itval = parseFloat(totalData) / limit
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    for (let i = 1; i < itval; i++) {
      await delay(1000)

      offset = i * limit
      searchDetails = //await Promise.all([
        await esClient.search({
          index: config.getProperties().es_index.users_index_name,
          body: {
            query: {
              bool: {
                must: query,
                filter: filter
              }
            },
            sort: sortData,
            timeout: '3000s',
            track_total_hits: true,
            size: limit,
            from: offset
          }
        })
      //])
      let mainArr = []
      let tenantId
      const csvData = searchDetails.body.hits.hits
      if (payload.time_zone_name) {
        const csvDataList = await Promise.all(
          csvData.map(async object => {
            // Formatting createdAt date with optional user timezone
            const createdDate = payload.time_zone_name ? moment.tz(object._source.creation_date, payload.time_zone_name) : moment(object._source.creation_date)
            if(object._source.last_login){
              const lastLoginDate = payload.time_zone_name ? moment.tz(object._source.last_login, payload.time_zone_name) : moment(object._source.last_login)
              object._source.last_login = lastLoginDate.format('DD-MM-YYYY HH:mm:ss')
            }
            if ('first_deposit_amount_date_time' in object._source && object._source.first_deposit_amount_date_time) {
              const firstDepositDate = payload.time_zone_name ? moment.tz(object._source.first_deposit_amount_date_time, payload.time_zone_name) : moment(object._source.first_deposit_amount_date_time)
              object._source.first_deposit_amount_date_time = firstDepositDate.format('DD-MM-YYYY HH:mm:ss')
            }
            object._source.creation_date = createdDate.format('DD-MM-YYYY HH:mm:ss')
            return object
          })
        )
      }
      if (csvData.length > 0) {
        tenantId = csvData[0]._source.tenant_id
        for (const txn of csvData) {
          const object = {
            player_id: txn._source.player_id_s,
            user_name: txn._source.user_name,
            agent_name: txn._source.agent_name,
            vip_level: txn._source.vip_level,
            total_balance: txn?._source?.total_balance ? parseFloat(txn._source.total_balance).toFixed(2) : 0,
            currency: txn._source.currency,
            country: txn._source.country,
            last_login_date: getDateInStringFormat(txn._source.last_login),
            first_deposit_amount: txn._source.first_deposit_amount,
            first_deposit_amount_txn_id: txn._source.first_deposit_amount_transaction_id,
            first_deposit_date_time: getDateInStringFormat(txn._source.first_deposit_amount_date_time),
            status: txn._source.status,
            phone: txn._source.phone,
          }
          mainArr = [...mainArr, object]
        }

        await Promise.all([csvWriter.writeRecords(mainArr)])
      }
    }


    //   //upload file to s3
    const s3Config = config.getProperties().s3
    const fileContent = await fs.promises.readFile(filePath);
    const key = `tenants/${tenantId}/csv/casino_transaction/transaction_${uuid}.csv`

    const s3Params = {
      ACL: 'public-read',
      Bucket: s3Config.bucket,
      Key: key,
      Body: fileContent
    }
    const uploadedFile = await s3.upload(s3Params).promise()
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    fs.unlink(filePath, (err) => {
      if (err) {
        throw err
      }
    });

    return true
  } catch (error) {
    console.log("=======errorr", error)
    throw error
  }
}
