import { PutObjectCommand } from '@aws-sdk/client-s3';
import { createObjectCsvStringifier } from 'csv-writer';
import { QueryTypes } from 'sequelize';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import config from '../../../configs/app.config';
import db, { sequelize } from '../../../db/models';
import { s3 } from '../../../libs/awsS3ConfigV2';
import { EXPORT_CSV_STATUS } from '../../constants';


// Function to fill empty fields with 'N/A'
const fillEmptyFields = (record) => {
  const fields = [
    "id",
    "user_name",
    "status",
    "remark",
  ]

  fields.forEach(field => {
    if (record[field] === null || record[field] === undefined || record[field] === '') {
      record[field] = 'N/A';
    }
  });

  return record;
};

export default async (data) => {
  await db.ExportCsvCenter.update(
    { status: EXPORT_CSV_STATUS.IN_PROGRESS },
    { where: { id: data.id } }
  );

  let params = { tenantId: 0, adminId: data.adminId };

  if (data.adminType === 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: { id: data.adminId },
      attributes: ['tenantId'],
    });

    params.tenantId = adminData.tenantId;
  }

  try {
    const csvUuid = uuidv4().replace(/-/g, '');
    const csvStringifier = createObjectCsvStringifier({
      header: [
        { id: 'id', title: 'ID' },
        { id: 'user_name', title: 'Username' },
        { id: 'status', title: 'Status' },
        { id: 'remark', title: 'Remark' },
      ]
    })

    let sql = `
       SELECT
        wr.id,
        users.user_name,
        wr.remark,
        wr.status
        FROM
            deposit_requests wr
        LEFT JOIN
          users ON users.id = wr.user_id AND users.demo = 'false'
              WHERE  wr.tenant_id = ${params.tenantId} and wr.verify_status = 'verified'
      `;

    sql += ` ORDER BY wr.id DESC LIMIT 10`;

    const depositRequests = await sequelize.query(sql, {
      type: QueryTypes.SELECT,
      replacements: { tenant_id: params.tenantId || null }
    });

    // Fill empty fields with 'N/A'
    const processedWithdrawRequests = depositRequests.map(fillEmptyFields);

    const s3Config = config.getProperties().s3;
    const key = `tenants/${params.tenantId}/csv/deposit_requests/deposit_requests_${csvUuid}.csv`;

    // Convert data to CSV format
    const csvData = csvStringifier.stringifyRecords(processedWithdrawRequests)

    const finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with data

    // Create a readable stream of the final CSV content
    const stream = Readable.from([finalCsvContent])
    const contentLength = Buffer.byteLength(finalCsvContent)

    // Upload the CSV file to S3
    const uploadFileParams = {
      Bucket: s3Config.bucket,
      Key: key,
      Body: stream,
      ContentType: 'text/csv',
      ContentLength: contentLength,
    };

    await s3.send(new PutObjectCommand(uploadFileParams))

    await db.ExportCsvCenter.update(
      {
        csvUrl: key,
        status: EXPORT_CSV_STATUS.DONE,
      },
      {
        where: { id: data.id },
      }
    );

    return { csvUrl: key };

  } catch (error) {
    throw error;
  }
};
