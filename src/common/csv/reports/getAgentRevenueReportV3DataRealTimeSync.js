import moment from 'moment';
import { QueryTypes } from 'sequelize';
import config from '../../../configs/app.config';
import { sequelize } from '../../../db/models';
import { GET_ST8_SPORTS_ACTIONS, SPORTS_PROVIDERS_STAGE, SPORTS_PROVIDER_PROD } from '../../constants';
import { calculateNGRForTenantRealtime, getAgentWiseRealTimeQuery, getTenantWiseRealTimeQuery } from './agentRevenueReportV3RealTimeSyncHelpers';
import getStartEndDates from './getStartEndDates';

export default async (payload, params) => {
  let timePeriod = payload.datetime || {}
  const dateFormat = 'YYYY-MM-DDTHH:mm:ss';
  let startDate;
  let endDate;
  if (!Object.keys(timePeriod).length) {
    timePeriod = await getStartEndDates(timeType)
  }
  startDate = moment.utc(timePeriod.start_date).format(dateFormat) + '.000000Z'
  endDate = moment.utc(timePeriod.end_date).format(dateFormat) + '.999999Z'

  let tenantFilter, currencyFilter, date, userFilter, agentIdList, actionFilter,
  sportsFilter, actionFilterComission, ggrFilter, ngrCal, ggrCal, ggrNgrFilter,
  ngrSuperCal, ggrSuperCal, gameType, ggrNgrFilterTotal, regDate, currencyRegFilter,
  agentType, agentAction, parentList, commissionRow;

  tenantFilter = currencyFilter = date = userFilter = agentIdList = actionFilter =
  sportsFilter = actionFilterComission = ggrFilter = ngrCal = ggrCal = ggrNgrFilter =
  ngrSuperCal = ggrSuperCal = gameType = ggrNgrFilterTotal = regDate = currencyRegFilter
  = agentType = agentAction = parentList = commissionRow = '';

  let gameProvider = '';

  let providerIds = Object.values(config.get('env') === 'production' ? SPORTS_PROVIDER_PROD : SPORTS_PROVIDERS_STAGE).join(',');

  let botUserIdFilter, botsql;
  if (params.isSuperAdmin || payload.checkSboRequest === true) {
    botsql = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
    if (payload.playerCategory === 'bot_players') {
      botUserIdFilter = " and  bu.id is not null";
    } else if (payload.playerCategory === 'real_players') {
      botUserIdFilter = " and bu.id is null";
    }
  } else if (payload.checkSboRequest === false) {
    botsql = 'LEFT JOIN bot_users bu ON bu.user_id = u.id'
    botUserIdFilter = " and bu.id is null";
  }
  params.playerCategory = payload.playerCategory

  if (params.tenantId != 0) {
    if (Array.isArray(params.tenantId)) {
      const tenantIds = params.tenantId.map(id => parseInt(id)).join(',')
      tenantFilter = `and tenant_id IN (${tenantIds})`;
    } else {
      tenantFilter = `and tenant_id = ${parseInt(params.tenantId)}`;
    }
  }
  if (params.agentId && params.agentId !== 0) {
    // Convert agentId to a comma-separated string if it's an array
    const agentIds = Array.isArray(params.agentId) ? params.agentId.map(Number).join(',') : params.agentId;

    const sql = `
      WITH RECURSIVE parents AS (
        SELECT id, tenant_id
        FROM admin_users
        WHERE (id != parent_id OR parent_id IS NULL)
        AND id IN (${agentIds})
        AND tenant_id = ${params.tenantId}
        UNION ALL
        SELECT child.id, child.tenant_id
        FROM admin_users child
        JOIN parents parent ON parent.id = child.parent_id
          AND parent.tenant_id = child.tenant_id
        WHERE child.parent_type = 'AdminUser'
        AND parent.id != child.id
      )
      SELECT id FROM parents
    `;
    // Execute the raw SQL query
    const ids = await sequelize.query(sql, { type: QueryTypes.SELECT });

    // Extract the IDs into an array
    const idArray = ids.map((item) => item.id);

    if (idArray.length > 0) {
      const idList = idArray.join(',');
      agentIdList = `and au.id in (${idList})`;
      parentList = `where parent_id in (${idList})`;
    }
  }
  if (params.currencyId && !isNaN(params.currencyId)) {
    currencyRegFilter = `where u.currency_id = ${parseInt(params.currencyId, 10)}`;
    currencyFilter = `and w.currency_id = ${parseInt(params.currencyId, 10)}`;
  }
  if (params.agentType && !isNaN(params.agentType)) {
    agentType = `and agent_type = ${parseInt(params.agentType, 10)}`;
  }
  if (params.agentAction) {
    agentAction = `and agent_action = '${params.agentAction.replace(/'/g, "''")}'`;
  }
  if (params.action_category) {
    const actionCategory = params.action_category.toLowerCase();

    if (actionCategory === 'casino') {
      actionFilter = `and (
        (coalesce(provider_id, 0) not in (${providerIds})
          and coalesce(seat_id, '') not in ('bti_sportsbook', 'sbs_sportsbook', 'sap_lobby') and transaction_type not in(69,76)) or
        transaction_type in (5, 11, 12, 15, 17, 37, 41, 44, 48, 52, 6, 39, 40, 42, 43, 49, 50, 51, 45, 3, 4,68, 70, 71 ,72 ,73, 74, 75)
      )`;


      actionFilterComission = `and (
        (coalesce(provider_id, 0) not in (${providerIds})
          and coalesce(seat_id, '') not in ('bti_sportsbook', 'sbs_sportsbook', 'sap_lobby')) or
        transaction_type in (5, 6, 48, 49, 44)
      )`;
    } else if (actionCategory === 'sports') {
      actionFilter = `and (
        (provider_id in (${providerIds})
          or seat_id in ('bti_sportsbook', 'sbs_sportsbook', 'sap_lobby')) or
        transaction_type in (5, 11, 12, 15, 17, 37, 41, 44, 48, 52, 6, 39, 40, 42, 43, 49, 50, 51, 45, 3, 4, 69, 70, 71, 72, 73, 74,76)
      )`;


      actionFilterComission = `and (
        (provider_id in (${providerIds}) or
          seat_id in ('bti_sportsbook', 'sbs_sportsbook', 'sap_lobby')) or
        transaction_type in (5, 6, 48, 49, 44))`;
      sportsFilter = `
        having
          coalesce(sum(case when type = 'bet' then t.count else 0 end), 0)
            - coalesce(sum(case when type = 'bet_refund' then t.count else 0 end), 0) > 0`;
    }
  }
  if (params.provider_id && params.provider_id.length > 0) {
    const providerIdsLower = params.provider_id.map(val => val.toLowerCase().replace(/'/g, "''")); // Handle apostrophes
    const providerIdsStr = providerIdsLower.map((id) => `'${id}'`).join(",");

    // Query to get provider IDs
    const providerList = await sequelize.query(
      `SELECT id FROM casino_providers WHERE lower(name) IN (${providerIdsStr})`,
      { type: QueryTypes.SELECT }
    );
    const providerIds = providerList.map(provider => provider.id);

    let providerCondition = '';
    if (providerIds.length > 0) {
      const id = providerIds.join(',');
      providerCondition = `provider_id in (${id})`;
    }

    // Query to get seat IDs
    const transactionSeatIds = await sequelize.query(
      `SELECT seat_ids FROM transactions_providers_list WHERE lower(title) IN (${providerIdsStr})`,
      { type: QueryTypes.SELECT }
    );

    // Flatten and process seat IDs
    const seatIds = transactionSeatIds.flatMap((row) =>
      row.seat_ids.map((id) => id.replace(/'/g, "''").toLowerCase())
    );

    const providerToGameId = GET_ST8_SPORTS_ACTIONS;
    for (const [provider, gameId] of Object.entries(providerToGameId)) {
      if (providerIdsLower.includes(provider.toLowerCase())) {
        seatIds.push(gameId.toLowerCase());
      }
    }

    let gameCondition = "";
    if (seatIds.length > 0) {
      const quotedSeatIds = seatIds.map((id) => `'${id}'`).join(",");
      gameCondition = `lower(seat_id::text) IN (${quotedSeatIds})`;
    }

    if (providerCondition && gameCondition) {
      // If both conditions are present, use OR
      gameProvider += ` and (${providerCondition} OR ${gameCondition})`;
    } else if (providerCondition) {
      // Only provider condition, use AND
      gameProvider += ` and ${providerCondition}`;
    } else if (gameCondition) {
      // Only game condition, use AND
      gameProvider += ` and ${gameCondition}`;
    }
    sportsFilter = `
      having coalesce(
        sum(case when type = 'bet' then t.count else 0 end), 0
      ) - coalesce(
        sum(case when type = 'bet_refund' then t.count else 0 end), 0
      ) > 0
    `;
  }
  if (params.game_type && params.game_type.length > 0) {
    params.game_type = params.game_type.map(x => "'" + x.trim().replace(/'/g, "''").toLowerCase() + "'");
    gameType = `
      and (
        seat_id::text IN (${params.game_type})
        OR game_id::text IN (${params.game_type})
        OR table_id::text IN (${params.game_type})
      )
    `;
  }
  if (params.search && !isNaN(params.search)) {
    userFilter = `and w.owner_id = ${parseInt(params.search, 10)}`;
  }
  if (startDate && endDate) {
    date = `created_at BETWEEN '${startDate}' AND '${endDate}'`;
    regDate = `and u.created_at BETWEEN '${startDate}' AND '${endDate}'`;
  }
  if (params.isAgentLoggedIn) {
    commissionRow = `
      case when t.agent_id = ${params.loggedInUserId}
        then min(adus.value::numeric(5,2))::numeric(5,2)
        else min(100 - adus.value::numeric(5,2))::numeric(5,2) end`;
  } else {
    commissionRow = "min(100 - adus.value::numeric(5,2))::numeric(5,2)";
  }

  const ngrnewFormula = calculateNGRForTenantRealtime(params.tenantId, date, tenantFilter, actionFilter, gameProvider, gameType, commissionRow);
  const qParams = {
    tenant_filter: tenantFilter,
    currency_filter: currencyFilter,
    currency_reg_filter: currencyRegFilter,
    agent_type: agentType,
    date: date,
    reg_date: regDate,
    user_filter: userFilter,
    agent_action: agentAction,
    ngr_cal: ngrCal,
    ggr_cal: ggrCal,
    ggr_ngr_filter: ggrNgrFilter,
    ggr_ngr_filter_total: ggrNgrFilterTotal,
    ngr_super_cal: ngrSuperCal,
    ggr_super_cal: ggrSuperCal,
    agent_ids: agentIdList,
    parent_id: parentList,
    action_filter: actionFilter,
    action_filter_comission: actionFilterComission,
    sort_by: params.sortBy,
    order: params.sortOrder,
    sports_filter: sportsFilter,
    game_provider: gameProvider,
    game_type: gameType,
    commission_row: commissionRow,
    bot_sql: botsql || '',
    bot_user_id_filter: botUserIdFilter || '',
    player_type: params.playerCategory || '',
    add_noncash_sql:ngrnewFormula.addSql ?? '',
    add_super_noncash_sql:ngrnewFormula.addSuperSql ?? '',
    ngr_formula:ngrnewFormula ?? ''
  };

  let mainQ;
  if (agentIdList) {
    mainQ = getAgentWiseRealTimeQuery(qParams);
  } else {
    mainQ = getTenantWiseRealTimeQuery(qParams);
  }

  return await sequelize.query(mainQ, {
    type: QueryTypes.SELECT
  })

};
