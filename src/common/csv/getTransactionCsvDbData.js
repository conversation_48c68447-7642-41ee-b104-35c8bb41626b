import { GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3'
import { createObjectCsvStringifier } from 'csv-writer'
import moment from 'moment'
import { QueryTypes } from 'sequelize'
import { Readable } from 'stream'
import { v4 as uuidv4 } from 'uuid'
import config from '../../configs/app.config'
import db, { sequelize } from '../../db/models'
import { s3 } from '../../libs/awsS3ConfigV2'
import { CHECK_SBO_DOMAIN } from '../../utils/constants/constant'
import { BOT_ALLOWED_TENANTS, CASINO_PROVIDER_IDS_ARRAY, EXPORT_CSV_STATUS, EXPORT_CSV_TYPE, GAME_ID_PROVIDER_IDS_ARRAY, QUEUE_WORKER_CONSTANT, SPORT_PROVIDER, SPORT_PROVIDER_IDS_ARRAY, ST8_PROVIDER_ID, ST8_SPORTS_SEAT_IDS_ARRAY, TABLE_ID_PROVIDERS_ARRAY } from '../constants'
import { expandBonusComment, fetchAgentIds, formatAmount, getCurrencyById, getCurrencyByValue, getSportsMarkets, getTransactionTypeByValueOrId } from '../helpers'
import pushInQueue from '../pushInQueue'
import getRolesDetails from './getAdminRolesDetail'
import { getColumnPermissions } from './getPermissionsForPlayerCsv'
import { getDateRange } from './reports/getStartEndDates'

export default async (data) => {
  try {
    const payload = data.payload
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.IN_PROGRESS
    },
      {
        where: { id: data.id }
      }
    )

    const currentUser = { id: data.adminId, parentType: data.adminType }
    const checkSboRequest = payload.sbo_request
    if (payload.timeZoneName === 'UTC +00:00') {
      payload.timeZoneName = 'UTC'
    }
    payload.searchId = payload.id || null

    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
    let isGameProviderAndTypeFilterApplied = false
    let addDepositRequestJoin = true
    let userFilterApplied = false
    let isSuperAdmin = false
    let isAgentLogin = false

    if (payload.playerId && !isNaN(payload.playerId)) {
      payload.userId = payload.playerId
    }

    const inputParams = {
      tenantIds: [],
      startDate: '',
      endDate: '',
      agentIds: [],
      subAgentIds: [],
      searchId: payload.searchId,
      currencyId: payload.currencyId ? getCurrencyByValue(payload.currencyId)?.id : payload.currencyId,
      actionType: [],
      actionCategory: payload.actionCategory,
      gameProvider: payload.gameProvider,
      amount: payload.amount,
      totalsCurrency: '',
      transactionId: payload.transactionId,
      debitTransactionId: payload.debitTransactionId,
      roundId: payload.roundId,
      transactionIdFromUtrNumber: '',
      userId: payload.userId,
      sportProviders: SPORT_PROVIDER_IDS_ARRAY,
      casinoProviders: CASINO_PROVIDER_IDS_ARRAY,
      gameIdProviders: GAME_ID_PROVIDER_IDS_ARRAY,
      st8ProviderId: ST8_PROVIDER_ID,
      tableIdProviders: TABLE_ID_PROVIDERS_ARRAY
    }

    if (data.adminType == 'AdminUser') {
      const adminData = await db.AdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['tenantId']
      })

      payload.tenantId = adminData.tenantId
    }

    // Tenant Filter Gathering
    const tenantId = Number(payload.tenantId)

    if (tenantId) {
      inputParams.tenantIds = [tenantId]
    } else if (data.adminType == 'SuperAdminUser') {
      const adminData = await db.SuperAdminUser.findOne({
        where: {
          id: data.adminId
        },
        attributes: ['parentType', 'tenantIds']
      })
      if (adminData.parentType === 'Manager') {
        inputParams.tenantIds = adminData.tenantIds.map(id => parseInt(id))
      }
    }

    // Agent Filter Gathering
    payload.agentId = isNaN(payload.agentId) ? '' : Number(payload.agentId)

    let searchAgentId
    let searchAgentWalletIds
    if (payload.agentId) {
      searchAgentId = payload.agentId
    } else if (data.adminType === 'AdminUser') {
      const roles = await getRolesDetails(currentUser.id)
      if (roles.includes('agent')) {
        searchAgentId = currentUser.id
        isAgentLogin = true
      }
    }
    if (searchAgentId) {
      inputParams.agentIds = await fetchAgentIds(searchAgentId)
      const searchAgentWalletsData = await sequelize.query(`
        SELECT
          id AS wallet_id
        FROM
          wallets
        WHERE
          owner_type = 'AdminUser' and owner_id IN (:ownerIds)
      `, {
        type: QueryTypes.SELECT,
        replacements: { ownerIds: inputParams.agentIds }
      })
      searchAgentWalletIds = searchAgentWalletsData.map(x => x.wallet_id)
    }

    if (currentUser.parentType === 'AdminUser') {
      const currentUserRole = await getRolesDetails(currentUser.id)
      if (currentUserRole.includes('agent')) {
        inputParams.subAgentIds = await fetchAgentIds(currentUser.id)
        isAgentLogin = true
      }
    }

    // Time Filter Gathering
    payload.timeType = 'custom'
    if (payload.timeType === 'custom') {
      payload.timePeriod = JSON.parse(payload.timePeriod)
    }
    const { start, end } = getDateRange(payload.timeType, payload.timeZoneName, payload.timePeriod.startDate, payload.timePeriod.endDate)
    inputParams.startDate = start.utc().format('YYYY-MM-DD HH:mm:ss').concat('.000000')
    inputParams.endDate = end.utc().format('YYYY-MM-DD HH:mm:ss').concat('.999999')

    // Action Type Filter Gathering
    if (payload.actionType) {
      const actionTypeArr = JSON.parse(payload.actionType)
      inputParams.actionType = await getTransactionTypeByValueOrId(actionTypeArr)
    }
    // Game Type Filter Gathering
    if (['Jetfair', 'Powerplay', 'Bti Sportsbook', 'Sap Exchange', 'Saba Sportsbook'].includes(payload.gameProvider)) {
      inputParams.gameType = ''
    }

    const txsUserDataCteFilters = []
    const gameTxsCteFilters = []

    // Check SBO User Logic
    isSuperAdmin = currentUser.parentType === 'SuperAdminUser'
    let botUsersFilterType = 'all'
    let botWalletIds = []
    if (
      (tenantId && BOT_ALLOWED_TENANTS.includes(String(tenantId))) ||
      isSuperAdmin
    ) {
      // In case of super admin, do not check SBO request flag. Just check player category flag.
      if (
        isSuperAdmin ||
        (checkSboRequest === CHECK_SBO_DOMAIN[1] && checkSboRequest !== CHECK_SBO_DOMAIN[2])
      ) {
        if (payload.playerCategory === 'bot_players') {
          botUsersFilterType = 'bot'
        } else if (payload.playerCategory === 'real_players') {
          botUsersFilterType = 'real'
        }
      } else if (!checkSboRequest) {
        botUsersFilterType = 'real'
      }

      if (botUsersFilterType !== 'all') {
        // Find all bot users wallet Id
        botWalletIds = await sequelize.query(`
          SELECT
            w.id AS wallet_id
          FROM
            bot_users bu
            JOIN wallets w ON (w.owner_id = bu.user_id AND w.owner_type = 'User')
          ${tenantId ? 'WHERE bu.tenant_id = :tenantId' : ''}
        `, {
          type: QueryTypes.SELECT,
          replacements: { tenantId }
        })
        botWalletIds = botWalletIds.map(rec => rec.wallet_id)

        if (botWalletIds.length > 0) {
          if (botUsersFilterType === 'bot') {
            gameTxsCteFilters.push(`(source_wallet_id IN (${botWalletIds}) OR target_wallet_id IN (${botWalletIds}))`)
          } else if (botUsersFilterType === 'real') {
            gameTxsCteFilters.push(`COALESCE(source_wallet_id, 0) NOT IN (${botWalletIds}) AND COALESCE(target_wallet_id, 0) NOT IN (${botWalletIds})`)
          }
        }
      }
    }

    // User Id Filter
    if (payload.userId && !isNaN(payload.userId)) {
      let agentCond = ''
      if (inputParams.agentIds.length > 0) {
        agentCond = 'u.parent_id IN (:agentIds)'
      }
      if (inputParams.subAgentIds.length > 0) {
        agentCond = 'u.parent_id IN (:subAgentIds)' // Extra check in case of agent login to validate if the payload agent Id should be from his sub-agents
      }
      const botUserCondMap = {
        bot: 'bu.id IS NOT NULL',
        real: 'bu.id IS NULL',
        all: ''
      }
      const userData = await sequelize.query(`
        SELECT
          w.id AS wallet_id
        FROM
          wallets w
          JOIN users u ON (w.owner_id = u.id)
          LEFT JOIN bot_users bu ON (bu.user_id = w.owner_id)
        WHERE
          w.owner_id = :userId
          AND w.owner_type = 'User'
          ${agentCond ? 'AND ' + agentCond : ''}
          ${botUserCondMap[botUsersFilterType] ? 'AND ' + botUserCondMap[botUsersFilterType] : ''}
      `, {
        type: QueryTypes.SELECT,
        replacements: { userId: payload.userId, agentIds: inputParams.agentIds, subAgentIds: inputParams.subAgentIds }
      })
      if (!userData || userData.length <= 0) {
        throw new Error('Data not found!')
      }
      gameTxsCteFilters.push(`(source_wallet_id = '${userData[0].wallet_id}' OR target_wallet_id = '${userData[0].wallet_id}')`)
      userFilterApplied = true
    }

    // UTR filter
    // Fetch transaction id from UTR number
    if (payload.utrNumber) {
      const transactionIdArr = await sequelize.query(`
        SELECT
          tracking_id
        FROM
          deposit_requests
        WHERE
          utr_number = :utrNumber
        LIMIT 1;
      `, {
        type: QueryTypes.SELECT,
        replacements: { utrNumber: payload.utrNumber }
      })

      const transactionId = transactionIdArr?.[0]?.tracking_id

      if (!transactionId) {
        throw new Error('Data not found!')
      }
      inputParams.transactionIdFromUtrNumber = transactionId
    }

    if (inputParams.startDate && inputParams.endDate) {
      gameTxsCteFilters.push('created_at BETWEEN :startDate AND :endDate')
    }
    if (inputParams.actionType.length > 0) {
      gameTxsCteFilters.push('transaction_type IN (:actionType)')
    }
    if (payload.debitTransactionId) {
      gameTxsCteFilters.push('debit_transaction_id = :debitTransactionId')
    }
    if (payload.transactionId) {
      gameTxsCteFilters.push('transaction_id = :transactionId')
    }
    if (inputParams.transactionIdFromUtrNumber) {
      gameTxsCteFilters.push('transaction_id = :transactionIdFromUtrNumber')
    }
    if (payload.roundId) {
      gameTxsCteFilters.push('round_id = :roundId')
    }
    if (payload.amount && !isNaN(payload.amount)) {
      gameTxsCteFilters.push('amount BETWEEN :amount - 0.05 AND :amount + 0.05')
    }
    if (payload.searchId && !isNaN(payload.searchId)) {
      gameTxsCteFilters.push('id = :searchId')
    }
    if (inputParams.currencyId) {
      gameTxsCteFilters.push('(source_currency_id = :currencyId OR target_currency_id = :currencyId)')
    }

    if (!userFilterApplied && inputParams.agentIds.length > 0) {
      let agentFilter
      if (inputParams.subAgentIds.length > 0) {
        agentFilter = '(u.parent_id IN (:agentIds) AND u.parent_id IN (:subAgentIds))' // Extra check in case of agent login to validate if the payload agent Id should be from his sub-agents
      } else {
        agentFilter = 'u.parent_id IN (:agentIds)'
      }
      if (searchAgentWalletIds?.length > 0) {
        agentFilter = agentFilter + ` OR source_wallet_id IN (${searchAgentWalletIds}) OR target_wallet_id IN (${searchAgentWalletIds})`
      }
      txsUserDataCteFilters.push('(' + agentFilter + ')')
    } else if (!userFilterApplied && inputParams.subAgentIds.length > 0) {
      let agentFilter = 'u.parent_id IN (:subAgentIds)'
      agentFilter = agentFilter + ` OR source_wallet_id IN (${searchAgentWalletIds}) OR target_wallet_id IN (${searchAgentWalletIds})`
      txsUserDataCteFilters.push('(' + agentFilter + ')')
    }
    if (inputParams.gameProvider) {
      // Query to get provider IDs
      let providerIds = await sequelize.query(`
        SELECT id FROM casino_providers WHERE name = :gameProvider
      `, { type: QueryTypes.SELECT, replacements: { gameProvider: inputParams.gameProvider } })

      providerIds = providerIds.map((row) => row.id)

      let providerCondition = ''
      if (providerIds.length > 0) {
        providerCondition = `provider_id IN (${providerIds})`
      }

      // Query to get transaction seat IDs
      let transactionSeatIds = await sequelize.query(`
        SELECT seat_ids FROM transactions_providers_list WHERE title = :gameProvider
      `, { type: QueryTypes.SELECT, replacements: { gameProvider: inputParams.gameProvider } })

      transactionSeatIds = transactionSeatIds[0]?.seat_ids || []

      let gameCondition = ''
      if (transactionSeatIds.length > 0) {
        const quotedSeatIds = transactionSeatIds.map(id => id.replace(/'/g, "''")).map(id => `'${id}'`).join(',')
        gameCondition = `seat_id IN (${quotedSeatIds})`
      }

      let finalProviderCondition = ''
      if (providerCondition && gameCondition) {
        finalProviderCondition = `(${providerCondition} OR ${gameCondition})`
      } else if (providerCondition) {
        finalProviderCondition = `${providerCondition}`
      } else if (gameCondition) {
        finalProviderCondition = `${gameCondition}`
      }
      if (finalProviderCondition) {
        isGameProviderAndTypeFilterApplied = true
        addDepositRequestJoin = false
        gameTxsCteFilters.push(finalProviderCondition)
      } else {
        throw new Error('Data not found!')
      }
    }
    if (payload.gameType) {
      // Fetch Provider IDs by Provider Name
      let providerIds = await sequelize.query(`
        SELECT id FROM casino_providers WHERE name = :gameProvider

        UNION ALL

        SELECT id FROM casino_providers
        WHERE
          name IN ('st8', 'Whitecliff')
          AND id IN (
            SELECT provider_id::bigint FROM transactions_providers_list WHERE title = :gameProvider
          )
      `, {
        type: QueryTypes.SELECT,
        replacements: { gameProvider: inputParams.gameProvider }
      })
      providerIds = providerIds.map(obj => obj.id)

      // Fetch all UUIDs by Provider ID and Game Name
      const uuidsDbData = await sequelize.query(`
        SELECT DISTINCT uuid FROM casino_items WHERE name = :gameType AND provider IN (:providerIds)
      `, {
        type: QueryTypes.SELECT,
        replacements: { gameType: payload.gameType, providerIds: providerIds }
      })
      const uuids = uuidsDbData.map(obj => obj.uuid.replace(/'/g, "''")).map(id => `'${id}'`)

      if (uuids.length) {
        // Case for Red Tiger and NetEnt Games
        if (providerIds.length > 1) {
          const uuidsFromDb = uuidsDbData.map(obj => obj.uuid)
          const tableIds = uuidsFromDb.filter(id => !isNaN(id))
          const seatIds = uuidsFromDb.filter(id => isNaN(id)).map(id => `'${id}'`)
          if (tableIds.length && seatIds.length) {
            gameTxsCteFilters.push(`(table_id IN (${tableIds}) OR seat_id IN (${seatIds}))`)
          } else if (tableIds.length) {
            gameTxsCteFilters.push(`table_id IN (${tableIds})`)
          } else {
            gameTxsCteFilters.push(`seat_id IN (${seatIds})`)
          }
        } else if (GAME_ID_PROVIDER_IDS_ARRAY.includes(providerIds[0])) {
          gameTxsCteFilters.push(`game_id IN (${uuids})`)
        } else if (TABLE_ID_PROVIDERS_ARRAY.includes(providerIds[0])) {
          gameTxsCteFilters.push(`table_id IN (${uuids})`)
        } else {
          gameTxsCteFilters.push(`seat_id IN (${uuids})`)
        }
        isGameProviderAndTypeFilterApplied = true
        addDepositRequestJoin = false
      }
    }
    if (inputParams.actionCategory && !isGameProviderAndTypeFilterApplied) {
      if (inputParams.actionCategory === 'financial') {
        gameTxsCteFilters.push('transaction_type IN (3, 4, 5, 6, 11, 12, 14, 15, 16, 17, 19, 37, 38, 39, 40, 41, 42, 43, 44, 45, 48, 49, 50, 51, 52, 59, 60, 68, 69, 70, 71, 72, 73, 74,75,76)')
      } else if (inputParams.actionCategory === 'sports') {
        addDepositRequestJoin = false
        gameTxsCteFilters.push('provider_id IN (:sportProviders)')
      } else if (inputParams.actionCategory === 'casino') {
        addDepositRequestJoin = false
        gameTxsCteFilters.push('provider_id IN (:casinoProviders)')
      }
    }
    if (inputParams.tenantIds.length > 0) {
      if (!userFilterApplied) {
        gameTxsCteFilters.push('tenant_id IN (:tenantIds)')
      }
    }

    const txsUserDataCteWhereCondition = txsUserDataCteFilters.length ? `WHERE ${txsUserDataCteFilters.join(' AND ')}` : ''
    const gameTxsCteWhereCondition = gameTxsCteFilters.length ? `WHERE ${gameTxsCteFilters.join(' AND ')}` : ''

    const sort = payload.sortBy || 'created_at'
    const sortOrder = payload.order || 'DESC'
    let sortCondition = ''
    let sortConditionWithAlias = ''
    if (sort && sortOrder) {
      sortCondition = 'ORDER BY ' + sort + ' ' + sortOrder
      sortConditionWithAlias = 'ORDER BY t.' + sort + ' ' + sortOrder
    }

    // Check UTR permission in Allowed Modules
    let UTREnabled = false
    if (!isSuperAdmin) {
      const UTREnabledData = await sequelize.query(`
        SELECT
          COUNT(*) AS count
        FROM
          tenant_theme_settings
        WHERE
          tenant_id = :tenantId
          AND 'utrNumber' = ANY(string_to_array(allowed_modules, ','));
      `, {
        type: QueryTypes.SELECT,
        replacements: { tenantId: inputParams.tenantIds[0] }
      })
      UTREnabled = Number(UTREnabledData[0].count) > 0
    }

    const ATTRIBUTE_PERMISSIONS = {
      id: null,
      created_at: ['transaction_attributes', 'created_at'],
      round_id: ['transaction_attributes', 'round_id'],
      transaction_id: ['transaction_attributes', 'operator_transaction_id'],
      debit_transaction_id: ['transaction_attributes', 'debit_transaction_id'],
      utr_number: ['transaction_attributes', 'utr_number'],
      from_wallet: ['players_key', 'before_balance'],
      from_currency: ['transaction_attributes', 'from_currency'],
      to_wallet: ['players_key', 'after_balance'],
      to_currency: ['transaction_attributes', 'to_currency'],
      from_before_balance: ['players_key', 'before_balance'],
      amount: ['players_key', 'total_balance'],
      to_after_balance: ['players_key', 'after_balance'],
      conversion_rate: ['transaction_attributes', 'conversion_rate'],
      action_type: ['transaction_attributes', 'action_type'],
      status: ['report_attributes', 'status'],
      comments: ['transaction_attributes', 'comments'],
      action_by: ['players_key', 'actionee'],
      ...((data.type === EXPORT_CSV_TYPE.ACCOUNT_SUMMARY_DB) && { table_id: ['report_attributes', 'table_id'] }),
      ...((data.type === EXPORT_CSV_TYPE.ACCOUNT_SUMMARY_DB) && { game_name: ['report_attributes', 'game_name'] }),
      ...((data.type === EXPORT_CSV_TYPE.ACCOUNT_SUMMARY_DB) && { game_provider: ['report_attributes', 'game_provider'] })
    }

    const COLUMN_TO_HEADER_MAP = {
      id: 'ID',
      created_at: 'Created At',
      round_id: 'Round ID',
      transaction_id: 'Operator Transaction ID',
      debit_transaction_id: 'Debit Transaction ID',
      utr_number: 'UTR Number',
      from_wallet: 'From Wallet',
      from_currency: 'From Currency',
      to_wallet: 'To Wallet',
      to_currency: 'To Currency',
      from_before_balance: 'From Before Balance',
      amount: 'Amount',
      to_after_balance: 'To After Balance',
      conversion_rate: 'Conversion Rate',
      action_type: 'Action Type',
      status: 'Status',
      comments: 'Comments',
      action_by: 'Action By',
      table_id: 'Table ID',
      game_name: 'Game Name',
      game_provider: 'Game Provider'
    }

    // Manage column permissions
    let finalAttributes = await getColumnPermissions(ATTRIBUTE_PERMISSIONS, currentUser.id, tenantId, currentUser.parentType)

    // Manage excluded permissions
    let hasUTRPermission = false
    finalAttributes = finalAttributes.filter(column => {
      if (column === 'utr_number') {
        if (!isAgentLogin && (isSuperAdmin || UTREnabled)) {
          hasUTRPermission = true
          return true
        } else {
          return false
        }
      }
      return true
    })

    const allowedColumnsObj = {}
    finalAttributes.forEach(attr => allowedColumnsObj[attr] = true)

    // Create CSV Writer
    let awsTenantFolderName = 0
    if (inputParams.tenantIds.length === 1) {
      awsTenantFolderName = inputParams.tenantIds[0]
    }

    // new code
    const chunkSize = 100000
    const awsConfig = config.getProperties()
    const s3Config = awsConfig.s3

    const uuid = uuidv4().replace(/-/g, '')
    const key = `tenants/${awsTenantFolderName}/csv/casino_transaction/transaction_${uuid}.csv`
    const csvStringifier = createObjectCsvStringifier({
      header: finalAttributes.map(columnKey => ({ id: columnKey, title: COLUMN_TO_HEADER_MAP[columnKey] }))
    })

    // Get count
    const gameTransactionReportCntQuery = prepareGameTransactionReportCountQuery({
      gameTxsCteWhereCondition,
      txsUserDataCteWhereCondition
    })
    const totalRecords = await sequelize.query(gameTransactionReportCntQuery, { type: sequelize.QueryTypes.SELECT, userMaster: false, replacements: inputParams })
    const totalRecordsCount = totalRecords[0]?.total_count || 0
    const totalChunks = Math.ceil(totalRecordsCount / chunkSize)

    if (totalRecordsCount > 100000) {
      const csvLogArr = []
      const multipleCsvObject = {
        csvId: data.id,
        status: 0
      }
      for (let i = 0; i < totalChunks; i++) {

        const isLastChunk = i === totalChunks - 1;
        const remainingRecords = totalRecordsCount - i * chunkSize;
        const currentChunkSize = isLastChunk ? remainingRecords : chunkSize;

        const key = `tenants/${awsTenantFolderName}/csv/casino_transaction/transaction_${uuid}_${i}.csv`;

        csvLogArr.push({
          ...multipleCsvObject,
          key,
          totalRecords: currentChunkSize  // 👈 Add record count here
        });
      }

      const multipleCsv = await db.MultipleCsv.bulkCreate(csvLogArr)
      let count = 0

      for (const csv of multipleCsv) {

        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.EXPORT_CSV,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [data.id],
          payload: {}
        }


        const chunkSize = 5000
        const totalChunks = Math.ceil(csv.totalRecords / chunkSize)
        for (let i = 0; i < totalChunks; i++) {
          const queueLogArr = []
          const payload = { id: csv.id, key: csv.key, offset: count + (i * chunkSize), chunkSize, totalRecordsCount: csv.totalRecords }
          queueLogArr.push({ ...queueLogObject, payload })
          const queueLogs = await db.QueueLog.bulkCreate(queueLogArr)

          for (const queue of queueLogs) {
            try {
              await delay(5000)
              await pushInQueue(queue.id)
            } catch (error) {
              console.log('==========error=========', error)
            }
          }
        }

        count = count + 100
      }




    } else {
      // Loop through each chunk
      for (let i = 0; i < totalChunks; i++) {
        const offset = i * chunkSize
        const paginationCondition = 'LIMIT ' + chunkSize + ' OFFSET ' + offset
        // Query preparation and fetch results
        const gameTransactionReportQuery = prepareGameTransactionReportQuery({
          gameTxsCteWhereCondition,
          txsUserDataCteWhereCondition,
          sortCondition,
          paginationCondition,
          sortConditionWithAlias,
          userFilterApplied
        })

        const reportData = await sequelize.query(gameTransactionReportQuery, { type: sequelize.QueryTypes.SELECT, userMaster: false, replacements: inputParams })

        // Set Game Name
        if (allowedColumnsObj.game_name) {
          let casinoItemUUIDs = reportData
            .filter(reportRec => (
              reportRec.provider_id &&
              reportRec.table_id &&
              !SPORT_PROVIDER_IDS_ARRAY.includes(String(reportRec.provider_id)) &&
              !ST8_SPORTS_SEAT_IDS_ARRAY.includes(reportRec.table_id)
            ))
            .map(reportRec => reportRec.table_id)
          casinoItemUUIDs = [...new Set(casinoItemUUIDs)]
          let uuidProviderNameMap = {}
          if (casinoItemUUIDs.length) {
            const uuidProviderNameMapData = await sequelize.query(`
            SELECT json_object_agg(uuid, provider_name_map) AS uuid_provider_name_map FROM (
              SELECT uuid, json_object_agg(provider, name) AS provider_name_map FROM (
                SELECT DISTINCT ON (uuid, provider) uuid, provider, name FROM casino_items WHERE uuid IN (:casinoItemUUIDs)
              ) S1 GROUP BY uuid
            ) S2;
          `, {
              type: QueryTypes.SELECT,
              replacements: { casinoItemUUIDs }
            })
            uuidProviderNameMap = uuidProviderNameMapData[0]?.uuid_provider_name_map
          }

          if (uuidProviderNameMap) {
            reportData.forEach(reportRec => {
              if (
                reportRec.provider_id &&
                reportRec.table_id &&
                !SPORT_PROVIDER_IDS_ARRAY.includes(String(reportRec.provider_id)) &&
                !ST8_SPORTS_SEAT_IDS_ARRAY.includes(reportRec.table_id)
              ) {
                reportRec.game_name = uuidProviderNameMap[reportRec.table_id]?.[reportRec.provider_id] || '-'
              } else {
                reportRec.game_name = reportRec.seat_id
              }
            })
          }
        }

        // Get Market Name
        await fetchSportMarketNames(reportData);

        // Get UTR Number and Transaction Receipt
        if (allowedColumnsObj.utr_number && addDepositRequestJoin) {
          // Only consider for deposit type transactions
          const transactionIds = []
          const tenantIds = new Set()
          reportData.forEach(rec => {
            if (rec.transaction_type === 3) {
              tenantIds.add(rec.tenant_id)
              if (rec.transaction_id) { transactionIds.push(rec.transaction_id) }
            }
          })
          const [utrNumberData] = await Promise.all([
            transactionIds.length && hasUTRPermission
              ? (
                sequelize.query(`
                SELECT
                  JSON_OBJECT_AGG(
                    tracking_id,
                    utr_number
                  ) AS transaction_id_utr_map
                FROM
                  deposit_requests
                WHERE tracking_id IN (:transactionIds) AND tenant_id IN (:tenantIds);
              `, {
                  type: QueryTypes.SELECT,
                  replacements: { transactionIds, tenantIds: [...new Set(tenantIds)] }
                })
              )
              : null
          ])
          const utrNumberDataMapping = utrNumberData?.[0]?.transaction_id_utr_map

          if (utrNumberDataMapping && Object.keys(utrNumberDataMapping).length >= 1) {
            reportData.forEach(rec => {
              rec.utr_number = utrNumberDataMapping[rec.transaction_id] || null
            })
          }
        }

        let mainArr = []
        for (const report of reportData) {
          if (payload.timeZoneName) {
            report.created_at = moment.utc(report.created_at).tz(payload.timeZoneName).format('DD-MM-YYYY HH:mm:ss')
          }
          if (report.transaction_type != null) {
            report.transaction_type = await getTransactionTypeByValueOrId(report.transaction_type)
          }
          if (report.source_currency_id) {
            const id = parseInt(report.source_currency_id || 0)
            report.source_currency = getCurrencyById(id)?.value || null
          }
          if (report.target_currency_id) {
            const id = parseInt(report.target_currency_id || 0)
            report.target_currency = getCurrencyById(id)?.value || null
          }
          const object = {
            ...(allowedColumnsObj.id && { id: report.internal_tracking_id }),
            ...(allowedColumnsObj.created_at && { created_at: report.created_at }),
            ...(allowedColumnsObj.round_id && { round_id: report.round_id }),
            ...(allowedColumnsObj.transaction_id && { transaction_id: report.transaction_id }),
            ...(allowedColumnsObj.debit_transaction_id && { debit_transaction_id: report.debit_transaction_id }),
            ...(allowedColumnsObj.utr_number && { utr_number: report.utr_number || '' }),
            ...(allowedColumnsObj.from_wallet && { from_wallet: (report.source_currency_id && report.from_wallet_uname) ? `${report.from_wallet_fname} ${report.from_wallet_lname} - ${report.from_wallet_uname} - ${report.from_wallet_email}` : '' }),
            ...(allowedColumnsObj.to_wallet && { to_wallet: (report.target_currency_id && report.to_wallet_email) ? `${report.to_wallet_fname} ${report.to_wallet_lname} - ${report.to_wallet_uname} - ${report.to_wallet_email}` : '' }),
            ...(allowedColumnsObj.from_currency && { from_currency: report.source_currency }),
            ...(allowedColumnsObj.to_currency && { to_currency: report.target_currency }),
            ...(allowedColumnsObj.from_before_balance && { from_before_balance: formatAmount(report.initial_balance, tenantId, report.source_currency) || '' }),
            ...(allowedColumnsObj.amount && { amount: formatAmount(report.amount, tenantId, report.source_currency) }),
            ...(allowedColumnsObj.to_after_balance && { to_after_balance: formatAmount(report.amount, tenantId, report.target_currency) || '' }),
            ...(allowedColumnsObj.conversion_rate && { conversion_rate: report.conversion_rate }),
            ...(allowedColumnsObj.action_type && { action_type: report.transaction_type }),
            ...(allowedColumnsObj.status && { status: report.status }),
            ...(allowedColumnsObj.comments && { comments: expandBonusComment(report.comments) }),
            ...(allowedColumnsObj.action_by && { action_by: (report.action_by_email) ? `${report.action_by_fname} ${report.action_by_lname} - ${report.action_by_email}` : '' }),
            ...(allowedColumnsObj.table_id && { table_id: report.table_id }),
            ...(allowedColumnsObj.game_name && { game_name: report.game_type || report.game_name || '' }),
            ...(allowedColumnsObj.game_provider && { game_provider: report.game_provider })
          }
          mainArr = [...mainArr, object]
        }

        // Convert data to CSV format
        const csvData = csvStringifier.stringifyRecords(mainArr)
        // S3 upload parameters
        const uploadParams = {
          Bucket: s3Config.bucket,
          Key: key
        }

        // Try to fetch the existing CSV file to append data
        let existingCsvContent = ''
        try {
          const data = await s3.send(new GetObjectCommand(uploadParams))
          const chunks = []
          for await (const chunk of data.Body) {
            chunks.push(chunk)
          }
          existingCsvContent = Buffer.concat(chunks).toString('utf-8')
        } catch (err) {
          if (err.name !== 'NoSuchKey') throw err // Ignore if file doesn't exist
        }
        // Append data or create new file
        let finalCsvContent = existingCsvContent
        if (existingCsvContent) {
          finalCsvContent += csvData // Append the new data (excluding the header)
        } else {
          finalCsvContent = csvStringifier.getHeaderString() + csvData // Create the file with the initial data
        }

        // Create a readable stream of the final CSV content
        const stream = Readable.from([finalCsvContent])
        const contentLength = Buffer.byteLength(finalCsvContent)

        // Upload the CSV file to S3
        const uploadFileParams = {
          Bucket: s3Config.bucket,
          Key: key,
          Body: stream,
          ContentType: 'text/csv',
          ContentLength: contentLength
        }

        await s3.send(new PutObjectCommand(uploadFileParams))
        await delay(500)
      }
      await db.ExportCsvCenter.update({
        csvUrl: key,
        status: EXPORT_CSV_STATUS.DONE
      },
        {
          where: { id: data.id }
        }
      )
    }
    return true
  } catch (error) {
    // Update export status to failed
    await db.ExportCsvCenter.update({
      status: EXPORT_CSV_STATUS.FAILED
    },
      {
        where: { id: data.id }
      }
    )
    throw error
  }
}

function prepareGameTransactionReportQuery ({
  gameTxsCteWhereCondition,
  txsUserDataCteWhereCondition,
  sortCondition,
  paginationCondition,
  sortConditionWithAlias,
  userFilterApplied
}) {
  let txsUserDataCte = ''
  let finalCte = 'game_txs'
  let selectWallet = ''

  if (txsUserDataCteWhereCondition) {
    selectWallet = `CASE
        WHEN transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN source_wallet_id
        WHEN transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_wallet_id
        ELSE COALESCE(target_wallet_id, source_wallet_id) -- For transaction type (2,10,47)
      END AS user_wallet_id,`
    finalCte = 'txs_user_data'
    txsUserDataCte = `
      , txs_user_data AS (
        SELECT
          t.*
        FROM
          game_txs t
          LEFT JOIN wallets w ON (w.id = t.user_wallet_id AND owner_type = 'User')
          LEFT JOIN users u ON (w.owner_id = u.id)
        ${txsUserDataCteWhereCondition}
        ${sortConditionWithAlias}
        ${paginationCondition}
      )
    `
    paginationCondition = ''
    sortCondition = ''
  }

  const INDEXING_HELPER_USER_FILTER_QUERY =
    `
  SELECT
    id,
    actionee_id,
    actionee_type,
    seat_id,
    game_id,
    table_id,
    round_id,
    created_at,
    tenant_id,
    provider_id,
    status,
    debit_transaction_id,
    conversion_rate,
    transaction_type,
    source_before_balance,
    target_before_balance,
    source_after_balance,
    target_after_balance,
    comments,
    1 AS added_amount,
    1 AS deducted_amount,
    ${selectWallet}
    amount,
    source_currency_id,
    target_currency_id,
    transaction_id,
    source_wallet_id,
    target_wallet_id
  FROM
    transactions
  WHERE false
  `;

  // Add the UNION ALL for the User Filter so that Postgres Query Planner uses the Source and Target Wallet ID related Indexes for Fast Result Across the Multiple Partitions.
  let userFilterIndexHelper = '';
  if (userFilterApplied) {
    userFilterIndexHelper = INDEXING_HELPER_USER_FILTER_QUERY + '\n' + 'UNION ALL' + '\n';
  }

  return `
  SET enable_seqscan = OFF;

  WITH game_txs AS (
    ${userFilterIndexHelper}
    SELECT
      t.id,
      t.actionee_id,
      t.actionee_type,
      t.seat_id,
      t.game_id,
      t.table_id,
      t.round_id,
      t.created_at,
      tenant_id,
      provider_id,
      status,
      debit_transaction_id,
      t.conversion_rate,
      t.transaction_type,
      t.source_before_balance,
      t.target_before_balance,
      t.source_after_balance,
      t.target_after_balance,
      t.comments,
      CASE
        WHEN transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN amount
        WHEN transaction_type IN (2,10,47) AND target_wallet_id IS NOT NULL THEN amount
        ELSE 0
      END AS added_amount,
      CASE
        WHEN transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN amount
        WHEN transaction_type IN (2,10,47) AND source_wallet_id IS NOT NULL THEN amount
        ELSE 0
      END AS deducted_amount,
      ${selectWallet}
      amount,
      t.source_currency_id,
      t.target_currency_id,
      transaction_id,
      source_wallet_id,
      target_wallet_id
      FROM transactions t
      ${gameTxsCteWhereCondition}
      ${sortCondition}
      ${paginationCondition}
  )
  ${txsUserDataCte}
    SELECT
      source_wallet_id,
      target_wallet_id,
      actionee_id,
      actionee_type,
      t.round_id,
      t.provider_id,
      t.transaction_id,
      t.debit_transaction_id,
      t.conversion_rate,
      t.transaction_type,
      t.comments,
      t.status,
      t.source_currency_id,
      t.target_currency_id,
      t.id AS internal_tracking_id,
      t.seat_id,
      t.tenant_id,
      tmd.meta_data,
      t.amount::numeric(20,2) AS amount,
      t.added_amount::numeric(20,2) AS added_amount,
      t.deducted_amount::numeric(20,2) AS deducted_amount,
      t.created_at::text AS created_at,
      CASE
        WHEN t.provider_id IN (:gameIdProviders) THEN game_id
        WHEN t.provider_id IN (:tableIdProviders) THEN table_id::text
        ELSE t.seat_id
      END AS table_id,
      CASE WHEN t.seat_id = 'bti_sportsbook' THEN 'Bti Sportsbook'
        WHEN t.seat_id = 'sap_lobby' THEN 'Sap Exchange'
        WHEN t.seat_id = 'sbs_sportsbook' THEN 'Saba Sportsbook'
        WHEN cp.name IN ('st8','Whitecliff') THEN tp.title
        ELSE cp.name
      END AS game_provider,
      COALESCE(su.first_name, sau.first_name) AS from_wallet_fname,
      COALESCE(su.last_name, sau.last_name) AS from_wallet_lname,
      COALESCE(su.user_name, sau.agent_name) AS from_wallet_uname,
      COALESCE(su.email, sau.email) AS from_wallet_email,
      COALESCE(tu.first_name, tau.first_name) AS to_wallet_fname,
      COALESCE(tu.last_name, tau.last_name) AS to_wallet_lname,
      COALESCE(tu.user_name, tau.agent_name) AS to_wallet_uname,
      COALESCE(tu.email, tau.email) AS to_wallet_email,
      COALESCE(atu.first_name, atau.first_name) AS action_by_fname,
      COALESCE(atu.last_name, atau.last_name) AS action_by_lname,
      COALESCE(atu.user_name, atau.agent_name) AS action_by_uname,
      COALESCE(atu.email, atau.email) AS action_by_email,
      CASE
        WHEN t.transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN sw.currency_id
        WHEN t.transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN tw.currency_id
        ELSE COALESCE(tw.currency_id, sw.currency_id) -- For transaction type (2,10,47)
      END AS currency,
      CASE
        WHEN t.transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN source_before_balance::numeric(20,2)
        WHEN t.transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_before_balance::numeric(20,2)
        WHEN source_wallet_id IS NULL THEN target_before_balance::numeric(20,2) -- For transaction type (2,10,47)
        ELSE source_before_balance::numeric(20,2) -- For transaction type (2,10,47)
      END AS initial_balance,
      CASE
        WHEN t.transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN source_after_balance::numeric(20,2)
        WHEN t.transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_after_balance::numeric(20,2)
        WHEN source_wallet_id IS NULL THEN target_after_balance::numeric(20,2) -- For transaction type (2,10,47)
        ELSE source_after_balance::numeric(20,2) -- For transaction type (2,10,47)
      END AS ending_balance,
      CASE
        WHEN t.transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN (source_after_balance - source_before_balance)::numeric(20,2)
        WHEN t.transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN (target_after_balance - target_before_balance)::numeric(20,2)
        WHEN source_wallet_id IS NULL THEN (target_after_balance - target_before_balance)::numeric(20,2) -- For transaction type (2,10,47)
        ELSE (source_after_balance - source_before_balance)::numeric(20,2) -- For transaction type (2,10,47)
      END AS revenue
    FROM ${finalCte} t
      LEFT JOIN transactions_meta_data tmd ON tmd.transaction_id = t.id
      -- Get Provider data
      LEFT JOIN casino_providers cp ON (cp.id = t.provider_id)
      LEFT JOIN transactions_providers_list tp ON (cp.name IN ('st8', 'Whitecliff') AND t.provider_id= cp.id AND tp.seat_ids ? t.seat_id)
      -- Get "From Wallet" data
      LEFT JOIN wallets sw ON (sw.id = t.source_wallet_id)
      LEFT JOIN users su ON (sw.owner_type = 'User' AND su.id = sw.owner_id)
      LEFT JOIN admin_users sau ON (sw.owner_type = 'AdminUser' AND sau.id = sw.owner_id)
      -- Get "To Wallet" data
      LEFT JOIN wallets tw ON (tw.id = t.target_wallet_id)
      LEFT JOIN users tu ON (tw.owner_type = 'User' AND tu.id = tw.owner_id)
      LEFT JOIN admin_users tau ON (tw.owner_type = 'AdminUser' AND tau.id = tw.owner_id)
      -- Get "Action By" data
      LEFT JOIN users atu ON (t.actionee_type = 'User' AND atu.id = t.actionee_id)
      LEFT JOIN admin_users atau ON (t.actionee_type = 'AdminUser' AND atau.id = t.actionee_id)
    ${sortConditionWithAlias};

    SET enable_seqscan = ON;
  `
}

function prepareGameTransactionReportCountQuery ({
  gameTxsCteWhereCondition,
  txsUserDataCteWhereCondition
}) {
  let finalQ = ''
  // When agent filter is not applied
  if (!txsUserDataCteWhereCondition) {
    finalQ = `
      SET enable_seqscan = OFF;

      SELECT COUNT(*) AS total_count
      FROM transactions
      ${gameTxsCteWhereCondition};

      SET enable_seqscan = ON;
    `
  } else {
    finalQ = `
      SET enable_seqscan = OFF;

      WITH game_txs AS (
        SELECT
          source_wallet_id,
          target_wallet_id,
          CASE
            WHEN transaction_type IN (0,4,6,7,8,13,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) THEN source_wallet_id
            WHEN transaction_type IN (1,3,5,9,11,12,14,15,16,17,19,22,25,26,29,30,31,33,35,37,38,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) THEN target_wallet_id
            ELSE COALESCE(target_wallet_id, source_wallet_id) -- For transaction type (2,10,47)
          END AS user_wallet_id
        FROM transactions
        ${gameTxsCteWhereCondition}
      )
      SELECT
        COUNT(*) AS total_count
      FROM game_txs t
        LEFT JOIN wallets w ON (w.id = t.user_wallet_id AND owner_type = 'User')
        LEFT JOIN users u ON (w.owner_id = u.id)
      ${txsUserDataCteWhereCondition};

      SET enable_seqscan = ON;`
  }
  return finalQ
}

async function fetchSportMarketNames (reportData) {
  let debitTxIds = [];
  reportData.forEach(report => {
    if (report.game_provider === SPORT_PROVIDER.JETFAIR || report.game_provider === SPORT_PROVIDER.POWERPLAY || report.game_provider === SPORT_PROVIDER.TURBOSTARS) {
      debitTxIds.push(report.debit_transaction_id);
    }
  });

  if (debitTxIds.length <= 0) return;

  let marketNames = await getSportsMarkets(debitTxIds);

  if (!marketNames) return;

  reportData.forEach(report => {
    if (report.game_provider === SPORT_PROVIDER.JETFAIR || report.game_provider === SPORT_PROVIDER.POWERPLAY || report.game_provider === SPORT_PROVIDER.TURBOSTARS) {
      report.game_type = (marketNames[report.debit_transaction_id] ? String(marketNames[report.debit_transaction_id]) : report.seat_id);
    }
  });
}

const INDEXING_HELPER_USER_FILTER_QUERY =
  `
SELECT
  id,
  actionee_id,
  actionee_type,
  seat_id,
  game_id,
  table_id,
  round_id,
  created_at,
  tenant_id,
  provider_id,
  status,
  debit_transaction_id,
  conversion_rate,
  transaction_type,
  source_before_balance,
  target_before_balance,
  source_after_balance,
  target_after_balance,
  comments,
  1 AS added_amount,
  1 AS deducted_amount,
  amount,
  source_currency_id,
  target_currency_id,
  transaction_id,
  source_wallet_id,
  target_wallet_id
FROM
  transactions
WHERE false
`;
