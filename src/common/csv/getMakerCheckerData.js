export default (txn, param, module, time_zone_name) => {
  let filteredObj = (module === 'maker') ? (JSON.parse(txn._source.maker_data)) : (JSON.parse(txn._source.checker_data));
  let data
  switch (param) {
    case 'email':
      data = filteredObj.email
      break;
    case 'time_stamp':
      data = getFormattedDateTime(filteredObj.time_stamp,time_zone_name)
      break;
    case 'user_name':
      data = (filteredObj?.user_name || '-')
      break;
    case 'remark':
      data = (filteredObj?.remark || '-')
      break;
    default: ''
      data = ''
      break;
  }
  return data
}

function convertToTimeZone(utcDate, timeZone) {
  const options = { timeZone };
  return utcDate.toLocaleString('en-US', options);
}

// Function to format the date and time
function getFormattedDateTime(date, timeZone) {
  let timeZoneName = timeZone
  if(timeZone=='UTC +00:00'){
    timeZoneName = 'Africa/Abidjan'
  }
  const formattedDateTime = convertToTimeZone(new Date(date), timeZoneName);
  return formattedDateTime;
}
