import db from "../../db/models"

export default async (id = '') => {
  let superAdmin = false
  let rolesArray = []
  if (id == '') {
    id = id
    superAdmin = true
  }

  const adminData = await db.AdminUser.findOne({
    where: {
      id: id
    },
    attributes: ['parent_type']
  }
  )
  if ((adminData?.parent_type && (adminData?.parent_type == 'SuperAdminUser' || adminData?.parent_type == 'Manager')) && superAdmin == true) {
    const roles = await db.SuperAdminUsersSuperRole.findAll({
      where: {
        superAdminUserId: id
      },
      attributes: ['superRoleId']
    })

    if (roles.length > 0) {
      for (const role of roles) {
        if (role.superRoleId == 1) {
          rolesArray.push('owner')
        }


        if (role.superRoleId == 2)
          rolesArray.push('owner')
      }
    }
  } else {
    const roles = await db.AdminUsersAdminRole.findAll({
      where: {
        adminUserId: id
      },
      attributes: ['adminRoleId']
    })
    if (roles.length > 0) {
      for (const role of roles) {
        if (role.adminRoleId == 1) {
          rolesArray.push('owner')
        }

        if (role.adminRoleId == 2) {
          rolesArray.push('agent')
        }

        if (role.adminRoleId == 3) {
          rolesArray.push('sub-admin')
        }

        if (role.adminRoleId == 4) {
          rolesArray.push('deposit-admin')
        }

        if (role.adminRoleId == 5) {
          rolesArray.push('withdrawal-admin')
        }
      }
    }

  }
  return rolesArray
}
