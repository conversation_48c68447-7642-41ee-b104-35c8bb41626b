import { Client } from '@elastic/elasticsearch'
import { isEmpty } from "lodash"
import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'
import { EXPORT_CSV_STATUS } from '../../common/constants'
import config from '../../configs/app.config'
import db from '../../db/models'
import { s3 } from '../../libs/awsS3Config'
import getRolesDetails from './getAdminRolesDetail'
import searchByKeyword from './searchByKeyword'
import { getDateInStringFormat } from '../helpers'
const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')

function getESclient () {
  const elasticUrl = config.get('elastic.url') + ':' + config.get('elastic.port') || 'http://elasticsearch:9200'
  const protocol = config.get('elastic.protocal')
  const esClient = new Client({ node: protocol + config.get('elastic.user') + ':' + config.get('elastic.password') + '@' + elasticUrl })
  return esClient;
}

export default async (data) => {
  let esClient = getESclient();
  const payload = data.payload
  await db.ExportCsvCenter.update({
    status: EXPORT_CSV_STATUS.IN_PROGRESS
  },
    {
      where: { id: data.id }
    }
  )
  let filter = []
  let mustArray = []
  let query
  data.adminType && data.adminId
  if (data.adminType == 'AdminUser') {
    const adminData = await db.AdminUser.findOne({
      where: {
        id: data.adminId
      },
      attributes: ['tenantId']
    }
    )
    payload.tenant_id = adminData.tenantId
  }

  if (payload.action_type) {
    filter = [...filter, { terms: { transaction_type: payload.action_type } }]
  }
  if (payload.tenant_id) {
    filter = [...filter, { term: { tenant_id: payload.tenant_id } }]
  }
  if (payload?.round_id) {
    filter = [...filter, { term: { round_id_s: payload.round_id } }]
  }
  if (payload?.round_id_s) {
    filter = [...filter, { term: { round_id_s: payload.round_id_s } }]
  }
  if (payload?.transaction_id) {
    filter = [...filter, { term: { transaction_id: payload.transaction_id } }]
  }
  if (payload?.debit_transaction_id) {
    filter = [...filter, { term: { custom_1: payload.debit_transaction_id } }]
  }

  if (payload?.utr_number) {
    filter = [...filter, { term: { custom_4: payload.utr_number } }]
  }
  if (payload?.id) {
    filter = [...filter, { term: { internal_tracking_id: payload.id } }]
  }
  if (payload?.amount) {
    filter = [...filter, { term: { amount: payload.amount } }]
  }
  if (payload?.user_id) {
    filter = [...filter, { term: { 'player_details.player_id_s': payload.user_id } }]
  }
  if (payload?.agent_id) {

    if (!payload.agent_data_only) {
        filter = [...filter, { term: { "agent_parent_chain_ids": { value: payload.agent_id } } }]
    }else{
      filter = [...filter,
        {
            bool: {
                must_not: {
                    terms: {
                        "agent_parent_chain_ids": [payload.agent_id]
                    }
                }
            }
        }
      ];
    }


  }

  if (!isEmpty(payload?.time_period)) {
    if (payload?.time_period?.start_date && payload?.time_period?.end_date) {
      if (payload.time_zone_name) {
         payload.time_period.start_date = moment.tz(payload.time_period.start_date, payload.time_zone_name).utc().format()
         payload.time_period.end_date = moment.tz(payload.time_period.end_date, payload.time_zone_name).utc().format()
      }
      let endDate = payload.time_period.end_date
      filter = [...filter, {
        range: {
          created_at: {
            from: payload.time_period.start_date,
            include_lower: true,
            to: endDate.replace("000000", "999999"),
            include_upper: true
          }
        }
      }]
    }
  }

  if (payload?.player_id) {
    filter = [...filter, { term: { "player_details.player_id": payload.player_id } }]
  }

  if (payload?.action_category != "" && payload?.game_provider == '') {
    if (payload.action_category == "casino") {
      filter.push({
        "terms": {
          "game_provider": [
            "Spribe",
            "Ezugi",
            "Evolution",
            "st8",
            "pgsoft",
            "8DEX"
          ]
        }
      })

      filter.push({ "bool": { "must_not": { "terms": { "game_type.keyword": ["Betting Exchange", "Sportsbook", "sbs_sportsbook"] } } } })
    }
    else if(payload.action_category == 'financial'){
      filter.push({
        "bool": {
          "must_not": {
            "terms": {
              "game_provider": [
                "Spribe",
                "Ezugi",
                "Evolution",
                "st8",
                "Jetfair",
                "pgsoft",
                "8DEX"

              ]
            }
          }
        }
      })

      filter.push({ "bool": { "must_not": { "terms": { "transaction_type": ["bet", "win", "refund", "tip", "bet_non_cash", "win_non_cash", "tip_non_cash", "refund_non_cash"] } } } })
    }
    else {
        filter.push({
          "bool": {
            "should": [
              {
                "bool": {
                  "must": {
                    "terms": {
                      "game_provider": [
                        "Jetfair"
                      ]
                    }
                  }
                }
              },
              {
                "bool": {
                  "must": {
                    "terms": {
                      "game_provider": [
                        "st8"
                      ]
                    }
                  },
                  "should": [
                    {
                      "bool": {
                        "must": {
                          "terms": {
                            "custom_2": [
                              "Betting Exchange",
                              "Sportsbook",
                              "sbs_sportsbook",
                              "bti_sportsbook",
                              "sap_lobby"
                            ]
                          }
                        }
                      }
                    }
                  ],
                  "minimum_should_match": 1
                }
              }
            ]
          }
        })
      // if (payload.action_category == "st8_exchange") {

      //   filter.push({ terms: { "game_type.keyword": ["Betting Exchange"] } })

      //   payload.action_category = "st8";
      // } else if (payload.action_category == "st8_sports_book") {
      //   filter.push({ terms: { "game_type.keyword": ["Sportsbook", "sbs_sportsbook"] } })

      //   payload.action_category = "st8";

      // }
      // else if (payload.action_category == 'st8'){
      //   filter.push({"bool":{"must_not":{"terms":{"game_type.keyword":["Betting Exchange","Sportsbook","sbs_sportsbook"]}}}})
      // }
      // filter.push({ term: { game_provider: { value: payload.action_category } } })
    }
  }

  if (payload.game_provider != '')  {
    filter.push({"bool":{"should":[{"term":{"custom_3":{"value":payload.game_provider}}},{"term":{"game_provider":{"value":payload.game_provider}}},{"term":{"custom_2":{"value":payload.game_provider}}}]}})
  }

  if (payload.game_type != ''){
    filter.push({"term":{"game_type":{"value":payload.game_type}}})
  }

  if (payload?.search && !isNaN(parseFloat(payload.search)) && isFinite(payload.search)) {
    const searchData = searchByKeyword(payload.search)
    filter = [...filter, searchData]
  }

  if (data.adminType && data.adminId) {
    const roles = await getRolesDetails(data.adminId)
    if(!roles.includes('owner') && !roles.includes('sub-admin') ){
      filter = [...filter, {
        term: {
          'player_details.parent_chain_ids': {
            value: data.adminId
          }
        }
      }]
    }

  }

  if (payload?.currency_id) {
    mustArray = [...mustArray, {
      bool: {
        should: [
          {
            match: {
              source_currency: payload.currency_id
            }
          },
          {
            match: {
              target_currency: payload.currency_id
            }
          }
        ]
      }
    }
    ]

  }

  filter = [...filter, {
    bool: {
      must_not: {
        terms: {
          transaction_type: ['dummy_for_admin']
        }
      }
    }
  }]

  filter = [...filter, {
    bool: {
      must_not: {
        terms: {
          'target_wallet_owner.type': ['SuperAdminUser']
        }
      }
    }
  }]

  if (payload.agent_data_only) {
    filter = [...filter, {
      bool: {
        must_not: {
          terms: {
            'target_wallet_owner.type': ['User']
          }
        }
      }
    }]

    filter = [...filter, {
      bool: {
        must_not: {
          terms: {
            'source_wallet_owner.type': ['User']
          }
        }
      }
    }]
  }

  if (mustArray.length > 0) {
    query = mustArray
  } else {
    query = {
      match_all: {}
    }
  }

  if (filter.length > 0) {
    filter = {
      bool: {
        filter: filter
      }
    }
  }

  let sortData = {}
  let sortOrder = payload?.order ? payload.order : 'ASC'
  if (payload?.sort_by) {
    let sortKey = payload.sort_by
    sortData[sortKey] = sortOrder
  }

  try {
    let searchDetails
    let limit = 1000
    let page = 0
    let offset = page * limit

    searchDetails = await esClient.search({
      index: config.getProperties().es_index.transactions_index_name,
      body: {
        query: {
          bool: {
            must: query,
            filter: filter
          }
        },
        sort: sortData,
        timeout: '3000s',
        track_total_hits: true,
        size: limit,
        from: offset
      }
    })

    const uuid = uuidv4().replace(/-/g, '')
    const filePath = `/tmp/transaction_${uuid}.csv`
    const csvWriter = createCsvWriter({
      path: filePath,
      header: [
        { id: 'id', title: 'ID' },
        { id: 'created_at', title: 'Created at' },
        { id: 'round_id', title: 'Round ID' },
        { id: 'transaction_id', title: 'Operator Transaction Id' },
        { id: 'debit_transaction_id', title: 'Debit Transaction Id' },
        { id: 'utr_number', title: 'UTR Number' },
        { id: 'from_wallet', title: 'From wallet' },
        { id: 'from_currency', title: 'From currency' },
        // { id: 'from_before_balance', title: 'From before balance' },
        // { id: 'from_after_balance', title: 'From after balance' },
        { id: 'to_wallet', title: 'To wallet' },
        { id: 'to_currency', title: 'To currency' },
        { id: 'to_before_balance', title: 'To before balance' },
        { id: 'to_after_balance', title: 'To after balance' },
        { id: 'amount', title: 'Amount' },
        { id: 'conversion_rate', title: 'Conversion rate' },
        { id: 'action_type', title: 'Action Type' },
        { id: 'comments', title: 'Comments' },
        { id: 'action_by', title: 'Action by' },
        { id: 'table_id', title: 'Table Id' },
      ]
    });
    const csvData = searchDetails.body.hits.hits

    if (payload.time_zone_name) {
      const csvDataList = await Promise.all(
        csvData.map(async object => {
          // Formatting createdAt date with optional user timezone
          const date = payload.time_zone_name ? moment.tz(object._source.created_at, payload.time_zone_name) : moment(object._source.created_at)
          object._source.created_at = date.format('DD-MM-YYYY HH:mm:ss')
          return object
        })
      )
    }

    let mainArr = []
    let tenantId
    if (csvData.length > 0) {
      tenantId = csvData[0]._source.tenant_id
      for (const txn of csvData) {
        const object = {
          id: txn._source.internal_tracking_id,
          created_at: getDateInStringFormat(txn._source.created_at),
          round_id: txn._source.round_id_s,
          transaction_id: txn._source.transaction_id,
          debit_transaction_id: txn._source.custom_1,
          utr_number: txn._source.custom_4 || '',
          from_wallet: txn?._source?.source_wallet_owner?.email ? `${txn._source.source_wallet_owner.first_name} ${txn._source.source_wallet_owner.last_name} - ${txn._source.source_wallet_owner.email}` : '',
          from_currency: txn._source.source_currency,
          // from_before_balance: txn?._source?.source_wallet_owner?.before_balance ? txn._source.source_wallet_owner.before_balance : '',
          // from_after_balance: txn?._source?.source_wallet_owner?.after_balance ? txn._source.source_wallet_owner.after_balance : '',
          to_wallet: txn?._source?.target_wallet_owner?.email ? `${txn._source.target_wallet_owner.first_name} ${txn._source.target_wallet_owner.last_name} - ${txn._source.target_wallet_owner.email}` : '',
          to_currency: txn._source.target_currency,
          to_before_balance: txn._source?.player_details ? (+txn._source?.player_details?.before_balance).toFixed(2) : '',
          to_after_balance: txn._source?.player_details ? (+txn._source?.player_details?.after_balance).toFixed(2) : '',
          amount: txn._source.amount,
          conversion_rate: txn._source.conversion_rate,
          action_type: txn._source.transaction_type,
          comments: txn._source.description,
          action_by: (txn?._source?.actionee?.email ? txn._source.actionee.email : '') + "-" + (txn._source?.actionee?.first_name?.toLowerCase()  ||'') + ' ' + (txn._source?.actionee?.last_name?.toLowerCase()  ||''),
          table_id: txn._source?.table_id || ((txn._source?.game_provider == 'st8' || txn._source?.game_provider == 'pgsoft') ? txn._source?.uuid : (txn._source?.game_id || ''))
        }
        mainArr = [...mainArr, object]
      }
    }
    await Promise.all([csvWriter.writeRecords(mainArr)])

    const totalData = searchDetails.body.hits.total.value
    let itval = parseFloat(totalData) / limit
    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    for (let i = 1; i < itval; i++) {
      await delay(1000)

      offset = i * limit
      searchDetails = //await Promise.all([
        await esClient.search({
          index: config.getProperties().es_index.transactions_index_name,
          body: {
            query: {
              bool: {
                must: query,
                filter: filter
              }
            },
            sort: sortData,
            timeout: '3000s',
            track_total_hits: true,
            size: limit,
            from: offset
          }
        })
      //])
      let mainArr = []
      let tenantId
      const csvData = searchDetails.body.hits.hits
      if (payload.time_zone_name) {
        const csvDataList = await Promise.all(
          csvData.map(async object => {
            // Formatting createdAt date with optional user timezone
            const date = payload.time_zone_name ? moment.tz(object._source.created_at, payload.time_zone_name) : moment(object._source.created_at)
            object._source.created_at = date.format('DD-MM-YYYY HH:mm:ss')
            return object
          })
        )
      }
      if (csvData.length > 0) {
        tenantId = csvData[0]._source.tenant_id
        for (const txn of csvData) {
          const object = {
            id: txn._source.internal_tracking_id,
            created_at: getDateInStringFormat(txn._source.created_at),
            round_id: txn._source.round_id_s,
            transaction_id: txn._source.transaction_id,
            debit_transaction_id: txn._source.custom_1,
            utr_number: txn._source.custom_4 || '',
            from_wallet: txn?._source?.source_wallet_owner?.email ? txn._source.source_wallet_owner.email : '',
            from_currency: txn._source.source_currency,
            // from_before_balance: txn?._source?.source_wallet_owner?.before_balance ? txn._source.source_wallet_owner.before_balance : '',
            // from_after_balance: txn?._source?.source_wallet_owner?.after_balance ? txn._source.source_wallet_owner.after_balance : '',
            to_wallet: txn?._source?.target_wallet_owner?.email ? txn._source.target_wallet_owner.email : '',
            to_currency: txn._source.target_currency,
            to_before_balance: txn._source?.player_details ? (+txn._source?.player_details?.before_balance).toFixed(2) : '',
            to_after_balance: txn._source?.player_details ? (+txn._source?.player_details?.after_balance).toFixed(2) : '',
            amount: txn._source.amount,
            conversion_rate: txn._source.conversion_rate,
            action_type: txn._source.transaction_type,
            comments: txn._source.description,
            action_by: (txn?._source?.actionee?.email ? txn._source.actionee.email : '') + "-" + (txn._source?.actionee?.first_name?.toLowerCase()  ||'') + ' ' + (txn._source?.actionee?.last_name?.toLowerCase()  ||''),
            table_id: txn._source?.table_id || ((txn._source?.game_provider == 'st8' || txn._source?.game_provider == 'pgsoft') ? txn._source?.uuid : (txn._source?.game_id || ''))
          }
          mainArr = [...mainArr, object]
        }

        await Promise.all([csvWriter.writeRecords(mainArr)])
      }
    }

    //upload file to s3
    const s3Config = config.getProperties().s3
    const fileContent = await fs.promises.readFile(filePath);
    const key = `tenants/${tenantId}/csv/casino_transaction/transaction_${uuid}.csv`

    const s3Params = {
      ACL: 'public-read',
      Bucket: s3Config.bucket,
      Key: key,
      Body: fileContent
    }
    const uploadedFile = await s3.upload(s3Params).promise()
    await db.ExportCsvCenter.update({
      csvUrl: key,
      status: EXPORT_CSV_STATUS.DONE
    },
      {
        where: { id: data.id }
      }
    )

    fs.unlink(filePath, (err) => {
      if (err) {
        throw err
      }
    });

    return true
  } catch (error) {
    console.log("=======errorr", error)
    throw error
  }
}
