import { sequelize } from '../db/models'

export default async (tenantId, winType, startDate, endDate, limit, providerIds = null) => {
  // Find tenant base currency
  const findTenantBaseCurrencyQuery = `
    select
      c.id as currency_id
    from
      tenant_credentials tc
      join currencies c on (tc.value = c.code)
    where
      key = 'TENANT_BASE_CURRENCY'
      and tenant_id = :tenantId;
  `;

  const tenantBaseCurrencyData = await sequelize.query(findTenantBaseCurrencyQuery, {
    replacements: {
      tenantId,
    },
    type: sequelize.QueryTypes.SELECT
  });

  const tenantBaseCurrencyId = tenantBaseCurrencyData[0]?.currency_id;

  // Create dynamic query based on win type
  let playerSummaryProviderTypes;
  let calculateWinningAmountQ = 'sum(psc.amount) as win_amount';

  if (winType === 'rollover') playerSummaryProviderTypes = [20];
  else if (winType === 'ggr') playerSummaryProviderTypes = [21];
  else if (winType === 'ngr') {
    playerSummaryProviderTypes = [21,31];
    calculateWinningAmountQ = `
      (sum(case when psp.type = 21 then psc.amount else 0 end)
			- sum(case when psp.type = 31 then psc.amount else 0 end)) as win_amount
    `;
  }
  else if (winType === 'total_wagered') {
    playerSummaryProviderTypes = [29];
  }

  // Build provider filter condition
  const providerCondition = providerIds && providerIds.length > 0 
    ? `and psp.provider_id IN (${providerIds.join(',')})` 
    : '';

  const findQuery = `
    with leaderboard_data as (
      select
        psp.user_id as user_id,
        ${calculateWinningAmountQ}
      from
        player_summary_provider_wise psp
        join player_provider_other_currency psc on (psp.id = psc.player_summary_provider_id and psc.currency_id = :tenantBaseCurrencyId)
      where
        psp.type in (${playerSummaryProviderTypes.join(',')})
        and psp.tenant_id = :tenantId
        and psp.date between :startDate and :endDate
        ${providerCondition}
      group by
        psp.user_id
      order by
        win_amount desc
      limit :limit
    )
    select
      ld.user_id as id, u.user_name as "userName", ld.win_amount as "winningAmount"
    from
      leaderboard_data ld join users u on (ld.user_id = u.id)
    where
      ld.win_amount > 0;
  `;

  const replacements = {
    tenantBaseCurrencyId,
    tenantId,
    startDate,
    endDate,
    limit: parseInt(limit)
  };

  const topUsers = await sequelize.query(findQuery, {
    replacements,
    type: sequelize.QueryTypes.SELECT
  });

  return topUsers;
}
