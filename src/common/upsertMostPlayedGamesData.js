import db, { sequelize } from '../db/models';
import { CRON_LOG_STATUS } from '../common/constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  const transaction = await sequelize.transaction();
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'upsert_most_played_games_data',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      cronLog.cronId = queueProcessStatus.id

      // Get most played games data
      const query = `
        WITH CountedGames AS (
          SELECT
            t.tenant_id,
            t.actionee_id AS user_id,
            t.provider_id,
            CASE
              WHEN cp.name IN ('Spribe','8DEX') THEN t.game_id
              ELSE COALESCE(t.seat_id, t.table_id::varchar)
            END AS game,
            MIN(cp.name) AS provider_name,
            COUNT(*) AS play_count
          FROM
            transactions t
            LEFT JOIN casino_providers cp ON (cp.id = t.provider_id)
          WHERE
            t.transaction_type IN (0, 8)
            AND t.created_at >= NOW() - INTERVAL '1 month'
            AND t.game_id NOT IN ('bti_sportsbook', 'sbs_sportsbook', 'sap_lobby')
          GROUP BY
            t.tenant_id, t.actionee_id, t.provider_id, game
        ),
        UniqueCasinoItems AS (
          SELECT DISTINCT ON (tenant_id, provider, uuid)
            id, name, image, tenant_id, provider::bigint AS provider, uuid
          FROM
            casino_items
        )
          SELECT
            cg.tenant_id AS "tenantId",
            cg.user_id AS "userId",
            cg.game,
            ci.id AS "casinoItemId",
            cg.game AS uuid,
            ci.name AS name,
            COALESCE(ci.image, 'tenants/1/itema/fabf5508-8f92-4c05-b2f6-0497631d460d____600x400.png') AS image,
            cg.play_count AS "playCount",
            cg.provider_name AS provider,
            now() AS "updatedAt"
          FROM
            CountedGames cg
          JOIN
            UniqueCasinoItems ci ON ci.uuid = cg.game
            AND ci.tenant_id = cg.tenant_id
            AND ci.provider = cg.provider_id;
      `;
      const mostPlayedGamesData = await sequelize.query(query, { type: sequelize.QueryTypes.SELECT, useMaster: false });

      await db.MostPlayedGames.bulkCreate(mostPlayedGamesData, { transaction, updateOnDuplicate: ['casinoItemId', 'uuid', 'name', 'image', 'playCount', 'updatedAt'] });
      await transaction.commit();
    }
  } catch(e) {
    await transaction.rollback();
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log("Upsert_most_played_games_scheduler_error:", e);
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
