import db, { sequelize } from '../db/models';

export default async () => {
  try {

    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'update_player_bet_amount_cron',
        status: 1
      },
      attributes: ['id']
    })

    if (!queueProcessStatus) return
    // Call the stored procedure directly without additional checks
    await sequelize.query('CALL update_bet_wager();');

  } catch (error) {
    console.error(`Error processing update player bet wager`, error);
    throw error
  }
}
