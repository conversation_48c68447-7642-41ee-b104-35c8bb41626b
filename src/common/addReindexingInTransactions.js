import { StatusCodes } from "http-status-codes";
import moment from 'moment';
import config from '../configs/app.config';
import { sequelize } from '../db/models';
import { QUEUE_WORKER_CONSTANT } from "./constants";

export default async () => {
  try {
    const isProdEnv = config.get('env') === 'production';
    const providerFilter = isProdEnv ? [11, 12, 13, 15, 48, 49, 50, 81] : [14, 5000, 5033, 5035, 13, 5034, 16, 17, 18, 6];
    const transactionTypeFilter = [0, 8, 1, 9, 2, 10];
    const beginningDate = moment('2023-09-01 00:00:00').toISOString();
    const chunkSizeInDays = 7;
    const dateRanges = getDateRange(beginningDate, chunkSizeInDays);

    const delay = ms => new Promise(resolve => setTimeout(resolve, ms))

    // Process each date range sequentially to avoid async issues
    for (const range of dateRanges) {
      await delay(1000)
      await processRange(range, providerFilter, transactionTypeFilter);
    }

    return { message: 'Data updated successfully!', status: StatusCodes.OK };
  } catch (error) {
    return { message: error.message, status: StatusCodes.INTERNAL_SERVER_ERROR };
  }
}

async function processRange(range, providerFilter, transactionTypeFilter) {
  try {
    const selectProviderIds =
      `CALL update_provider_id_and_log(
        ARRAY[${providerFilter.join(',')}],
        ARRAY[${transactionTypeFilter.join(',')}],
        '${range.from}'::TIMESTAMP,
        '${range.to}'::TIMESTAMP,
        '${QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION}',
         ${QUEUE_WORKER_CONSTANT.READY}
        );
      `
    const [response] = await sequelize.query(selectProviderIds);
  } catch (error) {
    console.error(`[ERROR] Error processing range: ${range.from} - ${range.to}`, error);
    throw error;
  }
}

function getDateRange(beginningDate, chunkSizeInDays) {
  let start = moment(beginningDate);
  const end = moment();
  const ranges = [];

  while (start.isBefore(end)) {
    let chunkEnd = moment(start).add(chunkSizeInDays, 'days').endOf('day');
    if (chunkEnd.isAfter(end)) {
      chunkEnd = end;
    }

    ranges.push({
      from: start.format('YYYY-MM-DD HH:mm:ss.SSS'),
      to: chunkEnd.format('YYYY-MM-DD 23:59:59.999')
    });
    start = moment(chunkEnd).add(1, 'days').startOf('day');
  }
  return ranges;
}
