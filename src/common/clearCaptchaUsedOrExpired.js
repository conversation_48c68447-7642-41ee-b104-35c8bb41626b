import { Op, literal } from 'sequelize';
import db from '../db/models';
import { CAPTCHA_VERIFICATION_STATUS, CRON_LOG_STATUS } from './constants';

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {

    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'cleanup_expired_or_used_captchas_cron',
        status: 1
      },
      attributes: ['id']
    })

    if (!queueProcessStatus) return
    cronLog.cronId = queueProcessStatus?.id;

    await db.CaptchaVerification.destroy({
      where: {
        [Op.or]: [
          { status: CAPTCHA_VERIFICATION_STATUS.USED },
          literal(`"created_at" + INTERVAL '45 seconds' <= NOW()`)
        ]
      }
    });

  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.error(`Error processing player bet summary and bonus`, e);
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
