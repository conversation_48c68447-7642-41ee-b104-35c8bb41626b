import axios from "axios";
import db from '../../db/models';
import { PAYOUT_API_END_POINT } from "../constants";

export default async (data, paymentCredentials) => {
  try {
    let apiConfig = {
      method: "post",
      url: PAYOUT_API_END_POINT,
      headers: {
        secretKey: paymentCredentials.secret_key,
      },
      data: {
        partnerUserName: paymentCredentials.partner_user_name,
        secretKey: paymentCredentials.secret_key,
        fdTransactionID: data.fdTransactionId,
      },
    };
    const apiResponse = await axios(apiConfig)
      .then(function (response) {
        return response;
      })
      .catch(function (error, response) {
          return error
      });

    await db.RequestResponseLog.create({
      requestJson: {
        headers: {
          secretKey: paymentCredentials.secret_key,
        },
        data: {
          partnerUserName: paymentCredentials.partner_user_name,
          secretKey: paymentCredentials.secret_key,
          fdTransactionID: data.fdTransactionId,
        },
      },
      responseJson: { data: apiResponse?.data },
      service: "payout_transaction_status",
      tenantId: data.tenantId,
    });
    return apiResponse?.data;
  } catch (err) {
      throw err
  }
};
