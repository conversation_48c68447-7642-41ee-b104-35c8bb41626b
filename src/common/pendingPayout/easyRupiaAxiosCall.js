import axios from "axios"
import db from '../../db/models'
import { EASY_RUPIA_END_POINT } from "../constants";

export default async (data, paymentCredentials) => {
  const apiData = {
    clientId: paymentCredentials.clientID,
    secretKey: paymentCredentials.secretKey,
    orderId: data.fdTransactionId,
  }

  try {
    let apiConfig = {
      method: "post",
      url: EASY_RUPIA_END_POINT,
      headers: {},
      data: apiData,
    };
    const apiResponse = await axios(apiConfig)
      .then(function (response) {
        return response;
      })
      .catch(function (error, response) {
        return {
          data: {
            statusCode: 0,
            status: 'Failure',
            orderId: data.fdTransactionId
          }
        }

        // return error.response.data;
      });

    await db.RequestResponseLog.create({
      requestJson: {
        headers: {},
        data: apiData,
      },
      responseJson: { data: apiResponse?.data },
      service: "payout_transaction_status",
      tenantId: data.tenantId,
    });
    return apiResponse?.data;
  } catch (err) {
    return {
      statusCode: 0,
      status: 'Failure',
      orderId: data.fdTransactionId
    }
  }
};
