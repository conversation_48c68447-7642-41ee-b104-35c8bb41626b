import axios from "axios"
import moment from 'moment'
import { PAYWINGS_INTEGRATION_CONSTANT } from '../../common/constants'
import db from '../../db/models'

export default async (data, paymentCredentials) => {
  const dateObj = new Date(data.actionedAt)
  const formattedDate = moment.tz(dateObj, 'Asia/Kolkata').format('YYYY-MM-DD')

  const basicAuth = Buffer.from( paymentCredentials.userId + ":" + paymentCredentials.apiKey ).toString("base64")
  const axiosHeaders = {
    "Content-Type": "application/json",
    Authorization: `Basic ${basicAuth}`,
  }
  const axiosData = {
    ClientTID: data.fdTransactionId,
    TxnDate: formattedDate
  }

  let apiConfig = {
    method: "post",
    url: PAYWINGS_INTEGRATION_CONSTANT.PAYOUT_STATUS_CHECK_API,
    headers: axiosHeaders,
    data: axiosData,
  }

  const apiResponse = await axios(apiConfig)
    .then(function (response) {
      return response
    })
    .catch(function (error, response) {
      return error
    })

  await db.RequestResponseLog.create({
    requestJson: {
      headers: axiosHeaders,
      data: axiosData,
    },
    responseJson: apiResponse?.data,
    service: "pending_transaction_status",
    tenantId: data.tenantId,
  })

  return apiResponse?.data
}
