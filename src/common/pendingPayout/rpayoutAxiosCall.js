import axios from "axios"
import { RPAYOUT_INTEGRATION_CONSTANT } from '../../common/constants'
import config from "../../configs/app.config"
import db from '../../db/models'

export default async (data, paymentCredentials) => {
  const axiosHeaders = {
    "Content-Type": "application/json",
    PrivateKey: `${paymentCredentials.privateKey}`,
  }
  const axiosData = {
    transactionID: data.fdTransactionId
  }

  const rpayoutUrl = config.get('env') === 'production' ? RPAYOUT_INTEGRATION_CONSTANT.PAYOUT_STATUS_CHECK_API_PROD : RPAYOUT_INTEGRATION_CONSTANT.PAYOUT_STATUS_CHECK_API_STAGE
  const apiConfig = {
    method: "post",
    url: rpayoutUrl,
    headers: axiosHeaders,
    data: axiosData,
  }

  const apiResponse = await axios(apiConfig)
    .then(function (response) {
      return response
    })
    .catch(function (error, response) {
      return error
    })

  await db.RequestResponseLog.create({
    requestJson: {
      headers: axiosHeaders,
      data: axiosData,
    },
    responseJson: apiResponse?.data,
    service: "pending_transaction_status",
    tenantId: data.tenantId,
  })

  return apiResponse?.data
}
