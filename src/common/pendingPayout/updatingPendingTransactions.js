import { Sequelize, Op } from 'sequelize'
import db from '../../db/models'
import { INDIGRIT_WITHDRAW, SKY_WITHDRAW, PAYMENT_GATEWAY_QUEUE_TYPE, CRON_LOG_STATUS } from '../constants'
import { PaymentGateway, PaymentGatewayQueue } from '../../queues/paymentGateway.queue'

export default async (data) => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const transactionType = PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_WITHDRAWAL_TRANSACTION
    const queue = PaymentGatewayQueue
    const queueName = PaymentGateway
    const jobTime = new Date()

    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'update_failed_withdraw_transactions',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      cronLog.cronId = queueProcessStatus?.id
      const findPendingTxn = await db.WithdrawRequest.findAll({
        attributes: ['id'],
        where: {
          status: {
            [Op.in]: ['pending_by_gateway', 'in_progress']
          },
          verify_status: 'verified',
          withdrawal_type: 'payment_gateway',
          payment_provider_name: data
        },
        raw: true
      })

      const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
      if (findPendingTxn.length > 0) {
        for (const txn of findPendingTxn) {
          await delay(1000)
          await Promise.all([
            await queue.add(
              queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                transactionId: txn.id
              },
              {
                jobId: `${txn.id}_${queueName}`,
                removeOnComplete: true,
                delay: 10
              }
            )
          ])
        }
      }
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log('==========error==========', e)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
