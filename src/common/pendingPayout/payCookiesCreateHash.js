import * as crypto from 'crypto';
export function generateSignatureHash (parameters, salt, hashingMethod = 'sha512') {
  if (salt === undefined || salt.trim() === '') {
    throw new Error('Salt value is required to generate signature hash.');
  }
  Object.keys(parameters).forEach((key) => {
    if (parameters[key] === undefined || parameters[key].toString().trim() === '' ||
      key === 'signature') {
      delete parameters[key];
    }
  });
  let secureHash = null;
  const sortedKeys = Object.keys(parameters).sort();
  let hashData = salt;
  for (const key of sortedKeys) {
    const value = parameters[key];
    if (value.length > 0) {
      hashData += '|' + value.toString().trim();
    }
  }
  console.log("hashdata : ", hashData);
  if (hashData.length > 0) {
    const hash = crypto.createHash(hashingMethod);
    hash.update(hashData);
    secureHash = hash.digest('hex').toUpperCase();
  } return secureHash;
}
