import { PAYOUT_WITHDRAW_PROVIDER, SKY_WITHDRAW, PAYWINGS_WITHDRAW, RPAYOUT_WITHDRAW, EASY_RUPIA_WITHDRAW, PAYCOOKIES_WITHDRAW, ACKOPAY_WITHDRAW, XAMAX_WITHDRAW_PROVIDER, UU_WALLET_WITHDRAW_PROVIDER } from "../constants";
import db from '../../db/models';
import payoutAxiosCall from "./payoutAxiosCall";
import skyAxiosCall from "./skyAxiosCall";
import paywingsAxiosCal from "./paywingsAxiosCall"
import rpayoutAxiosCalL from './rpayoutAxiosCall'
import easyRupiaAxiosCall from "./easyRupiaAxiosCall"
import payCookiesAxiosCall from "./payCookiesAxiosCall";
import xamaxPayoutAxiosCall from "./xamaxPayoutAxiosCall";
import { generateSignatureHash } from "./payCookiesCreateHash";
import ackoPayAxiosCall from "./ackoPayAxiosCall";
import uuWalletPayoutAxiosCall from "./uuWalletPayoutAxiosCall";

export default async (data) => {

  try {
    const paymentProviders = await db.paymentProviders.findOne({
      attributes: ['id'],
      where: {
        providerName: data.payment_provider_name,
        active: true,
        providerType: 'withdrawal'
      },
      include: {
        model: db.tenantPaymentConfiguration,
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: data.tenantId,
        },
        required: true
      },
      raw: true
    })

    if (paymentProviders && paymentProviders["tenantPaymentConfigurations.providerKeyValues"]) {
      const paymentCredentials = paymentProviders["tenantPaymentConfigurations.providerKeyValues"];

      if (data.payment_provider_name == SKY_WITHDRAW) {
        const res = await skyAxiosCall(data, paymentCredentials)
        return res
      }

      if (data.payment_provider_name === PAYWINGS_WITHDRAW) {
        const res = await paywingsAxiosCal(data, paymentCredentials)
        return res
      }

      if (data.payment_provider_name == PAYOUT_WITHDRAW_PROVIDER) {
          const res = await payoutAxiosCall(data, paymentCredentials)
          return res
      }

      if (data.payment_provider_name == RPAYOUT_WITHDRAW) {
        const res = await rpayoutAxiosCalL(data, paymentCredentials)
        return res
      }

      if (data.payment_provider_name === EASY_RUPIA_WITHDRAW) {
        const res = await easyRupiaAxiosCall(data, paymentCredentials)
        return res
      }
      if (data.payment_provider_name === PAYCOOKIES_WITHDRAW) {
        const res = await payCookiesAxiosCall(data, paymentCredentials)
        if(res?.data?.signature){
          const resSign = res?.data?.signature
          // validation check (hash)
          const hash = await generateSignatureHash(res?.data, paymentCredentials?.salt)
          if (hash === resSign) {
            return res
          }
          else {
            throw new Error("Signature did not match");
          }
        }else{
          return res
        }

      }
      if (data.payment_provider_name === ACKOPAY_WITHDRAW) {
        const res = await ackoPayAxiosCall(data, paymentCredentials)
        return res
      }
      if (data.payment_provider_name === XAMAX_WITHDRAW_PROVIDER) {
        const res = await xamaxPayoutAxiosCall(data, paymentCredentials)
        return res
      }
      if (data.payment_provider_name === UU_WALLET_WITHDRAW_PROVIDER) {
        const res = await uuWalletPayoutAxiosCall(data, paymentCredentials)
        return res
      }
    }
  } catch (err) {
    throw err
  }
}
