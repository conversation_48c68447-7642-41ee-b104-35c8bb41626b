import db from "../../db/models";
import cancelTransactionLedger from "../../common/pendingPayout/cancelTransactionLedger";
import { Op } from "sequelize";
import cancelDepositBonus from '../cancelDepositBonus'
import cancelLosingBonus from '../cancelLosingBonus'
import { bulkDataForSmartico } from '../../common/commonFunctions'

export default async (withdrawalRequest, transaction, apiRes, sequelizeTransaction) => {

  const txnIds = [];
  const bulkData = []
  if(apiRes.status.code !== 100){

    txnIds.push(withdrawalRequest.transactionId);
    await db.WithdrawRequest.update(
      {
        withdrawal_type: "payment_gateway",
        actioned_at: new Date(),
        status: 'rejected_by_gateway',
      },
      {
        where: { id: withdrawalRequest.id },
        transaction: sequelizeTransaction,
      }
    );

      await db.Transaction.update(
        {
          status: "cancelled",
          comments: "Rejected By payment Gateway",
          createdAt: new Date().toISOString(),
        },
        {
          where: {
            id: withdrawalRequest.transactionId,
            tenantId: withdrawalRequest.tenantId,
          },
          transaction: sequelizeTransaction,
        }
      );

      const userWalletMain = await db.Wallet.findOne({
        where: { id: transaction.sourceWalletId, ownerType: "User" },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet,
        },
        skipLocked: false,
      });
      await userWalletMain.reload({
        lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet },
        transaction: sequelizeTransaction,
      });

      userWalletMain.amount += parseFloat(transaction.amount);
      await userWalletMain.save({ transaction: sequelizeTransaction });

      const requestData = {
        transaction: transaction,
        withdrawRequest: withdrawalRequest,
        userWallet: userWalletMain,
      };
      const cancelTrnId = await cancelTransactionLedger(requestData);
      if (cancelTrnId) {
        txnIds.push(cancelTrnId);
      }

      //Entry in queueLog
      const queueLog = await db.QueueLog.create(
        {
          type: "user_transaction",
          ids: [withdrawalRequest.userId],
        },
        { transaction: sequelizeTransaction }
      );

      if (withdrawalRequest.transactionId) {
        const queueLog = await db.QueueLog.create(
          {
            type: "casino_transaction",
            ids: txnIds,
          },
          { transaction: sequelizeTransaction }
        );
        bulkDataForSmartico(bulkData, withdrawalRequest?.tenantId, false, withdrawalRequest?.transactionId)
        await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })
      }
      return true
  }
  if (apiRes.status.code === 100 && apiRes.status.returnMessage === 'Success') {

    try {
      if (withdrawalRequest) {
        txnIds.push(withdrawalRequest.transactionId);
        let status = "cancelled";

        let statusToUpdate = apiRes.data.status == 'Completed' ? "approved" : (apiRes.data.status == 'Failed' ? "rejected_by_gateway" : "pending_by_gateway")

        await db.WithdrawRequest.update(
          {
            withdrawal_type: "payment_gateway",
            actioned_at: new Date(),
            status: statusToUpdate,
          },
          {
            where: { id: withdrawalRequest.id },
            transaction: sequelizeTransaction,
          }
        );

        status = "approved by Payment Gateway";

        if (apiRes.data.status == 'Completed') {
          const type = 'payout'
          await cancelLosingBonus(sequelizeTransaction, withdrawalRequest.userId, withdrawalRequest.tenantId, type)
          await cancelDepositBonus(sequelizeTransaction, withdrawalRequest.userId, withdrawalRequest.tenantId, type)

          if (transaction) {
            await db.Transaction.update(
              {
                status: "success",
                comments: "Approved By Payment gateway",
                createdAt: new Date().toISOString(),
              },
              {
                where: {
                  id: withdrawalRequest.transactionId,
                  tenantId: withdrawalRequest.tenantId,
                },
                transaction: sequelizeTransaction,
              }
            );
            bulkDataForSmartico(bulkData, withdrawalRequest?.tenantId, true, withdrawalRequest?.transactionId)
            await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })
          }
        } else if (apiRes.data.status == 'Failed') {
          status = "rejected by Payment Gateway";

          await db.Transaction.update(
            {
              status: "cancelled",
              comments: "Rejected By payment Gateway",
              createdAt: new Date().toISOString(),
            },
            {
              where: {
                id: withdrawalRequest.transactionId,
                tenantId: withdrawalRequest.tenantId,
              },
              transaction: sequelizeTransaction,
            }
          );

          const userWalletMain = await db.Wallet.findOne({
            where: { id: transaction.sourceWalletId, ownerType: "User" },
            transaction: sequelizeTransaction,
            lock: {
              level: sequelizeTransaction.LOCK.UPDATE,
              of: db.Wallet,
            },
            skipLocked: false,
          });
          await userWalletMain.reload({
            lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet },
            transaction: sequelizeTransaction,
          });

          userWalletMain.amount += parseFloat(transaction.amount);
          await userWalletMain.save({ transaction: sequelizeTransaction });

          const requestData = {
            transaction: transaction,
            withdrawRequest: withdrawalRequest,
            userWallet: userWalletMain,
          };
          const cancelTrnId = await cancelTransactionLedger(requestData);
          if (cancelTrnId) {
            txnIds.push(cancelTrnId);
          }

          //Entry in queueLog
          const queueLog = await db.QueueLog.create(
            {
              type: "user_transaction",
              ids: [withdrawalRequest.userId],
            },
            { transaction: sequelizeTransaction }
          );
          bulkDataForSmartico(bulkData, withdrawalRequest?.tenantId, false, withdrawalRequest?.transactionId)
          await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })
        }

        if (withdrawalRequest.transactionId) {
          const queueLog = await db.QueueLog.create(
            {
              type: "casino_transaction",
              ids: txnIds,
            },
            { transaction: sequelizeTransaction }
          );
        }

        return {message: 'Success'}
      }
    } catch (error) {
      console.log(
        "======pending Payment Gateway Transaction worker error",
        error
      );
      throw error;
    }
  }
  //console.log('process payment ends');
}
