import axios from "axios"
import db from '../../db/models'
import CryptoJS from 'crypto-js';

export default async (data, paymentCredentials) => {
  const dateObject = new Date(data.actionedAt);
  const dateString = dateObject.toISOString().split("T")[0];

  const hashData =
    "Merchant_RefID=" + data.fdTransactionId + "|Txn_Date=" + dateString;

  const hash = CryptoJS.HmacSHA256(hashData, paymentCredentials.payoutApiKey);
  const gen_hmac = CryptoJS.enc.Hex.stringify(hash);

  let apiConfig = {
    method: "post",
    url: paymentCredentials.baseUrl + "/payout/TransferStatus",
    headers: {
      "Content-Type": "application/json",
      Authorization:
        "Basic " +
        Buffer.from(
          paymentCredentials.merchantUserId +
            ":" +
            paymentCredentials.payoutApiKey
        ).toString("base64"),
    },
    data: {
      Merchant_RefID: data.fdTransactionId,
      Txn_Date: dateString,
      Hash: gen_hmac,
    },
  };
  const apiResponse = await axios(apiConfig)
    .then(function (response) {
      return response;
    })
    .catch(function (error, response) {
      return error;
    });

  await db.RequestResponseLog.create({
    requestJson: {
      headers: {
        "Content-Type": "application/json",
        Authorization:
          "Basic " +
          Buffer.from(
            paymentCredentials.merchantUserId +
              ":" +
              paymentCredentials.payoutApiKey
          ).toString("base64"),
      },
      data: {
        Merchant_RefID: data.fdTransactionId,
        Txn_Date: dateString,
        Hash: gen_hmac,
      },
    },
    responseJson: apiResponse?.data,
    service: "pending_transaction_status",
    tenantId: data.tenantId,
  });

  return apiResponse?.data;
};
