import axios from "axios"
import db from '../../db/models'

export default async (data, paymentCredentials) => {
  const apiData = {
    clientId: paymentCredentials.clientId,
    secretKey: paymentCredentials.secretKey,
    clientOrderId: data.fdTransactionId,
  }

  try {
    let apiConfig = {
      method: "post",
      url: paymentCredentials.getStatusApiEndPoint,
      headers: { "Content-Type": "application/json" },
      data: apiData,
    };
    const apiResponse = await axios(apiConfig)
      .then(function (response) {
        return response
      })
      .catch(function (error, response) {
        return error
      })

    await db.RequestResponseLog.create({
      requestJson: {
        headers: {},
        data: apiData,
      },
      responseJson: { data: apiResponse?.data },
      service: "ackopay_pending_transaction_update",
      tenantId: data.tenantId,
    });
    return apiResponse?.data;
  } catch (err) {
    return err
  }
};
