import axios from "axios"
import db from '../../db/models'

export default async (data, paymentCredentials) => {

  // =============== generating JWT token ===============

  const appId = paymentCredentials.appId
  const apiKey = paymentCredentials.apiKey
  const basicAuth = Buffer.from( appId + ":" + apiKey ).toString("base64")
  const axiosTokenHeaders = {
    "Content-Type": "application/json",
    Authorization: `Basic ${basicAuth}`,
  }
  let apiTokenConfig = {
    method: "get",
    headers:axiosTokenHeaders,
    url: paymentCredentials.getTokenEndPoint,
  }
  const apiToken = await axios(apiTokenConfig)
    .then(function (response) {
      return response
    })
    .catch(function (error, response) {
      return error
    })

    console.log('jwt token res',apiToken );
    console.log('exact data',apiToken.data.data.token );

    // =============== generating JWT token ===============
  const axiosHeaders = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${apiToken.data.data.token}`,
  }

  let apiConfig = {
    method: "get",
    url: `${paymentCredentials.getStatusApiEndpoint}${data.fdTransactionId}`,
    headers: axiosHeaders,
  }

  let apiResponse
  try {
    apiResponse = await axios(apiConfig)
  } catch (err) {
    apiResponse = err.response
  }

  await db.RequestResponseLog.create({
    requestJson: {
      headers: axiosHeaders,
      data: {'url' : `${paymentCredentials.getStatusApiEndpoint}${data.fdTransactionId}`},
    },
    responseJson: apiResponse?.data,
    service: "paycookies_pending_transaction_update",
    tenantId: data.tenantId,
  })
  return apiResponse?.data
}
