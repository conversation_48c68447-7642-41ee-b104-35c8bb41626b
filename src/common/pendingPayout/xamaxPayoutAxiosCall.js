import axios from "axios"
import db from '../../db/models'

export default async (data, paymentCredentials) => {

  // ===========================================> Destructure keys <==========================================
  let { apiKey, tokenApi, payoutStatusCheckApi } = paymentCredentials;
  payoutStatusCheckApi = payoutStatusCheckApi + '/' + data.fdTransactionId;

  // ========================================> Generate Access Token <========================================
  const tokenApiBody = {
    'refresh_token': apiKey
  };
  const tokenResponse = await axios.post(tokenApi, tokenApiBody, {
    maxBodyLength: Infinity,
    headers: {
      maxBodyLength: Infinity,
      'Content-Type': 'application/x-www-form-urlencoded',
    }
  });
  const accessToken = tokenResponse.data.access_token;

  // =====================================> Call payout status check API <====================================
  const APIConfig = {
    maxBodyLength: Infinity,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    }
  };

  const withdrawAPIResponse = await axios.get(
    payoutStatusCheckApi,
    {
      ...APIConfig,
      validateStatus: function (status) {
        // Accept any status code (2xx, 3xx, 4xx, 5xx, etc.)
        return true; // Return true to resolve the promise for all status codes
      }
    }
  );

  // ========================================> Request Response Log <========================================
  await db.RequestResponseLog.create({
    requestJson: {
      headers: APIConfig.headers,
      data: {'url' : payoutStatusCheckApi},
    },
    responseJson: withdrawAPIResponse.data,
    service: "xamax_pending_transaction_update",
    tenantId: data.tenantId,
  });

  // ==================================> Return Payout Status API Response <=================================
  return withdrawAPIResponse;
}
