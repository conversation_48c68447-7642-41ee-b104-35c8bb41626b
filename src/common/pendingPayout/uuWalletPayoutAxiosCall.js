import axios from "axios";
import FormData from 'form-data';
import db from '../../db/models';
import { uuWalletDecryptByPublicKeyForOut, uuWalletEncryptByPublicKeyForOut } from "../../libs/encryption";

export default async (data, paymentCredentials) => {

  // ===========================================> Destructure keys <==========================================
  const { merchantId, apiUrl, apiKey, publicKey } = paymentCredentials
  const payoutStatusCheckApi = `${apiUrl}/api/order/getWithdrawOrder`

  // =====================================> Call payout status check API <====================================

  const symbol = data.ifscCode?.trim()
  const chainName = data.name?.trim()
  const toAddress = data.accountNumber?.trim()

  const APIBody = {
    callBackId: data.fdTransactionId?.trim(),
    userId: merchantId,
    tenantUserId: data.userId,
    tenantType: 1,
    symbol: symbol,
    chainName: chainName,
    toAddr: toAddress,
    tag: '',
  }

  const encryptedData = await uuWalletEncryptByPublicKeyForOut(APIBody, publicKey);

  if (!encryptedData) {
    throw new Error('Encryption Failed');
  }

  const form = new FormData()

  form.append('data', encryptedData)

  const APIConfig = {
    method: 'post',
    headers: {
      'X-API-KEY': apiKey,
      ...form.getHeaders()
    },
  };

  const config = {
    method: 'post',
    url: payoutStatusCheckApi,
    headers: APIConfig.headers,
    data: form,
    validateStatus: function (status) {
      // Accept any status code (2xx, 3xx, 4xx, 5xx, etc.)
      return true; // Return true to resolve the promise for all status codes
    }
  };

  let withdrawAPIResponse = await axios(config)
  let responseData = withdrawAPIResponse.data
  const decryptedResponseData = await uuWalletDecryptByPublicKeyForOut(responseData?.data, publicKey);
  if (decryptedResponseData) {
    withdrawAPIResponse.data.data = decryptedResponseData
  }

  // ========================================> Request Response Log <========================================
  await db.RequestResponseLog.create({
    requestJson: {
      headers: APIConfig.headers,
      data: { 'url': payoutStatusCheckApi },
    },
    responseJson: withdrawAPIResponse.data,
    service: "uu_wallet_pending_transaction_update",
    tenantId: data.tenantId,
  });

  // ==================================> Return Payout Status API Response <=================================
  return withdrawAPIResponse;
}
