import { v4 as uuidv4 } from 'uuid'
import db from '../../db/models';
import { Op } from 'sequelize'

export default async (data) => {
  try {

    const transaction = data.transaction
    const withdrawRequest = data.withdrawRequest
    const userWallet = data.userWallet
    const sequelizeTransaction = data.sequelizeTransaction;
    const uuid = uuidv4().replace(/-/g, '')
    const uniqueId = uuid.substr(uuid.length - 10)

    let insertData = {
      actioneeType: withdrawRequest.actionableType,
      actioneeId: withdrawRequest.actionableId,
      targetBeforeBalance: (parseFloat(userWallet.amount) - parseFloat(withdrawRequest.amount)),
      targetAfterBalance: userWallet.amount,
      status: 'success',
      tenantId: transaction.tenantId,
      createdAt: new Date(),
      comments: 'This withdrawal request was rejected by the payment provider and pertains to transaction ID ' + transaction.id,
      transactionType: 38,
      amount: withdrawRequest.amount,
      paymentMethod: withdrawRequest.mode,
      transactionId: uniqueId,
      targetWalletId: transaction.sourceWalletId,
      targetCurrencyId: transaction.sourceCurrencyId,
      cancelTransactionId: transaction?.id,
      conversionRate: transaction.conversionRate
    }


    const currencyAllowedCurrencies = await db.TenantConfiguration.findOne({
      where: { tenantId: transaction.tenantId },
      attributes: ['allowedCurrencies'],
    })
    const currencyAllAllowed = await db.Currency.findAll({
      where: { id: { [Op.in]: currencyAllowedCurrencies?.allowedCurrencies?.split(',') } },
    })

    const exMainTransactionCurrency = transaction.sourceCurrencyId
    const otherCurrencyAmount = {}
    const [currencyExchangeRate] = currencyAllAllowed.filter((item) => {
      return item.id == exMainTransactionCurrency
    })

    for (const record of currencyAllAllowed) {
      if (exMainTransactionCurrency !== record.id) {
        const convertedAmountOther = (parseFloat(withdrawRequest.amount) * (record.exchangeRate / currencyExchangeRate.exchangeRate)).toFixed(4)
        otherCurrencyAmount[record.code] = parseFloat(convertedAmountOther)
      } else {
        otherCurrencyAmount[record.code] = parseFloat(withdrawRequest.amount)
      }
    }
    insertData = {
      ...insertData,
      otherCurrencyAmount: JSON.stringify(otherCurrencyAmount)
    };

    const createdValues = await db.Transaction.create(insertData, { ...(sequelizeTransaction && {transaction: sequelizeTransaction}) })
    return createdValues.id;

  }
  catch (e) {
    throw e
  }

}
