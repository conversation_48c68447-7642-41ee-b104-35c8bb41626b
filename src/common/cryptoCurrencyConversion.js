import axios from "axios";
import db from "../db/models";
export default async () => {
  try {

    const {
      CryptoCurrency: CryptoCurrencyModel,
      CryptoCurrencyResponse: CryptoCurrencyResponseModel,
      CryptoConversionHistory: CryptoConversionHistoryModel,
    } = db

    const allCryptoCurrencies = await CryptoCurrencyModel.findAll({
      where: {},
      attributes: ['id', 'name'],
      raw: true
    })

    let cryptoCurrencyNames = allCryptoCurrencies.map(el => el.name)

    cryptoCurrencyNames = cryptoCurrencyNames.join(',')

    const baseUrl = 'https://api.coingecko.com/api/v3/simple/price'
    const baseCurrency = 'EUR'

    let config = {
      maxBodyLength: Infinity,
      headers: {}
    };
    const requestUrl = `${baseUrl}?ids=${cryptoCurrencyNames}&vs_currencies=${baseCurrency}`
    let currencyResponse = await axios.get(requestUrl, config);

    if (currencyResponse && currencyResponse?.status == 200) {

      currencyResponse = transformCurrencyResponse(baseCurrency, currencyResponse.data)

      const oldCurrencyResponse = await CryptoCurrencyResponseModel.findOne({
        where: {},
        order: [['createdAt', 'desc']]
      })

      const newCurrencyResponse = await CryptoCurrencyResponseModel.create({
        response: currencyResponse
      })

      for (const currency of allCryptoCurrencies) {
        const currencyName = currency.name.toLowerCase()
        const exchangeData = currencyResponse?.rates
        if (exchangeData && exchangeData[currencyName]) {
          let exchangeRate = exchangeData[currencyName]
          if (exchangeRate) {
            exchangeRate = parseFloat(exchangeRate).toFixed(5);

            // Update Exchange Rate
            await CryptoCurrencyModel.update({
              exchangeRate: exchangeRate,
              currencyResponseId: newCurrencyResponse.id
            }, {
              where: {
                id: currency.id
              }
            })

            await CryptoConversionHistoryModel.create({
              currencyId: currency.id,
              oldExchangeRate: currency.exchangeRate ? currency.exchangeRate : exchangeRate,
              newExchangeRate: exchangeRate,
              oldCurrencyResponseId: oldCurrencyResponse ? oldCurrencyResponse.id : newCurrencyResponse.id,
              newCurrencyResponseId: newCurrencyResponse.id,
            });
          }
        }
      }
    }

    return { success: 1, message: 'Success' }

  } catch (error) {
    return false
  }
}

function transformCurrencyResponse (baseCurrency, currencyResponse) {
  const result = {
    base: baseCurrency,
    rates: {}
  };

  for (const [currency, value] of Object.entries(currencyResponse)) {
    const rate = value.eur;
    if (typeof rate === 'number' && rate > 0) {
      result.rates[currency] = parseFloat((1 / rate).toFixed(6));
    }
  }

  return result;
}
