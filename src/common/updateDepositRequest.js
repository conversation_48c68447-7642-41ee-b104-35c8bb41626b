import { Op, Sequelize } from 'sequelize'
import db, { sequelize } from '../db/models'
import { DEPOSIT_REQUEST_STATUS, CRON_LOG_STATUS } from '../common/constants'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'update_deposit_request_cron',
        status: 1
      },
      attributes: ['id']
    })
    if(queueProcessStatus){
      cronLog.cronId = queueProcessStatus?.id
      const payInIds = await db.TenantCredential.findOne({
        where: {
          key: 'PAYIN_IDS'
        },
        attributes: ["value"]
      })
      await db.DepositRequest.update({
        status: DEPOSIT_REQUEST_STATUS.FAILED,
        remark: 'opened to failed'
      },
        {
          where: {
            status: DEPOSIT_REQUEST_STATUS.OPEN,
            paymentProviderId: { [Op.in]: payInIds.value.split(',') },
            createdAt: Sequelize.literal("created_at < now() - interval '24 hours'")
          }
        }
      )
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log("==========error", e)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
