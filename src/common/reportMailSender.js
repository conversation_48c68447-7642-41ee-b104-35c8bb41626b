import FormData from 'form-data'
import Mailgun from 'mailgun.js'
import nodemailer from 'nodemailer'
import { Op } from 'sequelize'
import { PROD_TENANTS, STAGE_TENANTS } from '../common/constants'
import config from '../configs/app.config'
import db from '../db/models'
import { EMAIL_PROVIDERS, EMAIL_PROVIDERS_CREDENTIAL_KEYS } from '../utils/constants/constant'

async function getCredentials (tenantId, credentialKeys, csvId) {
  const credentials = await db.TenantCredential.findAll({
    where: {
      value: { [Op.and]: [{ [Op.ne]: null }, { [Op.ne]: '' }] },
      tenantId,
      key: { [Op.in]: credentialKeys }
    },
    attributes: ['key', 'value'],
    raw: true
  })

  if (credentials.length !== credentialKeys.length) {
    await db.ExportCsvCenter.update({ emailSent: false }, { where: { id: csvId } })
    throw new Error('Invalid email credentials configuration.')
  }

  return Object.fromEntries(credentials.map(c => [c.key, c.value]))
}

function getDownloadUrl (url, tenantId) {
  const isProdEnv = config.get('env') === 'production'
  const reactDownloadUrlPath = '/csv-download'
  const encodedUrl = Buffer.from(url).toString('base64')
  let redirectUrl
  if (isProdEnv) {
    redirectUrl = `www.${PROD_TENANTS[tenantId].domain}${reactDownloadUrlPath}?assetsToken=${encodedUrl}`
  } else {
    redirectUrl = `www.${STAGE_TENANTS[tenantId].domain}${reactDownloadUrlPath}?assetsToken=${encodedUrl}`
  }
  return redirectUrl
}
async function sendEmail (transporter, emailOptions) {
  try {
    await transporter.sendMail(emailOptions)
    console.log('Email sent successfully')
  } catch (error) {
    console.error('Error sending email:', error)
    throw error
  }
}

export default async function sendReportEmail (jobData) {
  const { id: csvId } = jobData

  try {
    const csvDetail = await db.ExportCsvCenter.findOne({
      where: { id: csvId },
      useMaster: true,
      raw: true
    })

    if (!csvDetail) {
      throw new Error('CSV not found')
    }

    const adminUser = await db.AdminUser.findOne({
      where: { id: csvDetail.adminId },
      attributes: ['reportingEmail', 'reportingEmailVerified', 'tenantId'],
      raw: true
    })

    if (!adminUser) {
      await db.ExportCsvCenter.update({ emailSent: false }, { where: { id: csvId } })
      throw new Error('Admin user not found')
    }
    const { reportingEmail, reportingEmailVerified, tenantId } = adminUser

    if (!reportingEmail || !reportingEmailVerified) {
      await db.ExportCsvCenter.update({ emailSent: false }, { where: { id: csvId } })
      throw new Error('Reporting email not verified or not configured')
    }

  const TenantEmailProvider = await db.TenantThemeSetting.findOne({
    where: { tenantId },
    attributes: ['emailGateway'],
    raw: true
  })


  const credentialKeys = EMAIL_PROVIDERS_CREDENTIAL_KEYS[TenantEmailProvider?.emailGateway]

    const credentialMap = await getCredentials(tenantId, credentialKeys, csvId)
    let transporter
    if(TenantEmailProvider?.emailGateway === EMAIL_PROVIDERS.SENDGRID) {

      transporter = nodemailer.createTransport({
        service: 'SendGrid',
        host: credentialMap.APP_SENDGRID_HOST,
        port: parseInt(credentialMap.APP_SENDGRID_PORT),
        auth: {
          user: credentialMap.APP_SENDGRID_USERNAME,
          pass: credentialMap.APP_SENDGRID_RELAY_KEY
        }
      })
    }
    const downloadUrl = getDownloadUrl(csvDetail.csvUrl, tenantId)

    const csvType = csvDetail.type
      .toLowerCase()
      .replace(/_/g, ' ')
      .replace(/\b\w/g, char => char.toUpperCase())

    const emailOptions = {
      from: (TenantEmailProvider?.emailGateway === EMAIL_PROVIDERS.SENDGRID) ? credentialMap.APP_SENDGRID_EMAIL : credentialMap.MAILGUN_FROM_EMAIL,
      to: reportingEmail,
      subject: `${csvType}`,
      html: `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Download Your Report</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
            }
            .container {
              background-color: #f9f9f9;
              border-radius: 5px;
              padding: 30px;
              box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }
            h1 {
              color: #2c3e50;
              margin-bottom: 20px;
            }
            p {
              margin-bottom: 20px;
            }
            .button {
              display: inline-block;
              background-color: #3498db;
              color: #ffffff;
              text-decoration: none;
              padding: 12px 24px;
              border-radius: 5px;
              font-weight: bold;
              text-align: center;
              transition: background-color 0.3s ease;
            }
            .button:hover {
              background-color: #2980b9;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>Your Report is Ready!</h1>
            <p>Click the button below to access your report:</p>
            <a href="${downloadUrl}" class="button" target="_blank">Download CSV Report</a>
          </div>
        </body>
        </html>
      `
      }

    if (TenantEmailProvider?.emailGateway === EMAIL_PROVIDERS.MAILGUN) {
      const mailgun = new Mailgun(FormData)
      const mg = mailgun.client({
        username: credentialMap?.MAILGUN_FROM_USERNAME || 'api',
        key: credentialMap?.MAILGUN_API_KEY
      })
      transporter = {
        sendMail: async (emailOptions) => {
          await mg.messages.create(
            credentialMap?.MAILGUN_DOMAIN,
            {
              from: credentialMap?.MAILGUN_FROM_EMAIL,
              to: emailOptions.to,
              subject: emailOptions.subject,
              html: emailOptions.html
            }
          )
        }
      }
    }

    await sendEmail(transporter, emailOptions)
    await db.ExportCsvCenter.update({ emailSent: true }, { where: { id: csvId } })
  } catch (error) {
    await db.ExportCsvCenter.update({ emailSent: false }, { where: { id: csvId } })
    throw error
  }
}
