import { literal } from 'sequelize';
import db from '../db/models';

export const findAdminRole = async (sequelizeTransaction, userId) => {
  try {
    return await db.AdminUsersAdminRole.findOne({
      raw: true,
      attributes: [[literal('name'), 'name']],
      where: { adminUserId: userId },
      include: [{
        model: db.AdminRole,
        required: true,
        attributes: [],
      }],
      transaction: sequelizeTransaction
    });
  } catch (error) {
    throw error;
  }
};


export const checkTenantPermissions = async (sequelizeTransaction, module, action, tenantId) => {
  try {
    const params = {
      module,
      action,
      tenant_id: tenantId
    };
    const tenantPermission = await db.TenantPermission.findOne({
      where: { tenantId: params.tenant_id },
      attributes: ['permission'],
      transaction: sequelizeTransaction,
    });
    if (tenantPermission) {
      const permissions = tenantPermission.permission;

      // Check if the module exists in permissions
      if (permissions.hasOwnProperty(params.module)) {
        const moduleActions = permissions[params.module];

        // Check if the action is in moduleActions
        if (Array.isArray(moduleActions) && moduleActions.includes(params.action)) {
          return true;
        }
      }
    }
    return false;
  } catch (error) {
    throw error;
  }
};
