import { CRON_LOG_STATUS } from '../common/constants';
import config from '../configs/app.config';
import db, { sequelize } from '../db/models';
import ErrorLogHelper from './errorLog';

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const isProdEnv = config.get('env') === 'production';
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'agent_revenue_report_cron',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      cronLog.cronId = queueProcessStatus?.id
      const currentISTDateTime = "NOW()::timestamp AT TIME ZONE 'UTC' at time zone 'Asia/Kolkata'";
      const yesterdayISTDate = `date(date(${currentISTDateTime}) - INTERVAL '1 day')`;

      // Delete all records from agent revenue tables for today
      let deleteQuery = `
        delete from agent_revenue_report_currency where agent_revenue_report_id in (
          select id from agent_revenue_report where date = ${yesterdayISTDate}
        );
        delete from agent_revenue_report where date = ${yesterdayISTDate};
      `;

      await sequelize.query(deleteQuery);

      // Call stored procedure
      const callAgentRevenueSP = `CALL update_agent_revenue_report(
        CURRENT_DATE,
        concat(CURRENT_DATE, ' 23:59:59.999999')::timestamp,
        ${isProdEnv}
      )`

      await sequelize.query(callAgentRevenueSP);
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log("=============> Error in add Agent Revenue Report cron:", e)
    await ErrorLogHelper.logError(e, null, null)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
