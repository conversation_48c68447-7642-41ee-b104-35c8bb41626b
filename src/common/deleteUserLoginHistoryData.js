import { CRON_LOG_STATUS } from '../common/constants'
import db, { sequelize } from '../db/models'

export default async () => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'delete_user_login_history',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      cronLog.cronId = queueProcessStatus?.id
      const query = 'DELETE FROM user_login_history ulh WHERE last_login_date < ( SELECT MAX(last_login_date) - INTERVAL \'15 days\' FROM user_login_history WHERE user_id = ulh.user_id);'
      await sequelize.query(query, {
        type: sequelize.QueryTypes.RAW
      })
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log('==========error', e)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
