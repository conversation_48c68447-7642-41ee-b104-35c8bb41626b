import { sequelize } from '../db/models';
import syncCategory from './syncCategory';

const { Op } = require('sequelize');

export default async (tenantIds,  providerId,  pageName, overrideOtherProviders, sync, transaction) => {
  // const { tenantIds, providerId, pageName, overrideOtherProviders, sync, transaction } = data
  // filter if 0 is passed
  const filteredTenantIds = tenantIds.filter(id => id != 0);


  if (overrideOtherProviders || sync) {
    let categoriesWithOldProvider = await sequelize.query(`
Select cc.id, cc.top_menu_id  from custom_category cc left join custom_category_games ccg on  ccg.category_id = cc.id
         join public.pages p on ccg.page_id = p.id
         where cc.tenant_id = 0  and cc.is_deleted = false
         and p.title = :providerTitle and ccg.provider_id = :casinoProviderId
and ccg.is_deleted = false
 GROUP BY  cc.id, cc.top_menu_id
`, {
      replacements: {
        casinoProviderId: providerId,
        providerTitle: pageName.replace(/_/g, ' '),
      },
      type: sequelize.QueryTypes.SELECT
    }
    )

    if (categoriesWithOldProvider && categoriesWithOldProvider.length > 0) {
      for (const category of categoriesWithOldProvider) {
        await syncCategory({
          id: {
            id: category.id,
            tenantIds: [...filteredTenantIds],
            topMenuId: category.top_menu_id
          }
        })
      }
    }


  }

}
