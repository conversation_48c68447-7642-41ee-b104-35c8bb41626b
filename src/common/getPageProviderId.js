import { Sequelize } from 'sequelize'
import config from '../configs/app.config'
import db, { sequelize } from '../db/models'
import { ST8_PROVIDER_ID_PROD, ST8_PROVIDER_ID_STAGE, STAGE_WHITECLIFF_PROVIDER, PROD_WHITECLIFF_PROVIDER } from './constants'

export default async (tenantId, providerId, seatId) => {
  const st8ProviderId = config.get('env') === 'development' ? ST8_PROVIDER_ID_STAGE : ST8_PROVIDER_ID_PROD
  const whiteCliffProviderId = config.get('env') === 'development' ? STAGE_WHITECLIFF_PROVIDER : PROD_WHITECLIFF_PROVIDER

  if (parseInt(providerId) === parseInt(st8ProviderId) || parseInt(providerId) === parseInt(whiteCliffProviderId)) {
    const query = 'SELECT title FROM get_provider_name(:seatId, :tenantId)'

    const pageTitleResult = await sequelize.query(query, {
      type: Sequelize.QueryTypes.SELECT,
      replacements: { seatId, tenantId },
      useMaster: false
    })

    if (!pageTitleResult || pageTitleResult.length === 0) {
      return false
    }

    const pageTitle = pageTitleResult[0]?.title

    const page = await db.Page.findAll({
      attributes: ['id', 'topMenuId', 'title'],
      where: {
        title: pageTitle,
        tenantId: tenantId
      },
      raw: true
    })

    if (!page || page.length === 0) {
      return false
    }

    return page
  } else {
    const query = `
    SELECT pages.id, pages.top_menu_id AS "topMenuId", pages.title
    FROM pages
    INNER JOIN casino_providers ON pages.title = casino_providers.name
    WHERE tenant_id = :tenantId AND casino_providers.id = :providerId
    `

    const page = await sequelize.query(query, {
      type: Sequelize.QueryTypes.SELECT,
      replacements: { tenantId, providerId },
      useMaster: false
    })

    if (!page || page.length === 0) {
      return false
    }

    return page
  }
}
