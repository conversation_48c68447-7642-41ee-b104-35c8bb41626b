import { Sequelize, Op } from 'sequelize'
import db, { sequelize } from '../db/models'
import pushInQueue from './pushInQueue'
import { PUSH_IN_QUEUE_PAGE_COUNT, PUSH_IN_QUEUE_CRON } from '../utils/constants/constant'

export default async () => {

  if (PUSH_IN_QUEUE_CRON) {
    const {count} = await db.QueueLog.findOne({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      ],
      where: {
        [Op.or]: [
          { status: 0 },
          { status: 3 }
        ],
        ids: {
          [Op.ne]: []
        },
        type: 'create_csv'
      },
      raw: true
    })

    const pages = Math.ceil(count / PUSH_IN_QUEUE_PAGE_COUNT)

    for (let offset = 0; offset< pages; offset++){
      const queueLog = await db.QueueLog.findAll({
        attributes: ['id'],
        where: {
          [Op.or]: [
            { status: 0 },
            { status: 3 }
          ],
          ids: {
            [Op.ne]: []
          },
          type: 'create_csv'
        },
        limit: PUSH_IN_QUEUE_PAGE_COUNT,
        offset:offset,
        order: [
          [ 'id', 'ASC' ]
        ],
        raw: true
      })
      if (queueLog) {
        const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
        for (const queue of queueLog) {
          try {
            await delay(50)
            await pushInQueue(queue.id)
          } catch (error) {
            console.log('==========error=========', error)
            await db.QueueLog.update(
              { status: 3 },
              { where: { id: queue.id } }
            )
          }
        }
      }
    }
  }
}
