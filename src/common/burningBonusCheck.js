import { burningBonusAmountCheck } from './burningBonusAmountCheck'
import { burningCashBonusAmountCheck } from './burningCashBonusAmountCheck'
import { sequelize } from '../db/models'

export const burningBonusCheck = async (userId, tenantId) => {
  const today = new Date()
  let burningAmount

  // burning joining bonus check
  const userBurningJoiningBonusHistory = await sequelize.query(
    'SELECT * FROM get_user_burning_joining_bonus_history(:user_id, :tenant_id, :current_date);',
    {
      replacements: {
        user_id: userId,
        tenant_id: tenantId,
        current_date: today.toISOString()
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  if (userBurningJoiningBonusHistory && userBurningJoiningBonusHistory.length > 0) {
    burningAmount = await burningBonusAmountCheck(userBurningJoiningBonusHistory[0])
    if (burningAmount > 0) return false
  }

  // burning promocode bonus check
  const userBurningPromocodeBonusHistory = await sequelize.query(
    'SELECT * FROM get_user_burning_promocode_bonus_history(:user_id, :tenant_id, :current_date);',
    {
      replacements: {
        user_id: userId,
        tenant_id: tenantId,
        current_date: today.toISOString()
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  if (userBurningPromocodeBonusHistory && userBurningPromocodeBonusHistory.length > 0) {
    burningAmount = await burningBonusAmountCheck(userBurningPromocodeBonusHistory[0]) // as burning option is there in non cash only
    if (burningAmount > 0) return false
  }

  // casino, sports, both casino and sports deposit burning bonus check
  const userBurningDepositBonusHistory = await sequelize.query(
    'SELECT * FROM get_user_burning_deposit_bonus_history(:user_id, :tenant_id, :current_date);',
    {
      replacements: {
        user_id: userId,
        tenant_id: tenantId,
        current_date: today.toISOString()
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  if (userBurningDepositBonusHistory && userBurningDepositBonusHistory.length > 0) {
    for (let i = 0; i < userBurningDepositBonusHistory.length; i++) {
      if (userBurningDepositBonusHistory[i].walletType === 0) {
        burningAmount = await burningCashBonusAmountCheck(userBurningDepositBonusHistory[i])
      } else {
        burningAmount = await burningBonusAmountCheck(userBurningDepositBonusHistory[i])
      }
      if (burningAmount > 0) return false
    }
  }

  // instant deposit burning bonus check
  const userBurningInstantDepositBonusHistory = await sequelize.query(
    'SELECT * FROM get_user_burning_instant_deposit_bonus_history(:user_id, :tenant_id, :current_date);',
    {
      replacements: {
        user_id: userId,
        tenant_id: tenantId,
        current_date: today.toISOString()
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  if (userBurningInstantDepositBonusHistory && userBurningInstantDepositBonusHistory.length > 0) {
    for (let i = 0; i < userBurningInstantDepositBonusHistory.length; i++) {
      if (userBurningInstantDepositBonusHistory[i].walletType === 0) {
        burningAmount = await burningCashBonusAmountCheck(userBurningInstantDepositBonusHistory[i])
      } else {
        burningAmount = await burningBonusAmountCheck(userBurningInstantDepositBonusHistory[i])
      }
      if (burningAmount > 0) return false
    }
  }

  // automatic active losing burning bonus check
  const userBurningAutomaticActiveLosingBonusHistory = await sequelize.query(
    'SELECT * FROM get_user_burning_automatic_active_losing_bonus_history(:user_id, :tenant_id, :current_date);',
    {
      replacements: {
        user_id: userId,
        tenant_id: tenantId,
        current_date: today.toISOString()
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  if (userBurningAutomaticActiveLosingBonusHistory && userBurningAutomaticActiveLosingBonusHistory.length > 0) {
    for (let i = 0; i < userBurningAutomaticActiveLosingBonusHistory.length; i++) {
      burningAmount = await burningBonusAmountCheck(userBurningAutomaticActiveLosingBonusHistory[i])
      if (burningAmount > 0) return false
    }
  }

  // burning claim days losing bonus check
  const userBurningClaimDaysLosingBonusHistory = await sequelize.query(
    'SELECT * FROM get_user_burning_claim_days_losing_bonus_history(:user_id, :tenant_id, :current_date);',
    {
      replacements: {
        user_id: userId,
        tenant_id: tenantId,
        current_date: today.toISOString()
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  if (userBurningClaimDaysLosingBonusHistory && userBurningClaimDaysLosingBonusHistory.length > 0) {
    for (let i = 0; i < userBurningClaimDaysLosingBonusHistory.length; i++) {
      burningAmount = await burningBonusAmountCheck(userBurningClaimDaysLosingBonusHistory[i])
      if (burningAmount > 0) return false
    }
  }

  // burning claim interval losing bonus check
  const userBurningClaimIntervalLosingBonusHistory = await sequelize.query(
    'SELECT * FROM get_user_burning_claim_interval_losing_bonus_history(:user_id, :tenant_id, :current_date);',
    {
      replacements: {
        user_id: userId,
        tenant_id: tenantId,
        current_date: today.toISOString()
      },
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    }
  )

  if (userBurningClaimIntervalLosingBonusHistory && userBurningClaimIntervalLosingBonusHistory.length > 0) {
    for (let i = 0; i < userBurningClaimIntervalLosingBonusHistory.length; i++) {
      burningAmount = await burningBonusAmountCheck(userBurningClaimIntervalLosingBonusHistory[i])
      if (burningAmount > 0) return false
    }
  }
  return true
}
