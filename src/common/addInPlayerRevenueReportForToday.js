import moment from 'moment-timezone';
import { QueryTypes } from 'sequelize';
import config from '../configs/app.config';
import db, { sequelize } from '../db/models';
import { CRON_LOG_STATUS, ZAMBIA_TENANT_ID } from './constants';
import ErrorLogHelper from './errorLog';

// moment.tz.setDefault('UTC');

export default async () => {
  const transaction = await sequelize.transaction();
  const cronLog = {};
  cronLog.startTime = new Date();
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'player_revenue_report_cron',
        status: 1
      },
      attributes: ['id']
    })
    const IST_TIMEZONE = 'Asia/kolkata';
    const ZAMBIA_TIMEZONE = 'Africa/Lusaka';
    if(queueProcessStatus){
      cronLog.cronId = queueProcessStatus.id;
      const DATE_FORMAT = 'YYYY-MM-DDTHH:mm:ss';

      let currentISTDate, fromDatePlayerRevenue, toDatePlayerRevenue;
      if (moment.tz(IST_TIMEZONE).format('HH:mm') === '00:00') {
        currentISTDate = moment.tz(IST_TIMEZONE).subtract(1, 'day').format('YYYY-MM-DD');
        fromDatePlayerRevenue = moment.tz(IST_TIMEZONE).subtract(1, 'day').startOf('day').utc().format(DATE_FORMAT) + '.000000Z';
        toDatePlayerRevenue = moment.tz(IST_TIMEZONE).subtract(1, 'day').endOf('day').utc().format(DATE_FORMAT) + '.999999Z';
      } else {
        currentISTDate = moment.tz(IST_TIMEZONE).format('YYYY-MM-DD');
        fromDatePlayerRevenue = moment.tz(IST_TIMEZONE).startOf('day').utc().format(DATE_FORMAT) + '.000000Z';
        toDatePlayerRevenue = moment.tz(IST_TIMEZONE).subtract(1, 'minute').endOf('minute').utc().format(DATE_FORMAT) + '.999999Z';
      }

      let currentZambiaDate, fromDatePlayerRevenueZambia, toDatePlayerRevenueZambia;
      if (moment.tz(ZAMBIA_TIMEZONE).format('HH:mm') === '00:00') {
        currentZambiaDate = moment.tz(ZAMBIA_TIMEZONE).subtract(1, 'day').format('YYYY-MM-DD');
        fromDatePlayerRevenueZambia = moment.tz(ZAMBIA_TIMEZONE).subtract(1, 'day').startOf('day').utc().format(DATE_FORMAT) + '.000000Z';
        toDatePlayerRevenueZambia = moment.tz(ZAMBIA_TIMEZONE).subtract(1, 'day').endOf('day').utc().format(DATE_FORMAT) + '.999999Z';
      } else {
        currentZambiaDate = moment.tz(ZAMBIA_TIMEZONE).format('YYYY-MM-DD');
        fromDatePlayerRevenueZambia = moment.tz(ZAMBIA_TIMEZONE).startOf('day').utc().format(DATE_FORMAT) + '.000000Z';
        toDatePlayerRevenueZambia = moment.tz(ZAMBIA_TIMEZONE).subtract(1, 'minute').endOf('minute').utc().format(DATE_FORMAT) + '.999999Z';
      }

      const zambiaTenantsData = {
        tenantId: ZAMBIA_TENANT_ID,
        currentDate: currentZambiaDate,
        startTime: fromDatePlayerRevenueZambia,
        endTime: toDatePlayerRevenueZambia
      };

      // Delete all records from player revenue tables for today
      let deleteQuery = `
        set enable_seqscan = false;

        delete from player_provider_other_currency where player_summary_provider_id in (
          select id from player_summary_provider_wise
          where (tenant_id = ${zambiaTenantsData.tenantId} and date = '${zambiaTenantsData.currentDate}') or (tenant_id != ${zambiaTenantsData.tenantId} and date = '${currentISTDate}')
        );
        delete from player_summary_provider_wise
        where (tenant_id = ${zambiaTenantsData.tenantId} and date = '${zambiaTenantsData.currentDate}') or (tenant_id != ${zambiaTenantsData.tenantId} and date = '${currentISTDate}');

        set enable_seqscan = true;
      `;

      await sequelize.query(deleteQuery, { transaction });

      const playerRevenueDataQuery = prepareTodayPlayerRevenueDataQuery(fromDatePlayerRevenue, toDatePlayerRevenue, currentISTDate, zambiaTenantsData);

      const playerRevenueData = await sequelize.query(playerRevenueDataQuery, { type: QueryTypes.SELECT, useMaster: false });

      await db.PlayerSummaryProviderWise.bulkCreate(playerRevenueData, { transaction, returning: false });

      // Adding delay between insertion
      const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
      await delay(1000)

      const playerRevenueOtherCurrencyDataQuery = prepareTodayPlayerRevenueOtherCurrencyDataQuery(fromDatePlayerRevenue, toDatePlayerRevenue, currentISTDate, zambiaTenantsData);

      const playerRevenueOtherCurrencyData = await sequelize.query(playerRevenueOtherCurrencyDataQuery, { type: QueryTypes.SELECT, useMaster: true, transaction });

      await db.PlayerProviderOtherCurrency.bulkCreate(playerRevenueOtherCurrencyData, { transaction, returning: false });

      // Delete all records from agent revenue tables for today
      deleteQuery = `
        set enable_seqscan = false;

        delete from agent_revenue_report_currency where agent_revenue_report_id in (
          select id from agent_revenue_report
          where (tenant_id = ${zambiaTenantsData.tenantId} and date = '${zambiaTenantsData.currentDate}') or (tenant_id != ${zambiaTenantsData.tenantId} and date = '${currentISTDate}')
        );
        delete from agent_revenue_report
        where (tenant_id = ${zambiaTenantsData.tenantId} and date = '${zambiaTenantsData.currentDate}') or (tenant_id != ${zambiaTenantsData.tenantId} and date = '${currentISTDate}');

        set enable_seqscan = true;
      `;

      await sequelize.query(deleteQuery, { transaction });

      // Adding delay between insertion
      await delay(1000);

      let currentISTDateTimeSql, currentISTDateSql, toDateAgentRevenue, currentZambiaDateTimeSql, currentZambiaDateSql, toDateAgentRevenueZambia;
      if (moment.tz(IST_TIMEZONE).format('HH:mm') === '00:00') {
        currentISTDateTimeSql = "(NOW()::timestamp AT TIME ZONE 'UTC' at time zone 'Asia/Kolkata') - INTERVAL '1' DAY";
        currentISTDateSql = `date(${currentISTDateTimeSql})`;
        toDateAgentRevenue = `concat(${currentISTDateSql}, ' 23:59:59.999999')::timestamp`;
      } else {
        currentISTDateTimeSql = "NOW()::timestamp AT TIME ZONE 'UTC' at time zone 'Asia/Kolkata'";
        currentISTDateSql = `date(${currentISTDateTimeSql})`;
        toDateAgentRevenue = `concat(${currentISTDateSql}, ' 23:59:59.999999')::timestamp`;
      }

      if (moment.tz(ZAMBIA_TIMEZONE).format('HH:mm') === '00:00') {
        currentZambiaDateTimeSql = "(NOW()::timestamp AT TIME ZONE 'UTC' at time zone 'Africa/Lusaka') - INTERVAL '1' DAY";
        currentZambiaDateSql = `date(${currentZambiaDateTimeSql})`;
        toDateAgentRevenueZambia = `concat(${currentZambiaDateSql}, ' 23:59:59.999999')::timestamp`;
      } else {
        currentZambiaDateTimeSql = "NOW()::timestamp AT TIME ZONE 'UTC' at time zone 'Africa/Lusaka'";
        currentZambiaDateSql = `date(${currentZambiaDateTimeSql})`;
        toDateAgentRevenueZambia = `concat(${currentZambiaDateSql}, ' 23:59:59.999999')::timestamp`;
      }

      const isProdEnv = config.get('env') === 'production';

      // Call stored procedure
      const callAgentRevenueSP = `CALL update_agent_revenue_report(
        ${currentISTDateSql},
        ${toDateAgentRevenue},
        ${isProdEnv},
        ${currentZambiaDateSql},
        ${toDateAgentRevenueZambia}
      )`;

      await sequelize.query(callAgentRevenueSP, { transaction });

      await transaction.commit();

    }
  } catch (e) {
    console.log("Player_revenue_report_today_cron_error:", e)
    await transaction.rollback();
    cronLog.status = CRON_LOG_STATUS.FAILED;
    cronLog.errorMsg = e.message || null;
    await ErrorLogHelper.logError(e, null, null)
  }
  cronLog.endTime = new Date();
  await db.CronLog.create(cronLog);
}

function prepareTodayPlayerRevenueDataQuery(startTime, endTime, currentISTDate, zambiaTenantsData) {
  return `
    with txs as (
    select
      amount,
      provider_id,
      game_id,
      table_id,
      seat_id,
      case
        when transaction_type in (0,8,20,21,23,24,27,28,32,34,36,46,54,55,56,61,62,64,66) then 'bet'
        when transaction_type in (1,9,22,33,35,31,29,30,25,26,53,57,58,63,65,67) then 'win'
        when transaction_type in (2,10,47) and target_wallet_id is not null then 'bet_refund'
        when transaction_type in (2,10,47) and source_wallet_id is not null then 'win_refund'
        when transaction_type in (5,11,12,15,17,37,41,44,48,52,59,68,69,70,71,72,73,74,75,76) then 'bonus_claim'
        when transaction_type in (6,39,40,42,43,49,50,51,60) then 'bonus_withdraw'
        when transaction_type in (45) then 'royalty_cash_bonus'
        when transaction_type in (3) then
          case
            when (actionee_type='AdminUser' and coalesce(comments,'') != 'Deposit Request Approved' and coalesce(payment_method,'') = 'manual') then 'cash_deposit_by_admin'
            when (coalesce(comments,'') = 'Deposit Request Approved') then 'manual_cash_deposit_by_user'
            when (coalesce(comments,'') = 'Deposit Request') then 'gateway_cash_deposit_by_user'
            else 'unknown'
          end
        when transaction_type in (4) then
          case
            when (coalesce(comments,'') = 'approved by admin') then 'manual_cash_withdraw_by_user'
            when (coalesce(comments,'') = 'Approved By Payment gateway') then 'gateway_cash_withdraw_by_user'
            when (status = 'success' and actionee_type = 'AdminUser' and coalesce(comments,'') not in ('approved by admin','cancelled by admin','Approved By Payment gateway')) then 'cash_withdraw_by_admin'
            else 'unknown'
          end
        else 'unknown'
      end as type,
      case
        when transaction_type in (0,4,6,8,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) then source_wallet_id
        when transaction_type in (1,3,5,9,11,12,15,17,22,25,26,29,30,31,33,35,37,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) then target_wallet_id
        when transaction_type in (2,10,47) and target_wallet_id is not null then target_wallet_id
        when transaction_type in (2,10,47) and source_wallet_id is not null then source_wallet_id
        else coalesce(target_wallet_id, source_wallet_id)
      end as user_wallet_id
    from transactions
      where  (
        (tenant_id = ${zambiaTenantsData.tenantId} and created_at between '${zambiaTenantsData.startTime}' and '${zambiaTenantsData.endTime}')
        OR
        (tenant_id != ${zambiaTenantsData.tenantId} and created_at between '${startTime}' and '${endTime}')
      )
      and (transaction_type not in (5,11,12,15,17,37,41,44,48,52,59,68,69,70,71,72,73,74,75,76) or status = 'success') -- success status for bonus claim type

    union all

    -- some transaction types are used in more than one types. add them using union all.
    -- for example, transaction type 3 is added in 'bonus claim' as well as 'non cash by admin' also.
    select
      case
        when ts.transaction_type in (31,33) then (tsm.meta_data->>'commissionAmount')::double precision
        else ts.amount
      end as amount,
      provider_id,
      game_id,
      table_id,
      seat_id,
      case
        when ts.transaction_type in (5) then 'non_cash_granted_by_admin'
        when ts.transaction_type in (6) then 'non_cash_withdraw_by_admin'
        when ts.transaction_type in (48) then 'one_time_bonus_deposit'
        when ts.transaction_type in (49) then 'one_time_bonus_withdraw'
        when ts.transaction_type in (44) then 'royalty_non_cash_bonus'
        when ts.transaction_type in (31,33) then 'commission'
        when ts.transaction_type in (59) then 'sports_free_bet_deposit'
        when ts.transaction_type in (60) then 'sports_free_bet_withdraw'
      end as type,
      case
        when ts.transaction_type in (5,31,33,44,48,59) then target_wallet_id
        when ts.transaction_type in (6,49,60) then source_wallet_id
      end as user_wallet_id
    from transactions ts
      left join transactions_meta_data tsm on (ts.transaction_type in (31,33) and ts.id = tsm.transaction_id)
    where (
        (ts.tenant_id = ${zambiaTenantsData.tenantId} and ts.created_at between '${zambiaTenantsData.startTime}' and '${zambiaTenantsData.endTime}')
        OR
        (ts.tenant_id != ${zambiaTenantsData.tenantId} and ts.created_at between '${startTime}' and '${endTime}')
      )
      and ts.transaction_type in (5,6,31,33,44,48,49,59,60)

    union all

    -- different condition for total withdraw.
    select
      amount,
      provider_id,
      game_id,
      table_id,
      seat_id,
      'total_withdraw' as type,
      source_wallet_id as user_wallet_id
    from transactions
      where  (
        (tenant_id = ${zambiaTenantsData.tenantId} and created_at between '${zambiaTenantsData.startTime}' and '${zambiaTenantsData.endTime}')
        OR
        (tenant_id != ${zambiaTenantsData.tenantId} and created_at between '${startTime}' and '${endTime}')
      )
      and transaction_type in (4,6,49,60)
      and status = 'success'
      and coalesce(comments, '') not in ('cancelled by admin', 'cancelled by the player', 'Pending confirmation from admin')

    union all

    -- different condition for transaction type deposit.
    select
      amount,
      provider_id,
      game_id,
      table_id,
      seat_id,
      'transaction_type_deposit' as type,
      target_wallet_id as user_wallet_id
    from transactions
    where  (
        (tenant_id = ${zambiaTenantsData.tenantId} and created_at between '${zambiaTenantsData.startTime}' and '${zambiaTenantsData.endTime}')
        OR
        (tenant_id != ${zambiaTenantsData.tenantId} and created_at between '${startTime}' and '${endTime}')
      )
    and transaction_type in (3)

    union all

    -- condition for bet non cash, win non cash, bet non cash refund and win non cash refund.
    select
      amount,
      provider_id,
      game_id,
      table_id,
      seat_id,
      case
        when transaction_type in (8,20,23,27,46,55,61,62,64,66) then 'bet_non_cash'
        when transaction_type in (9,25,29,57,63,65,67) then 'win_non_cash'
        when transaction_type in (10,47) AND target_wallet_id IS NOT NULL then 'bet_refund_non_cash'
        when transaction_type in (10,47) AND source_wallet_id IS NOT NULL then 'win_refund_non_cash'
        else 'unknown'
      end as type,
      case
        when transaction_type in (8,20,23,27,46,55,61,62,64,66) then source_wallet_id
        when transaction_type in (9,25,29,57,63,65,67) then target_wallet_id
        when transaction_type in (10,47) AND target_wallet_id IS NOT NULL then target_wallet_id
        when transaction_type in (10,47) AND source_wallet_id IS NOT NULL then source_wallet_id
      end as user_wallet_id
    from transactions
    where (
        (tenant_id = ${zambiaTenantsData.tenantId} and created_at between '${zambiaTenantsData.startTime}' and '${zambiaTenantsData.endTime}')
        OR
        (tenant_id != ${zambiaTenantsData.tenantId} and created_at between '${startTime}' and '${endTime}')
      )
      and transaction_type in (8,9,10,20,23,25,27,29,46,47,55,57,61,62,63,64,65,66,67)
  ),
  txs_type as (
    select
      txs.amount as amount,
      txs.type as type,
      coalesce(txs.provider_id,0) as provider_id,
      case when cp.name in ('Jetfair', 'Powerplay') then null
        when cp.name in ('Evolution', 'Ezugi', 'Arcade', 'Red Tiger', 'NetEnt') then cast(txs.table_id as varchar)
        when cp.name = 'Spribe' then txs.game_id
        else txs.seat_id
      end as game_id,
      w.owner_id as user_id,
      w.currency_id as currency_id
    from txs
      join wallets w on (
        w.owner_type = 'User' and
        w.id = txs.user_wallet_id
      )
      join users u on (u.id = w.owner_id)
      left join casino_providers cp on (cp.id = txs.provider_id)
    where txs.type != 'unknown'
    ),
  agg_data as (
    select
      txs.user_id as user_id,
      txs.type as type,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      txs.currency_id as currency_id,
      sum(txs.amount)::numeric(20,5) as amount,
      count(*) as count
    from txs_type txs
    group by
      txs.user_id,
      txs.type,
      txs.provider_id,
      txs.game_id,
      txs.currency_id
  ),
  prepare_insertion_agg_data as (
    -- Prepare those types which are directly available
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      case when txs.type = 'cash_deposit_by_admin' then 1
        when txs.type = 'royalty_cash_bonus' then 3
        when txs.type = 'royalty_non_cash_bonus' then 4
        when txs.type = 'non_cash_granted_by_admin' then 5
        when txs.type = 'manual_cash_deposit_by_user' then 6
        when txs.type = 'gateway_cash_deposit_by_user' then 7
        when txs.type = 'non_cash_withdraw_by_admin' then 8
        when txs.type = 'manual_cash_withdraw_by_user' then 9
        when txs.type = 'gateway_cash_withdraw_by_user' then 10
        when txs.type = 'cash_withdraw_by_admin' then 11
        when txs.type = 'total_withdraw' then 12
        when txs.type = 'one_time_bonus_deposit' then 14
        when txs.type = 'one_time_bonus_withdraw' then 15
        when txs.type = 'commission' then 18
        when txs.type = 'bet' then 19
        when txs.type = 'bonus_claim' then 23
        when txs.type = 'bonus_withdraw' then 24
        when txs.type = 'win' then 32
        when txs.type = 'bet_non_cash' then 36
        when txs.type = 'win_non_cash' then 34
        when txs.type = 'sports_free_bet_deposit' then 38
        when txs.type = 'sports_free_bet_withdraw' then 39
        else 0
      end as type,
      txs.currency_id as currency_id,
      txs.amount as amount,
      case when txs.type in ('cash_deposit_by_admin', 'non_cash_granted_by_admin',
        'manual_cash_deposit_by_user', 'gateway_cash_deposit_by_user', 'non_cash_withdraw_by_admin',
        'manual_cash_withdraw_by_user', 'gateway_cash_withdraw_by_user', 'cash_withdraw_by_admin',
        'bet_non_cash', 'win_non_cash') then txs.count
        else null
      end as tx_count
    from
      agg_data txs
    where
      txs.type in ('cash_deposit_by_admin', 'royalty_cash_bonus', 'royalty_non_cash_bonus', 'non_cash_granted_by_admin',
        'manual_cash_deposit_by_user', 'gateway_cash_deposit_by_user', 'non_cash_withdraw_by_admin', 'manual_cash_withdraw_by_user',
        'gateway_cash_withdraw_by_user', 'cash_withdraw_by_admin', 'total_withdraw',
        'one_time_bonus_deposit', 'one_time_bonus_withdraw', 'commission', 'bet', 'bonus_claim', 'bonus_withdraw', 'win',
        'bet_non_cash', 'win_non_cash', 'sports_free_bet_deposit', 'sports_free_bet_withdraw')

    union all

    -- Prepare those count related types which are directly available
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      case when txs.type = 'total_withdraw' then 17
        when txs.type = 'bet' then 25
        when txs.type = 'win' then 33
        else 0
      end as type,
      txs.currency_id as currency_id,
      txs.count as amount,
      null as tx_count
    from
      agg_data txs
    where
      txs.type in ('total_withdraw', 'bet', 'win')

    union all

    -- Prepare query for 'total_deposit' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      2 as type,
      txs.currency_id as currency_id,
      sum(txs.amount)::numeric(20,5) as amount,
      null as tx_count
    from
      agg_data txs
    where
      txs.type in ('transaction_type_deposit','non_cash_granted_by_admin','one_time_bonus_deposit','sports_free_bet_deposit')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'deposit_count' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      16 as type,
      txs.currency_id as currency_id,
      sum(txs.count)::numeric(20,5) as amount,
      null as tx_count
    from
      agg_data txs
    where
      txs.type in ('transaction_type_deposit','non_cash_granted_by_admin','one_time_bonus_deposit','sports_free_bet_deposit')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'win_after_refund' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      20 as type,
      txs.currency_id as currency_id,
      coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('win')), 0)
        - coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('win_refund')), 0) as amount,
      null as tx_count
    from
      agg_data txs
    where
      txs.type in ('win','win_refund')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'win_after_refund_count' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      26 as type,
      txs.currency_id as currency_id,
      coalesce(sum(coalesce(txs.count,0)) filter (where type in ('win')), 0)
        - coalesce(sum(coalesce(txs.count,0)) filter (where type in ('win_refund')), 0) as amount,
      null as tx_count
    from
      agg_data txs
    where
      txs.type in ('win','win_refund')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'bet_after_refund' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      29 as type,
      txs.currency_id as currency_id,
      coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bet')), 0)
        - coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bet_refund')), 0) as amount,
      null as tx_count
    from
      agg_data txs
    where
      txs.type in ('bet','bet_refund')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'bet_after_refund_count' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      30 as type,
      txs.currency_id as currency_id,
      coalesce(sum(coalesce(txs.count,0)) filter (where type in ('bet')), 0)
        - coalesce(sum(coalesce(txs.count,0)) filter (where type in ('bet_refund')), 0) as amount,
      null as tx_count
    from
      agg_data txs
    where
      txs.type in ('bet','bet_refund')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'bet_non_cash_after_refund' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      37 as type,
      txs.currency_id as currency_id,
      coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bet_non_cash')), 0)
        - coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bet_refund_non_cash')), 0) as amount,
      coalesce(sum(coalesce(txs.count,0)) filter (where type in ('bet_non_cash')), 0)
        - coalesce(sum(coalesce(txs.count,0)) filter (where type in ('bet_refund_non_cash')), 0) as tx_count
    from
      agg_data txs
    where
      txs.type in ('bet_non_cash','bet_refund_non_cash')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'win_non_cash_after_refund' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      35 as type,
      txs.currency_id as currency_id,
      coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('win_non_cash')), 0)
        - coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('win_refund_non_cash')), 0) as amount,
      coalesce(sum(coalesce(txs.count,0)) filter (where type in ('win_non_cash')), 0)
        - coalesce(sum(coalesce(txs.count,0)) filter (where type in ('win_refund_non_cash')), 0) as tx_count
    from
      agg_data txs
    where
      txs.type in ('win_non_cash','win_refund_non_cash')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'bonus' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      31 as type,
      txs.currency_id as currency_id,
      coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bonus_claim')), 0)
        - coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bonus_withdraw')), 0) as amount,
      null as tx_count
    from
      agg_data txs
    where
      txs.type in ('bonus_claim','bonus_withdraw')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'ggr' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      21 as type,
      txs.currency_id as currency_id,
      (coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bet')), 0)
          - coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bet_refund')), 0))
        - (coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('win')), 0)
          - coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('win_refund')), 0)) as amount,
      null as tx_count
    from
      agg_data txs
    where
      txs.type in ('bet','bet_refund','win','win_refund')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'ngr' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      22 as type,
      txs.currency_id as currency_id,
      ((coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bet')), 0)
          - coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bet_refund')), 0))
        - (coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('win')), 0)
          - coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('win_refund')), 0)))
        - (coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bonus_claim')), 0)
          - coalesce(sum(coalesce(txs.amount,0)) filter (where type in ('bonus_withdraw')), 0)) as amount,
      null as tx_count
    from
      agg_data txs
    where
      txs.type in ('bet','bet_refund','win','win_refund','bonus_claim','bonus_withdraw')
      -- and (txs.provider_id != 0 or txs.game_id is not null)
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id
  ),
  insertion_agg_data as (
    select
      txs.user_id as "userId",
      u.tenant_id as "tenantId",
      txs.provider_id as "providerId",
      txs.game_id as "gameId",
      txs.type as "type",
      txs.currency_id as "currencyId",
      u.parent_id as "agentId",
      txs.amount as "amount",
      txs.tx_count as "txCount"
    from
      prepare_insertion_agg_data txs
      join users u on (txs.user_id = u.id)
    where txs.amount != 0
  )
  select
    *,
    case
      when "tenantId" = ${zambiaTenantsData.tenantId} then '${zambiaTenantsData.currentDate}'
      else '${currentISTDate}'
    end as "date"
  from insertion_agg_data;
  `;
}

function prepareTodayPlayerRevenueOtherCurrencyDataQuery(startTime, endTime, currentISTDate, zambiaTenantsData) {
  return `
    with txs as (
    select
      other_currency_amount::jsonb as amount,
      provider_id,
      game_id,
      table_id,
      seat_id,
      case
        when transaction_type in (0,8,20,21,23,24,27,28,32,34,36,46,54,55,56,61,62,64,66) then 'bet'
        when transaction_type in (1,9,22,33,35,31,29,30,25,26,53,57,58,63,65,67) then 'win'
        when transaction_type in (2,10,47) and target_wallet_id is not null then 'bet_refund'
        when transaction_type in (2,10,47) and source_wallet_id is not null then 'win_refund'
        when transaction_type in (5,11,12,15,17,37,41,44,48,52,59,68,69,70,71,72,73,74,75,76) then 'bonus_claim'
        when transaction_type in (6,39,40,42,43,49,50,51,60) then 'bonus_withdraw'
        when transaction_type in (45) then 'royalty_cash_bonus'
        when transaction_type in (3) then
          case
            when (actionee_type='AdminUser' and coalesce(comments,'') != 'Deposit Request Approved' and coalesce(payment_method,'') = 'manual') then 'cash_deposit_by_admin'
            when (coalesce(comments,'') = 'Deposit Request Approved') then 'manual_cash_deposit_by_user'
            when (coalesce(comments,'') = 'Deposit Request') then 'gateway_cash_deposit_by_user'
            else 'unknown'
          end
        when transaction_type in (4) then
          case
            when (coalesce(comments,'') = 'approved by admin') then 'manual_cash_withdraw_by_user'
            when (coalesce(comments,'') = 'Approved By Payment gateway') then 'gateway_cash_withdraw_by_user'
            when (status = 'success' and actionee_type = 'AdminUser' and coalesce(comments,'') not in ('approved by admin','cancelled by admin','Approved By Payment gateway')) then 'cash_withdraw_by_admin'
            else 'unknown'
          end
        else 'unknown'
      end as type,
      case
        when transaction_type in (0,4,6,8,20,21,23,24,27,28,32,34,36,39,40,41,42,43,46,49,50,51,54,55,56,60,61,62,64,66,73) then source_wallet_id
        when transaction_type in (1,3,5,9,11,12,15,17,22,25,26,29,30,31,33,35,37,44,45,48,52,53,57,58,59,63,65,67,68,69,70,71,72,74,75,76) then target_wallet_id
        when transaction_type in (2,10,47) and target_wallet_id is not null then target_wallet_id
        when transaction_type in (2,10,47) and source_wallet_id is not null then source_wallet_id
        else coalesce(target_wallet_id, source_wallet_id)
      end as user_wallet_id
    from transactions
      where  (
        (tenant_id = ${zambiaTenantsData.tenantId} and created_at between '${zambiaTenantsData.startTime}' and '${zambiaTenantsData.endTime}')
        OR
        (tenant_id != ${zambiaTenantsData.tenantId} and created_at between '${startTime}' and '${endTime}')
      )
      and (transaction_type not in (5,11,12,15,17,37,41,44,48,52,59,68,69,70,71,72,73,74,75,76) or status = 'success') -- success status for bonus claim type

    union all

    -- some transaction types are used in more than one types. add them using union all.
    -- for example, transaction type 3 is added in 'bonus claim' as well as 'non cash by admin' also.
    select
      case
        when ts.transaction_type in (31,33) then (tsm.meta_data->>'otherCurrencyCommissionAmountData')::jsonb
        else other_currency_amount::jsonb
      end as amount,
      provider_id,
      game_id,
      table_id,
      seat_id,
      case
        when ts.transaction_type in (5) then 'non_cash_granted_by_admin'
        when ts.transaction_type in (6) then 'non_cash_withdraw_by_admin'
        when ts.transaction_type in (48) then 'one_time_bonus_deposit'
        when ts.transaction_type in (49) then 'one_time_bonus_withdraw'
        when ts.transaction_type in (44) then 'royalty_non_cash_bonus'
        when ts.transaction_type in (31,33) then 'commission'
        when ts.transaction_type in (59) then 'sports_free_bet_deposit'
        when ts.transaction_type in (60) then 'sports_free_bet_withdraw'
      end as type,
      case
        when ts.transaction_type in (5,31,33,44,48,59) then target_wallet_id
        when ts.transaction_type in (6,49,60) then source_wallet_id
      end as user_wallet_id
    from transactions ts
      left join transactions_meta_data tsm on (ts.transaction_type in (31,33) and ts.id = tsm.transaction_id)
    where (
        (ts.tenant_id = ${zambiaTenantsData.tenantId} and ts.created_at between '${zambiaTenantsData.startTime}' and '${zambiaTenantsData.endTime}')
        OR
        (ts.tenant_id != ${zambiaTenantsData.tenantId} and ts.created_at between '${startTime}' and '${endTime}')
      )
      and ts.transaction_type in (5,6,31,33,44,48,49,59,60)

    union all

    -- different condition for total withdraw.
    select
      other_currency_amount::jsonb as amount,
      provider_id,
      game_id,
      table_id,
      seat_id,
      'total_withdraw' as type,
      source_wallet_id as user_wallet_id
    from transactions
      where  (
        (tenant_id = ${zambiaTenantsData.tenantId} and created_at between '${zambiaTenantsData.startTime}' and '${zambiaTenantsData.endTime}')
        OR
        (tenant_id != ${zambiaTenantsData.tenantId} and created_at between '${startTime}' and '${endTime}')
      )
      and transaction_type in (4,6,49,60)
      and (status = 'success'
        or comments not in ('cancelled by admin', 'cancelled by the player', 'Pending confirmation from admin'))

    union all

    -- different condition for transaction type deposit.
    select
      other_currency_amount::jsonb as amount,
      provider_id,
      game_id,
      table_id,
      seat_id,
      'transaction_type_deposit' as type,
      target_wallet_id as user_wallet_id
    from transactions
    where  (
        (tenant_id = ${zambiaTenantsData.tenantId} and created_at between '${zambiaTenantsData.startTime}' and '${zambiaTenantsData.endTime}')
        OR
        (tenant_id != ${zambiaTenantsData.tenantId} and created_at between '${startTime}' and '${endTime}')
      )
    and transaction_type in (3)

    union all

    -- condition for bet non cash, win non cash, bet non cash refund and win non cash refund.
    select
      other_currency_amount::jsonb as amount,
      provider_id,
      game_id,
      table_id,
      seat_id,
      case
        when transaction_type in (8,20,23,27,46,55,61,62,64,66) then 'bet_non_cash'
        when transaction_type in (9,25,29,57,63,65,67) then 'win_non_cash'
        when transaction_type in (10,47) AND target_wallet_id IS NOT NULL then 'bet_refund_non_cash'
        when transaction_type in (10,47) AND source_wallet_id IS NOT NULL then 'win_refund_non_cash'
        else 'unknown'
      end as type,
      case
        when transaction_type in (8,20,23,27,46,55,61,62,64,66) then source_wallet_id
        when transaction_type in (9,25,29,57,63,65,67) then target_wallet_id
        when transaction_type in (10,47) AND target_wallet_id IS NOT NULL then target_wallet_id
        when transaction_type in (10,47) AND source_wallet_id IS NOT NULL then source_wallet_id
      end as user_wallet_id
    from transactions
    where (
        (tenant_id = ${zambiaTenantsData.tenantId} and created_at between '${zambiaTenantsData.startTime}' and '${zambiaTenantsData.endTime}')
        OR
        (tenant_id != ${zambiaTenantsData.tenantId} and created_at between '${startTime}' and '${endTime}')
      )
      and transaction_type in (8,9,10,20,23,25,27,29,46,47,55,57,61,62,63,64,65,66,67)
  ),
  txs_type as (
    select
      txs.amount as amount,
      txs.type as type,
      coalesce(txs.provider_id,0) as provider_id,
      case when cp.name in ('Jetfair', 'Powerplay') then null
        when cp.name in ('Evolution', 'Ezugi', 'Arcade', 'Red Tiger', 'NetEnt') then cast(txs.table_id as varchar)
        when cp.name = 'Spribe' then txs.game_id
        else txs.seat_id
      end as game_id,
      w.owner_id as user_id,
      w.currency_id as currency_id
    from txs
      join wallets w on (
        w.owner_type = 'User' and
        w.id = txs.user_wallet_id
      )
      join users u on (u.id = w.owner_id)
      left join casino_providers cp on (cp.id = txs.provider_id)
    where txs.type != 'unknown'
    ),
  agg_data as (
    select
      txs.user_id as user_id,
      txs.type as type,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      txs.currency_id as currency_id,
      jsonb_build_object(
        'INR', coalesce(SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)), 0),
        'EUR', coalesce(SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)), 0),
        'USD', coalesce(SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)), 0),
        'ZMW', coalesce(SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)), 0),
        ${
        config.get('env') === 'production' ? '' :
        `'PHP', coalesce(SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)), 0),`
        }
        'LKR', coalesce(SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'BRL', coalesce(SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'GBP', coalesce(SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'YEN', coalesce(SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'uyl', coalesce(SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)), 0),`
        }
        'chips', coalesce(SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)), 0)
      ) as amount
    from txs_type txs
    group by
      txs.user_id,
      txs.type,
      txs.provider_id,
      txs.game_id,
      txs.currency_id
  ),
  prepare_insertion_agg_data as (
    -- Prepare those types which are directly available
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      case when txs.type = 'cash_deposit_by_admin' then 1
        when txs.type = 'royalty_cash_bonus' then 3
        when txs.type = 'royalty_non_cash_bonus' then 4
        when txs.type = 'non_cash_granted_by_admin' then 5
        when txs.type = 'manual_cash_deposit_by_user' then 6
        when txs.type = 'gateway_cash_deposit_by_user' then 7
        when txs.type = 'non_cash_withdraw_by_admin' then 8
        when txs.type = 'manual_cash_withdraw_by_user' then 9
        when txs.type = 'gateway_cash_withdraw_by_user' then 10
        when txs.type = 'cash_withdraw_by_admin' then 11
        when txs.type = 'total_withdraw' then 12
        when txs.type = 'one_time_bonus_deposit' then 14
        when txs.type = 'one_time_bonus_withdraw' then 15
        when txs.type = 'commission' then 18
        when txs.type = 'bet' then 19
        when txs.type = 'bonus_claim' then 23
        when txs.type = 'bonus_withdraw' then 24
        when txs.type = 'win' then 32
        when txs.type = 'bet_non_cash' then 36
        when txs.type = 'win_non_cash' then 34
        when txs.type = 'sports_free_bet_deposit' then 38
        when txs.type = 'sports_free_bet_withdraw' then 39
        else 0
      end as type,
      txs.currency_id as currency_id,
      txs.amount as amount
    from
      agg_data txs
    where
      txs.type in ('cash_deposit_by_admin', 'royalty_cash_bonus', 'royalty_non_cash_bonus', 'non_cash_granted_by_admin',
        'manual_cash_deposit_by_user', 'gateway_cash_deposit_by_user', 'non_cash_withdraw_by_admin', 'manual_cash_withdraw_by_user',
        'gateway_cash_withdraw_by_user', 'cash_withdraw_by_admin', 'total_withdraw',
        'one_time_bonus_deposit', 'one_time_bonus_withdraw', 'commission', 'bet', 'bonus_claim', 'bonus_withdraw', 'win', 'bet_non_cash', 'win_non_cash',
        'sports_free_bet_deposit', 'sports_free_bet_withdraw')

    union all

    -- Prepare query for 'total_deposit' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      2 as type,
      txs.currency_id as currency_id,
      jsonb_build_object(
        'INR', coalesce(SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)), 0),
        'EUR', coalesce(SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)), 0),
        'USD', coalesce(SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)), 0),
        'ZMW', coalesce(SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'PHP', coalesce(SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)), 0),`
        }
        'LKR', coalesce(SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'BRL', coalesce(SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'GBP', coalesce(SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'YEN', coalesce(SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'uyl', coalesce(SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)), 0),`
        }
        'chips', coalesce(SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)), 0)
      ) as amount
    from
      agg_data txs
    where
      txs.type in ('transaction_type_deposit','non_cash_granted_by_admin','one_time_bonus_deposit','sports_free_bet_deposit')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'win_after_refund' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      20 as type,
      txs.currency_id as currency_id,
      jsonb_build_object(
        'INR', coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('win_refund'))), 0),
        'EUR', coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('win_refund'))), 0),
        'USD', coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('win_refund'))), 0),
        'ZMW', coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('win_refund'))), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'PHP', coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('win_refund'))), 0),`
        }
        'LKR', coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('win_refund'))), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'BRL', coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('win_refund'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'GBP', coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('win_refund'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'YEN', coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('win_refund'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'uyl', coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('win_refund'))), 0),`
        }
        'chips', coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('win_refund'))), 0)
      ) as amount
    from
      agg_data txs
    where
      txs.type in ('win','win_refund')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'bet_after_refund' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      29 as type,
      txs.currency_id as currency_id,
      jsonb_build_object(
        'INR', coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bet_refund'))), 0),
        'EUR', coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bet_refund'))), 0),
        'USD', coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bet_refund'))), 0),
        'ZMW', coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bet_refund'))), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'PHP', coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bet_refund'))), 0),`
        }
        'LKR', coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bet_refund'))), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'BRL', coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bet_refund'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'GBP', coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bet_refund'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'YEN', coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bet_refund'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'uyl', coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bet_refund'))), 0),`
        }
        'chips', coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bet_refund'))), 0)
      ) as amount
    from
      agg_data txs
    where
      txs.type in ('bet','bet_refund')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'bet_non_cash_after_refund' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      37 as type,
      txs.currency_id as currency_id,
      jsonb_build_object(
        'INR', coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bet_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bet_refund_non_cash'))), 0),
        'EUR', coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bet_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bet_refund_non_cash'))), 0),
        'USD', coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bet_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bet_refund_non_cash'))), 0),
        'ZMW', coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bet_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bet_refund_non_cash'))), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'PHP', coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bet_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bet_refund_non_cash'))), 0),`
        }
        'LKR', coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bet_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bet_refund_non_cash'))), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'BRL', coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bet_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bet_refund_non_cash'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'GBP', coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bet_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bet_refund_non_cash'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'YEN', coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bet_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bet_refund_non_cash'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'uyl', coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bet_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bet_refund_non_cash'))), 0),`
        }
        'chips', coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bet_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bet_refund_non_cash'))), 0)
      ) as amount
    from
      agg_data txs
    where
      txs.type in ('bet_non_cash','bet_refund_non_cash')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'win_non_cash_after_refund' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      35 as type,
      txs.currency_id as currency_id,
      jsonb_build_object(
        'INR', coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('win_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('win_refund_non_cash'))), 0),
        'EUR', coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('win_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('win_refund_non_cash'))), 0),
        'USD', coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('win_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('win_refund_non_cash'))), 0),
        'ZMW', coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('win_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('win_refund_non_cash'))), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'PHP', coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('win_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('win_refund_non_cash'))), 0),`
        }
        'LKR', coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('win_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('win_refund_non_cash'))), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'BRL', coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('win_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('win_refund_non_cash'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'GBP', coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('win_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('win_refund_non_cash'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'YEN', coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('win_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('win_refund_non_cash'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'uyl', coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('win_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('win_refund_non_cash'))), 0),`
        }
        'chips', coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('win_non_cash'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('win_refund_non_cash'))), 0)
      ) as amount
    from
      agg_data txs
    where
      txs.type in ('win_non_cash','win_refund_non_cash')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'bonus' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      31 as type,
      txs.currency_id as currency_id,
      jsonb_build_object(
        'INR', coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0),
        'EUR', coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0),
        'USD', coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0),
        'ZMW', coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'PHP', coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0),`
        }
        'LKR', coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0),
        ${
          config.get('env') === 'production' ? '' :
        `'BRL', coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'GBP', coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'YEN', coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'uyl', coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0),`
        }
        'chips', coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0)
      ) as amount
    from
      agg_data txs
    where
      txs.type in ('bonus_claim','bonus_withdraw')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all


    -- Prepare query for 'ggr' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      21 as type,
      txs.currency_id as currency_id,
      jsonb_build_object(
        'INR', (coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('win_refund'))), 0)),
        'EUR', (coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('win_refund'))), 0)),
        'USD', (coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('win_refund'))), 0)),
        'ZMW', (coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('win_refund'))), 0)),

        ${
          config.get('env') === 'production' ? '' :
        `'PHP', (coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('win_refund'))), 0)),`
        }
        'LKR', (coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('win_refund'))), 0)),
        ${
          config.get('env') === 'production' ? '' :
        `'BRL', (coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('win_refund'))), 0)),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'GBP', (coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('win_refund'))), 0)),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'YEN', (coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('win_refund'))), 0)),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'uyl', (coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('win_refund'))), 0)),`
        }
        'chips', (coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('win_refund'))), 0))
      ) as amount
    from
      agg_data txs
    where
      txs.type in ('bet','bet_refund','win','win_refund')
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id

    union all

    -- Prepare query for 'ngr' type
    select
      txs.user_id as user_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      22 as type,
      txs.currency_id as currency_id,
      jsonb_build_object(
        'INR', ((coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('win_refund'))), 0))) - (coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'INR')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0)),
        'EUR', ((coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('win_refund'))), 0))) - (coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'EUR')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0)),
        'USD', ((coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('win_refund'))), 0))) - (coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'USD')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0)),
        'ZMW', ((coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('win_refund'))), 0))) - (coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'ZMW')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0)),

        ${
          config.get('env') === 'production' ? '' :
        `'PHP', ((coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('win_refund'))), 0))) - (coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'PHP')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0)),`
        }
        'LKR', ((coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('win_refund'))), 0))) - (coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'LKR')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0)),
        ${
          config.get('env') === 'production' ? '' :
        `'BRL', ((coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('win_refund'))), 0))) - (coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'BRL')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0)),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'GBP', ((coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('win_refund'))), 0))) - (coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'GBP')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0)),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'YEN', ((coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('win_refund'))), 0))) - (coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'YEN')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0)),`
        }
        ${
          config.get('env') === 'production' ? '' :
        `'uyl', ((coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('win_refund'))), 0))) - (coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'uyl')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0)),`
        }
        'chips', ((coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bet'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bet_refund'))), 0)) - (coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('win'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('win_refund'))), 0))) - (coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bonus_claim'))), 0) - coalesce((SUM(coalesce((txs.amount ->> 'chips')::numeric, 0)) filter (where type in ('bonus_withdraw'))), 0))
      ) as amount
    from
      agg_data txs
    where
      txs.type in ('bet','bet_refund','win','win_refund','bonus_claim','bonus_withdraw')
      -- and (txs.provider_id != 0 or txs.game_id is not null)
    group by
      txs.user_id,
      txs.provider_id,
      txs.game_id,
      txs.currency_id
  ),
  insertion_agg_data as (
    select
      txs.user_id as user_id,
      u.tenant_id as tenant_id,
      txs.provider_id as provider_id,
      txs.game_id as game_id,
      txs.type as type,
      txs.currency_id as currency_id,
      u.parent_id as agent_id,
      txs.amount as amount
    from
      prepare_insertion_agg_data txs
      join users u on (txs.user_id = u.id)
  ),
  today_player_summary_provider_wise_data as (
  	select * from player_summary_provider_wise
    where (tenant_id = ${zambiaTenantsData.tenantId} and date = '${zambiaTenantsData.currentDate}') or (tenant_id != ${zambiaTenantsData.tenantId} and date = '${currentISTDate}')
  )
  select
    pc.id as "playerSummaryProviderId",
    coalesce((kv.value)::NUMERIC(20, 5), 0) as "amount",
    c.id as "currencyId"
  from insertion_agg_data oc
    join today_player_summary_provider_wise_data pc on (
      pc.user_id = oc.user_id
      and pc.type = oc.type
      and coalesce(pc.provider_id, 0) = coalesce(oc.provider_id, 0)
      and pc.currency_id = oc.currency_id
      and coalesce(pc.game_id, '') = coalesce(oc.game_id, '')
    )
    cross join lateral jsonb_each_text(oc.amount) kv
    join currencies c on (c.code = kv.key)
    join tenant_configurations tc ON oc.tenant_id = tc.tenant_id AND c.id::text = ANY(STRING_TO_ARRAY(tc.allowed_currencies, ','))
  `;
}
