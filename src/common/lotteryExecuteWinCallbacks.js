import axios from 'axios';
import config from '../configs/app.config';
import db from '../db/models';
import { LOTTERY_JOB_CONSTANT } from './constants';

export default async (jobData) => {
  try {
    jobData = jobData?.data
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'lottery_win_callbacks_cron',
        status: 1
      },
      attributes: ['id']
    })
    if (!queueProcessStatus) {
      throw new Error('Queue Service Status Stopped  (lottery_win_callbacks_cron)')
    }

    const reqData = {
      amount: 0,
      reason: "Win Callback from Queue - 0 Win",
      game_id: jobData.gameId,
      user_id: jobData.userId,
      order_id: `${jobData.transactionId}____`,
      order_time: 1740745915,
      game_period: "20250228010751",
      bet_order_id: jobData.betOrderId,
    }

    const hostUrl = config.get('env') === 'production' ? LOTTERY_JOB_CONSTANT.PROD_EZUGI_URL : LOTTERY_JOB_CONSTANT.STAGE_EZUGI_URL

    const axiosConfig = {
      method: LOTTERY_JOB_CONSTANT.METHOD,
      maxBodyLength: LOTTERY_JOB_CONSTANT.MAX_BODY_LENGTH,
      url: hostUrl + '/api/v1/lottery777/transaction',
      headers: {
        ...LOTTERY_JOB_CONSTANT.HEADERS,
        host: hostUrl.replace("https://", "")
      },
      data: JSON.stringify(reqData)
    }

    const { data } = await axios(axiosConfig)

    return data
  } catch (e) {
    throw e
  }

}
