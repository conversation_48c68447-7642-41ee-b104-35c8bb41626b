/**
 * This function will help to find user currency exchange rate.
 * @export
 * @param {CurrencyId} currencyId contains user currency id
 * @param {object} context The argument object
 * @return {Result} user currency exchange rate
 */
export default async (Currency, currencyId) => {
  const CurrencyModel = Currency
  const currencyExchangeData = await CurrencyModel.findOne({
    where: {
      id: currencyId
    },
    attributes: ['exchangeRate'],
    raw: true
  })
  return currencyExchangeData.exchangeRate
}
