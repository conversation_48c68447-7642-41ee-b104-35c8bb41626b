import redis from '../libs/pubSubRedisClient'
import pushInQueue from '../common/pushInQueue'
export default async () => {
  const channelName = 'QUEUE_WORKER';

  redis.subscriberClient.on('message', (channel, message) => {
    if(channel === channelName){
      console.log(`Received the following Massage from ${channel}: ${message}`);
      const data = JSON.parse(message);
      if(data?.QueueLog?.queueLogId)
        pushInQueue(data.QueueLog.queueLogId)
    }
  });
  redis.subscriberClient.subscribe(channelName, (error, count) => {
      if (error) {
           console.log(`Received the following error from `,error);
      }
  });
  console.log('redis-subscribe');
}
