import { Client } from '@elastic/elasticsearch'
import config from '../configs/app.config'

export default function getESclient () {
  const elasticUrl = config.get('elastic.url') + ':' + config.get('elastic.port') || 'http://elasticsearch:9200'
  const protocol = config.get('elastic.protocal')
  const esClient = new Client({ node: protocol + config.get('elastic.user') + ':' + config.get('elastic.password') + '@' + elasticUrl })
  return esClient;
}
