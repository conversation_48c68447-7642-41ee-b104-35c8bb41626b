import { BonusEngineQueue } from '../queues/bonusEngine.queue'
import { CommonQueue } from '../queues/common.queue'
import { CreateCsvQueue } from '../queues/createCsv.queue'
import { ExportCsvQueue } from '../queues/exportCsv.queue'
import { PaymentGatewayQueue } from '../queues/paymentGateway.queue'
import { SmartiCoQueue } from '../queues/smartiCo.queue'
import { TransactionQueue } from '../queues/transaction.queue'
import Logger from './logger'

export default async function () {
  await CommonQueue.close()
  await SmartiCoQueue.close()
  await BonusEngineQueue.close()
  await TransactionQueue.close()
  await CreateCsvQueue.close()
  await ExportCsvQueue.close()
  await PaymentGatewayQueue.close()

  Logger.info('Close Queues', { message: 'Closed all queues, exiting gracefully' })
}
