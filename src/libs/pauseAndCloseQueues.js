import { BonusEngineQueue } from '../queues/bonusEngine.queue'
import { CommonQueue } from '../queues/common.queue'
import { CreateCsvQueue } from '../queues/createCsv.queue'
import { ExportCsvQueue } from '../queues/exportCsv.queue'
import { PaymentGatewayQueue } from '../queues/paymentGateway.queue'
import { SmartiCoQueue } from '../queues/smartiCo.queue'
import { TransactionQueue } from '../queues/transaction.queue'

import Logger from './logger'

export default async function () {
  // await demoQueue.pause(true)

  await CommonQueue.pause(true)
  await SmartiCoQueue.pause(true)
  await BonusEngineQueue.pause(true)
  await TransactionQueue.pause(true)
  await CreateCsvQueue.pause(true)
  await ExportCsvQueue.pause(true)
  await PaymentGatewayQueue.pause(true)

  Logger.info('Pause and Close Queues', { message: 'Paused all queues, exiting gracefully' })
}
