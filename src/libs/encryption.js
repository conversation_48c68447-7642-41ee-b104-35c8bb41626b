import crypto from 'crypto';
import CryptoJ<PERSON> from 'crypto-js';
import NodeRSA from 'node-rsa';

/**
 * This function creates the hash of message param and compares it with the given hash in context
 * @param {*} context -
 * @param {*} message - data to compare to hash found in context
 * @return {*} true/false depends if the given hash and message hash matches
 */
export async function hashCheck (ezugiCredentials, hash, message) {
  const shaHash = CryptoJS.HmacSHA256(message, ezugiCredentials.APP_EZUGI_HASH_KEY)
  const base64String = CryptoJS.enc.Base64.stringify(shaHash)
  return base64String === hash
}

export function encryptAES256CBC (jsonString, encryptionKey) {
  const algorithm = 'aes-256-cbc';
  const key = Buffer.from(encryptionKey, 'hex');
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(algorithm, key, iv);
  let encrypted = cipher.update(jsonString, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  const requestData = {
    cipher_text: encrypted,
    iv: iv.toString('hex'),
  };
  return JSON.stringify(requestData);
}

export function generateHmacSha256Signature(encryptData, hashSecret){
  const hmac = crypto.createHmac('sha256', hashSecret);
  const signatureHash = hmac.update(encryptData).digest('base64');
  return signatureHash;
}

export function  md5Encryption(dataString){
  return crypto.createHash('md5').update(dataString).digest('hex')
}

export function decryptAES256CBC(encryptedData, encryptionKey) {
  const algorithm = 'aes-256-cbc';
  const { cipher_text, iv } = JSON.parse(encryptedData);
  const key = Buffer.from(encryptionKey, 'hex');
  const ivBuffer = Buffer.from(iv, 'hex');
  const decipher = crypto.createDecipheriv(algorithm, key, ivBuffer);
  let decrypted = decipher.update(cipher_text, 'base64', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

export function encryptAES256GCM (payload, encryptionKey) {
  const cipher = 'aes-256-gcm'
  const iv = crypto.randomBytes(16)
  const jsonData = JSON.stringify(payload)
  const cipherInstance = crypto.createCipheriv(cipher, Buffer.from(encryptionKey, 'utf8'), iv)
  let encryptedData = cipherInstance.update(jsonData, 'utf8', 'base64')
  encryptedData += cipherInstance.final('base64')
  const tag = cipherInstance.getAuthTag()
  return {
    encrypted_data: encryptedData,
    iv: iv.toString('base64'),
    tag: tag.toString('base64')
  }
}

export function decryptAES256GCM (apiResponse, decryptionKey) {
  const cipher = 'aes-256-gcm'
  const key = Buffer.from(decryptionKey, 'utf-8')
  const ivBuffer = Buffer.from(apiResponse?.iv, 'base64')
  const tagBuffer = Buffer.from(apiResponse?.tag, 'base64')
  const { encrypted_data: encryptedData } = apiResponse || {}
  const encryptedBuffer = Buffer.from(encryptedData, 'base64')

  const decipher = crypto.createDecipheriv(cipher, Buffer.from(key, 'utf-8'), ivBuffer)
  decipher.setAuthTag(tagBuffer)

  let decrypted = decipher.update(encryptedBuffer, null, 'utf-8')
  decrypted += decipher.final('utf-8')
  return JSON.parse(decrypted)
}

export function encryptRSA (data, publicKey) {
  try {
    return crypto.publicEncrypt(
      {
        key: publicKey,
        padding: crypto.constants.RSA_PKCS1_PADDING
      },
      Buffer.from(data)
    );
  } catch (error) {
    return false
  }
}

export function decryptRSA (data,privateKey) {
  try {
    return crypto.privateDecrypt(
      {
        key: privateKey,
        padding: crypto.constants.RSA_PKCS1_PADDING
      },
      data
    );
  } catch (error) {
    return false
  }
}

export async function uuWalletEncryptByPublicKeyForOut (data, publicKeyBase64) {

  try {
    // Encrypt Data to base64
    data = Buffer.from(JSON.stringify(data))

    // Convert Base64 encoded DER format public key to PEM format
    // Node.js crypto module requires PEM format keys
    const publicKeyDER = Buffer.from(publicKeyBase64, 'base64');

    // Format as PEM by:
    // 1. Re-encoding to Base64 with line breaks every 64 characters
    // 2. Adding BEGIN/END PUBLIC KEY headers
    const publicKeyPEM = [
      '-----BEGIN PUBLIC KEY-----',
      publicKeyDER.toString('base64').match(/.{1,64}/g).join('\n'),
      '-----END PUBLIC KEY-----'
    ].join('\n');

    // Create public key object from PEM string
    const publicKey = crypto.createPublicKey({
      key: publicKeyPEM,
      format: 'pem',
      type: 'spki'
    });

    // Calculate encryption chunk size
    // RSA with PKCS#1 v1.5 padding requires:
    // data length <= key_size_in_bytes - 11
    const keySizeBytes = publicKey.asymmetricKeyDetails.modulusLength / 8;
    const chunkSize = keySizeBytes - 11;  // 11 bytes padding overhead

    // Convert input string to UTF-8 encoded Buffer
    const dataBuffer = Buffer.from(data, 'utf-8');
    const inputLen = dataBuffer.length;
    const encryptedChunks = [];

    // Chunked encryption loop
    let offset = 0;
    while (offset < inputLen) {
      // Calculate current chunk boundaries
      const end = Math.min(offset + chunkSize, inputLen);
      // Extract chunk from input buffer
      const chunk = dataBuffer.subarray(offset, end);
      // Encrypt the chunk using:
      // - RSA public key
      // - PKCS#1 v1.5 padding (same as Java's PKCS1Padding)
      const encrypted = crypto.publicEncrypt({
        key: publicKey,
        padding: crypto.constants.RSA_PKCS1_PADDING
      }, chunk);

      // Store encrypted chunk
      encryptedChunks.push(encrypted);
      // Move to next chunk
      offset = end;
    }

    // Combine all encrypted chunks into single Buffer
    const encryptedData = Buffer.concat(encryptedChunks);
    // Return as Base64 string (matching Java's Base64.getEncoder().encodeToString)
    return encryptedData.toString('base64');

  } catch (error) {
    return false
  }
}

export function uuWalletDecryptByPublicKeyForOut (dataBase64, publicKeyBase64) {
  try {
    // Convert Base64 encoded DER format public key to PEM
    const publicKeyDER = Buffer.from(publicKeyBase64, 'base64');
    const publicKeyPem = [
      '-----BEGIN PUBLIC KEY-----',
      publicKeyDER.toString('base64').match(/.{1,64}/g).join('\n'),
      '-----END PUBLIC KEY-----'
    ].join('\n');

    // 1. Create a NodeRSA instance and import the public key
    const key = new NodeRSA();
    key.importKey(publicKeyPem, 'pkcs8-public-pem');

    // 2. Ensure we use PKCS#1 v1.5 padding (same as Java’s Cipher.getInstance("RSA"))
    key.setOptions({ encryptionScheme: 'pkcs1' });

    // 3. Determine maximum block size for decryption:
    //    blockSize = keySizeInBits / 8 (e.g. 1024 bits → 128 bytes)
    const MAX_DECRYPT_BLOCK = key.getKeySize() / 8;

    // 4. Decode the Base64 input into a Buffer
    const encryptedBuf = Buffer.from(dataBase64, 'base64');

    // 5. Split buffer into blocks, decrypt each with the public key, collect results
    let offset = 0;
    const decryptedChunks = [];

    while (offset < encryptedBuf.length) {
      // Calculate end of the current block (don’t exceed buffer length)
      const end = Math.min(offset + MAX_DECRYPT_BLOCK, encryptedBuf.length);
      const chunk = encryptedBuf.slice(offset, end);

      // decryptPublic() performs the “public key decrypt” operation
      const decryptedChunk = key.decryptPublic(chunk);
      decryptedChunks.push(decryptedChunk);

      offset = end;
    }

    // 6. Concatenate all decrypted chunks and return as UTF-8 string
    const decryptedBuffer = Buffer.concat(decryptedChunks);
    const decryptedString = decryptedBuffer.toString('utf8')
    return JSON.parse(decryptedString);

  } catch (error) {
    return false
  }
}