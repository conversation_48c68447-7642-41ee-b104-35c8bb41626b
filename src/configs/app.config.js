import convict from 'convict'
import dotenv from 'dotenv'
import fs from 'fs'

if (fs.existsSync('.env')) {
  const envConfig = dotenv.parse(fs.readFileSync('.env'))

  for (const key in envConfig) {
    process.env[key] = envConfig[key]
  }
}

const config = convict({
  app: {
    name: {
      doc: 'Name of the service',
      format: String,
      default: 'queue-service',
      env: 'APP_NAME'
    }
  },

  env: {
    doc: 'The application environment.',
    format: ['production', 'development', 'staging', 'test'],
    default: 'development',
    env: 'NODE_ENV'
  },

  port: {
    doc: 'The port to bind.',
    format: 'port',
    default: 8080,
    env: 'PORT'
  },

  basic_auth: {
    username: {
      doc: 'Basic Auth User Name',
      format: String,
      default: 'rome_username',
      env: 'BASIC_AUTH_USERNAME'
    },
    password: {
      doc: 'Basic Auth User Password',
      format: String,
      default: 'rome_password',
      env: 'BASIC_AUTH_PASSWORD'
    }
  },
  db: {
    name: {
      doc: 'Database Name',
      format: String,
      default: 'api',
      env: 'DB_NAME'
    },
    username: {
      doc: 'Database user',
      format: String,
      default: 'postgres',
      env: 'DB_USERNAME'
    },
    password: {
      doc: 'Database password',
      format: '*',
      default: 'postgres',
      env: 'DB_PASSWORD'
    },
    host: {
      doc: 'DB host',
      format: String,
      default: '127.0.0.1',
      env: 'DB_HOST'
    },
    port: {
      doc: 'DB PORT',
      format: 'port',
      default: '5432',
      env: 'DB_PORT'
    },
    read_username: {
      doc: 'Database user',
      format: String,
      default: 'postgres',
      env: 'READ_DB_USERNAME'
    },
    read_password: {
      doc: 'Database password',
      format: '*',
      default: 'postgres',
      env: 'READ_DB_PASSWORD'
    },
    read_host: {
      doc: 'DB host',
      format: String,
      default: '127.0.0.1',
      env: 'READ_DB_HOST'
    },
    read_name: {
      doc: 'Database Name',
      format: String,
      default: '',
      env: 'READ_DB_NAME'
    },
    read_port: {
      doc: 'DB PORT',
      format: 'port',
      default: '5432',
      env: 'READ_DB_PORT'
    }
  },
  elastic: {
    url: {
      default: '',
      env: 'ELASTICSEARCH_HOST'
    },
    user: {
      default: '',
      env: 'ELASTICSEARCH_USER'
    },
    password: {
      default: '',
      env: 'ELASTICSEARCH_PASS'
    },
    port: {
      default: '',
      env: 'ELASTICSEARCH_PORT'
    },
    protocal: {
      default: '',
      env: 'ELASTICSEARCH_PROTOCAL'
    },
    httpCrtPath: {
      default: '',
      env: 'ELASTIC_HTTP_CRT_PATH'
    }
  },

  queue_worker_redis_db: {
    password: {
      doc: 'Redis Database password',
      format: '*',
      default: '',
      env: 'QUEUE_WORKER_REDIS_DB_PASSWORD'
    },
    host: {
      doc: 'Redis DB host',
      format: String,
      default: '127.0.0.1',
      env: 'QUEUE_WORKER_REDIS_DB_HOST'
    },
    port: {
      doc: 'Redis DB PORT',
      format: 'port',
      default: 6379,
      env: 'QUEUE_WORKER_REDIS_DB_PORT'
    }
  },

  pub_sub_redis_db: {
    password: {
      doc: 'Redis Database password',
      format: '*',
      default: '',
      env: 'PUB_SUB_REDIS_DB_PASSWORD'
    },
    host: {
      doc: 'Redis DB host',
      format: String,
      default: '127.0.0.1',
      env: 'PUB_SUB_REDIS_DB_HOST'
    },
    port: {
      doc: 'Redis DB PORT',
      format: 'port',
      default: 6379,
      env: 'PUB_SUB_REDIS_DB_PORT'
    }
  },

  log_level: {
    doc: 'level of logs to show',
    format: String,
    default: 'debug',
    env: 'LOG_LEVEL'
  },
  origin: {
    doc: 'cors origin',
    format: String,
    default: 'true',
    env: 'ORIGIN'
  },
  credentialEncryptionKey: {
    default: '',
    env: 'CREDENTIAL_ENCRYPTION_KEY'
  },
  queue_job: {
    apiUrl: {
      default: '',
      env: 'QUEUE_JOB_API_URL'
    },
    secretToken: {
      default: '',
      env: 'QUEUE_JOB_API_SECRET_TOKEN'
    }
  },
  es_index: {
    transactions_index_name: {
      doc: 'transactions index',
      format: String,
      default: 'transactions_development',
      env: 'TRANSACTIONS_INDEX_NAME'
    },
    users_index_name: {
      doc: 'users index',
      format: String,
      default: 'users_development',
      env: 'USERS_INDEX_NAME'
    },
    sports_index_name: {
      doc: 'bet index',
      format: String,
      default: 'bet_transaction',
      env: 'SPORT_INDEX_NAME'
    },
    audit_log_index_name: {
      doc: 'audit log index',
      format: String,
      default: 'audit_log',
      env: 'AUDIT_LOG_INDEX_NAME'
    }
  },
  s3: {
    region: {
      doc: 'Region where s3 located.',
      format: String,
      default: '',
      env: 'S3_REGION'
    },
    access_key_id: {
      doc: 'Access key for s3.',
      format: String,
      default: '',
      env: 'S3_ACCESS_KEY_ID'
    },
    secret_access_key: {
      doc: 'Secret key for s3.',
      format: String,
      default: '',
      env: 'S3_SECRET_ACCESS_KEY'
    },
    bucket: {
      doc: 'Bucket used in S3',
      format: String,
      default: '',
      env: 'S3_BUCKET'
    }
  },
  partnerMatrix: {
    host: {
      doc: 'Host of partnermatrix staging',
      format: String,
      default: '',
      env: 'PM_HOST'
    },
    user: {
      doc: 'username of partnermatrix staging',
      format: String,
      default: '',
      env: 'PM_USER'
    },
    password: {
      doc: 'password of partnermatrix staging',
      format: String,
      default: '',
      env: 'PM_PASSWORD'
    },
    API: {
      doc: 'API of partnermatrix staging',
      format: String,
      default: '',
      env: 'QUEUE_JOB_API_PARTNER_URL'
    },
    SECRET_TOKEN: {
      doc: 'Token of partnermatrix staging',
      format: String,
      default: '',
      env: 'SECRET_TOKEN'
    }
  },
  partnerMatrixAceXBet: {
    host: {
      doc: 'Host of partnermatrix staging',
      format: String,
      default: '',
      env: 'PM_HOST_ACEXBET'
    },
    user: {
      doc: 'username of partnermatrix staging',
      format: String,
      default: '',
      env: 'PM_USER_ACEXBET'
    },
    password: {
      doc: 'password of partnermatrix staging',
      format: String,
      default: '',
      env: 'PM_PASSWORD_ACEXBET'
    },
    API: {
      doc: 'API of partnermatrix staging',
      format: String,
      default: '',
      env: 'QUEUE_JOB_API_PARTNER_URL'
    },
    SECRET_TOKEN: {
      doc: 'Token of partnermatrix staging',
      format: String,
      default: '',
      env: 'SECRET_TOKEN'
    }
  },
  reportsEmail: {
    default: '<EMAIL>',
    env: 'CLIENT_REPORT_EMAIL' // Used to send comparative report in production
  },
  marina888ApiUrl: {
    userPasswordChangeUrl: {
      default: '',
      env: 'MARINA888_ROR_API_URL'
    },
    phpBaseUrl: {
      default: '',
      env: 'MARINA888_PHP_URL'
    }
  },
  smartiCo: {
    token: {
      default: '',
      env: 'SMARTICO_TOKEN'
    },
    url: {
      default: '',
      env: 'SMARTICO_URL'
    },
    extBrandId: {
      default: '',
      env: 'SMARTICO_EXT_BRAND_ID'
    }
  },
  marina888SecretKey: {
    secretKey: {
      default: '',
      env: 'MARINA888_SECRET_KEY'
    }
  },
  alanbase: {
    eventUrl: {
      default: '',
      env: 'ALANBASE_EVENT_URL'
    },
    goalUrl: {
      default: '',
      env: 'ALANBASE_GOAL_URL'
    }
  },
  wynta88Punt: {
    host: {
      doc: 'Host of wynta',
      format: String,
      default: '',
      env: 'WYNTA_88PUNT_HOST'
    },
    user: {
      doc: 'username of wynta',
      format: String,
      default: '',
      env: 'WYNTA_88PUNT_USER'
    },
    password: {
      doc: 'password of wynta',
      format: String,
      default: '',
      env: 'WYNTA_88PUNT_PASSWORD'
    }
  }

})

config.validate({ allowed: 'strict' })

export default config
