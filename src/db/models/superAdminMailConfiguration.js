
module.exports = (sequelize, DataTypes) => {
  const SuperAdminMailConfiguration = sequelize.define('SuperAdminMailConfiguration', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    displayKey: {
      type: DataTypes.STRING,
      allowNull: true
    },
    keyProvidedByAccount: {
      type: DataTypes.STRING,
      allowNull: true
    },
    value: {
      type: DataTypes.STRING,
      allowNull: false
    },
    parentType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    lastUpdatedParentId: {
      type: DataTypes.BIGINT,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'super_admin_mail_configuration',
    schema: 'public',
    timestamps: true
  })

  return SuperAdminMailConfiguration
}
