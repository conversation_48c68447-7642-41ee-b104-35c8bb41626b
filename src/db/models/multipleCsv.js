'use strict'
module.exports = (sequelize, DataTypes) => {
  const MultipleCsv = sequelize.define('MultipleCsv', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    csvId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    key: {
      type: DataTypes.STRING,
      allowNull: true
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    totalRecords: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'multiple_csv',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'multiple_csv_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return MultipleCsv
}
