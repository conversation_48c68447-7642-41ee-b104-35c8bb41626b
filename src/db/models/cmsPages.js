'use strict'
module.exports = (sequelize, DataTypes) => {
  const CmsPages = sequelize.define('CmsPage', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: true
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: true
    },
    content: {
      type: DataTypes.STRING,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    adminUserId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    enableCmsForRegister: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'cms_pages',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_pages_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'cms_pages_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return CmsPages
}
