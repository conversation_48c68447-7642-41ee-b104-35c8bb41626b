'use strict'
module.exports = (sequelize, DataTypes) => {
  const QueueProcessStatus = sequelize.define('QueueProcessStatus', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    service: {
      type: DataTypes.STRING,
      allowNull: true
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'queue_service_status',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'queue_service_status_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return QueueProcessStatus
}
