'use strict'
module.exports = (sequelize, DataTypes) => {
  const RiskFraudIndicators = sequelize.define('RiskFraudIndicators', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    depositSpikes: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    withdrawalSpikes: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    unusualBetSize: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    multiplePaymentMethodsUsed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    frequentLoginsDifferentIps: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    suspiciousAccountBehavior: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    riskScore: {
      type: DataTypes.SMALLINT,
      defaultValue: 1
    },
    potentialFraudRisk: {
      type: DataTypes.SMALLINT,
      defaultValue: 1
    },
    createdAt: {
      type: DataTypes.DATE,
    },
    updatedAt: {
      type: DataTypes.DATE,
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'risk_fraud_indicators',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'user_tenant_fraud_ukey',
        unique: true,
        fields: [
          'user_id',
          'tenant_id'
        ]
      }
    ]
  })

  return RiskFraudIndicators
}
