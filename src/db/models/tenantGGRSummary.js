module.exports = (sequelize, DataTypes) => {
  const TenantGGRSummary = sequelize.define('TenantGGRSummary', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false,
    },
    totalDebitAmount: {
      type: DataTypes.DOUBLE,
      allowNull: false
    },
    totalCreditAmount: {
      type: DataTypes.DOUBLE,
      allowNull: false
    },
    totalRollbackAmount: {
      type: DataTypes.DOUBLE,
      allowNull: false
    },
    totalDebitTrxns: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    totalCreditTrxns: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    totalRollbackTrxns: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    minDebitAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    maxDebitAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    ggr: {
      type: DataTypes.DOUBLE,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    cronId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    message: {
      type: DataTypes.STRING,
      allowNull: true
    },
    currentGgr: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_ggr_summary',
    timestamps: true,
    indexes: [
      {
        name: 'tenant_ggr_summary_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_tenant_ggr_summary_on_tenant_id',
        fields: [
          { name: 'tenantId' }
        ]
      },
    ]
  });

  return TenantGGRSummary;
};
