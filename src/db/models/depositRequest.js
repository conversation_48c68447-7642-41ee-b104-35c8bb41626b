'use strict'

module.exports = (sequelize, DataTypes) => {
  const DepositRequest = sequelize.define('DepositRequest', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    orderId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'opened'
    },
    ledgerId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    data: {
      type: DataTypes.HSTORE,
      allowNull: true,
      defaultValue: {}
    },
    paymentProviderId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'payment_providers',
        key: 'id'
      }
    },
    utrNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    transactionReceipt: {
      type: DataTypes.STRING,
      allowNull: true
    },
    amount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0
    },
    depositType: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'payment_gateway'
    },
    bankDetails: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    remark: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    actionId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    actionType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    sessionId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    trackingId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    paymentInitiate: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    countryCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    currencyConversion: {
      type: DataTypes.DECIMAL(12, 5),
      allowNull: true
    },
    requestedCurrencyCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    requestedAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    verifyStatus: {
      type: DataTypes.STRING,
      allowNull: true
    },
    oldAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    comment: {
      type: DataTypes.STRING,
      allowNull: true
    },
    checkerData: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    makerData: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    manualDepositType: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '1- bank, 2- virtual'
    },
    userRemark: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'deposit_requests',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'deposit_requests_pkey',
        unique: true,
        fields: [{ name: 'id' }]
      },
      {
        name: 'deposit_requests_order_id_status',
        fields: [
          { name: 'orderId' },
          { name: 'status' }
        ]
      },
      {
        name: 'index_deposit_requests_on_user_id',
        fields: [{ name: 'userId' }]
      },
      {
        name: 'index_deposit_requests_on_payment_provider_id',
        fields: [{ name: 'paymentProviderId' }]
      }
    ]
  })

  DepositRequest.associate = models => {
    DepositRequest.belongsTo(models.paymentProviders, {
      foreignKey: 'paymentProviderId'
    })
  }

  return DepositRequest
}
