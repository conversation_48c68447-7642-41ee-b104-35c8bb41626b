'use strict'
module.exports = (sequelize, DataTypes) => {
  const TenantBanner = sequelize.define('TenantBanner', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: DataTypes.INTEGER
    },
    tenantId: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    name: {
      allowNull: false,
      type: DataTypes.STRING
    },
    imageUrl: {
      default: false,
      type: DataTypes.STRING
    },
    redirectUrl: {
      default: false,
      type: DataTypes.STRING
    },
    bannerType: {
      allowNull: true,
      type: DataTypes.STRING
    },
    order: {
      allowNull: false,
      type: DataTypes.INTEGER
    },
    enabled: {
      allowNull: false,
      type: DataTypes.BOOLEAN
    },
    createdAt: {
      allowNull: false,
      type: DataTypes.DATE
    },
    updatedAt: {
      allowNull: false,
      type: DataTypes.DATE
    }
  }, {
    tableName: 'page_banners',
    underscored: true
  })

  TenantBanner.associate = models => {
    TenantBanner.belongsTo(models.Tenant, {
      onDelete: 'cascade',
      foreignKey: 'tenantId'
    })
  }
  return TenantBanner
}
