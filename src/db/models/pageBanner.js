'use strict'
module.exports = (sequelize, DataTypes) => {
  const PageBanner = sequelize.define('PageBanner', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    imageUrl: {
      type: DataTypes.STRING,
      allowNull: true

    },
    redirectUrl: {
      type: DataTypes.STRING,
      allowNull: true

    },
    bannerType: {
      type: DataTypes.STRING,
      allowNull: true

    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    enabled: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'page_banners',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_page_banners_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'page_banners_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return PageBanner
}
