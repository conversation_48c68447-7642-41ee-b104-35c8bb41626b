'use strict'
module.exports = (sequelize, DataTypes) => {
  const TransactionReceipt = sequelize.define('TransactionReceipt', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    transactionId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    receiptUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'transaction_receipts',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'transaction_receipts_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_transaction_receipts_on_tenant_id_and_transaction_id',
        unique: true,
        fields: [
          { name: 'tenant_id' },
          { name: 'transaction_id' }
        ]
      },
      {
        name: 'index_transaction_receipts_on_transaction_id',
        fields: [
          { name: 'transaction_id' },
        ]
      }
    ]
  })

    // Define associations
    TransactionReceipt.associate = (models) => {
      TransactionReceipt.belongsTo(models.Transaction, {
        foreignKey: 'transactionId',
        targetKey: 'id',
      })
    }

  return TransactionReceipt
}
