'use strict'
module.exports = (sequelize, DataTypes) => {
  const PlayerSummaryProviderWise = sequelize.define('PlayerSummaryProviderWise', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    providerId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    gameId: {
      type: DataTypes.STRING(150),
      allowNull: false
    },
    type: {
      type: DataTypes.SMALLINT,
      allowNull: false
    },
    currencyId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    agentId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    amount: {
      type: DataTypes.DECIMAL(20, 5),
    },
    txCount: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
    },
    updatedAt: {
      type: DataTypes.DATE,
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'player_summary_provider_wise',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'user_tenant_provider_type_transaction_uidx',
        unique: true,
        fields: [
          'user_id',
          'tenant_id',
          'type',
          'date',
          sequelize.literal('COALESCE(provider_id, 0)'),
          sequelize.literal("COALESCE(game_id, '')"),
          'currency_id',
          'agent_id'
        ]
      }
    ]
  })

  return PlayerSummaryProviderWise
}
