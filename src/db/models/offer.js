'use strict'

module.exports = (sequelize, DataTypes) => {
  const Offer = sequelize.define(
    'Offer',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true
      },
      tenantId: {
        type: DataTypes.BIGINT,
        allowNull: false
      },
      promotionTitle: {
        type: DataTypes.JSONB,
        allowNull: false
      },
      offerDescription: {
        type: DataTypes.JSONB,
        allowNull: false
      },
      validFrom: {
        type: DataTypes.DATE,
        allowNull: false
      },
      validTo: {
        type: DataTypes.DATE,
        allowNull: false
      },
      frequency: {
        type: DataTypes.SMALLINT,
        allowNull: false,
        comment: '0 - Daily, 1 - Weekly, 2 - Monthly, 3 - Specific Day of Week, 4 - Bi Monthly'
      },
      dayOfWeek: {
        type: DataTypes.SMALLINT,
        allowNull: true,
        comment: '1 - Monday, 2 - Tuesday, 3 - Wednesday, 4 - Thursday, 5 - Friday, 6 - Saturday, 7 - Sunday'
      },
      winningType: {
        type: DataTypes.SMALLINT,
        allowNull: false,
        comment: '0 - Rollover winning, 1 - GGR, 2 - NGR , 3 - Total Wagered'
      },
      image: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      status: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: true
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: true
      },
      minFakeUsersPercentage: {
        type: DataTypes.SMALLINT,
        allowNull: false,
        defaultValue: 0
      }
    },
    {
      sequelize,
      underscored: true,
      tableName: 'offers',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'offers_pkey',
          unique: true,
          fields: [
            { name: 'id' }
          ]
        }
      ]
    }
  )

  Offer.associate = (models) => {
    // One-to-Many relationship with Prizes
    Offer.hasMany(models.Prize, {
      foreignKey: 'offerId',
      as: 'prizes'
    })

    Offer.hasMany(models.OfferProvider, {
      foreignKey: 'offerId',
      onDelete: 'CASCADE'
    })

    Offer.hasMany(models.OfferFakeWinner, {
      foreignKey: 'offerId',
      onDelete: 'cascade'
    });
  }

  return Offer
}
