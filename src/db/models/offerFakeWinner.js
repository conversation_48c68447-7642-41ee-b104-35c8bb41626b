'use strict'
module.exports = (sequelize, DataTypes) => {
  const OfferFakeWinner = sequelize.define('OfferFakeWinner', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    offerId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    userName: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    winningAmount: {
      type: DataTypes.DECIMAL(20, 5),
      allowNull: false,
      default: 0
    },
    position: {
      type: DataTypes.SMALLINT,
      allowNull: false
    },
    percentage: {
      type: DataTypes.FLOAT(8,2),
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'offer_fake_winners',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'offer_fake_winners_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  OfferFakeWinner.associate = (models) => {
    // Many-to-One relationship with Offer
    OfferFakeWinner.belongsTo(models.Offer, {
      foreignKey: 'offerId',
      as: 'offer'
    })
  }

  return OfferFakeWinner
}
