'use strict'
module.exports = (sequelize, DataTypes) => {
  const MenuTenantSetting = sequelize.define('MenuTenantSetting', {
    id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true,
    },
    ordering: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    tenant_id: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    menu_id: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'menu_tenant_setting',
    schema: 'public',
    timestamps: true,
  })
  MenuTenantSetting.associate = models => {
    MenuTenantSetting.belongsTo(models.MenuMaster, {
      onDelete: 'cascade',
      foreignKey: 'menuId'
    })
  }
  return MenuTenantSetting
}
