'use strict'

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true
    },
    emailVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    encryptedPassword: {
      type: DataTypes.STRING,
      allowNull: true
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true
    },
    phoneVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    dateOfBirth: {
      type: DataTypes.DATE,
      allowNull: true
    },
    gender: {
      type: DataTypes.STRING,
      allowNull: true
    },
    signInCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    signInIp: {
      type: DataTypes.JSON,
      allowNull: true
    },
    parentType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    parentId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    userName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    countryCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    lastLoginDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    selfExclusion: {
      type: DataTypes.STRING,
      allowNull: true
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    vipLevel: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    nickName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    disabledAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    disabledByType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    disabledById: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    phoneCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    demo: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true
    },
    zipCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    kycDone: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    playerCategoryLevel: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    affiliatedData: {
      type: DataTypes.STRING,
      allowNull: true
    },
    wagerMultiplier: {
      type: DataTypes.SMALLINT,
      allowNull: true
    },
    withdrawWagerAllowed: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    categoryType: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      defaultValue: 4
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'users',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_users_on_disabled_by_type_and_disabled_by_id',
        fields: [
          { name: 'disabled_by_type' },
          { name: 'disabled_by_id' }
        ]
      },
      {
        name: 'index_users_on_parent_type_and_parent_id',
        fields: [
          { name: 'parent_type' },
          { name: 'parent_id' }
        ]
      },
      {
        name: 'index_users_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'index_users_on_tenant_id_and_email',
        unique: true,
        fields: [
          { name: 'tenant_id' },
          { name: 'email' }
        ]
      },
      {
        name: 'users_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  User.associate = models => {
    User.belongsTo(models.Tenant, {
      foreignKey: 'tenantId'
    })
    User.belongsTo(models.AdminUser, {
      foreignKey: 'parentId'
    })
    User.belongsTo(models.AdminUser, {
      foreignKey: 'parentId', as: 'AU'
    })
    User.belongsTo(models.AdminUserSetting, {
      foreignKey: 'parentId',
      targetKey: 'adminUserId'
    })
    User.hasMany(models.UserToken, {
      foreignKey: 'userId',
      onDelete: 'cascade'
    })
    User.hasOne(models.Wallet, {
      foreignKey: 'ownerId',
      onDelete: 'cascade',
      scope: {
        ownerType: 'User'
      }
    })
    User.hasOne(models.Wallet, {
      foreignKey: 'ownerId',
      onDelete: 'cascade',
      scope: {
        ownerType: 'User'
      },
      as: 'W'
    })
    User.hasMany(models.UserSetting, {
      onDelete: 'cascade',
      foreignKey: 'userId'
    })
    User.hasMany(models.UserDocument, {
      onDelete: 'cascade',
      foreignKey: 'userId'
    })
    User.hasMany(models.Transaction, {
      foreignKey: 'actioneeId',
      onDelete: 'cascade'
    })
    User.hasMany(models.UserLoginHistory, {
      onDelete: 'cascade',
      foreignKey: 'userId'
    })
    User.hasMany(models.UserBonus, {
      foreignKey: 'userId',
    });
    User.hasOne(models.UserPromoCodeBonus, {
      foreignKey: 'userId',
      as: 'UserPromoCodeBonus'
    })
    User.hasOne(models.BotUser, {
      foreignKey: 'userId',
    })

  }

  return User
}
