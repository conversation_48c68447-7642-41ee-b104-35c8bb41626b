'use strict'
module.exports = (sequelize, DataTypes) => {
  const MarketSettlement = sequelize.define('MarketSettlement', {
    settlementId: {
        primaryKey: true,
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        allowNull: false
    },
    marketId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'tenant_id'
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'market_settlement',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'settlement_id_pkey',
        unique: true,
        fields: [
          { name: 'settlement_id' }
        ]
      }
    ]
  })

  return MarketSettlement
}
