'use strict'
module.exports = (sequelize, DataTypes) => {
  const DepositWithdrawJob = sequelize.define('DepositWithdrawJob', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    type: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    requestObject: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    csv: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'deposit_withdraw_job',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'deposit_withdraw_job_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  DepositWithdrawJob.associate = (models) => {
    DepositWithdrawJob.hasMany(models.DepositWithdrawUser, {
      foreignKey: 'jobId',
    });
  };

  return DepositWithdrawJob
}
