'use strict'
module.exports = (sequelize, DataTypes) => {
  const MostPlayedGames = sequelize.define('MostPlayedGames', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    game: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    casinoItemId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    uuid: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    name: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    image: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    playCount: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    provider: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
    },
    updatedAt: {
      type: DataTypes.DATE,
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'most_played_games',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'most_played_games_ukey',
        unique: true,
        fields: [
          'tenant_id',
          'user_id',
          'provider',
          'game',
        ]
      }
    ]
  })

  return MostPlayedGames
}
