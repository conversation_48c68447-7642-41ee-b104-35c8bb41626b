'use strict'
module.exports = (sequelize, DataTypes) => {
  const DepositWithdrawUser = sequelize.define('DepositWithdrawUser', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    jobId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    amount: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    error: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    jobData: {
      type: DataTypes.JSONB,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'deposit_withdraw_users',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'deposit_withdraw_users_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  DepositWithdrawUser.associate = (models) => {
    DepositWithdrawUser.belongsTo(models.DepositWithdrawJob, {
      foreignKey: 'jobId',
    });
  };

  return DepositWithdrawUser
}
