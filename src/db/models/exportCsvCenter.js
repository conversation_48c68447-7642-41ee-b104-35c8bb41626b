'use strict'
module.exports = (sequelize, DataTypes) => {
  const ExportCsvCenter = sequelize.define('ExportCsvCenter', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    adminType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    adminId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    payload: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    csvUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    type: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    emailSent: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'export_csv_center',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'export_csv_center_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return ExportCsvCenter
}
