
module.exports = (sequelize, DataTypes) => {
  const RequestResponseLogsBackup = sequelize.define('RequestResponseLogsBackup', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    endDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    csvUrl: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    type: {
      default: 1,
      type: DataTypes.INTEGER,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'request_response_logs_backup',
    schema: 'public',
    timestamps: true
  })

  return RequestResponseLogsBackup
}
