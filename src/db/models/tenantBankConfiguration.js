'use strict'

module.exports = (sequelize, DataTypes) => {
  const TenantBankConfiguration = sequelize.define('TenantBankConfiguration', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    usedFor: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    bankName: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    accountHolderName: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    bankIfscCode: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    accountNumber: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdBy: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    lastUpdatedOwnerId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    countryCode: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    upiId: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    type: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    cryptoExchangeRate: {
      type: DataTypes.DECIMAL(10, 5),
      allowNull: true
    },
    image: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    playerCategory: {
      type: DataTypes.JSONB,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_bank_configuration',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'tenant_bank_configuration_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return TenantBankConfiguration
}
