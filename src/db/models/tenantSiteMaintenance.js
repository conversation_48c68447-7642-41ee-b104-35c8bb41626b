
module.exports = (sequelize, DataTypes) => {
  const TenantSiteMaintenance = sequelize.define('TenantSiteMaintenance', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    type: {
      type: DataTypes.STRING,
      allowNull: true

    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: ''
    },
    announcementTitle: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: ''

    },
    isAnnouncementActive: {
      type: DataTypes.BOOLEAN,
      allowNull: true

    },
    siteDown: {
      type: DataTypes.BOOLEAN,
      allowNull: true

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_site_maintenance',
    schema: 'public',
    timestamps: true,
    indexes: [

    ]
  })


  return TenantSiteMaintenance
}
