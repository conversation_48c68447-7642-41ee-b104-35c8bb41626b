module.exports = (sequelize, DataTypes) => {
  const PullsEvent = sequelize.define('PullsEvent', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_deleted'
    },
    fixtureId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'fixture_id'
    },
    fixtureStatus: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'fixture_status'
    },
    startDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'start_date'
    },
    lastUpdate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'last_update'
    },
    leagueId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'league_id'
    },
    locationId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'location_id'
    },
    sportId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'sport_id'
    },
    livescore: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    market: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    leagueNameDe: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'league_name_de'
    },
    leagueNameEn: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'league_name_en'
    },
    leagueNameFr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'league_name_fr'
    },
    leagueNameRu: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'league_name_ru'
    },
    leagueNameTr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'league_name_tr'
    },
    locationNameDe: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'location_name_de'
    },
    locationNameEn: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'location_name_en'
    },
    locationNameFr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'location_name_fr'
    },
    locationNameRu: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'location_name_ru'
    },
    locationNameTr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'location_name_tr'
    },
    sportNameDe: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'sport_name_de'
    },
    sportNameEn: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'sport_name_en'
    },
    sportNameFr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'sport_name_fr'
    },
    sportNameRu: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'sport_name_ru'
    },
    sportNameTr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'sport_name_tr'
    },
    isEventBlacklisted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_event_blacklisted'
    },
    leagueNameNl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'league_name_nl'
    },
    locationNameNl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'location_name_nl'
    },
    sportNameNl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'sport_name_nl'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    },
    nameEn: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_en'
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'tenant_id'
    }
  }, {
    sequelize,
    tableName: 'pulls_events',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'index_pulls_events_on_league_id',
        fields: [
          { name: 'league_id' }
        ]
      },
      {
        name: 'index_pulls_events_on_location_id',
        fields: [
          { name: 'location_id' }
        ]
      },
      {
        name: 'index_pulls_events_on_sport_id',
        fields: [
          { name: 'sport_id' }
        ]
      },
      {
        name: 'pulls_events_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  PullsEvent.associate = models => {
    PullsEvent.hasMany(models.TenantBlockedEvent, {
      foreignKey: 'pullsEventFixtureId'
    })

    PullsEvent.belongsTo(models.PullsSport, {
      foreignKey: 'sportId',
      //targetKey: 'sportId',
      as : 'sport'
    })

    PullsEvent.belongsTo(models.PullsLeague, {
      foreignKey: 'leagueId',
      as : 'league'
    })

    PullsEvent.belongsToMany(models.PullsParticipant, {
      through: models.PullsEventparticipant,
      foreignKey: 'eventId',
      otherKey: 'participantId'
    })
  }
  return PullsEvent
}
