'use strict'
module.exports = (sequelize, DataTypes) => {
  const SpribeAuthToken = sequelize.define('SpribeAuthToken', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    sessionToken: {
      type: DataTypes.STRING,
      allowNull: true
    },
    authToken: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'spribe_auth_token',
    schema: 'public',
    timestamps: true,
  })

  return SpribeAuthToken
}
