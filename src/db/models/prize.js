'use strict'

module.exports = (sequelize, DataTypes) => {
  const Prize = sequelize.define(
    'Prize',
    {
      id: {
        autoIncrement: true,
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true
      },
      offerId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        references: {
          model: 'Offer',
          key: 'id'
        }
      },
      title: {
        type: DataTypes.STRING(255),
        allowNull: false
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: true
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: true
      },
      prizesCount: {
        type: DataTypes.BIGINT,
        allowNull: false
      },
    },
    {
      sequelize,
      underscored: true,
      tableName: 'prizes',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'prizes_pkey',
          unique: true,
          fields: [
            { name: 'id' }
          ]
        }
      ]
    }
  )

  Prize.associate = (models) => {
    // Many-to-One relationship with Offer
    Prize.belongsTo(models.Offer, {
      foreignKey: 'offerId',
      as: 'offer'
    })
  }

  return Prize
}
