
module.exports = (sequelize, DataTypes) => {
  const SuperAdminUser = sequelize.define('SuperAdminUser', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: true

    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: true

    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: ''
    },
    encryptedPassword: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: ''

    },
    resetPasswordToken: {
      type: DataTypes.STRING,
      allowNull: true

    },
    resetPasswordSentAt: {
      type: DataTypes.DATE,
      allowNull: true

    },
    rememberCreatedAt: {
      type: DataTypes.DATE,
      allowNull: true

    },
    parentType: {
      type: DataTypes.STRING,
      allowNull: true

    },
    parentId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    tenantIds: {
      type: DataTypes.JSONB
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'super_admin_users',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_super_admin_users_on_email',
        unique: true,
        fields: [
          { name: 'email' }
        ]
      },
      {
        name: 'index_super_admin_users_on_parent_type_and_parent_id',
        fields: [
          { name: 'parent_type' },
          { name: 'parent_id' }
        ]
      },
      {
        name: 'index_super_admin_users_on_reset_password_token',
        unique: true,
        fields: [
          { name: 'reset_password_token' }
        ]
      },
      {
        name: 'super_admin_users_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  SuperAdminUser.associate = models => {
    SuperAdminUser.belongsToMany(models.SuperRole, {
      through: models.SuperAdminUsersSuperRole,
      foreignKey: 'superAdminUserId'
    })
  }
  return SuperAdminUser
}
