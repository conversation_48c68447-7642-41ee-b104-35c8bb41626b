const DataTypes = require('sequelize').DataTypes
const _ActiveStorageAttachment = require('./activeStorageAttachment')
const _ActiveStorageBlob = require('./activeStorageBlob')
const _AdminRole = require('./adminRole')
const _AdminUserSetting = require('./adminUserSetting')
const _AdminUser = require('./adminUser')
const _AdminUsersAdminRole = require('./adminUsersAdminRole')
const _ArInternalMetadatum = require('./arInternalMetadatum')
const _CasinoItem = require('./casinoItem')
const _CasinoMenu = require('./casinoMenu')
const _CasinoProvider = require('./casinoProvider')
const _CasinoTable = require('./casinoTable')
const _Currency = require('./currency')
const _Language = require('./language')
const _Layout = require('./layout')
const _MenuItem = require('./menuItem')
const _PageBanner = require('./pageBanner')
const _PageMenu = require('./pageMenu')
const _Page = require('./page')
const _SchemaMigration = require('./schemaMigration')
const _SuperAdminUser = require('./superAdminUser')
const _SuperAdminUsersSuperRole = require('./superAdminUsersSuperRole')
const _SuperRole = require('./superRole')
const _TenantConfiguration = require('./tenantConfiguration')
const _TenantCredential = require('./tenantCredential')
const _TenantThemeSetting = require('./tenantThemeSetting')
const _Tenant = require('./tenant')
const _Theme = require('./theme')
const _Transaction = require('./transaction')
const _UserDocument = require('./userDocument')
const _UserSetting = require('./userSetting')
const _UserToken = require('./userToken')
const _User = require('./user')
const _tenantPaymentConfiguration = require('./tenantPaymentConfiguration')
const _Wallet = require('./wallet')
const _ConversionHistory = require('./conversionHistory')
const _TenantBankConfiguration = require('./tenantBankConfiguration')
const _Referral = require('./referral')
const _ReferralSettings = require('./referralSettings')

function initModels (sequelize) {
  const ActiveStorageAttachment = _ActiveStorageAttachment(sequelize, DataTypes)
  const tenantPaymentConfiguration = _tenantPaymentConfiguration(sequelize, DataTypes)
  const ActiveStorageBlob = _ActiveStorageBlob(sequelize, DataTypes)
  const AdminRole = _AdminRole(sequelize, DataTypes)
  const AdminUserSetting = _AdminUserSetting(sequelize, DataTypes)
  const AdminUser = _AdminUser(sequelize, DataTypes)
  const AdminUsersAdminRole = _AdminUsersAdminRole(sequelize, DataTypes)
  const ArInternalMetadatum = _ArInternalMetadatum(sequelize, DataTypes)
  const CasinoItem = _CasinoItem(sequelize, DataTypes)
  const CasinoMenu = _CasinoMenu(sequelize, DataTypes)
  const CasinoProvider = _CasinoProvider(sequelize, DataTypes)
  const CasinoTable = _CasinoTable(sequelize, DataTypes)
  const Currency = _Currency(sequelize, DataTypes)
  const ConversionHistory = _ConversionHistory(sequelize, DataTypes)
  const Language = _Language(sequelize, DataTypes)
  const Layout = _Layout(sequelize, DataTypes)
  const MenuItem = _MenuItem(sequelize, DataTypes)
  const PageBanner = _PageBanner(sequelize, DataTypes)
  const PageMenu = _PageMenu(sequelize, DataTypes)
  const Page = _Page(sequelize, DataTypes)
  const SchemaMigration = _SchemaMigration(sequelize, DataTypes)
  const SuperAdminUser = _SuperAdminUser(sequelize, DataTypes)
  const SuperAdminUsersSuperRole = _SuperAdminUsersSuperRole(sequelize, DataTypes)
  const SuperRole = _SuperRole(sequelize, DataTypes)
  const TenantConfiguration = _TenantConfiguration(sequelize, DataTypes)
  const TenantCredential = _TenantCredential(sequelize, DataTypes)
  const TenantThemeSetting = _TenantThemeSetting(sequelize, DataTypes)
  const TenantBankConfiguration = _TenantBankConfiguration(sequelize, DataTypes)
  const Tenant = _Tenant(sequelize, DataTypes)
  const Theme = _Theme(sequelize, DataTypes)
  const Transaction = _Transaction(sequelize, DataTypes)
  const UserDocument = _UserDocument(sequelize, DataTypes)
  const UserSetting = _UserSetting(sequelize, DataTypes)
  const UserToken = _UserToken(sequelize, DataTypes)
  const User = _User(sequelize, DataTypes)
  const Wallet = _Wallet(sequelize, DataTypes)
  const Referral = _Referral(sequelize, DataTypes)
  const ReferralSettings = _ReferralSettings(sequelize, DataTypes)

  ActiveStorageAttachment.belongsTo(ActiveStorageBlob, { as: 'blob', foreignKey: 'blobId' })
  ActiveStorageBlob.hasMany(ActiveStorageAttachment, { as: 'activeStorageAttachments', foreignKey: 'blobId' })

  return {
    ActiveStorageAttachment,
    tenantPaymentConfiguration,
    ActiveStorageBlob,
    AdminRole,
    AdminUserSetting,
    AdminUser,
    AdminUsersAdminRole,
    ArInternalMetadatum,
    CasinoItem,
    CasinoMenu,
    CasinoProvider,
    CasinoTable,
    Currency,
    ConversionHistory,
    Language,
    Layout,
    MenuItem,
    PageBanner,
    PageMenu,
    Page,
    SchemaMigration,
    SuperAdminUser,
    SuperAdminUsersSuperRole,
    SuperRole,
    TenantConfiguration,
    TenantBankConfiguration,
    TenantCredential,
    TenantThemeSetting,
    Tenant,
    Theme,
    Transaction,
    UserDocument,
    UserSetting,
    UserToken,
    User,
    Wallet,
    Referral,
    ReferralSettings
  }
}
module.exports = initModels
module.exports.initModels = initModels
module.exports.default = initModels
