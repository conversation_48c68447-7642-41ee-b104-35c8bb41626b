module.exports = (sequelize, DataTypes) => {
  const PullsSport = sequelize.define('PullsSport', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      field: 'is_deleted'
    },
    sportId: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'sport_id'
    },
    nameDe: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_de'
    },
    nameEn: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_en'
    },
    nameFr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_fr'
    },
    nameRu: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_ru'
    },
    nameTr: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_tr'
    },
    nameNl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'name_nl'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at'
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'tenant_id'
    }
  }, {
    sequelize,
    tableName: 'pulls_sports',
    schema: 'public',
    timestamps: false,
    indexes: [
      {
        name: 'pulls_sports_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  PullsSport.associate = models => {
    PullsSport.hasMany(models.PullsEvent, {
      foreignKey: 'sportId',
      sourceKey: 'sportId'
    })
  }

  return PullsSport
}
