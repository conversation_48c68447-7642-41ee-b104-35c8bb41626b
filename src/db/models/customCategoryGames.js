'use strict'

module.exports = (sequelize, DataTypes) => {
  const CustomCategoryGames = sequelize.define(
    'CustomCategoryGames',
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true
      },
      tenantId: {
        type: DataTypes.BIGINT,
        allowNull: false
      },
      categoryId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        references: {
          model: 'CustomCategory',
          key: 'id'
        }
      },
      uuid: {
        type: DataTypes.STRING(100),
        allowNull: false
      },
      pageId: {
        type: DataTypes.BIGINT,
        allowNull: false
      },
      providerId: {
        type: DataTypes.BIGINT,
        allowNull: false
      },
      ordering: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      isOrderModify: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: true
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: true
      }
    },
    {
      sequelize,
      underscored: true,
      tableName: 'custom_category_games',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'idx_ccg_tenant_category_deleted',
          fields: ['tenantId', 'categoryId', 'isDeleted']
        },
        {
          name: 'idx_ccg_uuid_tenant',
          fields: ['uuid', 'tenantId']
        }
      ],
      uniqueKeys: {
        unique_category_uuid: {
          fields: ['categoryId', 'uuid']
        }
      }
    }
  );

  CustomCategoryGames.associate = (models) => {
    CustomCategoryGames.belongsTo(models.CustomCategory, {
      foreignKey: 'categoryId',
      as: 'customCategory'
    });
  };

  return CustomCategoryGames;
};
