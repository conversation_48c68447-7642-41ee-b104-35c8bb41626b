
module.exports = (sequelize, DataTypes) => {
  const WithdrawRequest = sequelize.define('WithdrawRequest', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'pending'
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    accountNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    ifscCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    amount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    phoneNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    transactionId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    actionableType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    actionableId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    actionedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    withdrawal_type: {
      type: DataTypes.STRING,
      allowNull: true
    },
    verify_status: {
      type: DataTypes.STRING,
      allowNull: true
    },
    payment_provider_name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    payment_transaction_id: {
      type: DataTypes.STRING,
      allowNull: true
    },
    paymentProviderId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    bankId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    bankName: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    fdTransactionId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    makerData: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    processedBankDetails: {
      type: DataTypes.JSON,
      allowNull: true
    },
    mode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    cryptoAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'withdraw_requests',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_withdraw_requests_on_actionable_type_and_actionable_id',
        fields: [
          { name: 'actionable_type' },
          { name: 'actionable_id' }
        ]
      },
      {
        name: 'index_withdraw_requests_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      },
      {
        name: 'withdraw_requests_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_withdraw_requests_on_bank_id',
        fields: [
          { name: 'bankId' }
        ]
      }
    ]
  })
  WithdrawRequest.associate = models => {
    WithdrawRequest.belongsTo(models.UserBankDetails, {
      foreignKey: 'bankId'
    }),
    WithdrawRequest.belongsTo(models.Transaction, {
      foreignKey: 'transactionId'
    }),
    WithdrawRequest.belongsTo(models.User, {
      foreignKey: 'userId'
    })
  }
  return WithdrawRequest
}
