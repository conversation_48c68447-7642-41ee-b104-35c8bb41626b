'use strict';
module.exports = (sequelize, DataTypes) => {
  const InstantDepositBonusHistory = sequelize.define('InstantDepositBonusHistory', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    bonusId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    userBonusId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    bonusTransactionId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    depositTransactionId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    depositAmount: {
      type: DataTypes.DOUBLE,
      allowNull: false
    },
    bonusAmount: {
      type: DataTypes.DOUBLE,
      allowNull: false
    },
    bonusType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    recurringBonusType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    burningTrxnId: {
      type: DataTypes.BIGINT,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'instant_deposit_bonus_history',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'instant_deposit_bonus_history_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  InstantDepositBonusHistory.associate = models => {
    InstantDepositBonusHistory.hasOne(models.BurningBonus, {
      foreignKey: 'instantBonusId'
    })
  }
  return InstantDepositBonusHistory
}
