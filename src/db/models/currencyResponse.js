'use strict'
module.exports = (sequelize, DataTypes) => {
  const CurrencyResponse = sequelize.define('CurrencyResponse', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    response: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'currency_response',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'currency_response_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  return CurrencyResponse
}
