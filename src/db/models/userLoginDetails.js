module.exports = (sequelize, DataTypes) => {
  const UserLoginHistory = sequelize.define('UserLoginHistory', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    ip: {
      type: DataTypes.STRING,
      allowNull: true
    },
    network: {
      type: DataTypes.STRING,
      allowNull: true
    },
    version: {
      type: DataTypes.STRING,
      allowNull: true
    },
    deviceId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    deviceType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    deviceModel: {
      type: DataTypes.STRING,
      allowNull: true
    },
    data: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    signInCount: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    lastLoginDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'user_login_history',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_user_login_history_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      },
      {
        name: 'user_login_history_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  UserLoginHistory.associate = function (models) {
    UserLoginHistory.belongsTo(models.User, {
      foreignKey:'userId'
    })
  }

  return UserLoginHistory
}
