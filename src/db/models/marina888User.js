
module.exports = (sequelize, DataTypes) => {
  const Marina888User = sequelize.define('Marina888User', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    externalUserId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    internalUserId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }

  }, {
    sequelize,
    underscored: true,
    tableName: 'marina888_users',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'marina888_users_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })


  return Marina888User
}
