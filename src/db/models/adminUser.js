'use strict'
module.exports = (sequelize, DataTypes) => {
  const AdminUser = sequelize.define('AdminUser', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    firstName: {
      type: DataTypes.STRING,
      allowNull: true

    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: true

    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true
    },
    phoneVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false

    },
    parentType: {
      type: DataTypes.STRING,
      allowNull: true

    },
    parentId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: ''
    },
    encryptedPassword: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: ''

    },
    resetPasswordToken: {
      type: DataTypes.STRING,
      allowNull: true

    },
    resetPasswordSentAt: {
      type: DataTypes.DATE,
      allowNull: true

    },
    rememberCreatedAt: {
      type: DataTypes.DATE,
      allowNull: true

    },
    confirmationToken: {
      type: DataTypes.STRING,
      allowNull: true

    },
    confirmedAt: {
      type: DataTypes.DATE,
      allowNull: true

    },
    confirmationSentAt: {
      type: DataTypes.DATE,
      allowNull: true

    },
    unconfirmedEmail: {
      type: DataTypes.STRING,
      allowNull: true

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    agentName: {
      type: DataTypes.STRING,
      allowNull: true

    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    deactivatedById: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    deactivatedByType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    deactivatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    kycRegulated: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    affiliateToken: {
      type: DataTypes.STRING,
      allowNull: true
    },
    reportingEmail: {
      type: DataTypes.STRING,
      allowNull: true
    },
    reportingEmailVerified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: true
    },
    agentType: {
      type: DataTypes.INTEGER,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'admin_users',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'admin_users_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_admin_users_on_confirmation_token',
        unique: true,
        fields: [
          { name: 'confirmation_token' }
        ]
      },
      {
        name: 'index_admin_users_on_email',
        unique: true,
        fields: [
          { name: 'email' }
        ]
      },
      {
        name: 'index_admin_users_on_parent_type_and_parent_id',
        fields: [
          { name: 'parent_type' },
          { name: 'parent_id' }
        ]
      },
      {
        name: 'index_admin_users_on_reset_password_token',
        unique: true,
        fields: [
          { name: 'reset_password_token' }
        ]
      },
      {
        name: 'index_admin_users_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      }
    ]
  })

  AdminUser.associate = models => {
    AdminUser.belongsTo(models.Tenant, {
      foreignKey: 'tenantId'
    })
    AdminUser.belongsToMany(models.AdminRole, {
      through: models.AdminUsersAdminRole,
      foreignKey: 'adminUserId'
    })
    AdminUser.belongsToMany(models.AdminUsersAdminRole, {
      through: models.AdminUsersAdminRole,
      foreignKey: 'adminUserId',
      otherKey: 'adminRoleId'
    })
    AdminUser.hasOne(models.Wallet, {
      through: models.Wallet,
      foreignKey: 'ownerId'
    })
    AdminUser.hasMany(models.User, {
      foreignKey: 'parentId'
    })
  }
  return AdminUser
}
