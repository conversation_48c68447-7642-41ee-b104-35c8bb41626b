'use strict'

module.exports = (sequelize, DataTypes) => {
  const Wallet = sequelize.define('Wallet', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    amount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0.0
    },
    primary: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    currencyId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    ownerType: {
      type: DataTypes.STRING,
      allowNull: true

    },
    ownerId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    nonCashAmount: {
      type: DataTypes.DOUBLE,
      defaultValue: 0.0
    },
    oneTimeBonusAmount: {
      type: DataTypes.DOUBLE,
      defaultValue: 0.0
    },
    sportsFreebetAmount: {
      type: DataTypes.DOUBLE,
      defaultValue: 0.0
    },
    withdrawalAmount: {
      type: DataTypes.DOUBLE,
      defaultValue: 0.0
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'wallets',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_wallets_on_currency_id',
        fields: [
          { name: 'currency_id' }
        ]
      },
      {
        name: 'index_wallets_on_owner_type_and_owner_id',
        fields: [
          { name: 'owner_type' },
          { name: 'owner_id' }
        ]
      },
      {
        name: 'wallets_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })


  Wallet.associate = (models) => {
    Wallet.belongsTo(models.User, {
      foreignKey: 'ownerId'
    })
    Wallet.belongsTo(models.AdminUser, {
      foreignKey: 'ownerId'
    })
    Wallet.belongsTo(models.SuperAdminUser, {
      foreignKey: 'ownerId'
    })
    Wallet.belongsTo(models.Currency, {
      foreignKey: 'currencyId'
    })
    Wallet.belongsTo(models.User, {
      foreignKey: 'ownerId', as:'u'
    })
    Wallet.belongsTo(models.AdminUser, {
      foreignKey: 'ownerId', as:'au'
    })
    Wallet.belongsTo(models.SuperAdminUser, {
      foreignKey: 'ownerId', as:'su'
    })
    Wallet.belongsTo(models.Currency, {
      foreignKey: 'currencyId',as:'C'
    })
    Wallet.hasMany(models.Transaction, {
      foreignKey: 'sourceWalletId',
      onDelete: 'cascade'
    })
    Wallet.hasMany(models.Transaction, {
      foreignKey: 'targetWalletId',
      onDelete: 'cascade'
    })
  }

  return Wallet
}
