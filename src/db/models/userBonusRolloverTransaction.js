module.exports = (sequelize, DataTypes) => {
  const UserBonusRolloverTransaction = sequelize.define('UserBonusRolloverTransaction', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    bonusId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'bonus_id'
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'user_id'
    },
    depositTransactionId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      field: 'deposit_transaction_id'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
      field: 'created_at'
    }
  }, {
    sequelize,
    tableName: 'user_bonus_rollover_transaction',
    schema: 'public',
    timestamps: true,
    updatedAt: false,
    createdAt: 'created_at',
    indexes: [
      {
        name: 'user_bonus_rollover_transaction_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'idx_user_bonus_rollover_transaction_user_id',
        fields: [
          { name: 'user_id' }
        ]
      },
      {
        name: 'idx_user_bonus_rollover_transaction_deposit_transaction_id',
        fields: [
          { name: 'deposit_transaction_id' }
        ]
      }
    ]
  })

  UserBonusRolloverTransaction.associate = function (models) {
    UserBonusRolloverTransaction.belongsTo(models.User, {
      foreignKey: 'user_id',
      targetKey: 'id',
      onDelete: 'CASCADE'
    })

    UserBonusRolloverTransaction.belongsTo(models.Transaction, {
      foreignKey: 'deposit_transaction_id',
      targetKey: 'id',
      onDelete: 'CASCADE'
    })
  }

  return UserBonusRolloverTransaction
}
