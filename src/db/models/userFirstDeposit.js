'use strict'
module.exports = (sequelize, DataTypes) => {
  const UserFirstDeposit = sequelize.define('UserFirstDeposit', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    firstDepositId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    firstDepositDate: {
      type: DataTypes.DATE,
      allowNull: true

    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    amount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'user_first_deposit',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'user_first_deposit_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return UserFirstDeposit
}
