
module.exports = (sequelize, DataTypes) => {
  const Marina888Bonus = sequelize.define('Marina888Bonus', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    externalBonusId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    internalBonusId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }

  }, {
    sequelize,
    underscored: true,
    tableName: 'marina888_bonus',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'marina888_bonus_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return Marina888Bonus
}
