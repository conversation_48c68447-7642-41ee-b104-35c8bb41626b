module.exports = (sequelize, DataTypes) => {
  const BetsTransactionSummary = sequelize.define('BetsTransactionSummary', {
    id: {
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true,
      autoIncrement: true
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    totalBet: {
      type: DataTypes.DOUBLE
    },
    totalBetEur: {
      type: DataTypes.DOUBLE
    },
    totalWin: {
      type: DataTypes.DOUBLE
    },
    totalWinEur: {
      type: DataTypes.DOUBLE
    },
    ggr: {
      type: DataTypes.DOUBLE
    },
    ggrEur: {
      type: DataTypes.DOUBLE
    },
    date: {
      type: DataTypes.DATE,
      allowNull: false
    },
    currencyCode: {
      type: DataTypes.STRING
    },
    providerId: {
      type: DataTypes.INTEGER
    }
  }, {
    tableName: 'bets_transaction_summary',
    timestamps: true
  })

  return BetsTransactionSummary
}
