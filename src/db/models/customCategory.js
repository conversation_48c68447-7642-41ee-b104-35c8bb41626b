'use strict'

module.exports = (sequelize, DataTypes) => {
  const CustomCategory = sequelize.define(
    'CustomCategory',
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true
      },
      tenantId: {
        type: DataTypes.BIGINT,
        allowNull: false
      },
      menuIconId: {
        type: DataTypes.BIGINT,
        allowNull: false
      },
      isMenuIconModify: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      title: {
        type: DataTypes.STRING(100),
        allowNull: false
      },
      isMenuModify: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      topMenuId: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      createdBy: {
        type: DataTypes.SMALLINT,
        allowNull: false,
        comment: '0: super admin, 1: admin'
      },
      isOrderModify: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      ordering: {
        type: DataTypes.INTEGER,
        allowNull: true
      },
      status: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      isStatusModify: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      referenceId: {
        type: DataTypes.BIGINT,
        allowNull: true
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      lastSyncedAt: {
        type: DataTypes.DATE,
        allowNull: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: true
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: true
      }
    },
    {
      sequelize,
      underscored: true,
      tableName: 'custom_category',
      schema: 'public',
      timestamps: true,
      indexes: [
        {
          name: 'idx_custom_category_top_menu_ordering',
          fields: ['topMenuId', 'ordering']
        },
        {
          name: 'idx_custom_category_reference_id',
          fields: ['referenceId']
        },
        {
          name: 'idx_custom_category_tenant_status_deleted',
          fields: ['tenantId', 'status', 'isDeleted']
        }
      ],
      uniqueKeys: {
        unique_reference_tenant: {
          fields: ['referenceId', 'tenantId']
        },
        unique_title_top_menuid: {
          fields: ['title', 'topMenuId', 'tenantId']
        }
      }
    }
  );

  CustomCategory.associate = (models) => {
    CustomCategory.hasMany(models.CustomCategoryGames, {
      foreignKey: 'categoryId',
      as: 'customCategoryGames'
    });
  };

  return CustomCategory;
};
