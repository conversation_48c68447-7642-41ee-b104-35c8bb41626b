
module.exports = (sequelize, DataTypes) => {
  const UserDepositSequence = sequelize.define('UserDepositSequence', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
    },
    bonusId: {
      type: DataTypes.BIGINT,
      allowNull: false,
    },
    currentSequence: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
    }
  }, {
    tableName: 'user_deposit_sequence',
    schema: 'public',
    underscored : true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        name: 'user_deposit_sequence_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'user_deposit_sequence_user_id_bonus_id_index',
        fields: [
          { name: 'user_id' },
          { name: 'bonus_id' }
        ]
      }
    ]
  });

  UserDepositSequence.associate = function (models) {
    UserDepositSequence.belongsTo(models.User, {
      foreignKey: 'user_id',
      targetKey: 'id',
      onDelete: 'CASCADE'
    });

    UserDepositSequence.belongsTo(models.Bonus, {
      foreignKey: 'bonus_id',
      targetKey: 'id',
      onDelete: 'CASCADE'
    });
  };

  return UserDepositSequence;
};