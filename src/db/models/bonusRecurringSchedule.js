module.exports = (sequelize, DataTypes) => {
  const BonusRecurringSchedule = sequelize.define('BonusRecurringSchedule', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    bonusId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'bonus',
        key: 'id'
      }
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false,
    },
    currencyId: {
      type: DataTypes.BIGINT,
      allowNull: false,
    },
    recurringType: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      comment: '0 = beginning_of_month, 1 = first_half_month, 2 = mid_late_month, 3 = second_half_of_month'
    },
    lastCreatedBonusId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      comment: 'Stores the ID of the most recently created bonus for this recurring schedule.',
    },
    active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'bonus_recurring_schedule',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'bonus_recurring_schedule_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  BonusRecurringSchedule.associate = models => {
    BonusRecurringSchedule.belongsTo(models.Bonus, {
      foreignKey: 'bonusId'
    });
  };

  return BonusRecurringSchedule
}
