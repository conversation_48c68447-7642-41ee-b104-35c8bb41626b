'use strict'

module.exports = (sequelize, DataTypes) => {
  const CryptoCurrency = sequelize.define('CryptoCurrency', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true
    },
    exchangeRate: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    currencyResponseId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    internalCode: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'crypto_currencies',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'crypto_currencies_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  CryptoCurrency.associate = models => {
    CryptoCurrency.hasMany(models.CryptoConversionHistory, {
      foreignKey: 'currencyId'
    })
  }

  return CryptoCurrency
}
