module.exports = (sequelize, DataTypes) => {
  const TenantPromoCodes = sequelize.define('TenantPromoCodes', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    promoName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    code: {
      type: DataTypes.STRING,
      allowNull: true
    },
    promoCodeUsedIn: {
      type: DataTypes.STRING,
      allowNull: true
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true
    },
    validFrom: {
      type: DataTypes.DATE,
      allowNull: true
    },
    validTill: {
      type: DataTypes.DATE,
      allowNull: true
    },
    promoCodeBonusType: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    bonusAmount: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    totalUsage: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    totalNumberOfBonusTillNow: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    walletType: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    vipLevel: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: null
    },
    burningDays: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    burnType: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_promo_codes',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'tenant_promo_codes_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return TenantPromoCodes
}
