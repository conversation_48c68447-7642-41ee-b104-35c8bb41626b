
module.exports = (sequelize, DataTypes) => {
  const ConfigurationBackup = sequelize.define('ConfigurationBackup', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    backupData: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    moduleType: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'configuration_backup',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'configuration_backup_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'tenant_module_type_index',
        fields: [
          { name: 'tenant_id' },
          { name: 'module_type' }
        ]
      }
    ]
  })

  return ConfigurationBackup
}
