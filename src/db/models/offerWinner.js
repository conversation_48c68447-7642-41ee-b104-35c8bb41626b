'use strict';
module.exports = (sequelize, DataTypes) => {
  const OfferWinner = sequelize.define('OfferWinner', {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    userName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    offerId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.SMALLINT,
      allowNull: false
    },
    winningType: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: '0: Rollover Win, 1: GGR, 2: NGR'
    },
    winningValue: {
      type: DataTypes.FLOAT,
      allowNull: false,
      comment: 'Value based on winningType (e.g., rollover win, GGR, NGR)'
    },
    prizeTitle: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Prize associated with the winner'
    },
    frequency: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: '0: Daily, 1: Weekly, 2: Monthly 3: End of Campaign Period'
    },
    isFakeUser: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      deffaultValue: false
    },
    winnerAnnounceDate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'offer_winners',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'offer_winners_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  });

  return OfferWinner;
};
