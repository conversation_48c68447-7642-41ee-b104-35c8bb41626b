// TODO: remove status
// TODO: remove source before and after balance
// TODO: remove actionee type
// TODO: remove comments
'use strict'
module.exports = (sequelize, DataTypes) => {
  const Transaction = sequelize.define('Transaction', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    actioneeType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    actioneeId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    sourceWalletId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    targetWalletId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    sourceCurrencyId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    targetCurrencyId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    conversionRate: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    amount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0.0
    },
    sourceBeforeBalance: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0.0

    },
    sourceAfterBalance: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0.0
    },
    targetBeforeBalance: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0.0

    },
    targetAfterBalance: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0.0

    },
    status: {
      type: DataTypes.STRING,
      allowNull: true
    },
    comments: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    transactionId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    timestamp: {
      type: DataTypes.STRING,
      allowNull: true
    },
    transactionType: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    success: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    },
    serverId: {
      allowNull: true,
      type: DataTypes.NUMBER
    },
    roundId: {
      allowNull: true,
      type: DataTypes.NUMBER
    },
    gameId: {
      allowNull: true,
      type: DataTypes.STRING
    },
    tableId: {
      allowNull: true,
      type: DataTypes.NUMBER
    },
    betTypeId: {
      allowNull: true,
      type: DataTypes.NUMBER
    },
    seatId: {
      allowNull: true,
      type: DataTypes.STRING
    },
    platformId: {
      allowNull: true,
      type: DataTypes.INTEGER
    },
    errorCode: {
      allowNull: true,
      type: DataTypes.NUMBER
    },
    errorDescription: {
      allowNull: true,
      type: DataTypes.STRING
    },
    returnReason: {
      allowNull: true,
      type: DataTypes.NUMBER
    },
    isEndRound: {
      allowNull: true,
      type: DataTypes.BOOLEAN
    },
    creditIndex: {
      allowNull: true,
      type: DataTypes.STRING
    },
    debitTransactionId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    paymentProviderId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    paymentMethod: {
      type: DataTypes.STRING,
      allowNull: true
    },
    otherCurrencyAmount: {
      type: DataTypes.STRING,
      allowNull: true
    },
    providerId: {
      allowNull: true,
      type: DataTypes.INTEGER
    },
    metaData: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    cancelTransactionId: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'transactions',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_transactions_on_actionee_type_and_actionee_id',
        fields: [
          { name: 'actionee_type' },
          { name: 'actionee_id' }
        ]
      },
      {
        name: 'index_transactions_on_source_currency_id',
        fields: [
          { name: 'source_currency_id' }
        ]
      },
      {
        name: 'index_transactions_on_source_wallet_id',
        fields: [
          { name: 'source_wallet_id' }
        ]
      },
      {
        name: 'index_transactions_on_target_currency_id',
        fields: [
          { name: 'target_currency_id' }
        ]
      },
      {
        name: 'index_transactions_on_target_wallet_id',
        fields: [
          { name: 'target_wallet_id' }
        ]
      },
      {
        name: 'index_transactions_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'index_transactions_on_payment_provider_id',
        fields: [
          { name: 'payment_provider_id' }
        ]
      },
      {
        name: 'transactions_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  Transaction.associate = models => {
    Transaction.belongsTo(models.Tenant, {
      foreignKey: 'tenantId'
    })
    Transaction.belongsTo(models.User, {
      foreignKey: 'actioneeId'
    })
    Transaction.belongsTo(models.Wallet, {
      foreignKey: 'sourceWalletId', as: 'sw'
    })
    Transaction.belongsTo(models.Wallet, {
      foreignKey: 'targetWalletId', as: 'tw'
    })
    Transaction.belongsTo(models.Currency, {
      foreignKey: 'sourceCurrencyId', as: 'sc'
    })
    Transaction.belongsTo(models.Currency, {
      foreignKey: 'targetCurrencyId', as: 'tc'
    })
    Transaction.belongsTo(models.paymentProviders, {
      foreignKey: 'paymentProviderId'
    })
    Transaction.belongsTo(models.CasinoProvider, {
      foreignKey: 'providerId'
    })
    Transaction.belongsTo(models.CasinoGame, {
      foreignKey: 'gameId',
      targetKey: 'gameId'
    })
    Transaction.hasOne(models.TransactionReceipt, {
      foreignKey: 'transactionId',
      sourceKey: 'id',
      as: 'tr'
    })
  }

  return Transaction
}
