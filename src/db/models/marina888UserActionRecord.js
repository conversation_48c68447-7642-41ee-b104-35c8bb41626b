
module.exports = (sequelize, DataTypes) => {
  const Marina888UserActionRecord = sequelize.define('Marina888UserActionRecord', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    actionType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    externalId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    internalId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    totalTransactionCount: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    externalMinId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    externalMaxId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true
    },
    errorDescription: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'marina888_user_action_records',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'marina888_user_action_records_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return Marina888UserActionRecord
}
