'use strict'
module.exports = (sequelize, DataTypes) => {
  const Tenant = sequelize.define('Tenant', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    domain: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenants',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'tenants_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  Tenant.associate = models => {
    Tenant.hasMany(models.AdminUser, {
      onDelete: 'cascade',
      foreignKey: 'tenantId'
    })
    Tenant.hasMany(models.User, {
      onDelete: 'cascade',
      foreignKey: 'tenantId'
    })
    Tenant.hasMany(models.TenantCredential, {
      onDelete: 'cascade',
      foreignKey: 'tenantId'
    })
    Tenant.hasMany(models.Transaction, {
      onDelete: 'cascade',
      foreignKey: 'tenantId'

    })
    Tenant.hasMany(models.TenantBanner, {
      onDelete: 'cascade',
      foreignKey: 'tenantId'
    })
  }
  return Tenant
}
