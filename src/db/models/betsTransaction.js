module.exports = (sequelize, DataTypes) => {
  const BetsTransaction = sequelize.define('BetsTransaction', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false
    },
    amount: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    journalEntry: {
      type: DataTypes.STRING,
      allowNull: false
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true
    },
    reference: {
      type: DataTypes.STRING,
      allowNull: true
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    betslipId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    marketType: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    sourceCurrencyId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    targetCurrencyId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    sourceWalletId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    targetWalletId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    conversionRate: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    paymentFor: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '1-bet placement, 2- won, 3- cashout, 4-refund'
    },
    nonCashAmount: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    currentBalance: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    sourceBeforeBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    sourceAfterBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    targetAfterBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    targetBeforeBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    transactionId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    reverseTransactionId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    transactionCode: {
      type: DataTypes.STRING,
      allowNull: true
    },
    runnerName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    netPl: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    commissionPer: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    commissionAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    marketId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    settlementId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    otherCurrencyAmount: {
      type: DataTypes.STRING,
      allowNull: true
    },
    otherCurrencyNonCashAmount: {
      type: DataTypes.STRING,
      allowNull: true
    },
    sourceNonCashBeforeBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    sourceNonCashAfterBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    targetNonCashBeforeBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    targetNonCashAfterBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    sportsFreebetAmount: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    sourceSportsFreebetBeforeBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    sourceSportsFreebetAfterBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    targetSportsFreebetBeforeBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    targetSportsFreebetAfterBalance: {
      type: DataTypes.DECIMAL(15, 4),
      allowNull: true
    },
    otherCurrencySportsFreebetAmount: {
      type: DataTypes.STRING,
      allowNull: true
    },
    providerId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    otherCurrencyCommissionAmount: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'bets_transactions',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'bets_transactions_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_bets_transactions_for_created_at_and_payment_for_and_jour',
        fields: ['createdAt', 'paymentFor', 'journalEntry']
      },
      {
        name: 'index_bets_transactions_on_betslip_id',
        fields: [
          { name: 'betslip_id' }
        ]
      },
      {
        name: 'index_bets_transactions_on_source_currency_id',
        fields: [
          { name: 'source_currency_id' }
        ]
      },
      {
        name: 'index_bets_transactions_on_source_wallet_id',
        fields: [
          { name: 'source_wallet_id' }
        ]
      },
      {
        name: 'index_bets_transactions_on_target_currency_id',
        fields: [
          { name: 'target_currency_id' }
        ]
      },
      {
        name: 'index_bets_transactions_on_target_wallet_id',
        fields: [
          { name: 'target_wallet_id' }
        ]
      },
      {
        name: 'index_bets_transactions_on_tenant_id',
        fields: [
          { name: 'tenant_id' }
        ]
      },
      {
        name: 'index_bets_transactions_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      },
      {
        name: 'index_bets_transactions_on_provider_id',
        fields: [
          { name: 'provider_id' }
        ]
      }
    ]
  })

  BetsTransaction.associate = models => {
    BetsTransaction.belongsTo(models.User, {
      foreignKey: 'userId'
    })
    BetsTransaction.belongsTo(models.BetsBetslip, {
      foreignKey: 'betslipId'
    })
    BetsTransaction.belongsTo(models.Currency, {
      foreignKey: 'sourceCurrencyId',
      as: 'sc'
    })
    BetsTransaction.belongsTo(models.Currency, {
      foreignKey: 'targetCurrencyId',
      as: 'tc'
    })
    BetsTransaction.belongsTo(models.Tenant, {
      foreignKey: 'tenantId'
    })
    BetsTransaction.belongsTo(models.CasinoProvider, {
      foreignKey: 'providerId'
    })
  }

  return BetsTransaction
}
