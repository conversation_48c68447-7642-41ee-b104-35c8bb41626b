module.exports = (sequelize, DataTypes) => {
  const UserBonus = sequelize.define('UserBonus', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    status: {
      type: DataTypes.ENUM(['active', 'claimed', 'cancelled', 'expired']),
      allowNull: true
    },
    bonusAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0.0
    },
    rolloverBalance: {
      type: DataTypes.DOUBLE,
      allowNull: true,
      defaultValue: 0.0
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    bonusId: {
      type: DataTypes.BIGINT,
      allowNull: false,
      references: {
        model: 'bonus',
        key: 'id'
      }
    },
    kind: {
      type: DataTypes.STRING,
      allowNull: true
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    claimedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    transactionId: {
      type: DataTypes.BIGINT
    },
    initialRolloverBalance: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    burningTrxnId: {
      type: DataTypes.BIGINT,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'user_bonus',
    underscored: true,
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_user_bonus_on_bonus_id',
        fields: [
          { name: 'bonus_id' }
        ]
      },
      {
        name: 'index_user_bonus_on_user_id',
        fields: [
          { name: 'user_id' }
        ]
      },
      {
        name: 'user_bonus_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'user_bonus_unique_constraint',
        unique: true,
        fields: [
          { name: 'user_id' },
          { name: 'bonus_id' }
        ]
      }
    ]
  });

  UserBonus.associate = models => {
    UserBonus.belongsTo(models.Bonus, {
      foreignKey: 'bonusId'
    })
    UserBonus.hasMany(models.BurningBonus, {
      foreignKey: 'userBonusId'
    })

    UserBonus.belongsTo(models.DepositBonusSetting, {
      foreignKey: 'bonusId' , targetKey: 'bonusId'
    })
    UserBonus.belongsTo(models.User, {
      foreignKey: 'userId',
    });
  }

  return UserBonus
}
