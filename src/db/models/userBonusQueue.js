'use strict'

module.exports = (sequelize, DataTypes) => {
  const UserBonusQueue = sequelize.define('UserBonusQueue', {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    bonusId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'bonus',
        key: 'id'
      }
    },
    status: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      validate: {
        isIn: [[0, 1, 2]]
      },
      comment: '0 => Pending, 1 => Completed, 2 => Failed'
    },
    bonusAmount: {
      type: DataTypes.DECIMAL(10, 2),
      comment: 'Bonus amount',
      allowNull: true,
    },
    rolloverTarget: {
      type: DataTypes.DECIMAL(10, 2),
      comment: 'Rollover target',
      allowNull: true,
    },
    depositId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'deposit_requests',
        key: 'id',
        allowNull: true,
      }
    },
    remainingRollover: {
      type: DataTypes.DECIMAL(10, 2),
      comment: 'Remaining rollover',
      allowNull: true
    },
    ordering: {
      type: DataTypes.INTEGER,
      defaultValue: sequelize.literal("nextval('user_bonus_queue_order_seq'::regclass)"),
      comment: 'Order of the bonus queue; can be manually updated.'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at'
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'user_bonus_queue',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'user_bonus_queue_user_id_idx',
        fields: [
          { name: 'user_id' }
        ]
      },
      {
        name: 'user_bonus_queue_bonus_id_idx',
        fields: [
          { name: 'bonus_id' }
        ]
      }
    ]
  });

  UserBonusQueue.associate = (models) => {
    UserBonusQueue.belongsTo(models.Bonus, {
      foreignKey: 'bonusId',
      as: 'Bonus'
    });
  };

  return UserBonusQueue;
};
