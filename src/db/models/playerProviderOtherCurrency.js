'use strict'
module.exports = (sequelize, DataTypes) => {
  const PlayerProviderOtherCurrency = sequelize.define('PlayerProviderOtherCurrency', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    playerSummaryProviderId: {
      type: DataTypes.BIGINT,
      allowNull: false,
    },
    currencyId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(20, 5),
    },
    createdAt: {
      type: DataTypes.DATE,
    },
    updatedAt: {
      type: DataTypes.DATE,
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'player_provider_other_currency',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'user_tenant_provider_currency_ukey',
        unique: true,
        fields: ['player_summary_provider_id', 'currency_id']
      }
    ]
  })

  PlayerProviderOtherCurrency.associate = models => {
    PlayerProviderOtherCurrency.belongsTo(models.PlayerSummaryProviderWise, {
      foreignKey: 'playerSummaryProviderId',
      allowNull: false
    })
  }

  return PlayerProviderOtherCurrency
}
