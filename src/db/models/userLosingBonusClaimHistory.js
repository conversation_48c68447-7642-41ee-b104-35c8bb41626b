module.exports = (sequelize, DataTypes) => {
  const UserLosingBonusClaimHistory = sequelize.define('UserLosingBonusClaimHistory', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    bonusAmount: {
      type: DataTypes.DOUBLE,
      allowNull: false
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    bonusId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    claimedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    transactionId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    burningTrxnId: {
      type: DataTypes.BIGINT,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'user_losing_bonus_claim_history',
    underscored: true,
    schema: 'public',
    timestamps: true
  })

  return UserLosingBonusClaimHistory
}
