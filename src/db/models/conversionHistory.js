'use strict'

module.exports = (sequelize, DataTypes) => {
  const ConversionHistory = sequelize.define('ConversionHistory', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    currencyId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    oldExchangeRate: {
      type: DataTypes.DECIMAL(12, 5),
      allowNull: false
    },
    newExchangeRate: {
      type: DataTypes.DECIMAL(12, 5),
      allowNull: false
    },
    oldCurrencyResponseId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    newCurrencyResponseId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'conversion_history',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'conversion_history_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  ConversionHistory.associate = models => {
    ConversionHistory.belongsTo(models.Currency, {
      foreignKey: 'currencyId'
    })
  }

  return ConversionHistory
}
