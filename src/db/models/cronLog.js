module.exports = (sequelize, DataTypes) => {
  const CronLog = sequelize.define('CronLog', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    cronId: {
      type: DataTypes.BIGINT,
      allowNull: true,
      references: {
        model: 'queue_service_status',
        key: 'id'
      }
    },
    startTime: {
      type: DataTypes.DATE,
      allowNull: true
    },
    endTime: {
      type: DataTypes.DATE,
      allowNull: true
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    errorMsg: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'cron_log',
    underscored: true,
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'cron_log_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  CronLog.associate = models => {
    CronLog.belongsTo(models.QueueProcessStatus, {
      foreignKey: 'cronId'
    })
  }

  return CronLog
}
