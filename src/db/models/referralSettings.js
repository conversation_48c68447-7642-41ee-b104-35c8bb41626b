'use strict'
module.exports = (sequelize, DataTypes) => {
  const ReferralSettings = sequelize.define('ReferralSettings', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    title: {
      type: DataTypes.JSONB,
      allowNull: false
    },
    description: {
      type: DataTypes.JSONB,
      allowNull: false
    },
    bonusAmount: {
      type: DataTypes.DECIMAL,
      allowNull: false
    },
    bonusType: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: "0: flat, 1: upto"
    },
    walletType: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: "0: cash, 1: non-cash"
    },
    event: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: "0: sign-up, 1: first deposit, 2: first wager"
    },
    applyTo: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: "0: referrer, 1: referee, 2: both"
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    minValue: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    maxValue: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    percentageValue: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    referrerBonusAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    refereeBonusAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'referral_settings',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'referral_settings_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  return ReferralSettings
}
