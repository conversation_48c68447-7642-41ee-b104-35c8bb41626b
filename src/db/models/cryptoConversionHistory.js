'use strict'

module.exports = (sequelize, DataTypes) => {
  const CryptoConversionHistory = sequelize.define('CryptoConversionHistory', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    currencyId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    oldExchangeRate: {
      type: DataTypes.DOUBLE,
      allowNull: false
    },
    newExchangeRate: {
      type: DataTypes.DOUBLE,
      allowNull: false
    },
    oldCurrencyResponseId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    newCurrencyResponseId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'crypto_conversion_history',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'crypto_conversion_history_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  CryptoConversionHistory.associate = models => {
    CryptoConversionHistory.belongsTo(models.CryptoCurrency, {
      foreignKey: 'currencyId'
    })
  }

  return CryptoConversionHistory
}
