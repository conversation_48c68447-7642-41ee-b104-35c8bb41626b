'use strict'
module.exports = (sequelize, DataTypes) => {
  const tenantPaymentConfiguration = sequelize.define('tenantPaymentConfiguration', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    providerId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    providerKeyValues: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    parentType: {
      type: DataTypes.STRING,
      allowNull: true
    },
    parentId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_payment_configurations',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'payment_providers_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_pages_on_tenant_id',
        fields: [
          { name: 'tenantId' }
        ]
      },
      {
        name: 'index_pages_on_provider_id',
        fields: [
          { name: 'providerId' }
        ]
      }
    ]
  })

  tenantPaymentConfiguration.associate = models => {
    tenantPaymentConfiguration.belongsTo(models.paymentProviders, {
      foreignKey: 'providerId'
    })
  }

  return tenantPaymentConfiguration
}
