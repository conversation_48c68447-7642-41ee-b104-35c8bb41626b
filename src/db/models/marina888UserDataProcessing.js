
module.exports = (sequelize, DataTypes) => {
  const Marina888UserDataProcessing = sequelize.define('Marina888UserDataProcessing', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userActionRecordId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    offset: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    limit: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    status: {
      type: DataTypes.STRING,
      allowNull: true
    },
    errorDescription: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }

  }, {
    sequelize,
    underscored: true,
    tableName: 'marina888_user_data_processing',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'marina888_user_data_processing_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return Marina888UserDataProcessing
}
