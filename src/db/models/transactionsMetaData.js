
module.exports = (sequelize, DataTypes) => {
  const TransactionsMetaData = sequelize.define('TransactionsMetaData', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    transactionId: {
      type: DataTypes.BIGINT,
      allowNull: false,
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    transactionType: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    metaData: {
      type: DataTypes.JSONB,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'transactions_meta_data',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'transactions_meta_data_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      },
      {
        name: 'index_transactions_meta_data_on_transaction_id',
        fields: [
          { name: 'transaction_id' }
        ]
      },
    ]
  })

  return TransactionsMetaData
}
