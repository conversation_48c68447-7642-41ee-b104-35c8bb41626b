
module.exports = (sequelize, DataTypes) => {
  const Marina888Agent = sequelize.define('Marina888Agent', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    externalAgentId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    internalAgentId: {
      type: DataTypes.BIGINT,
      allowNull: true
    },
    status: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }

  }, {
    sequelize,
    underscored: true,
    tableName: 'marina888_agents',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'marina888_agents_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })


  return Marina888Agent
}
