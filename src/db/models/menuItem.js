'use strict'
module.exports = (sequelize, DataTypes) => {
  const MenuItem = sequelize.define('MenuItem', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    pageMenuId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    casinoItemId: {
      type: DataTypes.BIGINT,
      allowNull: true

    },
    name: {
      type: DataTypes.STRING,
      allowNull: true
    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    featured: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false

    },
    popular:{
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'menu_items',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_menu_items_on_casino_item_id',
        fields: [
          { name: 'casino_item_id' }
        ]
      },
      {
        name: 'index_menu_items_on_page_menu_id',
        fields: [
          { name: 'page_menu_id' }
        ]
      },
      {
        name: 'menu_items_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  MenuItem.associate = models => {
    MenuItem.belongsTo(models.CasinoItem, {
      onDelete: 'cascade',
      foreignKey: 'casinoItemId'
    })
  }

  return MenuItem
}
