'use strict'
module.exports = (sequelize, DataTypes) => {
  const OcrTransaction = sequelize.define('OcrTransaction', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    depositRequestId: {
      type: DataTypes.BIGINT,
      allowNull: false,
    },
    status: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: '1 - Verified UTR number, 2 - UTR number already exists, 3 - Failed to extract UTR number, 4 - UTR Not Verified'
    },
    actionBy: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      comment: '1 - Through OCR system, 2 - Through admin manually'
    },
    ocrResult: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false
    },
  }, {
    sequelize,
    underscored: true,
    tableName: 'ocr_transactions',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'index_ocr_transactions_on_deposit_request_id',
        fields: [
          { name: 'deposit_request_id' }
        ]
      },
      {
        name: 'ocr_transactions_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })
  return OcrTransaction
}
