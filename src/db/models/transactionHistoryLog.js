'use strict'
module.exports = (sequelize, DataTypes) => {
  const TransactionHistoryLog = sequelize.define('TransactionHistoryLog', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    userId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    transactionId: {
      type: DataTypes.BIGINT,
      allowNull: false
    },
    type: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: '1 - Fraud Detection, 2 - Smartico'
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'transaction_history_log',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'user_tenant_transaction_type_idx',
        fields: [
          { name: 'userId' },
          { name: 'tenantId' },
          { name: 'transactionId' },
          { name: 'type' }
        ]
      }
    ]
  })

  TransactionHistoryLog.associate = models => {
  }

  return TransactionHistoryLog
}
