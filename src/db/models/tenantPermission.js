
module.exports = (sequelize, DataTypes) => {
  const TenantPermission = sequelize.define('TenantPermission', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    tenantId: {
      type: DataTypes.BIGINT,
      allowNull: true,
    },
    permission: {
      type: DataTypes.JSONB,
      allowNull: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'tenant_permissions',
    schema: 'public',
    timestamps: true,
  })

  TenantPermission.associate = models => {
    TenantPermission.belongsTo(models.Tenant, {
      foreignKey: 'tenantId',
      allowNull: false
    })
  }
  return TenantPermission
}
