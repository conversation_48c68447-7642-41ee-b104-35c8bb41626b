
'use strict'
module.exports = (sequelize, DataTypes) => {
  const Referral = sequelize.define('Referral', {
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT,
      allowNull: false,
      primaryKey: true
    },
    referrerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    refereeId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    tenantId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    event: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: "0: sign-up, 1: first deposit, 2: first wager"
    },
    status: {
      type: DataTypes.SMALLINT,
      allowNull: false,
      comment: "0: pending, 1: in progress, 2: completed, 3: rejected"
    },
    transactionId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "Event for first deposit or first wager"
    },
    transactionAmount: {
      type: DataTypes.DECIMAL,
      allowNull: true,
      comment: "Event for first deposit or first wager"
    },
    bonusAmount: {
      type: DataTypes.DECIMAL,
      allowNull: true
    },
    referrerBonusAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    refereeBonusAmount: {
      type: DataTypes.DOUBLE,
      allowNull: true,
    },
    bonusType: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      comment: "0: flat, 1: upto"
    },
    walletType: {
      type: DataTypes.SMALLINT,
      allowNull: true,
      comment: "0: cash, 1: non-cash"
    },
    comment: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    underscored: true,
    tableName: 'referrals',
    schema: 'public',
    timestamps: true,
    indexes: [
      {
        name: 'referrals_pkey',
        unique: true,
        fields: [
          { name: 'id' }
        ]
      }
    ]
  })

  return Referral
}
