import Bull from 'bull'
import Redis from 'ioredis'
import queueWorkerRedisClient from '../libs/queueWorkerRedisClient'

const opts = {
  createClient: function (type, opts) {
    switch (type) {
      case 'client':
        return queueWorkerRedisClient.client
      case 'subscriber':
        return queueWorkerRedisClient.publisherClient
      default:
        return new Redis(opts)
    }
  },
  redis: queueWorkerRedisClient.connection,
  defaultJobOptions: {
    attempts: 10,
    backoff: 60000,
    removeOnComplete: true
  }
}

export const CommonQueue = new Bull('Common-Queue', {
  ...opts
})
export const PremiumGameTransactionsSummary = 'PremiumGameTransactionsSummaryJob'

export const GameTransactionsSummary = 'GameTransactionsSummaryJob'

export const BetsTransactionsSummary = 'BetsTransactionsSummaryJob'

export const ComparativeReport = 'ComparativeReportJob'

export const BulkDepositWithdrawAndAuditLogQueueSchedule = 'BulkDepositWithdrawAndAuditLogQueuejob'

export const CommonJob = 'CommonJobQueue'

export const UpdateTodayPlayerRevenueJob = 'UpdateTodayPlayerRevenueJob'

export const UpdateRiskAndFraudIndicatorsJob = 'UpdateRiskAndFraudIndicatorsJob'

export const UpsertMostPlayedGamesJob = 'UpsertMostPlayedGamesJob'

export const BurningLosingBonusQueueSchedule = 'BurningLosingBonusQueueJob'

export const AutomaticLosingBonusQueueSchedule = 'AutomaticLosingBonusQueueJob'

export const AutomaticActiveLosingBonusQueueSchedule = 'AutomaticActiveLosingBonusQueueJob'

export const RequestResponseLogSchedule = 'RequestResponseLogJob'

export const BurningJoiningBonusQueueSchedule = 'BurningJoiningBonusQueueJob'

export const BurningDepositBonusQueueSchedule = 'BurningDepositBonusQueueJob'

export const IstAutomaticLosingBonusQueueSchedule = 'IstAutomaticLosingBonusQueueJob'

export const BurningPromocodeBonusQueueSchedule = 'BurningPromocodeBonusQueueJob'

export const BurningManualLosingBonusQueueSchedule = 'BurningManualLosingBonusQueueJob'

export const CombinedMenusGamesQueueSchedule = 'CombinedMenusGamesQueueJob'

export const ConfigurationBackupQueueSchedule = 'ConfigurationBackupQueueJob'

export const OfferWinnerAnnounceQueueSchedule = 'OfferWinnerAnnounceQueueJob'

export const LotteryGenerateWinCallbacksSchedule = 'LotteryGenerateWinCallbacksSchedule'
