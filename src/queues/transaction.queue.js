import Bull from 'bull'
import Redis from 'ioredis'
import queueWorkerRedisClient from '../libs/queueWorkerRedisClient'

const opts = {
  createClient: function (type, opts) {
    switch (type) {
      case 'client':
        return queueWorkerRedisClient.client
      case 'subscriber':
        return queueWorkerRedisClient.publisherClient
      default:
        return new Redis(opts)
    }
  },
  redis: queueWorkerRedisClient.connection,
  defaultJobOptions: {
    attempts: 10,
    backoff: 60000,
    removeOnComplete: true
  }
}

export const TransactionQueue = new Bull('Transaction-Queue', {
  ...opts
})
export const TransactionJob = 'TransactionJobQueue'

export const BetTransactionSchedule = 'BetTransactionScheduleJob'

export const CasinoTransactionSchedule = 'CasinoTransactionScheduleJob'

export const SportToCasinoTransactionSchedule = 'SportToCasinoTransactionScheduleJob'

export const UserTransactionSchedule = 'UserTransactionScheduleJob'

export const RollbackTransactionSchedule = 'RollbackTransactionScheduleJob'
