import Bull from 'bull'
import Redis from 'ioredis'
import queueWorkerRedisClient from '../libs/queueWorkerRedisClient'

const opts = {
  createClient: function (type, opts) {
    switch (type) {
      case 'client':
        return queueWorkerRedisClient.client
      case 'subscriber':
        return queueWorkerRedisClient.publisherClient
      default:
        return new Redis(opts)
    }
  },
  redis: queueWorkerRedisClient.connection,
  defaultJobOptions: {
    attempts: 3,
    timeout: 500000,
    backoff: 60000,
    removeOnComplete: true
  },
  settings: {
    maxStalledCount: 5,      // Good: 5 retries
    stalledInterval: 500000   // Good: check every 120s
  }
}

export const ExportCsvQueue = new Bull('Export-Csv-Queue', {
  ...opts
})

export const ExportCsv = 'ExportCsvQueue'

export const ExportSchedule = 'ExportCsvScheduleJob'
