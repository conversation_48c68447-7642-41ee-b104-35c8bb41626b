import Bull from 'bull'
import Redis from 'ioredis'
import queueWorkerRedisClient from '../libs/queueWorkerRedisClient'

const opts = {
  createClient: function (type, opts) {
    switch (type) {
      case 'client':
        return queueWorkerRedisClient.client
      case 'subscriber':
        return queueWorkerRedisClient.publisherClient
      default:
        return new Redis(opts)
    }
  },
  redis: queueWorkerRedisClient.connection,
  defaultJobOptions: {
    attempts: 10,
    backoff: 60000,
    removeOnComplete: true
  },
  settings: {
    maxStalledCount: 5,      // Good: 5 retries
    stalledInterval: 30000   // Good: check every 30s
  }
}

export const CreateCsvQueue = new Bull('Create-Csv-Queue', {
  ...opts
})

export const CreateCsv = 'CreateCsvQueue'

export const CreateCsvSchedule = 'CreateCsvScheduleJob'
