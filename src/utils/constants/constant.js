// SOCKET RELATED
export const SOCKET_NAMESPACES = {
  DEMO: '/demo'
}

export const SOCKET_EMITTERS = {
  DEMO_HELLO_WORLD: SOCKET_NAMESPACES.DEMO + '/helloWorld'
}

export const SOCKET_LISTENERS = {
  DEMO_HELLO_WORLD: SOCKET_NAMESPACES.DEMO + '/helloWorld'
}

export const SOCKET_ROOMS = {
  DEMO_USER: SOCKET_NAMESPACES.DEMO + '/demo' // append id of the demo like this /demo:1 for one to one
}
// SOCKET RELATED

export const TYPE = {
  CRYPTO: 'CRYPTO',
  FIAT: 'FIAT',
  CRYPTO_ID: 0,
  FIAT_ID: 1
}
export const CASINO_TRANSACTION_STATUS = {
  PENDING: 0,
  COMPLETED: 1,
  FAILED: 2,
  ROLLBACK: 3
}

export const AMOUNT_TYPE = {
  CASH: 0,
  NON_CASH: 1,
  CASH_NON_CASH: 2
}

export const TRANSACTION_STATUS = {
  PENDING: 0,
  SUCCESS: 1,
  CANCELLED: 2,
  FAILED: 3,
  ROLLBACK: 4,
  APPROVED: 5,
  REJECTED: 6
}
export const EMAIL_TEMPLATE_PRIMARY_STATUS = {
  PRIMARY: 1,
  DISABLE: 0,
  alias: {
    0: 'disable',
    1: 'primary'
  }
}
export const STATUS_VALUE = {
  APPROVED: 'APPROVED',
  PENDING: 'PENDING',
  REJECTED: 'REJECTED',
  REQUESTED: 'REQUESTED',
  RE_REQUESTED: 'RE-REQUESTED'
}
export const ROLE = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  SUPPORT: 'support',
  USER: 'user'
}
export const EmailTemplateJobStatus = {
  complete: 3,
  inprogress: 2,
  initiate: 1,
  fail: 4
}
export const SUCCESS_MSG = {
  CREATE_SUCCESS: 'Record created successfully',
  GET_SUCCESS: 'Record get successfully',
  DELETE_SUCCESS: 'Record deleted successfully',
  UPDATE_SUCCESS: 'Record updated successfully',
  STATUS_UPDATED: 'Status has been updated successfully',
  VERIFICATION_EMAIL: 'Verification email is valid for ' + process.env.EMAIL_TOKEN_EXPIRY,
  DEPOSIT_SUCCESS: 'Amount deposited successfully',
  EMAIL_SUCCESS: 'Email sent.',
  BONUS_ISSUE: 'Bonus issued successfully',
  CANCEL_SUCCESS: 'Bonus cancelled successfully',
  REDIS_SUCCESS: 'PONG',
  HEALTHCHECK_SUCCESS: 'OK',
  METHOD: 'Healthcheck',
  CREDENTIALS_REQUIRED: 'Credentials required to send mail'
}
export const defaultLanguage = 'EN'
export const defaultBase64 = 'BASE64'
export const defaultUtf8 = 'utf8'
export const notificationType = {
  UpcomingEventNotification: 'Upcoming Event Notification',
  PromotionalNotification: 'Promotional Notification',
  SiteMaintenanceNotification: 'Site Maintenance Notification',
  ActiveUserNotification: 'Active User Notification',
  InactiveUserNotification: 'Inactive User Notification'
}
export const bonusType = {
  LOSING: 'losing',
  LOSING_SPORT: 'losing_sport',
  LOSING_BOTH: 'losing_both'
}

export const BONUS_CLAIM_TYPE = {
  MANUAL: 'manual',
  AUTOMATIC: 'automatic',
  AUTOMATIC_ACTIVE: 'automatic_active'
}

export const BONUS_INTERVAL_TYPE = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  BIWEEKLY: 'biweekly',
  MONTHLY: 'monthly'
}

export const BONUS_CALCULATION_TYPE = {
  GGR: 'GGR',
  NGR: 'NGR'
}

export const BONUS_DATES = {
  daily: 1,
  weekly: 7,
  biweekly: 14,
  monthly: 30
}

export const BETS_TRANSACTIONS = {
  BET: 1,
  WIN: 2,
  REFUND: 4,
  SETTLED_MARKET: 'SettledMarket',
  CR: 'CR',
  DR: 'DR'
}

export const TRANSACTIONS = {
  BET_ONE: 0,
  BET_TWO: 8,
  WIN_ONE: 1,
  WIN_TWO: 9,
  TIP_ONE: 7,
  TIP_TWO: 13,
  REFUND_ONE: 2,
  REFUND_TWO: 10
}

export const PUSH_IN_QUEUE_PAGE_COUNT = 1000

export const PUSH_IN_QUEUE_CRON = true

export const TRANSACTION_TYPE = {
  CASINO_TRANSACTION: 'casino_transaction',
  USER_TRANSACTION: 'user_transaction',
  BET_TRANSACTION: 'bet_transaction',
  SPORT_CASINO_TRANSACTION: 'sport_casino_transaction',
  CREATE_CSV: 'create_csv',
  AUDIT_LOG: 'audit_log',
  BULK_DEPOSIT_WITHDRAW: 'bulk_deposit_withdraw',
  PLAYER_COMMISION: 'player_commission',
  BONUS: 'bonus',
  ROLLBACK_TRANSACTION: 'rollback_transaction',
  EXPORT_CSV: 'export_csv'
}

export const QUEUE_TYPE = {
  BET_PLACED: 'bet_placed'
}

export const TRANSACTION_STATUS_APPROVED = ['approved', 'approve', 'successful', 'completed', 'confirmed', 'accepted']
export const TRANSACTION_STATUS_REJECTED = ['rejected', 'reject', 'failed', 'error', 'invalid', 'canceled', 'declined', 'cancelled']

export const BONUS_KIND = {
  DEPOSIT: 'deposit',
  DEPOSIT_SPORT: 'deposit_sport',
  DEPOSIT_INSTANT: 'deposit_instant',
  DEPOSIT_BOTH: 'deposit_both',
  WITHDRAW: 'withdraw'
}

export const WEEKDAYS = { sunday: 0, monday: 1, tuesday: 2, wednesday: 3, thursday: 4, friday: 5, saturday: 6 }

export const REFERRAL_EVENT = {
  SIGNUP: 0,
  FIRST_DEPOSIT: 1,
  FIRST_WAGER: 2
}

export const REFERRAL_STATUS = {
  PENDING: 0,
  IN_PROGRESS: 1,
  COMPLETED: 2,
  REJECTED: 3
}

export const REFERRAL_BONUS_TYPE = {
  FLAT: 0,
  UPTO: 1,
  PERCENTAGE:2
}

export const REFERRAL_WALLET_TYPE = {
  CASH: 0,
  NON_CASH: 1
}

export const REFERRAL_APPLY_TO = {
  REFERRER: 0,
  REFEREE: 1,
  BOTH: 2
}

export const REFERRAL_BONUS_STATUS = {
  PENDING: 0,
  IN_PROGRESS: 1,
  COMPLETED: 2,
  REJECTED: 3
}

export const PROGRESS_TRACKER_STATUS = {
  'NOT_STARTED': 0,
  'IN_PROGRESS': 1,
  'COMPLETED': 2,
  'FAILED': 3
}

// sp1 = update_player_summary_provider_wise_sp, sp2 = update_player_summary_provider_game_wise_transaction_sp, sp3 = update_agent_revenue_report
export const AGENT_PLAYER_REVENUE_TRACKER_SP_TYPE = {
  'UPDATE_PLAYER_SUMMARY_PROVIDER_WISE': 1,
  'UPDATE_PLAYER_SUMMARY_PROVIDER_GAME_WISE_TRANS': 2,
  'UPDATE_AGENT_REVENUE_REPORT': 3
}

export const BUCKET_PATH = {
  'STAGE': 'https://ezugi-main-platform-staging-storage.s3.us-east-1.amazonaws.com',
  'PROD': 'https://ezugi-main-platform-prod-active-storage.s3.us-east-1.amazonaws.com'
}

export const OCR_TRANSACTION_STATUS = {
  VERIFIED_UTR_NUMBER: 1,
  UTR_NUMBER_ALREADY_EXISTS: 2,
  FAILED_TO_EXTRACT_UTR_NUMBER: 3,
  UTR_NOT_MATCH: 4
};

export const OCR_TRANSACTION_ACTION_BY = {
  THROUGH_OCR_SYSTEM: 1,
  THROUGH_ADMIN_MANUALLY: 2
};

export const MULTI_BONUS_STATUS = {
  PENDING: 0,
  COMPLETED: 1,
  FAILED: 2,
}

export const CHECK_SBO_DOMAIN = {
  0: false, // SHOW REAL PLAYERS
  1: true, // SHOW REAL AND BOT PLAYERS WITH FLAG
  2: 'bot_verified' // SHOW REAL AND BOT PLAYERS WITHOUT FLAG
}

export const EZUGI_ROLLBACK_CALLBACK_URL = {
  STAGE: 'https://stgezugi.z1k9txvm.net/api/v1/ezugi/rollback',
  PROD: 'https://ezugi.z1k9txvm.net/api/v1/ezugi/rollback'
}
export const WHITECLIFF_PROVIDERS = [
  "Alize",
  "Asia Gaming",
  "Betgames",
  "BNG",
  "CQ9 Slot",
  "Dream Gaming",
  "Fast Game",
  "Habanero",
  "Illustrate Analytics",
  "Live 88",
  "Micro Gaming Live",
  "Micro Gaming Slot",
  "Naga Games", "NextSpin",
  "OneTouch",
  "Playson",
  "Skywind Live",
  "Skywind Slot",
  "Spade Gaming",
  "World Match",
  "BTI Sports"
]


export const EMAIL_PROVIDERS = {
  SENDGRID: 0,
  MAILGUN: 1
}

export const EMAIL_PROVIDERS_CREDENTIAL_KEYS = {
  [EMAIL_PROVIDERS.SENDGRID]: [
    'APP_SENDGRID_HOST',
    'APP_SENDGRID_PORT',
    'APP_SENDGRID_USERNAME',
    'APP_SENDGRID_RELAY_KEY',
    'APP_SENDGRID_EMAIL'
  ],
  [EMAIL_PROVIDERS.MAILGUN]: [
    'MAILGUN_DOMAIN',
    'MAILGUN_FROM_EMAIL',
    'MAILGUN_FROM_USERNAME',
    'MAILGUN_ENDPOINT',
    'MAILGUN_API_KEY',
  ]
}



export const WHITECLIFF_AXIOS_DELAY = 60000 // 1 minute Interval

export const WHITECLIFF_PROVIDERS_KEYS =[
  { key: 'Evolution Asia', id: 1 },
  { key: 'Big Gaming', id: 2 },
  { key: 'Micro Gaming Live', id: 3 },
  { key: 'PlayAce', id: 5 },
  { key: 'Dream Gaming', id: 6 },
  { key: 'Sexy Gaming', id: 9 },
  { key: 'Live 88', id: 11 },
  { key: 'Ezugi', id: 17 },
  { key: 'Skywind Live', id: 19 },
  { key: 'Pragmatic Play Live', id: 28 },
  { key: 'Betgames', id: 41 },
  { key: 'SA Gaming', id: 44 },
  { key: 'Illustrate Analytics', id: 100 },
  { key: 'BTI Sports', id: 105 },
  { key: 'Habanero', id: 201 },
  { key: 'Spade Gaming', id: 205 },
  { key: 'Play\'N Go', id: 207 },
  { key: 'World Match', id: 208 },
  { key: 'Micro Gaming Slot', id: 209 },
  { key: 'OneTouch', id: 211 },
  { key: 'Red Tiger Asia', id: 213 },
  { key: 'Netent Asia', id: 214 },
  { key: 'YGGDrasil', id: 216 },
  { key: 'BNG', id: 217 },
  { key: 'CQ9 Slot', id: 220 },
  { key: 'Skywind Slot', id: 221 },
  { key: 'Wazdan', id: 222 },
  { key: 'PG Soft', id: 223 },
  { key: 'Big Time Gaming Asia', id: 225 },
  { key: 'Pragmatic Play Slots', id: 226 },
  { key: 'No Limit City Asia', id: 227 },
  { key: 'Relax Gaming', id: 229 },
  { key: 'NextSpin', id: 231 },
  { key: 'Naga Games', id: 249 },
  { key: 'Hacksaw Gaming', id: 255 },
  { key: 'JiLi', id: 263 },
  { key: 'Booming Games', id: 271 },
  { key: 'Spribe', id: 300 },
  { key: 'Alize', id: 302 },
  { key: 'Aviatrix', id: 303 },
  { key: 'Fast Game', id: 304 }
]
