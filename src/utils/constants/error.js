import { StatusCodes } from 'http-status-codes'

export const ERROR_MSG = {
  SERVER_ERROR: 'Something went wrong',
  EXISTS: 'already exists',
  NOT_FOUND: 'not found',
  NOT_EXISTS: 'does not exists',
  ID_REQUIRED: 'ID required',
  EMAIL_INVALID: 'Email not valid',
  SUPPORT_EMAIL_REQUIRED: 'support_email required',
  SITE_NAME_REQUIRED: 'site_name required',
  ORIGIN_REQUIRED: 'origin required',
  KEY_VALUE_REQUIRED: 'value required',
  EMAIL_EXIST: 'Email Address already exist',
  NOT_ALLOWED: 'Action not allowed',
  FAILED: 'failed',
  CREATE_ERROR: 'Cannot create admin user',
  PERMISSION_DENIED: ' permission denied',
  SENDGRID_ERROR: 'Unable to send email.',
  ELASTIC_CONNECTION: 'Unable to fetch data from elastic',
  SENDGRID_CREDENTIALS: 'Sendgrid credentials not found',
  PRIMARY_TEMPLATE_ERROR: 'Select other primary template',
  CREDENTIALS_NOT_FOUND: 'Send Grid credentials not found',
  ELASTIC_DOWN: 'Elastic cluster is down !!'
}

export const RequestInputValidationErrorType = {
  name: 'RequestInputValidationError',
  statusCode: StatusCodes.BAD_REQUEST,
  isOperational: true,
  description: 'Please check the request data',
  errorCode: 3001
}

export const ResponseValidationErrorType = {
  name: 'ResponseInputValidationError',
  statusCode: StatusCodes.BAD_REQUEST,
  isOperational: false,
  description: 'Response validation failed please refer json schema of response',
  errorCode: 3002
}

export const SocketRequestInputValidationErrorType = {
  name: 'SocketRequestInputValidationError',
  statusCode: StatusCodes.BAD_REQUEST,
  isOperational: true,
  description: 'Please check the request data',
  errorCode: 3003
}

export const SocketResponseValidationErrorType = {
  name: 'SocketResponseValidationError',
  statusCode: StatusCodes.BAD_REQUEST,
  isOperational: false,
  description: 'Response validation of socket failed please refer json schema of response',
  errorCode: 3004
}

export const InternalServerErrorType = {
  name: 'InternalServerError',
  statusCode: StatusCodes.INTERNAL_SERVER_ERROR,
  isOperational: true,
  description: 'Internal Server Error',
  errorCode: 3005
}

export const InvalidSocketArgumentErrorType = {
  name: 'InvalidSocketArgumentError',
  statusCode: StatusCodes.BAD_REQUEST,
  isOperational: true,
  description: 'Please provide, proper arguments eventName, [payloadObject], and [callback]',
  errorCode: 3006
}

export const EmailTemplateNotFoundErrorType = {
  name: 'EmailTemplateNotFound',
  statusCode: StatusCodes.NOT_FOUND,
  isOperational: true,
  description: 'Email template not found',
  errorCode: 3007
}
export const EmailTemplateActionAllowErrorType = {
  name: 'EmailTemplateNot',
  statusCode: StatusCodes.EXPECTATION_FAILED,
  isOperational: true,
  description: 'Email template action not allow',
  errorCode: 3008
}
export const CredentialsNotFoundErrorType = {
  name: 'CredentialsNotFound',
  statusCode: StatusCodes.NOT_FOUND,
  isOperational: true,
  description: 'Credentials Not found',
  errorCode: 309
}
