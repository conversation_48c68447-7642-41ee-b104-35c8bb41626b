import { createCanvas } from 'canvas'
import FormData from 'form-data'
import Mailgun from 'mailgun.js'
import nodemailer from 'nodemailer'
import config from '../configs/app.config'
import Logger from '../libs/logger'
import { EMAIL_PROVIDERS } from './constants/constant'
export const monthlyWeeklyChange = (currentValue, lastValue) => {
  let percentageIncrease = 0
  if (lastValue) {
    percentageIncrease = ((currentValue - lastValue) / lastValue) * 100
  }

  const totalValue = (+percentageIncrease > 0) ? `+${+percentageIncrease.toFixed(2)}%` : +percentageIncrease.toFixed(2) + '%'
  return totalValue
}

export const formatEURPrice = (number) => {
  return number.toLocaleString('en-MT', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

export const createSnapshot = ({ jsonData, headers }) => {
  try {
  // Calculate canvas height based on number of rows
    const extraSpace = 40
    const numRows = jsonData.resultData.length
    const numRowsWithEmptyRows = numRows + Math.floor(numRows / 12) // Add space for empty rows
    const rowHeight = 20
    const headerHeight = 30
    const cellPadding = 10 // Extra padding around each cell
    const cellWidth = 200 // Adjust cell width as needed
    const tableWidth = cellWidth * headers.length // Number of columns
    const canvasWidth = tableWidth + 2 * cellPadding + extraSpace // Add padding on both sides
    const canvasHeight = (numRowsWithEmptyRows + 1) * rowHeight + headerHeight + 2 * cellPadding + extraSpace// Add padding on top and bottom

    // Create a canvas
    const canvas = createCanvas(canvasWidth + extraSpace * 2, canvasHeight + extraSpace) // Set canvas size dynamically
    const context = canvas.getContext('2d')

    context.fillStyle = '#ffffff' // White color
    context.fillRect(0, 0, canvas.width, canvas.height)

    // Draw table headers
    const borderLineWidth = 2 // Adjust border line width
    context.fillStyle = '#5d6d7e'
    context.fillRect(cellPadding + extraSpace, cellPadding + extraSpace, tableWidth, headerHeight)
    context.font = 'bold 16px Arial'
    context.fillStyle = '#FFFFFF'
    context.textAlign = 'center' // Center-align text
    headers.forEach((header, index) => {
      let headerName = header
      if (header === 'Yesterday') {
        headerName = ((d => `${d.getDate()} ${d.toLocaleString('en-GB', { month: 'long' })}, ${d.getFullYear()}`)(new Date(new Date().setDate(new Date().getDate() - 2))))
      }
      context.fillText(headerName, cellPadding + extraSpace + (index + 0.5) * cellWidth, cellPadding + extraSpace + headerHeight / 2 + 5) // Adjust x position for center alignment
    })

    // Draw table rows
    const rows = jsonData.resultData
    context.font = '12px Arial'
    context.fillStyle = '#000000'
    let rowIndex = 0
    let rowsSinceLastEmptyRow = 0

    rows.forEach((row, i) => {
      if (rowsSinceLastEmptyRow === 10) {
      // Draw row lines with extra space and increased line width
        const y = cellPadding + extraSpace + (rowIndex + 1) * rowHeight + headerHeight // Adjust y position to draw row line
        context.fillStyle = '#000000'
        context.fillRect(cellPadding + extraSpace, y, tableWidth, rowHeight)

        context.strokeStyle = '#0C090A' // Light gray color for row lines
        context.lineWidth = borderLineWidth // Increase border line width

        context.beginPath()
        context.moveTo(cellPadding + extraSpace, y)
        context.lineTo(cellPadding + tableWidth + extraSpace, y)
        context.stroke()

        rowIndex++
        rowsSinceLastEmptyRow = 0
      }

      if (rowsSinceLastEmptyRow > 3 && rowsSinceLastEmptyRow < 8) {
        const y = cellPadding + extraSpace + (rowIndex + 1) * rowHeight + headerHeight
        context.fillStyle = '#b2babb'
        context.fillRect(cellPadding + extraSpace, y, tableWidth, rowHeight)
        context.fillStyle = '#000000'
      } else if (rowsSinceLastEmptyRow < 4) {
        const y = cellPadding + extraSpace + (rowIndex + 1) * rowHeight + headerHeight
        context.fillStyle = '#a569bd'
        context.fillRect(cellPadding + extraSpace, y, tableWidth, rowHeight)
        context.fillStyle = '#000000'
      } else {
        context.fillStyle = '#000000'
      }

      // Iterate only over the columns that match the headers
      headers.forEach((header, colIndex) => {
        const value = row[header]
        const x = cellPadding + extraSpace + colIndex * cellWidth
        const y = cellPadding + extraSpace + (rowIndex + 1) * rowHeight + headerHeight // Offset by 1 for header and 0-index

        // Draw cell border with extra space and increased line width
        context.strokeStyle = '#0C090A' // Light gray color for cell border
        context.lineWidth = borderLineWidth // Increase border line width
        context.strokeRect(x, y, cellWidth, rowHeight)
        context.textAlign = 'center' // Center-align text within cell

        if (header === 'Value') {
          context.font = 'bold 12px Arial' // Set font to bold
          context.textAlign = 'left' // Center-align text within cell
          // Draw text
          context.fillText(value, (x + cellWidth / 2) - 95, y + rowHeight / 2 + 5) // Adjust x, y position for center alignment
        } else if (header === 'Name') {
          context.font = 'bold 12px Arial' // Set font to bold
          // Draw text
          context.fillText(value, x + cellWidth / 2, y + rowHeight / 2 + 5) // Adjust x, y position for center alignment
        } else {
          context.font = '12px Arial' // Reset font to normal
          // Draw text
          context.fillText(value, x + cellWidth / 2, y + rowHeight / 2 + 5) // Adjust x, y position for center alignment
        }
      })

      rowIndex++
      rowsSinceLastEmptyRow++
    })

    // Draw row lines with extra space and increased line width
    const y = cellPadding + extraSpace + (rowIndex + 1) * rowHeight + headerHeight // Adjust y position to draw row line
    context.fillStyle = '#000000'
    context.fillRect(cellPadding + extraSpace, y, tableWidth, rowHeight)

    context.strokeStyle = '#0C090A' // Light gray color for row lines
    context.lineWidth = borderLineWidth // Increase border line width

    context.beginPath()
    context.moveTo(cellPadding + extraSpace, y)
    context.lineTo(cellPadding + tableWidth + extraSpace, y)
    context.stroke()

    // Draw column lines
    context.beginPath()
    for (let i = 0; i < headers.length - 1; i++) { // Loop over the fixed number of columns
      const x = cellPadding + extraSpace + (i + 1) * cellWidth
      context.moveTo(x, cellPadding + extraSpace)
      context.lineTo(x, canvas.height - cellPadding - extraSpace - rowHeight)
    }
    context.lineWidth = borderLineWidth
    context.stroke()

    // Convert canvas to JPEG
    const jpegStream = canvas.createJPEGStream({
      quality: 0.8 // JPEG quality, between 0 and 1
    })

    return jpegStream
  } catch (error) {
    Logger.error(`Error in creating canvas: ${error}`)
    return false
  }
}

export const sendMail = async ({ credentials, emailProvider, casinoJpegStream }) => {
  const credentialMap = Object.fromEntries(credentials.map(c => [c.keyProvidedByAccount, c.value]))
  const date = new Date()
  date.setDate(date.getDate() - 1)
  const finalDate = date.toISOString().substring(0, 10)

  // const outputPath = '/tmp/snap.jpg'
  // const out = fs.createWriteStream(outputPath)
  // casinoJpegStream.pipe(out)
  // out.on('finish', () => {
  //   console.log(`JPEG saved to ${outputPath}`)
  // })

  // Utility function to convert stream to buffer
  const streamToBuffer = (stream) => {
    return new Promise((resolve, reject) => {
      const chunks = []
      stream.on('data', (chunk) => chunks.push(chunk))
      stream.on('end', () => resolve(Buffer.concat(chunks)))
      stream.on('error', (err) => reject(err))
    })
  }

  // Convert streams to buffers and then to base64
  const casinoBuffer = await streamToBuffer(casinoJpegStream)
  const casinoBase64 = casinoBuffer.toString('base64')

  // Define email details with embedded image? cre : s
  const cid = (emailProvider === EMAIL_PROVIDERS.MAILGUN) ? `Casino_Comparative_Report_(${finalDate}).jpg` : 'casinoReport'
  const email = {
    from: (emailProvider === EMAIL_PROVIDERS.MAILGUN) ? credentialMap?.MAILGUN_FROM_EMAIL : credentialMap?.APP_SENDGRID_EMAIL,
    to: config.get('env') === 'production' ? config.get('reportsEmail').split(',') : '<EMAIL>',
    bcc: '<EMAIL>,<EMAIL>,<EMAIL>',
    subject: `Comparative Report (${finalDate})`,
    html: `
    <h1>Comparative Report</h1>
    <img src="cid:${cid}" />
    <br><br>
  `,
    attachments: [
      {
        filename: `Casino_Comparative_Report_(${finalDate}).jpg`,
        content: casinoBase64,
        type: 'image/jpeg',
        cid: 'casinoReport', // used as <img src="cid:casinoReport" />
        disposition: 'inline',
        encoding: 'base64' // required
      },
      {
        filename: `Casino_Comparative_attachment_Report_(${finalDate}).jpg`,
        content: casinoBase64,
        type: 'image/jpeg',
        disposition: 'attachment',
        encoding: 'base64' // required
      }
    ]
  }

  // Send mail
  try {
    if (emailProvider === EMAIL_PROVIDERS.MAILGUN) {
      const MailgunClient = new Mailgun(FormData)
      const mg = MailgunClient.client({
        username: credentialMap?.MAILGUN_FROM_USERNAME || 'api',
        key: credentialMap?.MAILGUN_API_KEY
      })

      await mg.messages.create(
        credentialMap?.MAILGUN_DOMAIN,
        {
          from: email.from,
          to: email.to,
          bcc: email.bcc,
          subject: email.subject,
          html: email.html,
          inline: [
            {
              data: casinoBuffer,
              filename: `Casino_Comparative_Report_(${finalDate}).jpg`,
              contentType: 'image/jpeg',
              cid: 'casinoReport'
            }
          ],
          attachment: [
            {

              data: casinoBuffer,
              filename: `Casino_Comparative_attachment_Report_(${finalDate}).jpg`,
              contentType: 'image/jpeg'
            }
          ]
        }
      )
      Logger.info('Email sent successfully')
    } else {
      const transporter = await nodemailer.createTransport({
        service: 'SendGrid',
        host: credentialMap?.APP_SENDGRID_HOST,
        port: credentialMap?.APP_SENDGRID_PORT,
        auth: {
          user: credentialMap?.APP_SENDGRID_USERNAME,
          pass: credentialMap?.APP_SENDGRID_RELAY_KEY
        }
      })
      await transporter.sendMail(email)
      Logger.info('Email sent successfully')
    }
  } catch (error) {
    Logger.error(`Error in sending mail: ${error}`)
  }
}
