import { BonusEngineQueue, CashbackBonusRecurringActivationQueueSchedule, MultipleBonusActivationQueueSchedule } from '../queues/bonusEngine.queue'
import {
  AutomaticActiveLosingBonusQueueSchedule, AutomaticLosingBonusQueueSchedule, BetsTransactionsSummary, BulkDepositWithdrawAndAuditLogQueueSchedule, BurningDepositBonusQueueSchedule, BurningJoiningBonusQueueSchedule, BurningLosingBonusQueueSchedule, BurningManualLosingBonusQueueSchedule, BurningPromocodeBonusQueueSchedule, CombinedMenusGamesQueueSchedule, CommonQueue, ComparativeReport,
  ConfigurationBackupQueueSchedule,
  GameTransactionsSummary, IstAutomaticLosingBonusQueueSchedule,
  LotteryGenerateWinCallbacksSchedule,
  OfferWinnerAnnounceQueueSchedule,
  PremiumGameTransactionsSummary, RequestResponseLogSchedule,
  UpdateRiskAndFraudIndicatorsJob,
  UpdateTodayPlayerRevenueJob, UpsertMostPlayedGamesJob
} from '../queues/common.queue'
import { SmartiCoEventsDataQueueSchedule, SmartiCoEventsTransactionSchedule, SmartiCoQueue } from '../queues/smartiCo.queue'
import { BetTransactionSchedule, CasinoTransactionSchedule, RollbackTransactionSchedule, SportToCasinoTransactionSchedule, TransactionQueue, UserTransactionSchedule } from '../queues/transaction.queue'
const reportsTime = '15 0 * * *'
const todayPlayerRevenueUpdateTime = '*/15 * * * *';
const riskAndFraudIndicatorsUpdateTime = '30 1 * * *';
const upsertMostPlayedGamesTime = '0 */6 * * *';
const comparativeReportTime = '0 4 * * *'
const BulkDepositWithdrawAuditLogScheduleTime = '*/2 * * * *'
const BurningLosingBonusQueueScheduleTime = '0 2 * * *' // utc 2 am
const AutomaticLosingBonusQueueScheduleTime = '0 0 * * *'
const AutomaticActiveLosingBonusQueueScheduleTime = '0 0 * * *'
const requestResponseScheduleTime = '36 0 * * *'
const BurningJoiningBonusQueueScheduleTime = '0 */2 * * *' // every 2 hours
const BurningDepositBonusQueueScheduleTime = '0 */2 * * *' // every 2 hours
const SmartiCoEventsDataQueueScheduleTime = '*/2 * * * *' // every 2 minutes
const IstAutomaticLosingBonusQueueScheduleTime = '30 18 * * *' // ist 0 am
const BurningPromocodeBonusQueueScheduleTime = '0 */2 * * *' // every 2 hours

const userTransactionScheduleTime = '*/5 * * * *'
const casinoTransactionScheduleTime = '*/2 * * * *'
const betTransactionScheduleTime = '*/2 * * * *'
const sportCasinoTransactionScheduleTime = '*/2 * * * *'
const BurningManualLosingBonusQueueScheduleTime = '0 */2 * * *' // every 2 hours
const CombinedMenusGamesQueueScheduleTime = '0 */3 * * *' // every 3 hours
const SmartiCoEventsTransactionQueueScheduleTime = '*/2 * * * *' // every 2 minutes
const ConfigurationBackupQueueScheduleTime = '*/10 * * * *' // utc 2 am
const OfferWinnerAnnounceQueueScheduleTime = '30 2 * * *' // utc 2 am
const MultipleBonusActivationQueueScheduleTime ='*/2 * * * *' // every 2 minutes
const CashbackBonusRecurringActivationQueueScheduleTime = '30 18 * * *' // 12 am in IST
const lotteryGenerateWinCallbacksTime = '*/30 * * * *';
const RollbackTransactionScheduleTime = '*/10 * * * *'

CommonQueue.add(UpdateRiskAndFraudIndicatorsJob, {},
  {
    jobId: UpdateRiskAndFraudIndicatorsJob,
    removeOnComplete: true,
    repeat: { cron: riskAndFraudIndicatorsUpdateTime }
  }
)
CommonQueue.add(UpdateTodayPlayerRevenueJob, {},
  {
    jobId: UpdateTodayPlayerRevenueJob,
    removeOnComplete: true,
    repeat: { cron: todayPlayerRevenueUpdateTime }
  }
)
CommonQueue.add(UpsertMostPlayedGamesJob, {},
  {
    jobId: UpsertMostPlayedGamesJob,
    removeOnComplete: true,
    repeat: { cron: upsertMostPlayedGamesTime }
  }
)
CommonQueue.add(GameTransactionsSummary, {},
  {
    jobId: GameTransactionsSummary,
    removeOnComplete: true,
    repeat: { cron: reportsTime }
  }
)
CommonQueue.add(PremiumGameTransactionsSummary, {},
  {
    jobId: PremiumGameTransactionsSummary,
    removeOnComplete: true,
    repeat: { cron: reportsTime }
  }
)
CommonQueue.add(BetsTransactionsSummary, {},
  {
    jobId: BetsTransactionsSummary,
    removeOnComplete: true,
    repeat: { cron: reportsTime }
  }
)

CommonQueue.add(ComparativeReport, {},
  {
    jobId: ComparativeReport,
    removeOnComplete: true,
    repeat: { cron: comparativeReportTime }
  }
)

CommonQueue.add(BulkDepositWithdrawAndAuditLogQueueSchedule, {
  time: new Date(),
  transactionType: 'Bulk Deposit Withdraw And Audit Log schedule',
  transactionId: ''
},
{
  jobId: BulkDepositWithdrawAndAuditLogQueueSchedule,
  removeOnComplete: true,
  repeat: { cron: BulkDepositWithdrawAuditLogScheduleTime }
}
)

CommonQueue.add(BurningLosingBonusQueueSchedule, {
  time: new Date(),
  transactionType: 'Burning losing bonus',
  transactionId: ''
},
{
  jobId: BurningLosingBonusQueueSchedule,
  removeOnComplete: true,
  repeat: { cron: BurningLosingBonusQueueScheduleTime }
}
)

CommonQueue.add(AutomaticLosingBonusQueueSchedule, {
  time: new Date(),
  transactionType: 'Automatic losing bonus',
  transactionId: ''
},
{
  jobId: AutomaticLosingBonusQueueSchedule,
  removeOnComplete: true,
  repeat: { cron: AutomaticLosingBonusQueueScheduleTime }
}
)

CommonQueue.add(AutomaticActiveLosingBonusQueueSchedule, {
  time: new Date(),
  transactionType: 'Automatic active losing bonus',
  transactionId: ''
},
{
  jobId: AutomaticActiveLosingBonusQueueSchedule,
  removeOnComplete: true,
  repeat: { cron: AutomaticActiveLosingBonusQueueScheduleTime }
}
)

CommonQueue.add(RequestResponseLogSchedule, {
  time: new Date(),
  transactionType: 'Request Response Log schedule',
  transactionId: ''
},
{
  jobId: RequestResponseLogSchedule,
  removeOnComplete: true,
  repeat: { cron: requestResponseScheduleTime }
}
)

CommonQueue.add(BurningJoiningBonusQueueSchedule, {
  time: new Date(),
  transactionType: 'Burning joining bonus',
  transactionId: ''
},
{
  jobId: BurningJoiningBonusQueueSchedule,
  removeOnComplete: true,
  repeat: { cron: BurningJoiningBonusQueueScheduleTime }
}
)

CommonQueue.add(BurningDepositBonusQueueSchedule, {
  time: new Date(),
  transactionType: 'Burning deposit bonus',
  transactionId: ''
},
{
  jobId: BurningDepositBonusQueueSchedule,
  removeOnComplete: true,
  repeat: { cron: BurningDepositBonusQueueScheduleTime }
}
)

SmartiCoQueue.add(SmartiCoEventsDataQueueSchedule, {
  time: new Date(),
  transactionType: 'SmartiCo Events Data Schedule',
  transactionId: ''
},
{
  jobId: SmartiCoEventsDataQueueSchedule,
  removeOnComplete: true,
  repeat: { cron: SmartiCoEventsDataQueueScheduleTime }
}
)

SmartiCoQueue.add(SmartiCoEventsTransactionSchedule, {
  time: new Date(),
  transactionType: 'SmartiCo Events Transactions Schedule',
  transactionId: ''
},
{
  jobId: SmartiCoEventsTransactionSchedule,
  removeOnComplete: true,
  repeat: { cron: SmartiCoEventsTransactionQueueScheduleTime }
}
)

CommonQueue.add(IstAutomaticLosingBonusQueueSchedule, {
  time: new Date(),
  transactionType: 'IST Automatic losing bonus',
  transactionId: ''
},
{
  jobId: IstAutomaticLosingBonusQueueSchedule,
  removeOnComplete: true,
  repeat: { cron: IstAutomaticLosingBonusQueueScheduleTime }
}
)

CommonQueue.add(BurningPromocodeBonusQueueSchedule, {
  time: new Date(),
  transactionType: 'Burning promocode bonus',
  transactionId: ''
},
{
  jobId: BurningPromocodeBonusQueueSchedule,
  removeOnComplete: true,
  repeat: { cron: BurningPromocodeBonusQueueScheduleTime }
}
)

TransactionQueue.add(BetTransactionSchedule, {
  time: new Date(),
  transactionType: 'Bet transaction schedule',
  transactionId: ''
},
{
  jobId: BetTransactionSchedule,
  removeOnComplete: true,
  repeat: { cron: betTransactionScheduleTime }
}
)
TransactionQueue.add(CasinoTransactionSchedule, {
  time: new Date(),
  transactionType: 'Casino transaction schedule',
  transactionId: ''
},
{
  jobId: CasinoTransactionSchedule,
  removeOnComplete: true,
  repeat: { cron: casinoTransactionScheduleTime }
}
)
TransactionQueue.add(UserTransactionSchedule, {
  time: new Date(),
  transactionType: 'User transaction schedule',
  transactionId: ''
},
{
  jobId: UserTransactionSchedule,
  removeOnComplete: true,
  repeat: { cron: userTransactionScheduleTime }
}
)
TransactionQueue.add(SportToCasinoTransactionSchedule, {
  time: new Date(),
  transactionType: 'Sport To Casino transaction schedule',
  transactionId: ''
},
{
  jobId: SportToCasinoTransactionSchedule,
  removeOnComplete: true,
  repeat: { cron: sportCasinoTransactionScheduleTime }
}
)
TransactionQueue.add(RollbackTransactionSchedule, {
  time: new Date(),
  transactionType: 'Rollback transaction schedule',
  transactionId: ''
},
{
  jobId: RollbackTransactionSchedule,
  removeOnComplete: true,
  repeat: { cron: RollbackTransactionScheduleTime }
}
)

CommonQueue.add(BurningManualLosingBonusQueueSchedule, {
  time: new Date(),
  transactionType: 'Burning manual losing bonus',
  transactionId: ''
  },
  {
    jobId: BurningManualLosingBonusQueueSchedule,
    removeOnComplete: true,
    repeat: { cron: BurningManualLosingBonusQueueScheduleTime }
  }
)
CommonQueue.add(CombinedMenusGamesQueueSchedule, {
  time: new Date(),
  transactionType: 'Combined menu games',
  transactionId: ''
  },
  {
    jobId: CombinedMenusGamesQueueSchedule,
    removeOnComplete: true,
    repeat: { cron: CombinedMenusGamesQueueScheduleTime }
  }
)

CommonQueue.add(ConfigurationBackupQueueSchedule, {
  time: new Date(),
  transactionType: 'Configuration Backup',
  transactionId: ''
},
  {
    jobId: ConfigurationBackupQueueSchedule,
    removeOnComplete: true,
    repeat: { cron: ConfigurationBackupQueueScheduleTime }
  }
)

CommonQueue.add(OfferWinnerAnnounceQueueSchedule, {
  time: new Date(),
  transactionType: 'Announce Offer Winners',
  transactionId: ''
},
  {
    jobId: OfferWinnerAnnounceQueueSchedule,
    removeOnComplete: true,
    repeat: { cron: OfferWinnerAnnounceQueueScheduleTime }
  }
)

BonusEngineQueue.add(MultipleBonusActivationQueueSchedule, {
  time: new Date(),
  transactionType: 'Multiple Bonus Activation',
  transactionId: ''
},
  {
    jobId: MultipleBonusActivationQueueSchedule,
    removeOnComplete: true,
    repeat: { cron: MultipleBonusActivationQueueScheduleTime }
  }
)

BonusEngineQueue.add(CashbackBonusRecurringActivationQueueSchedule, {
  time: new Date(),
  transactionType: 'Cashback Bonus Recurring Activation',
  transactionId: ''
},
  {
    jobId: CashbackBonusRecurringActivationQueueSchedule,
    removeOnComplete: true,
    repeat: { cron: CashbackBonusRecurringActivationQueueScheduleTime }
  }
)

CommonQueue.add(LotteryGenerateWinCallbacksSchedule, {},
  {
    jobId: LotteryGenerateWinCallbacksSchedule,
    removeOnComplete: true,
    repeat: { cron: lotteryGenerateWinCallbacksTime }
  }
)
