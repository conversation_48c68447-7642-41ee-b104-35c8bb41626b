import { BonusEngineQueue, BonusEngineSchedule } from '../queues/bonusEngine.queue'
import { CreateCsvQueue, CreateCsvSchedule } from '../queues/createCsv.queue'
//import { AuditLogSchedule, AuditLogQueue } from '../queues/auditLog.queue'
//import { BulkDepositWithdrawTransactionSchedule, BulkDepositWithdrawTransactionQueue } from '../queues/bulkDepositWithdrawlTransaction.queue'
const createCsvScheduleTime = '*/1 * * * *'
const BonusEngineScheduleTime = '*/1 * * * *'
//const auditLogScheduleTime = '*/2 * * * *'
//const bulkDepositWithdrawTransactionScheduleTime = '*/2 * * * *'


CreateCsvQueue.add(CreateCsvSchedule, {
  time: new Date(),
  transactionType: 'Create Csv schedule',
  transactionId: ''
},
{
  jobId: CreateCsvSchedule,
  removeOnComplete: true,
  repeat: { cron: createCsvScheduleTime }
}
)

BonusEngineQueue.add(BonusEngineSchedule, {
  time: new Date(),
  transactionType: 'Bonus Engine schedule',
  transactionId: ''
},
{
  jobId: BonusEngineSchedule,
  removeOnComplete: true,
  repeat: { cron: BonusEngineScheduleTime }
}
)

// AuditLogQueue.add(AuditLogSchedule, {
//   time: new Date(),
//   transactionType: 'Audit Log schedule',
//   transactionId: ''
// },
// {
//   jobId: AuditLogSchedule,
//   removeOnComplete: true,
//   repeat: { cron: auditLogScheduleTime }
// }
// )

// BulkDepositWithdrawTransactionQueue.add(BulkDepositWithdrawTransactionSchedule, {
//   time: new Date(),
//   transactionType: 'Bet transaction schedule',
//   transactionId: ''
// },
// {
//   jobId: BulkDepositWithdrawTransactionSchedule,
//   removeOnComplete: true,
//   repeat: { cron: bulkDepositWithdrawTransactionScheduleTime }
// }
// )
