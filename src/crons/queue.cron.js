import getQueueData from '../common/getQueueData'
// import runAutomaticActiveLosingBonus from '../common/runAutomaticActiveLosingBonus'
export default (cron) => {
  const date = new Date()

  cron.schedule('*/2 * * * *', async () => {
    await getQueueData()
    console.log('Queue_log', date)
  })
//   cron.schedule('30 1 * * *', async () => {
//     await archiveTransactions()
//     console.log('archiveTransactions', date)
//   })
//   cron.schedule('25 1 * * *', async () => {
//     await partitionMaintenance()
//     console.log('partitionMaintenance', date)
//   })
//   cron.schedule('0 1 * * *', async () => {
//     await backupLogData()
//     console.log('Backup_log_data_cron', date)
//   })
//   cron.schedule('0 0 * * *', async () => {
//     await updateDepositRequest()
//     console.log('Run_deposit_request_cron', date)
//     // await runAutomaticLosingBonus()
//     // console.log('Run automatic losing bonus cron', date);
//     await updateLosingBonusStatus()
//     console.log('Update_manual_losing_bonus_status', date)
//     // await runAutomaticActiveLosingBonus()
//     await deleteTokenData()
//     console.log('Delete_spribe_auth_token_data_cron', date)

//     // await updatePlayerCategoryLevel()
//     // console.log('Run_update_player_category_cron', date)

//     await deleteCsvExportData()
//     console.log('Delete_export_CSV_data_cron', date)
//   })
//   cron.schedule('0 * * * *', async () => {
//     await deleteQueueLogsData()
//     console.log('Delete_queue_log_data_cron', date)
//   })

//   cron.schedule('0 */24 * * *', async () => {
//     await updatingPendingTransactions(PAYOUT_WITHDRAW_PROVIDER)
//     console.log('Updating_pending_transactions', date)
//   })
//   // cron for backup request response log
//   cron.schedule('0 3 * * *', async () => {
//     // await deleteUserLoginHistoryData()

//     // update user'category dynamically
//     await updatePlayerCategoryScoreWise()
//   })

//   // cron for backup request response log
//   cron.schedule('0 */5 * * *', async () => {
//     await updatePayInStatus()
//     console.log('PayIn_status_updated_successfully_successully_done', date)
//   })

//   cron.schedule('0 */6 * * *', async () => { // running every 6 hours
//     await pendingDepositQueueHandler(PAYMENT_GATEWAY_NAMES.PAYWINGS_PAYIN)
//     console.log('Updating_pending_paywings_payin_transactions', date)
//   })

//   cron.schedule('0 */6 * * *', async () => { // running every 6 hours
//     await pendingDepositQueueHandler(PAYMENT_GATEWAY_NAMES.ACKO_PAYIN)
//     console.log('Updating pending paycookies payin transactions', date)
//   })

//   cron.schedule('0 */3 * * *', async () => { // running every 3 hours
//     await pendingDepositQueueHandler(PAYMENT_GATEWAY_NAMES.PAYCOOKIES_PAYIN)
//     await pendingDepositQueueHandler(PAYMENT_GATEWAY_NAMES.JASPAY_PAYIN)
//     await updatingPendingTransactions(PAYCOOKIES_WITHDRAW)
//     await pendingDepositQueueHandler(PAYMENT_GATEWAY_NAMES.XAMAX_PAYIN)
//     await pendingDepositQueueHandler(PAYMENT_GATEWAY_NAMES.PEER_PAY_PAYIN)
//     await updatingPendingTransactions(XAMAX_WITHDRAW_PROVIDER)
//     await pendingDepositQueueHandler(PAYMENT_GATEWAY_NAMES.CLOUDCASH_PAYIN)
//     console.log('Updating_pending_paycookies_payin_and_payout_transactions', date)
//   })

//   cron.schedule('0 */2 * * *', async () => {
//     await createPartnerMatrixQueue()
//     await createWyntaQueue()
//     console.log('partner_matrix_cron_run', new Date())
//   }, {
//     scheduled: true,
//     timezone: 'Asia/Kolkata'
//   })
//   cron.schedule('15 0 * * *', async () => {
//     await createPartnerMatrixQueue(true)
//     await createWyntaQueue(true)
//     console.log('partner_matrix_cron_run_15_0_*_*_*', new Date())
//   }, {
//     scheduled: true,
//     timezone: 'Asia/Kolkata'
//   })

//   // marina888 transaction migration cron (not needed now)
//   // cron.schedule('*/5 * * * *', async () => {
//   //   await marina888MigrationCron()
//   //   console.log('marina888 Migration Cron', date)
//   // })

//   // cron for user First deposit
//   cron.schedule('0 2 * * *', async () => {
//     await insertUserFirstDeposit()
//   })

//   // Cron for adding entries in 'callback_transactions' table everyday.
//   cron.schedule('0 23 * * *', async () => {
//     await addCallbackTransactions()
//     console.log('Add_Callback_Transactions_cron', date)
//   })

//   cron.schedule('*/5 * * * *', async () => {
//     await updatePlayerBetAmount()
//   })

//   // Cron for deleting records from error log table, except for the data from the last 10 days.. everyday @4:00 AM.
//   cron.schedule('0 4 * * *', async () => {
//     await deleteOldErrorLogs()
//     console.log('Deleting_data_from_error_log_table_cron', date)
//   })

//   // Cron for updating latest winners data tenant wise.. every 3 hours.
//   cron.schedule('0 */2 * * *', async () => {
//     await getLatestWinners()
//     console.log('Updating_latest_winners_data_tenant_wise', date)
//   })

//   // Cron for pg soft game seeding table everyday @2:30 AM. 30 2 * * *
//   cron.schedule('30 2 * * *', async () => {
//     await pgSoftGamePopulate()
//     console.log('Pg_soft_game_seeding_cron', date)
//   })

//   // Cron for st8 game seeding table everyday @2:30 AM. 30 2 * * *
//   cron.schedule('30 2 * * *', async () => {
//     await st8GamePopulate()
//     console.log('st8_game_seeding_cron', date)
//   })

//   // Cron for spribe game seeding table everyday @2:30 AM. 30 2 * * *
//   cron.schedule('30 2 * * *', async () => {
//     await spribeGamePopulate()
//     console.log('spribe_game_seeding_cron', date)
//   })

//   // Cron for lottery game seeding table everyday @2:30 AM. 30 2 * * *
//   cron.schedule('30 2 * * *', async () => {
//     await lotteryGamePopulation()
//     console.log('lottery777_game_population', date)
//   })

//   //Cron for darwin game seeding table everyday @2:30 AM.
//   cron.schedule('30 2 * * *', async () => {
//     await darwinGamePopulate()
//     console.log("Darwin_game_seeding_cron", date)
//   })

//   // Cron for FunkyGames game seeding table everyday @2:30 AM.
//   cron.schedule('30 2 * * *', async () => {
//     await funkyGamesGamePopulation()
//     console.log("FunkyGames_game_seeding_cron", date)
//   })

//   // Cron for Ezugi game seeding table everyday @2:30 AM.
//   cron.schedule('30 2 * * *', async () => {
//     await ezugiGamePopulate()
//     console.log("Ezugi_game_seeding_cron", date)
//   })


//   // Cron for Spinacchio game seeding table everyday @2:30 AM.
//   cron.schedule('30 2 * * *', async () => {
//     await spinocchioGamePopulation()
//     console.log('spinocchio_game_population', date)
//   })

//   cron.schedule('5 0 * * *', async () => {
//     await addInPlayerRevenueReport()
//     console.log("player_revenue_report_cron")
//   }, {
//     scheduled: true,
//     timezone: 'Africa/Lusaka'
//   })

//   // Cron to update offer leaderboard using player revenue tables everyday @12.15 AM.
//   // Dependent on 'addInPlayerRevenueReport' cron
//   // cron.schedule('15 0 * * *', async () => {
//   cron.schedule('*/5 * * * *', async () => {
//     await offerLeaderBoard()
//     console.log("Offer_leaderboard_update_cron", date)
//   }, {
//     scheduled: true,
//     timezone: 'Asia/Kolkata'
//   })

//   cron.schedule('0 1 * * *', async () => {
//     await addInAgentRevenueReport()
//     console.log("agent_revenue_report_cron")
//   }, {
//     scheduled: true,
//     timezone: 'Africa/Lusaka'
//   })

//   cron.schedule('*/5 * * * * *', async () => {
//     await agentPlayerRevenueCustomTypesSeedingCron();
//     console.log("Agent_Player_Revenue_Custom_Type_Seeding_Cron", new Date());
//   });

//   // Added a cron which runs every 30 sec
//   cron.schedule('*/10 * * * * *', async () => {
//     await agentPlayerRevenueTrackerCron();
//     console.log("Agent_Player_Revenue_Pending_Task_Check", new Date());
//   });

//   //  Cron for WhiteCliff game seeding table everyday @2:30 AM. 30 2 * * * // every 15 min for testing
//   cron.schedule('0 0 * * 0', async () => {
//     await whiteCliffGamePopulation()
//     console.log("WhiteCliff game seeding cron", date)
//   })

//  // Whitecliff : Cron to fetch Sports Rawdata
//   cron.schedule('*/2 * * * *', async () => {
//     await whiteCliffSportsRawdata();
//     console.log("Whitecliff_sports_rawdata_fetch_today_cron", date)
//   })

//   // Cron for generating GGR summary which will run everyday at 12 am
//   // cron.schedule('*/1 * * * *', async () => {
//   //   await ggrSummary()
//   //   console.log('Create_ggr_summary', date)
//   // })

//   // Cron to create bot transaction which will run every minute..
//   cron.schedule('*/1 * * * *', async () => {
//     await botTransactionsV2()
//     console.log('create_bot_transactions', date)
//   })

//   cron.schedule('*/2 * * * *', async () => {
//     await updatePlayerCategoryLevel()
//     console.log('Run_update_player_category_cron', date)
//   })

//   cron.schedule('*/50 * * * * *', async () => {
//     const now = new Date().toISOString();
//     await clearCaptchaUsedOrExpired();
//     console.log('cleared_expired_captchas_at', now);
//   });

//   // Cron to update crypto conversion rates everyday at 12:00 am
//   cron.schedule('0 0 * * *', async () => {
//     await cryptoCurrencyConversion()
//     console.log('Financial_Overview_update_cron', date)
//   })

//   // Cron for Turbo Games seeding table everyday @2:30 AM.
//   cron.schedule('30 2 * * *', async () => {
//     await turboGamesPopulation()
//     console.log('turbo_game_population', date)
//   })
}
