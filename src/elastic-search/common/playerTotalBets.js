import config from "../../configs/app.config"
/**
 *
 * this return player total bets
 *
 * @export
 * @param {object} param
 * @param {object} esClient
 * @returns {object} player total bet amount,total bets
 */
export default async function playerTotalBets (param, esClient) {
  try {
    const playerWalletBalance = await esClient.search({
      index: config.getProperties().es_index.users_index_name,
      body: {
        query: {
          bool: {
            must: [
              {
                match: { player_id: param.dataValues.actioneeId }
              }
            ]
          }
        }
      }
    })

    return {
      totalBetAmount: +playerWalletBalance.body.hits.hits[0]._source.total_bet_amount,
      totalBets: +playerWalletBalance.body.hits.hits[0]._source.total_bets
    }
  } catch (e) {
    throw new Error(e)
  }
}
