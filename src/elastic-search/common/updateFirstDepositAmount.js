
import config from "../../configs/app.config"
/**
 *
 *
 * @export
 * @param {*} params
 * @param {*} esClient
 * @returns
 */
export default async function updateFirstDepositAmount (id, sourceObj, esClient) {
  try {
    const playerUpdated = await esClient.updateByQuery({
      index: config.getProperties().es_index.users_index_name,
      refresh: true,
      body: {
        script: {
          lang: 'painless',
          source: sourceObj
        },
        query: {
          match: {
            player_id: id
          }
        }
      }
    })
    return playerUpdated
  } catch (e) {
    throw new Error(e)
  }
}
