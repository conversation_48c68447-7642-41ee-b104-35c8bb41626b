import { Sequelize } from 'sequelize'

export default async function playerParentIds (startWithId, parent_type, tenant_id, sequelize, startID = '') {
  const admin_users = await sequelize.query(`
  WITH RECURSIVE cte_query AS
  (
    SELECT id, email, parent_type, parent_id, tenant_id,agent_name FROM admin_users as m WHERE id = ${startWithId} and tenant_id = ${tenant_id}

    UNION

    SELECT e.id, e.email, e.parent_type, e.parent_id, e.tenant_id,e.agent_name FROM admin_users as e
    INNER JOIN cte_query c ON c.parent_id = e.id  and e.tenant_id = ${tenant_id}
  )
  SELECT * FROM cte_query;
    `,
    { type: Sequelize.QueryTypes.SELECT, useMaster: false }
  )
  return admin_users
}
