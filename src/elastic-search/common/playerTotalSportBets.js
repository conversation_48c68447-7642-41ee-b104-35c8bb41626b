import config from "../../configs/app.config"
/**
 *
 * this return player total bets
 *
 * @export
 * @param {object} param
 * @param {object} esClient
 * @returns {object} player total sports bet amount,total bets
 */
export default async function playerTotalSportBets (param, esClient) {
  try {
    const playerWalletBalance = await esClient.search({
      index: config.getProperties().es_index.users_index_name,
      body: {
        query: {
          bool: {
            must: [
              {
                match: { player_id: param.userId }
              }
            ]
          }
        }
      }
    })

    return {
      totalBetAmount: +playerWalletBalance.body.hits.hits[0]._source.total_sport_bet_amount,
      totalBets: +playerWalletBalance.body.hits.hits[0]._source.total_sport_bets
    }
  } catch (e) {
    throw new Error(e)
  }
}
