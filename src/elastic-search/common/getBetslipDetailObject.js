
/**
 *
 */
export default async (betSlipId, context, sequelizeTransaction) => {
  const {
    BetsBetslip: BetSlipModel,
    BetsBet: BetModel,
    PullsEvent: PullsEventModel,
    //PullsLeague: PullsLeagueModel,
    //PullsSport: PullsSportModel
  } = context
  let betSlipDetail = await BetSlipModel.findOne({
    where: { id: betSlipId },
    include: [{
      model: BetModel,
      as: 'bets',
      include: [{
        model: PullsEventModel,
        as: 'event',
        attributes:['start_date','name_en'],
        // include: [{
        //   model: PullsLeagueModel,
        //   as: 'league'
        // },
        // {
        //   model: PullsSportModel,
        //   as: 'sport',
        //   attributes:['name_en']
        // }]
      }],
    }],

    transaction:sequelizeTransaction
  })

  betSlipDetail = JSON.parse(JSON.stringify(betSlipDetail))
  return betSlipDetail
}
