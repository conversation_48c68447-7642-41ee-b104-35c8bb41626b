
/**
 *
 */
export default async (userId, sequelize) => {
  const {
    User,
    Wallet,
    Cur<PERSON>cy,
    Tenant,
    AdminUser,
    AdminUserSetting
  } = sequelize

  const userDetail = await User.findOne({
    where: {
      id: userId
    },
    include: [{
      model: Wallet,
      include: {
        model: Currency
      }
    },
    {
      model: Tenant
    },
    {
      model: AdminUser
    },
    {
      model: AdminUserSetting
    }]
  })

  return userDetail
}
