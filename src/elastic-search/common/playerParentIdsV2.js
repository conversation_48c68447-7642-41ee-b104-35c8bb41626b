/**
 *
 *
 * @export
 * @param {object} playerId
 * @param {object} sequelize
 * @returns
 */
export default async function playerParentIdsV2 (playerId, tenantId, sequelize) {

  const idsArr = await sequelize.query(
    `select * from get_user_and_admin_users(:playerId, :tenantId);`, {
    replacements: {
      playerId,
      tenantId
    },
    type: sequelize.QueryTypes.SELECT, useMaster: false
  })
  const result = Object.values(idsArr).filter((item) => {
    return item.user_type !== 'User'
  }).map((item) => {
    return item
  })
  return result
}
