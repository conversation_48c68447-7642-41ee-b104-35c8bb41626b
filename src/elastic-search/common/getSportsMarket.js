/**
 *
 *
 * @export
 * @param {object} dabitTransactionId
 * @param {object} sequelize
 * @returns {Promise<object[]>}
 */
export default async function getSportsMarket (dabitTransactionId, sequelize) {
  const market = await sequelize.query(
    `
    SELECT bets_bets.market
    FROM bets_transactions
    INNER JOIN bets_bets
      ON bets_bets.market_id = bets_transactions.market_id
    WHERE bets_transactions.id = :dabitTransactionId limit 1

      `, {

    replacements: { dabitTransactionId },
    type: sequelize.QueryTypes.SELECT,
    useMaster: false
  })
  return  market[0] || null;
}
