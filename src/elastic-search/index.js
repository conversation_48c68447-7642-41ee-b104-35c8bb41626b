import { Client } from '@elastic/elasticsearch'
import config from '../configs/app.config'
import addDummyTransaction from './services/addDummyTransaction'
import addPlayer from './services/addPlayer'
import addSportBetTransaction from './services/addSportBetTransaction'
import addTransaction from './services/addTransaction'
import updatePlayer from './services/updatePlayer'
import updatePlayerInTransaction from './services/updatePlayersInTransaction'
import updateSportTransaction from './services/updateSportTransaction'
import updateTransaction from './services/updateTransaction'
import updateWalletInUserDevelopmentIndex from './services/updateUserWalletIndex'
import addTransactionNew from './services/addTransactionNew'
import addPlayerNew from './services/addPlayerNew'
import addAuditLog from './services/addAuditLog'
import deleteDepositBonusTransaction from './services/deleteDepositBonusTransaction'

function getESclient () {
  const elasticUrl = config.get('elastic.url') + ':' + config.get('elastic.port') || 'http://elasticsearch:9200'
  const protocol = config.get('elastic.protocal')
  const esClient = new Client({ node: protocol + config.get('elastic.user') + ':' + config.get('elastic.password') + '@' + elasticUrl })
  return esClient;
}


/**
 *
 * Use to create document in Transaction index
 * @export
 * @param {object} params it contain inserted transaction values
 * @param {object} sequelize
 * @returns {object} it'll return elastic-search inserted transaction
 */
export async function updateTransactionIndex (txnId, sequelize) {
  try {
    let esClient = getESclient();
    // const params = await sequelize.models.Transaction.findOne({
    //   where: {
    //     id: txnId
    //   },
    //   useMaster: true
    // })
    //const transactionAdded = await addTransaction(params, sequelize, esClient)
    const transactionAdded = await addTransactionNew(txnId, sequelize, esClient)
    return transactionAdded
  } catch (e) {
    throw new Error(e)
  }
}

/**
 *
 * Use to create document in sports Transaction index
 * @export
 * @param {object} params it contain inserted transaction values
 * @param {object} sequelize
 * @returns {object} it'll return elastic-search inserted transaction
 */
export async function updateSportsBetTransactionIndex (txnId, sequelize) {
  try {
    let esClient = getESclient();
    const params = await sequelize.models.BetsTransaction.findOne({
      where: {
        id: txnId
      },
      useMaster: true
    })
    const transactionAdded = await addSportBetTransaction(params, sequelize, esClient)
    return transactionAdded
  } catch (e) {
    throw new Error(e)
  }
}

/**
 *
 * Use to create document in User index
 * @export
 * @param {object} params it contain inserted user values
 * @param {object} sequelize
 * @returns {object} it'll return elastic-search inserted player
 */
export async function updateUserIndex (params, sequelize) {
  try {
    let esClient = getESclient();
    const playerAdded = await addPlayer(params, sequelize, esClient)
    return playerAdded
  } catch (e) {
    throw new Error(e)
  }
}

export async function signupUserIndex (userId, sequelize) {
  try {
    let esClient = getESclient();
    const params = {
        id: userId
      }
    //const playerAdded = await addPlayer(params, sequelize, esClient)
    const playerAdded = await addPlayerNew(params, sequelize, esClient)
    return playerAdded
  } catch (e) {
    throw new Error(e)
  }
}
/**
 *
 * Use to update document in User index
 * @export
 * @param {object} params it contain updater player values
 * @returns {object} it'll return updated elastic-search player
 */
export async function updateUserDocument (params) {
  try {
    let esClient = getESclient();
    const sourceObj = `ctx._source["user_name"] = "${params.dataValues.userName}"; ctx._source["nick_name"] = "${params.dataValues.nickName}"`
    const playerUpdated = await updatePlayer(params.dataValues, sourceObj, esClient)
    return playerUpdated
  } catch (e) {
    throw new Error(e)
  }
}

/**
 *
 *
 * @export
 * @param {object} params it contain updated transaction values
 * @returns {object} it'll return updated elastic search transaction
 */
export async function updateTransactionDocument (params) {
  try {
    let esClient = getESclient();
    const sourceObj = `ctx._source.actionee = ["user_name": "${params.dataValues.userName}", "first_name": "${params.dataValues.firstName}","last_name" : "${params.dataValues.lastName}","active" : "${params.dataValues.active}","email" : "${params.dataValues.email}","country_code" : "${params.dataValues.countryCode}"]`
    const transactionUpdated = await updatePlayerInTransaction(params.dataValues, sourceObj, esClient)
    return transactionUpdated
  } catch (e) {
    throw new Error(e)
  }
}

/**
 *
 * Use to update document in User index
 * @export
 * @param {object} params it contain updater player values
 * @returns {object} it'll return updated elastic-search player
 */
export async function updateUserLastLogin (params) {
  try {
    let esClient = getESclient();
    const sourceObj = `ctx._source["last_login"] =  "${new Date(params.dataValues.lastLoginDate).toISOString()}"`
    const playerUpdated = await updatePlayer(params.dataValues, sourceObj, esClient)
    return playerUpdated
  } catch (e) {
    throw new Error(e)
  }
}

/**
 *
 * Use to add dummy document in Transaction index
 * @export
 * @param {object} params it contain updater user values
 * @returns {object} it'll return updated elastic-search player
 */
export async function addDummyTransactionDocument (params, sequelize) {
  try {
    let esClient = getESclient();
    const transactionAdded = await addDummyTransaction(params, sequelize, esClient)
    return transactionAdded
  } catch (e) {
    throw new Error(e)
  }
}

/**
 *
 * Use to update deposit bonus detail in Transaction index
 * @export
 * @param {object} params it contain updater transaction values
 * @returns {object} it'll return updated elastic-search player
 */
export async function updateDepositBonusInTransaction (params) {
  try {
    let esClient = getESclient();
    params.dataValues.id = +params.dataValues.transactionId
    const sourceObj = `ctx._source.player_details.deposit_bonus_details.active_deposit_bonus_remaining_rollover=${params.dataValues.rolloverBalance}`
    const transactionUpdated = await updateTransaction(params.dataValues, sourceObj, esClient)
    return transactionUpdated
  } catch (e) {
    throw new Error(e)
  }
}

/**
 *
 * Use to update status of document in Transaction index
 * @export
 * @param {object} params it contain updater transaction values
 * @returns {object} it'll return updated elastic-search player
 */
export async function updateTransactionStatus (params, userActiveDepositBonus) {
  try {
    let esClient = getESclient();
    const sourceObj = `ctx._source["status"] = "${params.dataValues.status}"; ctx._source["description"] = "${params.dataValues.comments}";  ctx._source.player_details.amount = ${userActiveDepositBonus.dataValues.bonusAmount}; ctx._source.player_details.added_amount = ${userActiveDepositBonus.dataValues.bonusAmount}; ctx._source.player_details.before_balance = ${params.dataValues.targetBeforeBalance}; ctx._source.player_details.after_balance = ${params.dataValues.targetAfterBalance}; ctx._source.player_details.deposit_bonus_details.active_deposit_bonus_remaining_rollover=${userActiveDepositBonus.dataValues.rolloverBalance}`
    const transactionUpdated = await updateTransaction(params.dataValues, sourceObj, esClient)
    return transactionUpdated
  } catch (e) {
    throw new Error(e)
  }
}

/**
 *
 * Use to update bonus code of document in Transaction index
 * @export
 * @param {object} params it contain updater transaction values
 * @returns {object} it'll return updated elastic-search player
 */
export async function updateBonusCodeInTransaction (transaction, bonusCode) {
  try {
    let esClient = getESclient();
    const sourceObj = `ctx._source["bonus_id"] = "${bonusCode}"`
    const transactionUpdated = await updateTransaction(transaction, sourceObj, esClient)
    return transactionUpdated
  } catch (e) {
    throw new Error(e)
  }
}

/**
 *
 * Use to update deposit bonus detail in sports bet Transaction index
 * @export
 * @param {object} params it contain updater transaction values
 * @returns {object} it'll return updated elastic-search player
 */
export async function updateDepositBonusInSportTransaction (params) {
  try {
    let esClient = getESclient();
    params.dataValues.id = +params.dataValues.transactionId
    const sourceObj = `ctx._source.player_details.deposit_bonus_details.active_deposit_bonus_remaining_rollover=${params.dataValues.rolloverBalance}`
    const transactionUpdated = await updateSportTransaction(params.dataValues, sourceObj, esClient)
    return transactionUpdated
  } catch (e) {
    throw new Error(e)
  }
}

/**
 *
 * Use to update status of document in sports bet Transaction index
 * @export
 * @param {object} params it contain updater transaction values
 * @returns {object} it'll return updated elastic-search player
 */
export async function updateSportTransactionStatus (params, userActiveDepositBonus) {
  try {
    let esClient = getESclient();
    const sourceObj = `ctx._source["status"] = "${params.dataValues.status}"; ctx._source["description"] = "${params.dataValues.description}";  ctx._source.player_details.amount = ${userActiveDepositBonus.dataValues.bonusAmount}; ctx._source.player_details.added_amount = ${userActiveDepositBonus.dataValues.bonusAmount}; ctx._source.player_details.before_balance = ${params.dataValues.currentBalance}; ctx._source.player_details.after_balance = ${(+params.dataValues.currentBalance) + (+params.dataValues.amount)}; ctx._source["payment_for"] = ${params.paymentFor}; ctx._source.player_details.deposit_bonus_details.active_deposit_bonus_remaining_rollover=${userActiveDepositBonus.dataValues.rolloverBalance}`
    const transactionUpdated = await updateSportTransaction(params.dataValues, sourceObj, esClient)
    return transactionUpdated
  } catch (e) {
    throw new Error(e)
  }
}

/**
 *
 * Use to update deposit bonus detail in bet Transaction index
 * @export
 * @param {object} params it contain updater transaction values
 * @returns {object} it'll return updated elastic-search player
 */
export async function updateDepositBonusInSportsTransaction (params) {
  try {
    let esClient = getESclient();
    params.dataValues.id = +params.dataValues.transactionId
    const sourceObj = `ctx._source.player_details.deposit_bonus_details.active_deposit_bonus_remaining_rollover=${params.dataValues.rolloverBalance}`
    const transactionUpdated = await updateSportTransaction(params.dataValues, sourceObj, esClient)
    return transactionUpdated
  } catch (e) {
    throw new Error(e)
  }
}

/**
 * Use to update wallet in user index
 * @export
 * @param {object} params it contain updated wallet values
 * @param {object} sequelize
 * @returns {object} it'll return elastic-search updated wallet
 */
export async function updateWalletInUserIndex (params, sequelize) {
  try {
    let esClient = getESclient();
    const sourceObj = `ctx._source["real_balance"] = "${params.amount}"; ctx._source["non_cash_balance"] = "${params.nonCashAmount}"; ctx._source["total_balance"] = "${(+params.nonCashAmount) + (+params.amount)}";`
    const userDevelopmentUpdated = await updateWalletInUserDevelopmentIndex(params, sourceObj, esClient)
    return userDevelopmentUpdated
  } catch (e) {
    throw new Error(e)
  }
}

// Audit log function
export async function updateAuditLog (logId, sequelize) {
  try {
    let esClient = getESclient();
    const params = await sequelize.models.AuditLog.findOne({
      where: {
        id: logId
      },
      useMaster: true
    })
    const auditLogAdded = await addAuditLog(params, sequelize, esClient)
    return auditLogAdded
  } catch (e) {
    throw new Error(e)
  }
}


/**
 * Deletes a deposit bonus transaction from Elasticsearch.
 * @export
 * @async
 * @param {string} transactionId - The ID of the transaction to be deleted.
 * @param {object} sequelize - The Sequelize instance for database operations.
 * @throws {Error} - Throws an error if the transaction deletion or logging operation fails.
 */

// Remove transaction from elastic db function
export async function deleteDepositBonusTranx (params, sequelize) {
  try {
    let esClient = getESclient();
    const result = await deleteDepositBonusTransaction(params, sequelize, esClient)
    return result
  } catch (e) {
    throw new Error(e)
  }
}
