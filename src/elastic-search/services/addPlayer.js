import playerParentIds from '../common/playerParentIds'
import config from '../../configs/app.config'

/**
 *
 *
 * @export
 * @param {*} params
 * @param {*} sequelize
 * @returns
 */
export default async function addPlayer (params, sequelize, esClient) {
  const { User, Wallet, Currency, AdminUser } = sequelize.models
  const post = params.dataValues
  const active = post.active ? 'Active' : 'Inactive'

  const playerWallet = await User.findOne({
    where: {
      id: post.id
    },
    include: [{
      model: Wallet,
      include: {
        model: Currency
      }
    }, {
      model: AdminUser,
      where: {
        id: post.parentId
      }
    }],
    useMaster: true
  })

  const playerParentIdArr = await playerParentIds(post.id, playerWallet.dataValues.tenantId, sequelize)
  const parentChainIds = await playerParentIdArr.map(item => +item.id)
  try {
    const player = await esClient.index({
      index: config.getProperties().es_index.users_index_name,
      id: +post.id,
      body: {
        player_id: +post.id,
        player_id_s: post.id.toString(),
        vip_level: post.vipLevel,
        nick_name: post.nickName,
        user_name: post.userName,
        agent_full_name: `${playerWallet.AdminUser?.dataValues.firstName} ${playerWallet.AdminUser.dataValues.lastName}`,
        agent_name: playerWallet?.AdminUser?.dataValues?.agentName,
        last_login: post?.lastLoginDate,
        country: post?.countryCode,
        creation_date: post?.createdAt,
        real_balance: playerWallet?.Wallet?.dataValues?.amount,
        non_cash_balance: playerWallet?.Wallet?.dataValues?.nonCashAmount,
        total_balance: playerWallet?.Wallet?.dataValues?.amount + playerWallet?.Wallet?.dataValues?.nonCashAmount,
        currency: playerWallet?.Wallet?.dataValues?.Currency?.dataValues?.code,
        wallet_id: +playerWallet?.Wallet?.dataValues?.id,
        total_bets: 0,
        total_bet_amount: 0,
        total_sport_bets: 0,
        total_sport_bet_amount: 0,
        parent_id: +post.parentId,
        parent_chain_ids: parentChainIds,
        status: active,
        tenant_id: +post.tenantId,
        phone: post.phone ? post.phone : '',
        email: post.email ? post.email : '',
        first_deposit_amount: 0,
        first_deposit_amount_transaction_id: 0,
        phone_code: post.phoneCode ? post.phoneCode : null,
        demo: post.Demo ? "true" : "false",
        kyc_done: post.kycDone ? "true" : "false",
        parent_type: post.parentType,
        category_type: +playerWallet.category_type
      },
      refresh: 'wait_for'
    })
    return player
  } catch (e) {
    throw new Error(e)
  }
}
