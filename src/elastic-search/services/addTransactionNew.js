import { Op, QueryTypes, Sequelize } from 'sequelize'
import {
  ALLOWED_PERMISSIONS, BONUS_TYPES,
  CASINO_PROVIDERS_PROD,
  CASINO_PROVIDERS_STAG,
  PROD_ALLOWED_TENANTS_SMARTICO, QUEUE_WORKER_CONSTANT,
  SPORTS_PROVIDERS_PROD,
  SPORTS_PROVIDERS_STAG,
  SPORT_PROVIDER,
  ST8_PROVIDER_ID_PROD, ST8_PROVIDER_ID_STAGE, STAG_ALLOWED_TENANTS_SMARTICO,
  TRANSACTION_HISTORY_LOG_TYPE
} from '../../common/constants'
import getProviderName from '../../common/getProviderName'
import { publishToRedis } from '../../common/queueService/publishToRedis'
import config from '../../configs/app.config'
import getAdminUserHierarchy from '../common/getAdminUserHierarchy'
import getSportsMarket from '../common/getSportsMarket'
import playerParentIds from '../common/playerParentIds'

/**
 * insert transaction in elastic-search DB
 *
 * @export
 * @param {object} params it contains inserted transaction values
 * @param {object} sequelize
 * @returns {object} it'll return elastic-search inserted transaction
 */
async function getTransactionTypeString (transactionType) {
  let str = ''
  switch (transactionType) {
    case 0:
      str = 'bet'
      break
    case 1:
      str = 'win'
      break
    case 2:
      str = 'refund'
      break
    case 3:
      str = 'deposit'
      break
    case 4:
      str = 'withdraw'
      break
    case 5:
      str = 'non_cash_granted_by_admin'
      break
    case 6:
      str = 'non_cash_withdraw_by_admin'
      break
    case 7:
      str = 'tip'
      break
    case 8:
      str = 'bet_non_cash'
      break
    case 9:
      str = 'win_non_cash'
      break
    case 10:
      str = 'refund_non_cash'
      break
    case 11:
      str = 'non_cash_bonus_claim'
      break
    case 12:
      str = 'deposit_bonus_claim'
      break
    case 13:
      str = 'tip_non_cash'
      break
    case 14:
      str = 'withdraw_cancel'
      break
    case 15:
      str = 'joining_bonus_claimed'
      break
    case 16:
      str = 'failed'
      break
    case 17:
      str = 'promo_code_bonus_claimed'
      break
    case 19:
      str = 'commission_received'
      break
    case 20:
      str = 'exchange_place_bet_non_cash_debit'
      break
    case 21:
      str = 'exchange_place_bet_cash_debit'
      break
    case 22:
      str = 'exchange_place_bet_cash_credit'
      break
    case 23:
      str = 'exchange_refund_cancel_bet_non_cash_debit'
      break
    case 24:
      str = 'exchange_refund_cancel_bet_cash_debit'
      break
    case 25:
      str = 'exchange_refund_cancel_bet_non_cash_credit'
      break
    case 26:
      str = 'exchange_refund_cancel_bet_cash_credit'
      break
    case 27:
      str = 'exchange_refund_market_cancel_non_cash_debit'
      break
    case 28:
      str = 'exchange_refund_market_cancel_cash_debit'
      break
    case 29:
      str = 'exchange_refund_market_cancel_non_cash_credit'
      break
    case 30:
      str = 'exchange_refund_market_cancel_cash_credit'
      break
    case 31:
      str = 'exchange_settle_market_cash_credit'
      break
    case 32:
      str = 'exchange_settle_market_cash_debit'
      break
    case 33:
      str = 'exchange_resettle_market_cash_credit'
      break
    case 34:
      str = 'exchange_resettle_market_cash_debit'
      break
    case 35:
      str = 'exchange_cancel_settled_market_cash_credit'
      break
    case 36:
      str = 'exchange_cancel_settled_market_cash_debit'
      break
    case 37:
      str = 'exchange_deposit_bonus_claim'
      break
    case 38:
      str = 'withdraw_rejected_by_admin'
      break
    case 39:
      str = 'burning_losing_bonus'
      break
    case 40:
      str = 'burning_joining_bonus'
      break
    case 41:
      str = 'player_bulk_categorization_bonus'
      break
    case 42:
      str = 'burning_deposit_bonus'
      break
    case 43:
      str = 'burning_non_cash_bonus_amount'
      break
    case 44:
      str = 'royalty_non_cash_bonus'
      break
    case 45:
      str = 'royalty_cash_bonus'
      break
    case 46:
      str = 'bet_one_time_bonus'
      break
    case 47:
      str = 'refund_one_time_bonus'
      break
    case 48:
      str = 'one_time_bonus_deposit'
      break
    case 49:
      str = 'one_time_bonus_withdraw'
      break
    case 50:
      str = 'burning_promo_code_bonus'
      break
    case 51:
      str = 'burning_manual_losing_bonus'
      break
    case 53:
      str = 'exchange_adjust_settled_market_cash_credit'
      break
    case 54:
      str = 'exchange_adjust_settled_market_cash_debit'
      break
    case 55:
      str = 'exchange_cancel_settled_bet_non_cash_debit'
      break
    case 56:
      str = 'exchange_cancel_settled_bet_cash_debit'
      break
    case 57:
      str = 'exchange_cancel_settled_bet_non_cash_credit'
      break
    case 58:
      str = 'exchange_cancel_settled_bet_cash_credit'
      break
    case 53:
      str = 'exchange_adjust_settled_market_cash_credit'
      break
    case 54:
      str = 'exchange_adjust_settled_market_cash_debit'
      break
    case 55:
      str = 'exchange_cancel_settled_bet_non_cash_debit'
      break
    case 56:
      str = 'exchange_cancel_settled_bet_cash_debit'
      break
    case 57:
      str = 'exchange_cancel_settled_bet_non_cash_credit'
      break
    case 58:
      str = 'exchange_cancel_settled_bet_cash_credit'
      break
    default:
      str = 'na'
      break
  }
  return str
}
async function getTransaction (id, sequelize) {
  const {
    Wallet,
    Currency,
    Transaction,
    TransactionReceipt
  } = sequelize.models

  const st8ProviderId = config.get('env') === 'development' ? ST8_PROVIDER_ID_STAGE : ST8_PROVIDER_ID_PROD
  const transaction = await Transaction.findOne({
    where: {
      id: id
    },
    attributes: [
      'id', 'tenant_id', 'actionee_id', 'actionee_type', 'source_wallet_id', 'target_wallet_id', 'target_currency_id',
      'source_currency_id', 'transaction_type', 'transaction_id', 'amount', 'conversion_rate', 'status', 'comments', 'created_at',
      'source_after_balance', 'source_before_balance', 'target_after_balance', 'target_before_balance', 'other_currency_amount',
      'table_id', 'round_id', 'error_code', 'error_description', 'payment_method', 'game_id', 'payment_provider_id', 'provider_id', 'seat_id', 'debit_transaction_id', 'meta_data', 'credit_index',
      [sequelize.col('sw.owner_type'), 'source_owner_type'],
      [sequelize.col('tw.owner_type'), 'target_owner_type'],
      [sequelize.col('sw.owner_id'), 'source_owner_id'],
      [sequelize.col('tw.owner_id'), 'target_owner_id'],
      [sequelize.col('sc.code'), 'source_currency_code'],
      [sequelize.col('tc.code'), 'target_currency_code'],
      [sequelize.col('tr.receipt_url'), 'receipt_url'],
      [Sequelize.literal(
        `CASE
          WHEN
            "Transaction".provider_id = ${st8ProviderId} THEN
            (
              SELECT title from get_provider_name("Transaction".seat_id, "Transaction".tenant_id)
            )
            ELSE ''
          END
        `
      ), 'custom3']
    ],
    include: [
      {
        model: Wallet,
        as: 'sw',
        attributes: []
      },
      {
        model: Wallet,
        as: 'tw',
        attributes: []
      },
      {
        model: Currency,
        as: 'tc',
        attributes: []
      },
      {
        model: Currency,
        as: 'sc',
        attributes: []
      },
      {
        model: TransactionReceipt,
        as: 'tr',
        attributes: []
      }
    ],
    raw: true,
    useMaster: true
  })
  return transaction
}
async function getActioneeInfo (actionee_type, actionee_id, tenant_id, sequelize) {
  const {
    AdminUser,
    User,
    SuperAdminUser
  } = sequelize.models
  let actioneeInfo
  if (actionee_type === 'AdminUser') {
    actioneeInfo = await AdminUser.findOne({
      where: {
        id: actionee_id
      },
      attributes: ['first_name', 'last_name', 'email']
    })
  } else if (actionee_type === 'User') {
    actioneeInfo = await User.findOne({
      where: {
        id: actionee_id
      },
      attributes: ['id', 'first_name', 'last_name', 'email', 'user_name']
    })
  } else if (actionee_type === 'SuperAdminUser') {
    actioneeInfo = await SuperAdminUser.findOne({
      where: {
        id: actionee_id
      },
      attributes: ['id', 'first_name', 'last_name', 'email']
    })
  } else {
    return []
  }
  const ownerParentIdArr = await playerParentIds(actionee_id, tenant_id, sequelize)
  const parentChainIds = []
  const parentChainIdsValues = ownerParentIdArr.reduce((currentObj, parent) => {
    parentChainIds.push(+parent.id)
    currentObj[+parent.id] = parent
    return currentObj
  }, {})
  actioneeInfo.dataValues.type = actionee_type
  if (parentChainIds.length > 0) {
    actioneeInfo.dataValues.parent_chain_ids = parentChainIds
  } else {
    if (actionee_type == 'AdminUser') {
      actioneeInfo.dataValues.user_name = null
      actioneeInfo.dataValues.parent_chain_ids = [actionee_id]
    } else {
      actioneeInfo.dataValues.parent_chain_ids = []
    }
  }
  actioneeInfo.dataValues.id = Number(actioneeInfo.dataValues.id)
  return actioneeInfo
}
async function walletOwnerInfo (wallet_id, tenant_id, sequelize) {
  const [wallet_info] = await sequelize.query(`
  select w.id, w.owner_type as type, w.owner_id, COALESCE (u.email, au.email,su.email) as email, COALESCE (u.first_name,
  au.first_name,su.first_name) as first_name,
  COALESCE(u.last_name, au.last_name,su.last_name) as last_name, COALESCE(u.user_name, au.agent_name) as user_name from
  wallets as w left join users as u on w.owner_id = u.id and w.owner_type = 'User'
  left join admin_users as au on w.owner_id = au.id and w.owner_type = 'AdminUser'
  left join super_admin_users as su on w.owner_id = su.id and w.owner_type = 'SuperAdminUser' where w.id = ${wallet_id} limit 1;
    `,
  { type: Sequelize.QueryTypes.SELECT, useMaster: false }
  )
  if (wallet_info) {
    const ownerParentIdArr = await playerParentIds(wallet_info.owner_id, tenant_id, sequelize)
    const parentChainIds = []
    const parentChainIdsValues = ownerParentIdArr.reduce((currentObj, parent) => {
      parentChainIds.push(+parent.id)
      currentObj[+parent.id] = parent
      return currentObj
    }, {})

    if (parentChainIds.length > 0) {
      wallet_info.parent_chain_ids = parentChainIds
    } else {
      wallet_info.parent_chain_ids = [wallet_info.owner_id]
    }
  }
  return wallet_info
}
async function getUserInfo (player_id, tenant_id, sequelize) {
  const {
    Wallet,
    Currency,
    User,
    AdminUser
  } = sequelize.models

  const [user_info] = await sequelize.query(
    'SELECT * FROM get_user_data(:player_id, :tenant_id)',
    {
      replacements: { player_id, tenant_id },
      type: QueryTypes.SELECT
    }
  );


  user_info.currency_id = +user_info.currency_id
  user_info.parent_id = +user_info.parent_id
  user_info.tenant_id = +user_info.tenant_id
  user_info.wallet_id = +user_info.wallet_id
  return user_info
}
async function getPlayerDetails (player_id, tenant_id, sequelize) {
  const {
    AdminUserSetting
  } = sequelize.models
  const parentInfo = []
  const userInfo = await getUserInfo(player_id, tenant_id, sequelize)
  const commission = '000.00'

  if (userInfo) {
    const ownerParentIdArr = await playerParentIds(player_id, tenant_id, sequelize)
    const parentChainIds = []
    const parentChainIdsValues = ownerParentIdArr.reduce((currentObj, parent) => {
      parentChainIds.push(+parent.id)
      currentObj[+parent.id] = parent
      return currentObj
    }, {})
    userInfo.parent_chain_ids = parentChainIds
    userInfo.player_id = +player_id
    userInfo.player_id_s = String(player_id)
    userInfo.player_name = userInfo.user_name
    userInfo.demo = false
    userInfo.agent_full_name = userInfo.agent_first_name + ' ' + userInfo.agent_last_name
    const AdminUserCommissions = await AdminUserSetting.findAll({ where: { adminUserId: { [Op.in]: parentChainIds }, key: 'commission_percentage' }, raw: true })
    const reducedAdminCommission = AdminUserCommissions.reduce((currentObj, adminCommission) => {
      currentObj[+adminCommission.adminUserId] = adminCommission
      return currentObj
    }, {})

    const adminCommissionPercentage = []
    for (const [key, value] of Object.entries(parentChainIdsValues)) {
      let commissionPercentage = '000.00'
      if (reducedAdminCommission[key]) {
        const percentageStr = reducedAdminCommission[key].value.split('.')
        commissionPercentage = !percentageStr[1] ? `${percentageStr[0].padStart(3, '0')}.00` : `${percentageStr[0].padStart(3, '0')}.${percentageStr[1].padEnd(2, '0')}`
        adminCommissionPercentage.push(`${key}-${value.agent_name}-${userInfo.currency}-${commissionPercentage}`)
      } else {
        adminCommissionPercentage.push(`${key}-${value.agent_name}-${userInfo.currency}-${commissionPercentage}`)
      }
    }
    userInfo.parent_chain_detailed = adminCommissionPercentage
  }
  return userInfo
}
async function getAdminParentDetails (param, sequelize) {
  if (!param) {
    return []
  }

  const parent_chain_ids = await getAdminUserHierarchy(param.id, 'AdminUser', param.tenant_id, sequelize)

  const parentChainIds = []
  const parentChainIdsValues = parent_chain_ids.reduce((currentObj, parent) => {
    parentChainIds.push(+parent.id)
    currentObj[+parent.id] = parent
    return currentObj
  }, {})
  return parentChainIds
}

export default async function addTransactionNew (params, sequelize, esClient) {
  const {
    User,
    Wallet,
    Currency,
    Tenant,
    AdminUser,
    CasinoGame,
    CasinoProvider,
    AdminUserSetting,
    UserBonus,
    Transaction,
    QueueLog,
    CasinoItem,
    CasinoTable,
    TenantThemeSetting,
    TransactionHistoryLog,
    TransactionsMetaData
  } = sequelize.models

  // const post = params.dataValues
  // const transactionType = post.transactionType

  const txnInfo = await getTransaction(params, sequelize)
  if (!txnInfo) {
    throw new Error('Transaction not found')
  }
  const transactionType = txnInfo.transaction_type

  let actionee = []
  if (txnInfo.actionee_type) {
    actionee = await getActioneeInfo(txnInfo.actionee_type, txnInfo.actionee_id, txnInfo.tenant_id, sequelize)
  }

  let source_wallet = []
  let target_wallet = []
  let player_details = null
  const gameProvider = []
  let playerIp = ''
  let currenct_balance
  if (txnInfo.source_wallet_id) {
    source_wallet = await walletOwnerInfo(txnInfo.source_wallet_id, txnInfo.tenant_id, sequelize)
    if (source_wallet.type == 'User') {
      player_details = await getPlayerDetails(source_wallet.owner_id, txnInfo.tenant_id, sequelize)
      player_details.before_balance = String(txnInfo.source_before_balance)
      player_details.after_balance = String(txnInfo.source_after_balance)
      currenct_balance = parseFloat(txnInfo.source_after_balance)
    }
    source_wallet.id = Number(source_wallet.owner_id)
    source_wallet.before_balance = String(txnInfo.source_before_balance)
    source_wallet.after_balance = String(txnInfo.source_after_balance)
    delete source_wallet.owner_id
  }

  if (txnInfo.target_wallet_id) {
    target_wallet = await walletOwnerInfo(txnInfo.target_wallet_id, txnInfo.tenant_id, sequelize)
    if (target_wallet) {
      if (target_wallet.type == 'User') {
        player_details = await getPlayerDetails(target_wallet.owner_id, txnInfo.tenant_id, sequelize)
        player_details.before_balance = String(txnInfo.target_before_balance)
        player_details.after_balance = String(txnInfo.target_after_balance)
        currenct_balance = parseFloat(txnInfo.target_after_balance)
      }
      target_wallet.id = Number(target_wallet.owner_id)
      target_wallet.before_balance = String(txnInfo.target_before_balance)
      target_wallet.after_balance = String(txnInfo.target_after_balance)
      delete target_wallet.owner_id
    }
  }

  if (player_details) {
    player_details.self_exclusion = null
    player_details.active = player_details.active ? 'true' : 'false'
    player_details.demo = player_details.demo ? 'true' : 'false'
    player_details.amount = parseFloat(0)
    player_details.non_cash_amount = parseFloat(0)
    player_details.created_at = player_details.created_at ? player_details.created_at.toISOString().replace(/T/, ' ').replace(/\..+/, '') : null
    player_details.updated_at = player_details.updated_at ? player_details.updated_at.toISOString().replace(/T/, ' ').replace(/\..+/, '') : null
    player_details.date_of_birth = player_details.date_of_birth ? player_details.date_of_birth.toISOString().replace(/T/, ' ').replace(/\..+/, '') : null
    player_details.last_login_date = player_details.last_login_date ? player_details.last_login_date.toISOString().replace(/T/, ' ').replace(/\..+/, '') : null

    playerIp = player_details?.user_ip || ''
    // deleting due to issue in behavior tracking
    delete player_details?.user_ip

    const balance_difference = parseFloat(player_details.after_balance) - parseFloat(player_details.before_balance)
    player_details.revenue = balance_difference
    const userConditionArray = [
      'bet', 'win', 'refund', 'tip', 'deposit', 'withdraw', 'withdraw_cancel', 'deposit_bonus_claim',
      'exchange_place_bet_cash_debit', 'exchange_refund_cancel_bet_cash_debit', 'exchange_refund_market_cancel_cash_debit',
      'exchange_settle_market_cash_debit', 'exchange_resettle_market_cash_debit', 'exchange_cancel_settled_market_cash_debit',
      'exchange_place_bet_cash_credit', 'exchange_refund_cancel_bet_cash_credit', 'exchange_refund_market_cancel_cash_credit',
      'exchange_settle_market_cash_credit', 'exchange_resettle_market_cash_credit', 'exchange_cancel_settled_market_cash_credit',
      'exchange_deposit_bonus_claim', 'exchange_adjust_settled_market_cash_credit', 'exchange_adjust_settled_market_cash_debit',
      'exchange_cancel_settled_bet_non_cash_debit', 'exchange_cancel_settled_bet_cash_debit', 'exchange_cancel_settled_bet_non_cash_credit',
      'exchange_cancel_settled_bet_cash_credit'
    ]
    if (userConditionArray.includes(await getTransactionTypeString(txnInfo.transaction_type))) {
      player_details.amount = currenct_balance
      player_details.non_cash_amount = parseFloat(0)
    } else {
      player_details.amount = parseFloat(0)
      player_details.non_cash_amount = currenct_balance
    }

    const DEDUCTED_FROM_WALLET_TYPES = [
      'bet', 'bet_non_cash',
      'withdraw', 'tip',
      'tip_non_cash', 'joining_bonus_claimed',
      'exchange_place_bet_non_cash_debit', 'exchange_place_bet_cash_debit', 'exchange_refund_cancel_bet_cash_debit',
      'exchange_refund_cancel_bet_non_cash_debit', 'exchange_refund_market_cancel_non_cash_debit', 'exchange_refund_market_cancel_cash_debit',
      'exchange_settle_market_cash_debit', 'exchange_resettle_market_cash_debit', 'exchange_cancel_settled_market_cash_debit', 'bet_one_time_bonus',
      'exchange_adjust_settled_market_cash_debit', 'exchange_cancel_settled_bet_non_cash_debit', 'exchange_cancel_settled_bet_cash_debit'
    ]
    if (DEDUCTED_FROM_WALLET_TYPES.includes(await getTransactionTypeString(txnInfo.transaction_type))) {
      player_details.deducted_amount = parseFloat(txnInfo.amount)
    } else {
      player_details.deducted_amount = 0.0
    }

    const ADDED_TO_WALLET_TYPES = [
      'win', 'win_non_cash',
      'refund', 'refund_non_cash',
      'withdraw_cancel',
      'deposit', 'deposit_bonus_claim', 'joining_bonus_claimed',
      'exchange_place_bet_cash_credit', 'exchange_refund_cancel_bet_non_cash_credit', 'exchange_refund_market_cancel_non_cash_credit',
      'exchange_refund_cancel_bet_cash_credit', 'exchange_refund_market_cancel_cash_credit', 'exchange_settle_market_cash_credit',
      'exchange_resettle_market_cash_credit', 'exchange_cancel_settled_market_cash_credit', 'exchange_deposit_bonus_claim',
      'exchange_adjust_settled_market_cash_credit', 'exchange_cancel_settled_bet_non_cash_credit', 'exchange_cancel_settled_bet_cash_credit'
    ]
    if (ADDED_TO_WALLET_TYPES.includes(await getTransactionTypeString(txnInfo.transaction_type))) {
      player_details.added_amount = parseFloat(txnInfo.amount)
    } else {
      player_details.added_amount = 0.0
    }

    if (txnInfo.transaction_type == 3 || txnInfo.transaction_type == 12) {
      if (txnInfo.transaction_type == 12) {
        const userActiveDepositBonus = await UserBonus.findOne({
          where: {
            kind: BONUS_TYPES.DEPOSIT,
            userId: txnInfo.actionee_id
          },
          raw: true,
          useMaster: true
        })
        if (userActiveDepositBonus) {
          player_details.deposit_bonus_details = {
            unconverted_active_deposit_bonus: parseFloat(userActiveDepositBonus.bonusAmount),
            active_deposit_bonus_remaining_rollover: parseFloat(userActiveDepositBonus.rolloverBalance)
          }
        }
      }
    }
  }

  // this return casino game and associated provider of casino
  let casinoDetail
  let providerName
  let roundId
  if (txnInfo.game_id) {
    casinoDetail = await CasinoGame.findOne({
      where: {
        gameId: txnInfo.game_id.toString(),
        casinoProviderId: txnInfo.provider_id
      },
      raw: true,
      include: [{
        model: CasinoProvider
      }]
    })
    if (casinoDetail) {
      providerName = casinoDetail['CasinoProvider.name']
    } else {
      if (txnInfo.provider_id) {
        providerName = await getProviderName(txnInfo.provider_id)
      }
    }
  }
  if (providerName === 'st8' || providerName === 'pgsoft' || providerName === 'Darwin') {
    const st8GameName = await CasinoItem.findOne({
      attributes: ['name', 'uuid'],
      where: {
        tenantId: txnInfo.tenant_id,
        uuid: txnInfo.seat_id,
        provider: '' + txnInfo.provider_id
      },
      raw: true
    })
    if (st8GameName?.name && casinoDetail) {
      // if(['sbs_sportsbook','bti_sportsbook','sap_lobby'].includes(txnInfo.seat_id)){
      //   const data = txnInfo?.meta_data?.provider?.metadata?.selections[0]
      //   casinoDetail.name =
      //   `${data?.sport_name}->${data?.tournament_name}->${data?.competitor_name[0]} v ${data?.competitor_name[1]}->${data?.market_name}`
      // }else
      casinoDetail.name = st8GameName.name
      casinoDetail.uuid = (st8GameName?.uuid || '')
    }
    roundId = -9999
  } else {
    roundId = txnInfo.round_id ? parseInt(txnInfo.round_id) : null
  }
  let gameProviderName
  let gameType
  let game_uuid_name
  gameProviderName = providerName || null
  gameType = casinoDetail ? casinoDetail.name : null
  game_uuid_name = casinoDetail ? casinoDetail?.uuid : null
  if (txnInfo.game_id === SPORT_PROVIDER.JETFAIR) {
    const marketName = await getSportsMarket(txnInfo.debit_transaction_id, sequelize);
    gameProviderName = SPORT_PROVIDER.JETFAIR
    gameType = (marketName)? marketName.market : txnInfo.seat_id
  }
  if (txnInfo.game_id === SPORT_PROVIDER.POWERPLAY) {
    const marketName = await getSportsMarket(txnInfo.debit_transaction_id, sequelize);
    gameProviderName = SPORT_PROVIDER.POWERPLAY
    gameType = (marketName)? marketName.market : txnInfo.seat_id
  }
  if (txnInfo.game_id === SPORT_PROVIDER.TURBOSTARS) {
    const marketName = await getSportsMarket(txnInfo.debit_transaction_id, sequelize)
    gameProviderName = SPORT_PROVIDER.TURBOSTARS
    gameType = (marketName)? marketName.market : txnInfo.seat_id
  }
  let parentChainIds = []
  if (txnInfo.actionee_type == 'User') {
    const ownerParentIdArr = await playerParentIds(txnInfo.actionee_id, txnInfo.tenant_id, sequelize)
    const parentChainIdsValues = ownerParentIdArr.reduce((currentObj, parent) => {
      parentChainIds.push(+parent.id)
      currentObj[+parent.id] = parent
      return currentObj
    }, {})
  }
  if (txnInfo.actionee_type == 'AdminUser') {
    const param = { id: txnInfo.actionee_id, tenant_id: txnInfo.tenant_id }
    parentChainIds = await getAdminParentDetails(param, sequelize)
  }

  // let amountInCurrencies = null
  // if(txnInfo.other_currency_amount){
  //   amountInCurrencies = JSON.parse(txnInfo.other_currency_amount)
  //   for (let curData in amountInCurrencies) {
  //     if (amountInCurrencies.hasOwnProperty(curData) && amountInCurrencies[curData].includes(',')) {
  //         amountInCurrencies[curData] = amountInCurrencies[curData].replace(/,/g, '');
  //     }
  //   }
  // }
  let amountInCurrencies = null
  if(txnInfo.other_currency_amount){
    amountInCurrencies = JSON.parse(txnInfo.other_currency_amount)
    if ('chips' in amountInCurrencies) {
      amountInCurrencies.CHIPS = parseFloat(amountInCurrencies.chips)
    }
  }

  let tableName = '';
  const casinoItem = await CasinoItem.findOne({
      attributes: ['name'],
      where: {
          tenantId: txnInfo.tenant_id,
          uuid: txnInfo.table_id ? txnInfo.table_id.toString() : null,
          provider: txnInfo.provider_id ? txnInfo.provider_id.toString() : null
      },
      raw: true
  });
  const casinoTable = await CasinoTable.findOne({
    attributes: ['name'],
    where: {
      tableId: txnInfo.seat_id,
      providerId: txnInfo.provider_id ? txnInfo.provider_id.toString() : null
    },
    raw: true
  });
  if (providerName === 'Funky' || providerName === 'Lottery777') {
    gameType = casinoTable ? casinoTable.name : ''
  }
  tableName = casinoItem ? casinoItem.name : '';

  let game_uuid = ((providerName === 'st8' || providerName === 'pgsoft') || providerName === 'Darwin' ? game_uuid_name : '')

  let game_id = ''
  if(!game_uuid && !txnInfo?.table_id && providerName?.toLowerCase() == 'spribe'){
     game_id = (txnInfo.game_id || '')
  }

  //meta data code
  const txnMetaData = await TransactionsMetaData.findOne({
    where: {
      transactionId: txnInfo.id
    },
    raw: true
  })
  try {
    const queueProcessStatus = await sequelize.models.QueueProcessStatus.findOne({
      where: {
        service: 'create_ES_transactions',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      const transaction = await esClient.index({
        index: config.getProperties().es_index.transactions_index_name,
        id: +txnInfo.id,
        body: {
          internal_tracking_id: String(txnInfo.id),
          tenant_id: +txnInfo.tenant_id,
          transaction_id: txnInfo.transaction_id,
          actionee_id: +txnInfo.actionee_id,
          transaction_type: await getTransactionTypeString(txnInfo.transaction_type),
          amount: parseFloat(txnInfo.amount),
          created_at: txnInfo.created_at,
          source_currency: txnInfo.source_currency_code,
          target_currency: txnInfo.target_currency_code,
          actionee: actionee,
          source_wallet_owner: source_wallet || [],
          target_wallet_owner: target_wallet || [],
          conversion_rate: parseFloat(txnInfo.conversion_rate),
          status: txnInfo.status,
          description: txnInfo.comments,
          player_details: (player_details || null),
          round_id: roundId,
          round_id_s: txnInfo.round_id ? String(txnInfo.round_id) : '',
          internal_error_code: txnInfo.error_code,
          internal_error_description: txnInfo?.error_description,
          gp_error_code: null,
          game_provider: gameProviderName,
          game_type: gameType,
          table_id: txnInfo.table_id ? String(txnInfo.table_id) : (game_uuid || game_id),
          // type: (transactionType === 4 || transactionType === 14) ? 'financial' : 'game',
          type: 'financial',
          transfer_method: txnInfo.payment_method,
          amount_in_currencies: amountInCurrencies ? amountInCurrencies : [],
          payment_provider_id: txnInfo.payment_provider_id,
          bonus_id: null,
          agent_parent_chain_ids: parentChainIds,
          custom_1: txnInfo.debit_transaction_id,
          custom_2: txnInfo.receipt_url ? txnInfo.receipt_url : txnInfo.seat_id,
          custom_3: txnInfo.seat_id == 'bti_sportsbook' ? 'BTI Sportsbook' : (txnInfo.seat_id == 'sbs_sportsbook' ? 'Saba Sportsbook' : (txnInfo.seat_id == 'sap_lobby' ? 'Sap Exchange' : txnInfo.custom3)),
          custom_4: txnInfo?.credit_index || '',
          custom_5: playerIp,
          custom_6: player_details?.affiliated_data || '',
          custom_7: '',
          custom_8: '',
          custom_9: '',
          custom_10: '',
          checker_data: txnMetaData?.metaData?.checker_data ? JSON.stringify(txnMetaData?.metaData?.checker_data) : null,
          maker_data: txnMetaData?.metaData?.maker_data ? JSON.stringify(txnMetaData?.metaData?.maker_data) : null,
          commission_details: (txnInfo.game_id === SPORT_PROVIDER.JETFAIR || txnInfo.game_id === SPORT_PROVIDER.POWERPLAY) ? txnMetaData?.metaData : null,
          table_name: tableName || null,
        }
      })
    }


    if (player_details) {
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.USER_TRANSACTION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [player_details.player_id]
      }
      await QueueLog.create(queueLogObject)
    }

    // const tenantId = +txnInfo.tenant_id
    // const isDev = config.get('env') === 'development';
    // const casinoProviders = isDev ? CASINO_PROVIDERS_STAG : CASINO_PROVIDERS_PROD;
    // const sportsProviders = isDev ? SPORTS_PROVIDERS_STAG : SPORTS_PROVIDERS_PROD;

    // // Try to find provider name in casino first, if not found, check sports
    // const providerName = casinoProviders[+txnInfo?.provider_id] ?? sportsProviders[+txnInfo?.provider_id];
    // const allowedTenantSmartico = config.get('env') === 'development' ? STAG_ALLOWED_TENANTS_SMARTICO : PROD_ALLOWED_TENANTS_SMARTICO

    // const providers = await TenantThemeSetting.findOne({ attributes: ['allowedModules'], where: { tenantId } })
    // const sportsBetTypes = [21, 32] // include all sports provider place bet, settle market types

    // if (providers?.allowedModules && providers?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION)
    //   && sportsBetTypes.includes(txnInfo.transaction_type) && [SPORT_PROVIDER.JETFAIR, SPORT_PROVIDER.POWERPLAY, SPORT_PROVIDER.TURBOSTARS].includes(providerName)) {

    //   let transactionHistoryLogEntity = { transactionId: txnInfo.id, tenantId, userId: +txnInfo.actionee_id, type: TRANSACTION_HISTORY_LOG_TYPE.FRAUD_DETECTION };

    //   let isTransactionExists = await TransactionHistoryLog.findOne({ attributes: ['id'], where: transactionHistoryLogEntity })

    //   if (!isTransactionExists) {
    //     await TransactionHistoryLog.create(transactionHistoryLogEntity)

    //     const betPlacedQueueLogObject = {
    //       type: QUEUE_WORKER_CONSTANT.BET_PLACED,
    //       status: QUEUE_WORKER_CONSTANT.READY,
    //       ids: [txnInfo?.id],
    //       tenantId
    //     }
    //     await QueueLog.create(betPlacedQueueLogObject)
    //   }
    // }

    // if (allowedTenantSmartico.includes(tenantId) && [5, 6, 8, 9, 10, 12, 13, 15, 17, 39, 44, 48, 49, 51, 46, 47].includes(+transactionType)) {
    //   let walletId, userId
    //   if ([5, 9, 10, 11, 12, 15, 39, 51, 17, 48, 47, 44].includes(transactionType)) {
    //     walletId = txnInfo.target_wallet_id
    //   } else {
    //     walletId = txnInfo.source_wallet_id
    //   }
    //   if (walletId) {
    //     if (txnInfo.actionee_type === 'User') {
    //       userId = +txnInfo.actionee_id
    //     }
    //     else {
    //       const user = await Wallet.findOne({ where: { id: walletId, ownerType: 'User' }, attributes: ['ownerId'] })
    //       userId = user?.ownerId
    //     }
    //     let transactionHistoryLog = await TransactionHistoryLog.findOne({ where: { transactionId: txnInfo.id, tenantId, userId, type: TRANSACTION_HISTORY_LOG_TYPE.SMARTICO  } })
    //     if (!transactionHistoryLog && userId) {
    //       const smarticoQueueLogObject = {
    //         type: QUEUE_WORKER_CONSTANT.SMARTICO_WALLET_UPDATE,
    //         status: QUEUE_WORKER_CONSTANT.READY,
    //         ids: [+userId],
    //         tenantId
    //       }
    //       const smarticoQueueLog = await QueueLog.create(smarticoQueueLogObject)
    //       transactionHistoryLog = await TransactionHistoryLog.create({ transactionId: txnInfo.id, tenantId: tenantId, userId: userId, type: TRANSACTION_HISTORY_LOG_TYPE.SMARTICO })
    //       try {
    //         await publishToRedis.publishToQueueService({
    //           QueueLog: { queueLogId: smarticoQueueLog.id }
    //         })
    //       }
    //       catch (error) {
    //         console.log(error)
    //       }
    //     }
    //   }
    // }
    return true
  } catch (e) {
    throw new Error(e)
  }
}
