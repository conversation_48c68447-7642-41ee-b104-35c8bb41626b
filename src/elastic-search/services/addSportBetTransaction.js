import { Op } from 'sequelize'
//import { paymentForCodes } from '../../betting-integration/common/constant'
import { BONUS_STATUS, BONUS_TYPES, JETFAIR_INTEGRATION_CONSTANT, QUEUE_WORKER_CONSTANT, paymentForCodes } from '../../common/constants'
import config from '../../configs/app.config'
import getBetslipDetailObject from '../common/getBetslipDetailObject'
import getUserDetailObject from '../common/getUserDetailObject'
import playerParentIds from '../common/playerParentIds'

/**
 * insert Sport betting transaction in elastic-search DB
 *
 * @export
 * @param {object} params it contains inserted transaction values
 * @param {object} sequelize
 * @returns {object} it'll return elastic-search inserted transaction
 */
export default async function addSportBetTransaction (params, sequelize, esClient) {
  try {
    const post = params.dataValues
    const queueProcessStatus = await sequelize.models.QueueProcessStatus.findOne({
      where: {
        service: 'create_ES_transactions',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      let sourceWalletOwnerDetails = null
      let targetWalletOwnerDetails = null
      const totalBetObj = {
        id: post.userId
      }

      // this return user detail, user wallet detail,
      // user tenant detail and associated agent/admin
      const userDetail = await getUserDetailObject(params.userId, sequelize.models)
      const userActiveDepositBonus = await sequelize.models.UserBonus.findOne({
        where: {
          status: BONUS_STATUS.ACTIVE,
          kind: BONUS_TYPES.DEPOSIT_SPORTS,
          userId: params.userId
        },
        raw: true
      })

      let betSlipDetail = null
      let betsArray = []
      let betslipDetailsObject = null
      if ((+post.paymentFor) !== paymentForCodes.DEPOSIT_BONUS_PENDING) {
        betSlipDetail = await getBetslipDetailObject(params.betslipId, sequelize.models)
        if (betSlipDetail) {
          betsArray = betSlipDetail.bets.map(ele => {
            return {
              id: +ele.id,
              is_deleted: ele.isDeleted,
              bet_id: ele.betId,
              fixture_id: ele.fixtureId,
              provider_id: ele.providerId,
              champ: ele.champ,
              match: ele.match,
              market: ele.market,
              name: ele.name,
              price: ele.price,
              start_date: ele.startDate,
              betslip_id: +ele.betslipId,
              market_id: +ele.marketId,
              bet_status: +ele.betStatus,
              event_id: ele.eventId,
              settlement_status: ele.settlementStatus,
              livescore: ele.livescore,
              created_at: ele.createdAt,
              updated_at: ele.updatedAt,
              event: ele.event
            }
          })
          betslipDetailsObject = {
            id: +betSlipDetail.id,
            is_deleted: betSlipDetail.isDeleted,
            bettype: +betSlipDetail.bettype,
            stake: betSlipDetail.stake,
            user_id: +betSlipDetail.userId,
            multi_price: betSlipDetail.multiPrice,
            betslip_status: betSlipDetail.betslipStatus,
            coupon_id: null,
            coupon_id_str: betSlipDetail.couponId,
            possible_win_amount: betSlipDetail.possibleWinAmount,
            created_at: betSlipDetail.createdAt,
            updated_at: betSlipDetail.updatedAt,
            settlement_status: betSlipDetail.settlementStatus,
            bets: betsArray,
            run: betSlipDetail.run
          }

          // update paymentFor in elastic
          if (params.paymentFor === JETFAIR_INTEGRATION_CONSTANT.CANCEL_BET) {
            const placeBetTransaction = await sequelize.models.BetsTransaction.findOne({
              where: {
                transactionId: post.reverseTransactionId
              },
              attributes: ['id'],
              useMaster: true
            })
            // const sourceBetObj = `ctx._source["description"] = "${JETFAIR_INTEGRATION_CONSTANT.PLACE_BET_REFUND_DESCRIPTION}"; ctx._source["payment_for"] = ${JETFAIR_INTEGRATION_CONSTANT.PLACE_BET_CANCEL}`
            // await updateSportBetTransactionStatus(placeBetTransaction, sourceBetObj, esClient)
            // const sourceObjEs = `ctx._source["betslip_details"]["settlement_status"] = "${JETFAIR_INTEGRATION_CONSTANT.CANCEL_BET_TXN_CODE}"`
            // await updateSportSettlementStatus(placeBetTransaction, sourceObjEs, esClient)
            const queueLogObject = {
              type: QUEUE_WORKER_CONSTANT.BET_TRANSACTION,
              status: QUEUE_WORKER_CONSTANT.READY,
              ids: [placeBetTransaction.id]
            }
            const queueLog = await sequelize.models.QueueLog.create(queueLogObject)
          }

        } else {
          let betTransactions
          if (params.transactionCode === JETFAIR_INTEGRATION_CONSTANT.MARKET_CANCEL_TXN_CODE || params.transactionCode === JETFAIR_INTEGRATION_CONSTANT.CANCEL_BET_TXN_CODE) {
            betTransactions = await sequelize.models.BetsTransaction.findAll({
              where: {
                marketId: params.marketId,
                userId: params.userId,
                transactionCode: JETFAIR_INTEGRATION_CONSTANT.PLACE_BET_TXN_CODE,
                transactionId: params.reverseTransactionId
              },
              useMaster: true
            })
          } else {
            betTransactions = await sequelize.models.BetsTransaction.findAll({
              where: {
                marketId: params.marketId,
                userId: params.userId,
                transactionCode: JETFAIR_INTEGRATION_CONSTANT.PLACE_BET_TXN_CODE
              }
            })
          }
          if (betTransactions.length > 0) {
            let bulkData = []
            for (const bet of betTransactions) {
              const queueLogObject = {
                type: QUEUE_WORKER_CONSTANT.BET_TRANSACTION,
                status: QUEUE_WORKER_CONSTANT.READY,
                ids: [bet.id]
              }
              bulkData.push(queueLogObject)
            }
            const queueLog = await sequelize.models.QueueLog.bulkCreate(bulkData)
          }
        }
      }

      // const amountInCurrencies = {}
      // const nonCashAmountInCurrencies = {}

      const tenatConfiguration = await sequelize.models.TenantConfiguration.findOne({
        where: {
          tenantId: post.tenantId
        }
      })
      // const currencies = await sequelize.models.Currency.findAll({
      //   where: {
      //     id: {
      //       [Sequelize.Op.in]: tenatConfiguration.allowedCurrencies.split(",")
      //     }
      //   },
      //   raw: true
      // })

      // currencies.map(x => (amountInCurrencies[x.code] = Math.abs(((+post.amount)) * userDetail.Wallet.Currency.exchangeRate / x.exchangeRate))
      // )
      // currencies.map(x => (nonCashAmountInCurrencies[x.code] = Math.abs(((+post.nonCashAmount)) * userDetail.Wallet.Currency.exchangeRate / x.exchangeRate))
      // )
      const ownerParentIdArr = await playerParentIds(userDetail.dataValues.id, userDetail.tenantId, sequelize)

      const marketNameResult = await sequelize.query(`SELECT "market" FROM "bets_bets" WHERE "market_id" = :market_id limit 1`, {
        replacements: {
          market_id: post.marketId
        },
        type: sequelize.QueryTypes.SELECT, useMaster: false
      })
      let marketName = null;
      if (marketNameResult.length) {
        marketName = marketNameResult[0].market
      }
      const parentChainIds = []
      const parentChainIdsValues = ownerParentIdArr.reduce((currentObj, parent) => {
        parentChainIds.push(+parent.id)
        currentObj[+parent.id] = parent
        return currentObj
      }, {})

      const AdminUserCommissions = await sequelize.models.AdminUserSetting.findAll({ where: { adminUserId: { [Op.in]: parentChainIds }, key: 'commission_percentage' }, raw: true })

      const reducedAdminCommission = AdminUserCommissions.reduce((currentObj, adminCommission) => {
        currentObj[+adminCommission.adminUserId] = adminCommission
        return currentObj
      }, {})

      const adminCommissionPercentage = []
      for (const [key, value] of Object.entries(parentChainIdsValues)) {
        let commissionPercentage = '000.00'
        if (reducedAdminCommission[key]) {
          const percentageStr = reducedAdminCommission[key].value.split('.')
          commissionPercentage = !percentageStr[1] ? `${percentageStr[0].padStart(3, '0')}.00` : `${percentageStr[0].padStart(3, '0')}.${percentageStr[1].padEnd(2, '0')}`
          adminCommissionPercentage.push(`${key}-${value.agent_name}-${userDetail.Wallet.dataValues.Currency.dataValues.code}-${commissionPercentage}`)
        } else {
          adminCommissionPercentage.push(`${key}-${value.agent_name}-${userDetail.Wallet.dataValues.Currency.dataValues.code}-${commissionPercentage}`)
        }
      }


      const playerDetails = {
        first_name: userDetail.firstName,
        last_name: userDetail.lastName,
        email: userDetail.email,
        phone: userDetail.phone,
        date_of_birth: userDetail.dateOfBirth,
        gender: userDetail.gender,
        locale: userDetail.locale,
        parent_type: userDetail.parentType,
        parent_id: +userDetail.parentId,
        created_at: userDetail.createdAt,
        updated_at: userDetail.updatedAt,
        user_name: userDetail.userName,
        country_code: userDetail.countryCode,
        tenant_id: parseInt(userDetail.tenantId),
        active: userDetail.active,
        demo: userDetail.demo,
        last_login_date: userDetail.lastLoginDate,
        self_exclusion: userDetail.selfExclusion,
        vip_level: userDetail.vipLevel,
        nick_name: userDetail.nickName,
        phone_code: userDetail.phoneCode,
        kyc_done: userDetail.kycDone,
        wallet_id: +userDetail.Wallet.id,
        amount: +userDetail.Wallet.amount,
        deducted_amount: 0.0,
        added_amount: 0.0,
        non_cash_amount: +userDetail.Wallet.nonCashAmount,
        currency_id: +userDetail.Wallet.currencyId,
        agent_first_name: userDetail.AdminUser.firstName,
        agent_email: userDetail.AdminUser.email,
        agent_last_name: userDetail.AdminUser.lastName,
        agent_name: userDetail.AdminUser.agentName,
        currency: userDetail.Wallet.Currency.code,
        parent_chain_ids: parentChainIds,
        player_id: +userDetail.id,
        deposit_bonus_details: null,
        player_id_s: userDetail.id,
        player_name: userDetail.userName,
        agent_full_name: `${userDetail.AdminUser.firstName} ${userDetail.AdminUser.lastName}`,
        parent_chain_detailed: adminCommissionPercentage
      }
      if (post.paymentFor === 7 && userActiveDepositBonus) {
        playerDetails.deposit_bonus_details = {
          unconverted_active_deposit_bonus: userActiveDepositBonus.bonusAmount,
          active_deposit_bonus_remaining_rollover: userActiveDepositBonus.rolloverBalance
        }
      }

      let currentBalance
      let afterBalance
      if (post.journalEntry === "DR") {
        afterBalance = ((+post.sourceBeforeBalance) - ((+post.amount) + (+post.nonCashAmount)))
        playerDetails.deducted_amount = ((+post.amount) + (+post.nonCashAmount))
      } else {
        afterBalance = ((+post.sourceBeforeBalance) + ((+post.amount) + (+post.nonCashAmount)))
        playerDetails.added_amount = ((+post.amount) + (+post.nonCashAmount))
      }
      if (post.sourceWalletId != null) {

        sourceWalletOwnerDetails = {
          id: +userDetail.Wallet.id,
          type: 'User',
          user_name: userDetail.userName,
          first_name: userDetail.firstName,
          last_name: userDetail.lastName,
          email: userDetail.email,
          parent_chain_ids: parentChainIds,
          before_balance: (+post.sourceBeforeBalance),
          after_balance: (+post.sourceAfterBalance),
          non_cash_before_balance: (+post.sourceNonCashBeforeBalance),
          non_cash_after_balance: (+post.sourceNonCashAfterBalance)
        }
        //currentBalance = sourceWalletOwnerDetails.after_balance
        playerDetails.amount = ((+userDetail.Wallet.amount))
        playerDetails.non_cash_amount = ((+userDetail.Wallet.nonCashAmount))

      } else {
        targetWalletOwnerDetails = {
          id: +userDetail.Wallet.id,
          type: 'User',
          user_name: userDetail.userName,
          first_name: userDetail.firstName,
          last_name: userDetail.lastName,
          email: userDetail.email,
          parent_chain_ids: parentChainIds,
          before_balance: (+post.targetBeforeBalance),
          after_balance: (+post.targetAfterBalance),
          non_cash_before_balance: (+post.targetNonCashBeforeBalance),
          non_cash_after_balance: (+post.targetNonCashAfterBalance)
        }
        //currentBalance = targetWalletOwnerDetails.after_balance
        playerDetails.amount = ((+userDetail.Wallet.amount))
        playerDetails.non_cash_amount = ((+userDetail.Wallet.nonCashAmount))
      }

      switch (post.paymentFor) {
        case 2: {
          post.transactionType = 'won'
          break
        }
        case 3: {
          post.transactionType = 'cashout'
          break
        }
        case 4: {
          post.transactionType = 'refund'
          break
        }
        case 5: {
          post.transactionType = 'lost_by_settlement'
          break
        }
        case 6: {
          post.transactionType = 'Deposit_bonus_claimed'
          break
        }
        case 7: {
          post.transactionType = 'Deposit_bonus_pending'
          break
        }
        case 8: {
          post.transactionType = 'Deposit_bonus_cancelled'
          break
        }
        default: {
          post.transactionType = 'bet_placement'
          break
        }
      }

      let otherCurrencyAmount = null
      let otherCurrencyNonCashAmount = null
      if (post.otherCurrencyAmount) {
        otherCurrencyAmount = JSON.parse(post.otherCurrencyAmount)
        for (const key in otherCurrencyAmount) {
          if (key === "LKR") {
            otherCurrencyAmount["lkr"] = otherCurrencyAmount[key]
            delete otherCurrencyAmount[key]
            break
          }
        }
      }
      if (post.otherCurrencyNonCashAmount) {
        otherCurrencyNonCashAmount = JSON.parse(post.otherCurrencyNonCashAmount)
        for (const key in otherCurrencyNonCashAmount) {
          if (key === "LKR") {
            otherCurrencyNonCashAmount["lkr"] = otherCurrencyNonCashAmount[key]
            delete otherCurrencyNonCashAmount[key]
            break
          }
        }
      }
      let otherCurrencyCommissionAmount = []
      if (post.otherCurrencyCommissionAmount) {
        otherCurrencyCommissionAmount = JSON.parse(post.otherCurrencyCommissionAmount)
        for (const key in otherCurrencyCommissionAmount) {
          if (key === "LKR") {
            otherCurrencyCommissionAmount["lkr"] = otherCurrencyCommissionAmount[key]
            delete otherCurrencyCommissionAmount[key]
            break
          }
        }
      }

      const transaction = await esClient.index({
        index: config.getProperties().es_index.sports_index_name,
        id: +post.id,
        body: {
          internal_tracking_id: +post.id,
          is_deleted: false,
          amount: +post.amount,
          journal_entry: post.journalEntry,
          status: post.status,
          reference: post.reference,
          description: post.description,
          user_id: +post.userId,
          player_details: playerDetails,
          betslip_id: +betSlipDetail?.id,
          betslip_details: betslipDetailsObject,
          created_at: post.createdAt,
          updated_at: post.updatedAt,
          tenant_id: +post.tenantId,
          target_currency: !post.sourceWalletId ? userDetail.Wallet.Currency.code : null,
          target_wallet: targetWalletOwnerDetails,
          source_currency: post.sourceWalletId ? userDetail.Wallet.Currency.code : null,
          source_wallet: sourceWalletOwnerDetails,
          transaction_type: post.transactionType,
          transaction_id: post.transactionId,
          conversion_rate: Number(post.conversionRate),
          payment_for: +post.paymentFor,
          non_cash_amount: +post.nonCashAmount,
          current_balance: +post.currentBalance,
          amount_in_currencies: otherCurrencyAmount,
          non_cash_amount_in_currencies: otherCurrencyNonCashAmount,
          reverse_transaction_id: post.reverseTransactionId,
          market_id: post.marketId,
          market_name: marketName,
          transaction_code: post.transactionCode,
          runner_name: post.runnerName,
          net_pl: post.netPl,
          commission_per: post.commissionPer,
          commission_amount: post.commissionAmount,
          commission_amount_in_currencies: otherCurrencyCommissionAmount,
          provider_id: post.providerId
        }
      })
    }
    const queueLogObject = {
      type: QUEUE_WORKER_CONSTANT.USER_TRANSACTION,
      status: QUEUE_WORKER_CONSTANT.READY,
      ids: [post.userId]
    }
    const queueLog = await sequelize.models.QueueLog.create(queueLogObject)
    return true
  } catch (e) {
    throw new Error(e)
  }
}
