import { PROD_TENANTS, STAGE_TENANTS } from '../../common/constants'
import config from '../../configs/app.config'
import playerParentIds from '../common/playerParentIds'


/**
 *
 *
 * @export
 * @param {*} params
 * @param {*} sequelize
 * @returns
 */
async function getActioneeInfo (actionee_type, actionee_id, tenant_id, sequelize) {
  const {
    AdminUser,
    User,
    SuperAdminUser
  } = sequelize.models
  let actioneeInfo

  if (actionee_type === 'AdminUser') {
    actioneeInfo = await AdminUser.findOne({
      where: {
        id: actionee_id
      },
      attributes: ['first_name', 'last_name', 'email']
    })
  } else if (actionee_type === 'User') {
    actioneeInfo = await User.findOne({
      where: {
        id: actionee_id
      },
      attributes: ['id', 'first_name', 'last_name', 'email', 'user_name']
    })
  } else if (actionee_type === 'SuperAdminUser') {
    actioneeInfo = await SuperAdminUser.findOne({
      where: {
        id: actionee_id
      },
      attributes: ['id', 'first_name', 'last_name', 'email']
    })
  } else {
    return []
  }

  const ownerParentIdArr = await playerParentIds(actionee_id, tenant_id, sequelize)
  const parentChainIds = []
  const parentChainIdsValues = ownerParentIdArr.reduce((currentObj, parent) => {
    parentChainIds.push(+parent.id)
    currentObj[+parent.id] = parent
    return currentObj
  }, {})
  actioneeInfo.dataValues.type = actionee_type
  if (parentChainIds.length > 0) {
    actioneeInfo.dataValues.parent_chain_ids = parentChainIds;
  } else {

    if (actionee_type == 'AdminUser') {
      actioneeInfo.dataValues.user_name = null
      actioneeInfo.dataValues.parent_chain_ids = [actionee_id];
    } else {
      actioneeInfo.dataValues.parent_chain_ids = [];
    }
  }
  actioneeInfo.dataValues.id = Number(actioneeInfo.dataValues.id)
  return actioneeInfo
}

export default async function addAuditLog (params, sequelize, esClient) {

  const post = params.dataValues
  let actionee = []
  let actioneeType = 'AdminUser'
  if (post.event === 'Cashback Bonus Claim'){
    actioneeType = 'User'
  }
  if(post.action == 'Referral Code Verification'){
    actioneeType ='User'
  }

  if (post.actioneeId) {
    actionee = await getActioneeInfo( actioneeType, post.actioneeId, post.tenantId, sequelize)
  }
  const tenantObj = config.get('env') === 'production' ? PROD_TENANTS : STAGE_TENANTS
  const tenant = tenantObj[post.tenantId] || null
  try {
    const logData = await esClient.index({
      index: config.getProperties().es_index.audit_log_index_name,
      id: +post.id,
      body: {
        internal_tracking_id: +post.id,
        id_s: post.id,
        tenant_id: post.tenantId,
        tenant_name: tenant?.name || null,
        event_type: post.eventType,
        description: post.description,
        action: post.action,
        actionee_ip: post.actioneeIp,
        event_id: post.eventId,
        event: post.event,
        actionee_id: post.actioneeId,
        created_at: post.createdAt,
        updated_at: post.updatedAt,
        previous_data: post.previousData,
        modified_data: post.modifiedData,
        actionee: actionee
      }
    })
    return logData
  } catch (e) {
    console.log("========main error",e)
    throw new Error(e)
  }

}
