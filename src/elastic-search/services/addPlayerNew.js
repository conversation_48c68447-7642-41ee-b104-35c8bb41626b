import { Sequelize } from 'sequelize'
import { QUEUE_WORKER_CONSTANT } from '../../common/constants'
import { publishToRedis } from '../../common/queueService/publishToRedis'

/**
 *
 *
 * @export
 * @param {*} params
 * @param {*} sequelize
 * @returns
 */
async function getUserInfo (player_id, sequelize) {
  const {
    Wallet,
    Currency,
    User,
    AdminUser
  } = sequelize.models
  const user_info = await User.findOne({
    where: {
      id: player_id
    },
    attributes: [
      'first_name', 'last_name', 'email', 'phone', 'date_of_birth', 'gender', 'locale', 'parent_type', 'parent_id', 'created_at',
      'updated_at', 'user_name', 'country_code', 'tenant_id', 'active', 'demo', 'last_login_date', 'self_exclusion', 'vip_level',
      'nick_name', 'phone_code', 'kyc_done', 'category_type',
      [sequelize.col('W.id'), 'wallet_id'],
      [sequelize.col('W.amount'), 'amount'],
      [sequelize.col('W.non_cash_amount'), 'non_cash_amount'],
      [sequelize.col('W.currency_id'), 'currency_id'],
      [sequelize.col('AU.first_name'), 'agent_first_name'],
      [sequelize.col('AU.email'), 'agent_email'],
      [sequelize.col('AU.last_name'), 'agent_last_name'],
      [sequelize.col('AU.first_name'), 'agent_first_name'],
      [sequelize.col('AU.agent_name'), 'agent_name'],
      [sequelize.col('W.C.code'), 'currency']
    ]
    ,
    include: [
      {
        model: AdminUser,
        as: 'AU',
        attributes: []
      },
      {
        model: Wallet,
        as: 'W',
        attributes: [],
        include: {
          model: Currency,
          as: 'C',
          attributes: []
        }
      }
    ],
    raw: true
  })
  user_info.currency_id = +user_info.currency_id
  user_info.parent_id = +user_info.parent_id
  user_info.tenant_id = +user_info.tenant_id
  user_info.wallet_id = +user_info.wallet_id
  return user_info
}
async function getTotalBets (player_id, sequelize) {
  const {
    Transaction
  } = sequelize.models
  const [bet_info] = await sequelize.query(`
  select COUNT( id ) AS total_bets, SUM( amount ) AS total_bet_amount from transactions where actionee_id = ${player_id}
  and actionee_type = 'User' and transaction_type in (0, 8)
  group by actionee_id;
    `,
  { type: Sequelize.QueryTypes.SELECT, useMaster: false }
  )
  return bet_info
}
async function getTotalDistinctBets (player_id, tenant_id, sequelize) {
  const {
    Transaction
  } = sequelize.models
  const [bet_info] = await sequelize.query(`
  select count(distinct "transaction_id") as total from transactions where tenant_id=${tenant_id} and actionee_id = ${player_id}
  and actionee_type = 'User' and transaction_type in (0, 8) group by actionee_id;
    `,
  { type: Sequelize.QueryTypes.SELECT, useMaster: false }
  )
  return bet_info
}
async function getBetDetails (player_id, sequelize) {
  const {
    Transaction
  } = sequelize.models
  const [bet_info] = await sequelize.query(`
  select COUNT( id ) AS total_bets, SUM(COALESCE(non_cash_amount, 0) + COALESCE(amount,0)) AS total_bet_amount from bets_transactions where user_id = ${player_id} and payment_for = 1;
    `,
  { type: Sequelize.QueryTypes.SELECT, useMaster: false }
  )
  return bet_info
}
async function getFirstDepositAmount (playerId, walletId, sequelize) {
  const {
    Transaction
  } = sequelize.models
  const [firstDeposit] = await sequelize.query(`
  SELECT id, amount, created_at from transactions WHERE transaction_type = 3   AND (( comments = 'Deposit Request Approved'
  AND target_wallet_id = ${walletId}) OR (actionee_type='User' AND actionee_id = ${playerId} AND comments='Deposit Request'))
  order by id ASC LIMIT 1 OFFSET 0;
    `,
  { type: Sequelize.QueryTypes.SELECT, useMaster: false }
  )
  return firstDeposit
}
async function getUserStatusString (status) {
  let str = ''
  switch (status) {
    case false:
      str = 'InActive'
      break;
    case true:
      str = 'Active'
      break;
    default:
      str = 'NA'
  }
  return str
}

export default async function addPlayer (post, sequelize, esClient) {
  const {
    User: UserModel,
    QueueLog: QueueLogModel
  } = sequelize.models

  const data = await UserModel.findOne({
    raw: true,
    where: { id: post.id },
    attributes: ['id', 'tenantId']
  })

  // const body = {
  //   player_id: +post.id,
  //   player_id_s: post.id.toString(),
  //   vip_level: data.vip_level,
  //   tenant_id: +data.tenant_id,
  //   nick_name: data.nick_name,
  //   user_name: data.user_name,
  //   email: data.email,
  //   phone: data.phone,
  //   phone_code: data.phone_code,
  //   agent_full_name: data.agent_first_name + ' ' + data.agent_last_name,
  //   agent_name: data.agent_name,
  //   last_login: data.last_login_date,
  //   country: data.country_code,
  //   creation_date: data.created_at,
  //   real_balance: parseFloat(data.wallet_amount),
  //   non_cash_balance: parseFloat(data.wallet_non_cash_amount),
  //   one_time_bonus_amount: parseFloat(data.one_time_bonus_amount) || parseFloat(0),
  //   total_balance: totalBalance,
  //   currency: data.currency,
  //   wallet_id: parseInt(data.wallet_id),
  //   total_bets: data?.dist_transaction_id_count ? parseInt(data?.dist_transaction_id_count) : 0,
  //   total_bet_amount: data?.total_bet_amount ? parseFloat(data?.total_bet_amount) : 0,
  //   total_sport_bets,
  //   total_sport_bet_amount,
  //   parent_id: parseInt(data.parent_id),
  //   parent_type: data.parent_type,
  //   parent_chain_ids: parentChainIds,
  //   kyc_done: data.kyc_done ? "true" : "false",
  //   demo: data.demo ? "true" : "false",
  //   status: data.active,
  //   first_deposit_amount,
  //   first_deposit_amount_transaction_id,
  //   first_deposit_amount_date_time,
  //   category_type: +data.category_type
  // }

  try {
    // const player = await esClient.index({
    //   index: config.getProperties().es_index.users_index_name,
    //   id: +post.id,
    //   body,
    //   refresh: 'wait_for'
    // })

    const smarticoQueueLogObject = {
      type: QUEUE_WORKER_CONSTANT.SMARTICO_WALLET_UPDATE,
      status: QUEUE_WORKER_CONSTANT.READY,
      ids: [+post.id],
      tenantId: +data.tenantId,
    }

    const smarticoQueueLog = await QueueLogModel.create(smarticoQueueLogObject)

    try {
      await publishToRedis.publishToQueueService({ QueueLog: { queueLogId: smarticoQueueLog.id } })
    } catch (error) {
      console.log("error_while_publishing_smartico", error)
    }
  } catch (e) {
    console.log("========main error",e)
    throw new Error(e)
  }

}
