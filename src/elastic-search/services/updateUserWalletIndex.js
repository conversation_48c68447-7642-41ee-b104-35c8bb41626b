
import config from "../../configs/app.config"
/**
 * @export
 * @param {object} params it contain updated wallet values
 * @param {object} esClient
 * @return {object} it'll return updated wallet object
 */
export default async function updateWalletInUserDevelopmentIndex (params, sourceObj, esClient) {
  try {
    const wallet = await esClient.updateByQuery({
      index: config.getProperties().es_index.users_index_name,
      refresh: 'true',
      body: {
        script: {
          lang: 'painless',
          source: sourceObj
        },
        query: {
          match: {
            internal_tracking_id: params.id
          }
        }
      }
    })
    return wallet
  } catch (e) {
    throw new Error(e)
  }
}
