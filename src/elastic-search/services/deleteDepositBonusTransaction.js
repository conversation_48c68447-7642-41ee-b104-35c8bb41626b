import config from "../../configs/app.config"
/**
 *
 *
 * @export
 * @param {object} params it contain updated player values
 * @returns {object} it'll return updated player
 */
export default async function deleteDepositBonusTransaction (params, sequelize, esClient) {
  try {
    // Define index based on condition
    const index = params?.transactionId?.type === 'casino'
      ? config.getProperties().es_index.transactions_index_name
      : config.getProperties().es_index.sports_index_name;

    const response = await esClient.deleteByQuery({
      index,
      refresh: true,
      body: {
        query: {
          match: {
            internal_tracking_id: params?.transactionId?.id
          }
        }
      }
    });
    return response
  } catch (e) {
    throw new Error(e)
  }
}
