import { Op } from 'sequelize'
import { BONUS_STATUS, BONUS_TYPES, TRANSACTION_TYPES } from '../../common/constants'
import playerParentIds from '../common/playerParentIds'
import playerTotalBets from '../common/playerTotalBets'
import totalDebitTransaction from '../common/totalDebitTransaction'
import updatePlayerTotalBets from '../common/updatePlayerTotalBeta'
import updateFirstDepositAmount from '../common/updateFirstDepositAmount'
import config from '../../configs/app.config'

/**
 * insert transaction in elastic-search DB
 *
 * @export
 * @param {object} params it contains inserted transaction values
 * @param {object} sequelize
 * @returns {object} it'll return elastic-search inserted transaction
 */
export default async function addTransaction (params, sequelize, esClient) {
  const {
    User,
    Wallet,
    Currency,
    Tenant,
    AdminUser,
    CasinoGame,
    CasinoProvider,
    AdminUserSetting,
    UserBonus,
    Transaction
  } = sequelize.models
  const post = params.dataValues
  const transactionType = post.transactionType

  let sourceWalletOwnerDetails = null
  let targetWalletOwnerDetails = null
  const totalBetObj = {
    id: post.actioneeId
  }

  const userActiveDepositBonus = await UserBonus.findOne({
    where: {
      status: BONUS_STATUS.ACTIVE,
      kind: BONUS_TYPES.DEPOSIT,
      userId: post.actioneeId
    },
    raw: true,
    useMaster: true
  })

  // this return user detail, user wallet detail,
  // user tenant detail and associated agent/admin
  const userDetail = await User.findOne({
    where: {
      id: post.actioneeId
    },
    include: [{
      model: Wallet,
      include: {
        model: Currency
      }
    },
    {
      model: Tenant
    },
    {
      model: AdminUser
    },
    {
      model: AdminUserSetting
    }],
    useMaster: true
  })

  // this return casino game and associated provider of casino
  let casinoDetail
  if (post.gameId) {
    casinoDetail = await CasinoGame.findOne({
      where: {
        gameId: post.gameId.toString()
      },
      raw: true,
      include: [{
        model: CasinoProvider
      }]
    })
  }
  // this provide all currencies
  const currencies = await Currency.findAll({
    raw: true
  })
  const amountInCurrencies = {}

  const ownerParentIdArr = await playerParentIds(userDetail.dataValues.id, userDetail.dataValues.tenantId, sequelize)
  const parentChainIds = []
  const parentChainIdsValues = ownerParentIdArr.reduce((currentObj, parent) => {
    parentChainIds.push(+parent.id)
    currentObj[+parent.id] = parent
    return currentObj
  }, {})

  const AdminUserCommissions = await AdminUserSetting.findAll({ where: { adminUserId: { [Op.in]: parentChainIds }, key: 'commission_percentage' }, raw: true })

  const reducedAdminCommission = AdminUserCommissions.reduce((currentObj, adminCommission) => {
    currentObj[+adminCommission.adminUserId] = adminCommission
    return currentObj
  }, {})

  const adminCommissionPercentage = []
  for (const [key, value] of Object.entries(parentChainIdsValues)) {
    let commissionPercentage = '000.00'
    if (reducedAdminCommission[key]) {
      const percentageStr = reducedAdminCommission[key].value.split('.')
      commissionPercentage = !percentageStr[1] ? `${percentageStr[0].padStart(3, '0')}.00` : `${percentageStr[0].padStart(3, '0')}.${percentageStr[1].padEnd(2, '0')}`
      adminCommissionPercentage.push(`${key}-${value.agent_name}-${userDetail.Wallet.dataValues.Currency.dataValues.code}-${commissionPercentage}`)
    } else {
      adminCommissionPercentage.push(`${key}-${value.agent_name}-${userDetail.Wallet.dataValues.Currency.dataValues.code}-${commissionPercentage}`)
    }
  }

  const actionee = {
    type: 'User',
    user_name: userDetail.dataValues.userName,
    first_name: userDetail.dataValues.firstName,
    last_name: userDetail.dataValues.lastName,
    email: userDetail.dataValues.email ? userDetail.dataValues.email : '',
    phone: userDetail.dataValues.phone ? userDetail.dataValues.phone : '',
    parent_chain_ids: parentChainIds
  }

  const playerDetails = {
    player_id: +userDetail.dataValues.id,
    user_name: userDetail.dataValues.userName,
    player_id_s: userDetail.dataValues.id,
    player_name: userDetail.dataValues.userName,
    agent_full_name: `${userDetail.AdminUser.dataValues.firstName} ${userDetail.AdminUser.dataValues.lastName}`,
    agent_name: userDetail.AdminUser.dataValues.agentName,
    agent_email: userDetail.AdminUser.dataValues.email,
    email: userDetail.dataValues.email ? userDetail.dataValues.email : '',
    phone: userDetail.dataValues.phone ? userDetail.dataValues.phone : '',
    parent_id: +userDetail.dataValues.parentId,
    parent_chain_ids: parentChainIds,
    currency: userDetail.Wallet.dataValues.Currency.dataValues.code,
    parent_chain_detailed: adminCommissionPercentage,
    deducted_amount: 0.0,
    added_amount: 0.0,
    deposit_bonus_details: null,
    demo: userDetail.dataValues.demo
  }

  if (userActiveDepositBonus) {
    playerDetails.deposit_bonus_details = {
      unconverted_active_deposit_bonus: userActiveDepositBonus.bonusAmount,
      active_deposit_bonus_remaining_rollover: userActiveDepositBonus.rolloverBalance
    }
  }

  let differenceBalance = 0.0

  if (post.sourceWalletId != null) {
    sourceWalletOwnerDetails = {
      id: userDetail.dataValues.id,
      type: 'User',
      user_name: userDetail.dataValues.userName,
      first_name: userDetail.dataValues.firstName,
      last_name: userDetail.dataValues.lastName,
      email: userDetail.dataValues.email ? userDetail.dataValues.email : '',
      phone: userDetail.dataValues.phone ? userDetail.dataValues.phone : ''
    }

    differenceBalance = post.sourceAfterBalance - post.sourceBeforeBalance
    currencies.map(x => (amountInCurrencies[x.code] = Math.abs(differenceBalance * userDetail.Wallet.dataValues.Currency.dataValues.exchangeRate / x.exchangeRate))
    )

    playerDetails.revenue = differenceBalance
    playerDetails.before_balance = post.sourceBeforeBalance
    playerDetails.after_balance = post.sourceAfterBalance
    if (transactionType === TRANSACTION_TYPES.DEBIT || transactionType === TRANSACTION_TYPES.CREDIT || transactionType === TRANSACTION_TYPES.ROLLBACK || transactionType === TRANSACTION_TYPES.TIP || transactionType === TRANSACTION_TYPES.WITHDRAW) {
      playerDetails.amount = Math.abs(post.sourceBeforeBalance - post.sourceAfterBalance)
      playerDetails.non_cash_amount = 0.0
    } else {
      playerDetails.amount = 0.0
      playerDetails.non_cash_amount = Math.abs(post.sourceBeforeBalance - post.sourceAfterBalance)
    }
  } else {
    targetWalletOwnerDetails = {
      id: userDetail.dataValues.id,
      type: 'User',
      user_name: userDetail.dataValues.userName,
      first_name: userDetail.dataValues.firstName,
      last_name: userDetail.dataValues.lastName,
      email: userDetail.dataValues.email
    }
    differenceBalance = post.targetAfterBalance - post.targetBeforeBalance
    currencies.map(x => (amountInCurrencies[x.code] = Math.abs(differenceBalance * userDetail.Wallet.dataValues.Currency.dataValues.exchangeRate / x.exchangeRate))
    )
    playerDetails.revenue = differenceBalance
    playerDetails.before_balance = post.targetBeforeBalance
    playerDetails.after_balance = post.targetAfterBalance
    if (transactionType === TRANSACTION_TYPES.DEBIT || transactionType === TRANSACTION_TYPES.CREDIT || transactionType === TRANSACTION_TYPES.ROLLBACK || transactionType === TRANSACTION_TYPES.TIP || transactionType === TRANSACTION_TYPES.WITHDRAW_CANCEL || transactionType === TRANSACTION_TYPES.DEPOSIT_BONUS_CLAIM || transactionType === TRANSACTION_TYPES.DEPOSIT) {
      playerDetails.non_cash_amount = 0.0
      playerDetails.amount = Math.abs(post.targetBeforeBalance - post.targetAfterBalance)
    } else {
      playerDetails.amount = 0.0
      playerDetails.non_cash_amount = Math.abs(post.targetBeforeBalance - post.targetAfterBalance)
    }
  }

  switch (transactionType) {
    case 1: {
      post.transactionType = 'win'
      playerDetails.added_amount = Math.abs(differenceBalance)
      break
    }
    case 2: {
      post.transactionType = 'refund'
      playerDetails.added_amount = Math.abs(differenceBalance)
      break
    }
    case 3: {
      post.transactionType = 'deposit'
      playerDetails.added_amount = Math.abs(differenceBalance)
      break
    }
    case 4: {
      post.transactionType = 'withdraw'
      playerDetails.deducted_amount = Math.abs(differenceBalance)
      break
    }
    case 7: {
      post.transactionType = 'tip'
      playerDetails.deducted_amount = Math.abs(differenceBalance)
      break
    }
    case 8: {
      post.transactionType = 'bet_non_cash'
      playerDetails.deducted_amount = Math.abs(differenceBalance)
      break
    }
    case 9: {
      post.transactionType = 'win_non_cash'
      playerDetails.added_amount = Math.abs(differenceBalance)
      break
    }
    case 10: {
      post.transactionType = 'refund_non_cash'
      playerDetails.added_amount = Math.abs(differenceBalance)
      break
    }
    case 11: {
      post.transactionType = 'non_cash_bonus_claim'
      break
    }
    case 12: {
      post.transactionType = 'deposit_bonus_claim'
      playerDetails.added_amount = Math.abs(differenceBalance)
      break
    }
    case 13: {
      post.transactionType = 'tip_non_cash'
      playerDetails.deducted_amount = Math.abs(differenceBalance)
      break
    }
    case 14: {
      post.transactionType = 'withdraw_cancel'
      playerDetails.added_amount = Math.abs(differenceBalance)
      break
    }
    case 15: {
      post.transactionType = 'joining_bonus_claimed'
      playerDetails.added_amount = Math.abs(differenceBalance)
      break
    }
    case 16: {
      post.transactionType = 'failed'
      playerDetails.added_amount = Math.abs(differenceBalance)
      break
    }
    case 17: {
      post.transactionType = 'promo_code_bonus_claimed'
      playerDetails.added_amount = Math.abs(differenceBalance)
      break
    }
    default: {
      post.transactionType = 'bet'
      playerDetails.deducted_amount = Math.abs(differenceBalance)
      break
    }
  }

  try {
    const transaction = await esClient.index({
      index: config.getProperties().es_index.transactions_index_name,
      id: +post.id,
      body: {
        internal_tracking_id: +post.id,
        tenant_id: +post.tenantId,
        transaction_id: post.transactionId,
        actionee_id: +post.actioneeId,
        transaction_type: post.transactionType,
        amount: post.amount,
        created_at: post.createdAt,
        source_currency: post.sourceWalletId ? userDetail.Wallet.dataValues.Currency.dataValues.code : null,
        target_currency: !post.sourceWalletId ? userDetail.Wallet.dataValues.Currency.dataValues.code : null,
        actionee: actionee,
        source_wallet_owner: sourceWalletOwnerDetails,
        target_wallet_owner: targetWalletOwnerDetails,
        conversion_rate: post.conversionRate,
        status: post.status,
        description: post.comments,
        player_details: playerDetails,
        round_id: +post.roundId,
        round_id_s: post.roundId,
        non_cash_amount: 0,
        internal_error_code: post.errorCode,
        internal_error_description: post?.errorDescription,
        gp_error_code: null,
        game_provider: casinoDetail ? casinoDetail['CasinoProvider.name'] : null,
        game_type: casinoDetail ? casinoDetail.name : null,
        table_id: post.tableId ? post.tableId.toString() : null,
        type: (transactionType === 4 || transactionType === 14) ? 'financial' : 'game',
        transfer_method: 'manual',
        amount_in_currencies: amountInCurrencies,
        payment_provider_id: post.paymentProviderId

      }
    })
    if (post.transactionType === 3) {
      const findUserDeposit = await Transaction.findOne({
        where: {
          transactionType: 3,
          actioneeId: userDetail.id,
          actioneeType: 'User',
          comments: 'Deposit Request'
        }
      })
      const findAdminDeposit = await Transaction.findOne({
        where: {
          transactionType: 3,
          targetWalletId: userDetail.Wallet.id,
          comments: 'Deposit Request Approved'
        }
      })
      if (!findUserDeposit && !findAdminDeposit) {
        const sourceObj = `ctx._source["first_deposit_amount"] =  ${post.amount}; ctx._source["first_deposit_amount_transaction_id"] =  ${post.id}`
        updateFirstDepositAmount(userDetail.dataValues.id, sourceObj, esClient)
      }
    }
    if (!transactionType || transactionType === 8) {
      const playerTotalBet = await playerTotalBets(params, esClient)

      if (+playerTotalBet.totalBets) {
        const totalDebitAmount = await totalDebitTransaction(params, esClient)
        totalBetObj.totalBets = ++totalDebitAmount.totalBets
        totalBetObj.totalBetAmount = totalDebitAmount.totalBetAmount + post.amount
        const sourceObj = `ctx._source["total_bets"] = "${totalBetObj.totalBets}"; ctx._source["total_bet_amount"] = "${totalBetObj.totalBetAmount}"; ctx._source["real_balance"] =  ${userDetail.Wallet.dataValues.amount}; ctx._source["total_balance"] =  ${userDetail.Wallet.dataValues.amount}+${userDetail.Wallet.dataValues.nonCashAmount}; ctx._source["non_cash_balance"] = ${userDetail.Wallet.dataValues.nonCashAmount}`
        updatePlayerTotalBets(totalBetObj, sourceObj, esClient)
      } else {
        totalBetObj.totalBets = ++playerTotalBet.totalBets
        totalBetObj.totalBetAmount = playerTotalBet.totalBetAmount + post.amount

        const sourceObj = `ctx._source["total_bets"] = "${totalBetObj.totalBets}"; ctx._source["total_bet_amount"] = "${totalBetObj.totalBetAmount}"; ctx._source["real_balance"] = ${userDetail.Wallet.dataValues.amount}; ctx._source["total_balance"] =  ${userDetail.Wallet.dataValues.amount}+${userDetail.Wallet.dataValues.nonCashAmount}; ctx._source["non_cash_balance"] = ${userDetail.Wallet.dataValues.nonCashAmount}`
        updatePlayerTotalBets(totalBetObj, sourceObj, esClient)
      }
    } else {
      const sourceObj = `ctx._source["real_balance"] =  ${userDetail.Wallet.dataValues.amount}; ctx._source["non_cash_balance"] = ${userDetail.Wallet.dataValues.nonCashAmount}; ctx._source["total_balance"] =  ${userDetail.Wallet.dataValues.amount}+${userDetail.Wallet.dataValues.nonCashAmount}`
      updatePlayerTotalBets(totalBetObj, sourceObj, esClient)
    }
    return transaction
  } catch (e) {
    throw new Error(e)
  }
}
