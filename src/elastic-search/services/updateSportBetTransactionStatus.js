import config from "../../configs/app.config"
/**
 *
 *
 * @export
 * @param {object} params it contain updated player values
 * @returns {object} it'll return updated player
 */
export default async function updateSportBetTransactionStatus (params, sourceObj, esClient) {
    try {
        const transaction = await esClient.updateByQuery({
          index: config.getProperties().es_index.sports_index_name,
          refresh: 'true',
          body: {
            script: {
              lang: 'painless',
              source: sourceObj
            },
            query: {
              match: {
                internal_tracking_id: params.id
              }
            }
          }
        })
        return transaction
      } catch (e) {
        throw new Error(e)
      }
  }
