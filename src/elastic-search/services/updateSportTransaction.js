
import config from "../../configs/app.config"
/**
 *
 *
 * @export
 * @param {object} params it contain updated transaction values
 * @param {object} esClient
 * @return {object} it'll return updated transaction object
 */
export default async function updateSportTransaction (params, sourceObj, esClient) {
  try {
    const transaction = await esClient.updateByQuery({
      index: config.getProperties().es_index.sports_index_name,
      refresh: 'true',
      body: {
        script: {
          lang: 'painless',
          source: sourceObj
        },
        query: {
          match: {
            internal_tracking_id: params.id
          }
        }
      }
    })
    return transaction
  } catch (e) {
    throw new Error(e)
  }
}
