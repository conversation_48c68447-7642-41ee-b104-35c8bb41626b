import { createBullBoard } from '@bull-board/api'
import { BullAdapter } from '@bull-board/api/bullAdapter'
import { ExpressAdapter } from '@bull-board/express'
import { BonusEngineQueue } from '../../queues/bonusEngine.queue'
import { CommonQueue } from '../../queues/common.queue'
import { CreateCsvQueue } from '../../queues/createCsv.queue'
import { ExportCsvQueue } from '../../queues/exportCsv.queue'
import { PaymentGatewayQueue } from '../../queues/paymentGateway.queue'
import { SmartiCoQueue } from '../../queues/smartiCo.queue'
import { TransactionQueue } from '../../queues/transaction.queue'

/**
 *
 *
 * @export
 * @class DashboardController
 */
export default class DashboardController {
  /**
   *
   *
   * @static
   * @return {object}
   * @memberof DashboardController
   */
  static dashboard () {
    const serverAdapter = new ExpressAdapter()

    createBullBoard({
      queues: [
        // new BullAdapter(BetTransactionQueue),
        // new BullAdapter(CasinoTransactionQueue),
        // new BullAdapter(DepositTransactionQueue),
        // new BullAdapter(UserTransactionQueue),
        // new BullAdapter(WithdrawTransactionQueue),
        // new BullAdapter(BulkDepositWithdrawTransactionQueue),
        // new BullAdapter(SportsCasinoTransactionQueue),
        new BullAdapter(TransactionQueue),
        new BullAdapter(CreateCsvQueue),
        new BullAdapter(ExportCsvQueue),
        // new BullAdapter(ActiveLosingBonusJobsQueue),
        // new BullAdapter(AuditLogQueue),
        new BullAdapter(PaymentGatewayQueue),
        new BullAdapter(CommonQueue),
        new BullAdapter(BonusEngineQueue),
        new BullAdapter(SmartiCoQueue)
        // new BullAdapter(Marina888Queue)    (used for migrating marina888 transaction (not needed now))

      ],
      serverAdapter: serverAdapter
    })

    serverAdapter.setBasePath('/dashboard')
    return serverAdapter.getRouter()
  }
}
