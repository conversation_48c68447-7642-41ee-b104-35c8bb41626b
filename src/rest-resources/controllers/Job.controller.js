import addAgentRevenueSummary from '../../common/addAgentRevenueSummary'
import addProviderInCasinoTables from '../../common/addProviderInCasinoTables'
import addReindexingInTransactions from '../../common/addReindexingInTransactions'
import botTransactionsV2 from '../../common/botTransactionsV2'
import checkRollbackWithAxios from '../../common/checkRollbackWithAxios'
import createPartnerMatrixQueue from '../../common/createPartnerMatrixQueue'
import cryptoCurrencyConversion from '../../common/cryptoCurrencyConversion'
import darwinGamePopulate from '../../common/darwinGamePopulation'
import ezugiGamePopulate from '../../common/ezugiGamesPopulation'
import funkyGamesGamePopulation from '../../common/funkyGamesGamePopulation'
import ggrSummary from '../../common/ggrSummary'
import lotteryGamePopulation from '../../common/lotteryGamePopulation'
import pgSoftGamePopulate from '../../common/pgSoftGamePolulation'
import st8GamePopulate from '../../common/st8GamePopulation'
import spribeGamePopulate from '../../common/spribeGamePopulation'
import updateConsole from '../../common/updateConsole'
import updateGames from '../../common/updateGames'
import whiteCliffGamePopulation from '../../common/whiteCliffGamePopulation'
import whiteCliffSportsRawdata from '../../common/whiteCliffSportsRawdata'
import { sendResponse } from '../../helpers/response.helpers'
import { CreateJobService, FindJobService, PayInStatusService } from '../../services/job'
import spinocchioGamePopulation from '../../common/spinocchioGamePopulation'
import createWyntaQueue from '../../common/createWyntaQueue'

export default class JobController {
  static async createJob (req, res, next) {
    try {
      const { result, successful, errors } = await CreateJobService.execute({ ...req.body }, req.context)
      sendResponse({ req, res, next }, { result, successful, serviceErrors: errors })
    } catch (error) {
      next(error)
    }
  }

  static async findJob (req, res, next) {
    try {
      const { result, successful, errors } = await FindJobService.execute({ ...req.body }, req.context)
      sendResponse({ req, res, next }, { result, successful, serviceErrors: errors })
    } catch (error) {
      next(error)
    }
  }

  static async payInStatus (req, res, next) {
    try {
      const { result, successful, errors } = await PayInStatusService.execute({ ...req.body }, req.context)
      sendResponse({ req, res, next }, { result, successful, serviceErrors: errors })
    } catch (error) {
      next(error)
    }
  }

  static async darwinGameUpdation (req, res, next) {
    try {
      const result = await darwinGamePopulate()
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async pgSoftGamePopulate (req, res, next) {
    try {
      const result = await pgSoftGamePopulate()
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async st8GamePopulate (req, res, next) {
    try {
      const result = await st8GamePopulate()
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async spribeGamePopulate (req, res, next) {
    try {
      const result = await spribeGamePopulate()
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async funkyGamesUpdation (req, res, next) {
    try {
      const result = await funkyGamesGamePopulation({ ...req.body }, req.context)
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }
  static async ezugiGameUpdation (req, res, next) {
    try {
      const result = await ezugiGamePopulate()
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async whiteCliffGamesUpdation (req, res, next) {
    try {
      const result = await whiteCliffGamePopulation({ ...req.body }, req.context)
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async whiteCliffSportsRawdata (req, res, next) {
    try {
      const result = await whiteCliffSportsRawdata(req.body)
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async partnerMatrixController (req, res, next) {
    try {
      const data = await createPartnerMatrixQueue()
      sendResponse({ req, res, next }, { result: { data: data }, successful: true, serviceErrors: null })
    } catch (error) {
      next(error)
    }
  }

  static async updateGamesController (req, res, next) {
    try {
      const tokenToUpdateGames = req.headers['games-auth-token']
      const sheetId = req.query.sheet_id?.trim()
      const tenantIds = req.query.tenant_ids?.trim()?.split(',')?.map(id => id.trim())
      const onlyMasterTenant = req.query.only_master_tenant === 'true'
      const { status, message } = await updateGames(tokenToUpdateGames, sheetId, tenantIds, onlyMasterTenant)
      res.status(status).json({ message })
    } catch (error) {
      next(error)
    }
  }

  static async updateCategoryController (req, res, next) {
    try {
      const sheetId = req.query.sheet_id?.trim()
      const { status, message, noToInsert, allInactive } = await updateConsole(sheetId, res)

      res.status(status).json({ message, allInactive, noToInsert })
    } catch (error) {
      next(error)
    }
  }

  static async addProviderInCasinoTablesController (req, res, next) {
    try {
      const { status, message } = await addProviderInCasinoTables()
      res.status(status).json({ message })
    } catch (error) {
      next(error)
    }
  }

  static addReindexingInTransactionsController (req, res, next) {
    try {
      addReindexingInTransactions()
      res.status(200).json({ message: 'Reindexing is in progress.' })
    } catch (error) {
      next(error)
    }
  }

  // data seeding for agent and player revenue and summary with ptogress tracker
  static addAgentRevenueSummaryController (req, res, next) {
    try {
      addAgentRevenueSummary(req.body)
      res.status(200).json({ message: 'Agent and Player revenue summary is in progress.' })
    } catch (error) {
      next(error)
    }
  }

  static async botTransactions (req, res, next) {
    try {
      const result = await botTransactionsV2()
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async ggrSummaryCron (req, res, next) {
    try {
      const result = await ggrSummary(req?.body)
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async lotteryGamesUpdation (req, res, next) {
    try {
      const result = await lotteryGamePopulation({ ...req.body }, req.context)
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async checkRollback (req, res, next) {
    try {
      const result = await checkRollbackWithAxios()
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async spinocchioGamesUpdation (req, res, next) {
    try {
      const result = await spinocchioGamePopulation({ ...req.body }, req.context)
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }

  static async cryptoCurrencyConversion (req, res, next) {
    try {
      const result = await cryptoCurrencyConversion()
      res.json({ ...result })
    } catch (error) {
      next(error)
    }
  }
  static async wyntaController (req, res, next) {
    try {
      const data = await createWyntaQueue()
      sendResponse({ req, res, next }, { result: { data: data }, successful: true, serviceErrors: null })
    } catch (error) {
      next(error)
    }
  }
}
