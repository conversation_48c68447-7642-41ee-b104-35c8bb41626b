import express from 'express'
import <PERSON><PERSON><PERSON>roller from '../../../controllers/Job.controller'
import requestValidationMiddleware from '../../../middlewares/requestValidation.middleware'
import responseValidationMiddleware from '../../../middlewares/responseValidation.middleware'

import {
  createJobSchemas
} from '../../../../rest-resources/middlewares/validation/job-validation.schemas'

const jobRoutes = express.Router()
jobRoutes.route('/').post(
  requestValidationMiddleware(createJobSchemas),
  JobController.createJob,
  responseValidationMiddleware(createJobSchemas)
)
jobRoutes.route('/addInQueue').post(
  requestValidationMiddleware(),
  JobController.findJob,
  responseValidationMiddleware()
)

jobRoutes.route('/payInStatus').post(
  requestValidationMiddleware(),
  JobController.payInStatus,
  responseValidationMiddleware()
)

jobRoutes.route('/darwin').post(
  requestValidationMiddleware(),
  JobController.darwinGameUpdation,
  responseValidationMiddleware()
)

 jobRoutes.route('/pgSoft').post(
  requestValidationMiddleware(),
  JobController.pgSoftGamePopulate,
  responseValidationMiddleware()
)

 jobRoutes.route('/st8').post(
  requestValidationMiddleware(),
  JobController.st8GamePopulate,
  responseValidationMiddleware()
)

 jobRoutes.route('/spribe').post(
  requestValidationMiddleware(),
  JobController.spribeGamePopulate,
  responseValidationMiddleware()
)

jobRoutes.route('/funky').post(
  requestValidationMiddleware(),
  JobController.funkyGamesUpdation,
  responseValidationMiddleware()
)
jobRoutes.route('/ezugi').post(
  requestValidationMiddleware(),
  JobController.ezugiGameUpdation,
  responseValidationMiddleware()
)

jobRoutes.route('/whitecliff').post(
  requestValidationMiddleware(),
  JobController.whiteCliffGamesUpdation,
  responseValidationMiddleware()
)

 jobRoutes.route('/lottery777').post(
  requestValidationMiddleware(),
  JobController.lotteryGamesUpdation,
  responseValidationMiddleware()
)

jobRoutes.route('/whitecliff/rawData').post(
  requestValidationMiddleware(),
  JobController.whiteCliffSportsRawdata,
  responseValidationMiddleware()
)

/* jobRoutes.route('/partner-matrix').post(
  requestValidationMiddleware(),
  JobController.partnerMatrixController,
  responseValidationMiddleware()
) */

jobRoutes.route('/update-games').get(
  JobController.updateGamesController
)

jobRoutes.route('/add-provider-in-casino-tables').get(
  JobController.addProviderInCasinoTablesController
)

jobRoutes.route('/reindex-provider-id-transactions').get(
  JobController.addReindexingInTransactionsController
)

jobRoutes.route('/update-games-console').get(
  JobController.updateCategoryController
)

jobRoutes.route('/agent-revenue-summary-update').post(
  requestValidationMiddleware(),
  JobController.addAgentRevenueSummaryController,
  responseValidationMiddleware()
);
jobRoutes.route('/botTransactions').post(
  requestValidationMiddleware(),
  JobController.botTransactions,
  responseValidationMiddleware()
)

jobRoutes.route('/ezugiRollback').post(
  requestValidationMiddleware(),
  JobController.checkRollback,
  responseValidationMiddleware()
)

jobRoutes.route('/spinocchio').post(
  requestValidationMiddleware(),
  JobController.spinocchioGamesUpdation,
  responseValidationMiddleware()
)

// For Testing Purpose only
jobRoutes.route('/cryptoConversion').get(
  requestValidationMiddleware(),
  JobController.cryptoCurrencyConversion,
  responseValidationMiddleware()
)

// jobRoutes.route('/wynta').post(
//   requestValidationMiddleware(),
//   JobController.wyntaController,
//   responseValidationMiddleware()
// )
export default jobRoutes
