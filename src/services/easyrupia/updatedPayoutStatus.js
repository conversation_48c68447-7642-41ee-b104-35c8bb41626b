import db from "../../db/models"
import cancelTransactionLedger from "../../common/pendingPayout/cancelTransactionLedger"
import { Op } from "sequelize"
import { EASY_RUPIA_INTEGRATION_CONSTANT, QUEUE_WORKER_CONSTANT, EASY_RUPIA_STATUS } from '../../common/constants'
import cancelDepositBonus from "../../common/cancelDepositBonus"
import cancelLosingBonus from "../../common/cancelLosingBonus"
import { bulkDataForSmartico } from "../../common/commonFunctions"

const updateWithdrawRequest = async (status, orderId, withdrawRequestId, sequelizeTransaction) => {
  await db.WithdrawRequest.update(
    {
      withdrawal_type: "payment_gateway",
      actioned_at: new Date(),
      status,
      payment_transaction_id: orderId
    },
    {
      where: { id: withdrawRequestId },
      transaction: sequelizeTransaction,
    }
  )
}

const updateTransactionRequest = async (status, comments, transactionId, withdrawalRequest, sequelizeTransaction) => {
  await db.Transaction.update(
    {
      status,
      comments,
      transactionId,
      createdAt: new Date().toISOString(),
    },
    {
      where: {
        id: withdrawalRequest.transactionId,
        tenantId: withdrawalRequest.tenantId,
      },
      transaction: sequelizeTransaction,
    }
  )
}

export default async (withdrawalRequest, transaction, apiRes, sequelizeTransaction) => {
  try {
    const txnIds = []
    const bulkData = []

    const transactionFailedStatus =
      (apiRes.statusCode === EASY_RUPIA_INTEGRATION_CONSTANT.FAILED_STATUS) ||
      (apiRes.status === EASY_RUPIA_STATUS.FAILED)

    const transactionSuccessStatus =
      (apiRes.statusCode === EASY_RUPIA_INTEGRATION_CONSTANT.SUCCESS_STATUS) ||
      (apiRes.status === EASY_RUPIA_STATUS.SUCCESS)

    const transactionPendingStatus =
      (apiRes.statusCode === EASY_RUPIA_INTEGRATION_CONSTANT.PENDING_STATUS) ||
      (apiRes.status === EASY_RUPIA_STATUS.PENDING)

    if (transactionFailedStatus) {
      txnIds.push(withdrawalRequest.transactionId)

      await updateWithdrawRequest('rejected_by_gateway', apiRes.orderId, withdrawalRequest.id, sequelizeTransaction)
      await updateTransactionRequest('cancelled', 'Rejected By payment Gateway', apiRes.orderId, withdrawalRequest, sequelizeTransaction)

      const userWalletMain = await db.Wallet.findOne({
        where: { id: transaction.sourceWalletId, ownerType: "User" },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet,
        },
        skipLocked: false,
      })

      await userWalletMain.reload({
        lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet },
        transaction: sequelizeTransaction,
      })

      userWalletMain.amount += parseFloat(transaction.amount)
      await userWalletMain.save({ transaction: sequelizeTransaction })

      const requestData = {
        transaction: transaction,
        withdrawRequest: withdrawalRequest,
        userWallet: userWalletMain,
      }

      const cancelTrnId = await cancelTransactionLedger(requestData)
      if (cancelTrnId) {
        txnIds.push(cancelTrnId)
      }

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.USER_TRANSACTION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [withdrawalRequest.userId]
      }

      bulkData.push(queueLogObject)

      if (withdrawalRequest.transactionId) {
        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds
        }
        bulkData.push(queueLogObject)
        bulkDataForSmartico(bulkData, withdrawalRequest?.tenantId, false, withdrawalRequest?.transactionId)
        await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })
      }
    }

    if (transactionSuccessStatus) {
      const type = 'payout'
      await cancelLosingBonus(sequelizeTransaction, withdrawalRequest.userId, withdrawalRequest.tenantId, type)
      await cancelDepositBonus(sequelizeTransaction, withdrawalRequest.userId, withdrawalRequest.tenantId, type)

      await updateWithdrawRequest('approved', apiRes.orderId, withdrawalRequest.id)
      await updateTransactionRequest('success', 'Approved By Payment gateway', apiRes.orderId, withdrawalRequest)

      if (withdrawalRequest.transactionId) {
        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds
        }
        bulkData.push(queueLogObject)
        bulkDataForSmartico(bulkData, withdrawalRequest?.tenantId, true, withdrawalRequest?.transactionId)
        await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })
      }
    }
  } catch (error) {
    console.log(
      "====== update easyrupia =======",
      error
    )
    throw error
  }
}
