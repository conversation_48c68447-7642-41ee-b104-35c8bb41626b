import ServiceBase from "../../libs/serviceBase";
import ajv from "../../libs/ajv";
import { BONUS_CLAIM_TYPE, BONUS_INTERVAL_TYPE, bonusType } from "../../utils/constants/constant";
import { CreateJobService } from "../job";
import { v4 as uuidv4 } from 'uuid'
import db, { sequelize } from '../../db/models'
import { ActiveLosingBonusJobs, ActiveLosingBonusJobsQueue } from "../../queues/activeLosingBonusJob.queue";

const schema = {

}

const constraints = ajv.compile(schema)

export class AutomaticLosingBonus extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      Bonus: BonusModel,
      LosingBonusSetting: LosingBonusSettingModel,
      Wallet: WalletModel,
      Transaction: TransactionModel,
      BetsTransaction: BetsTransactionModel,
      LosingBonusTier: LosingBonusTierModel,
      UserBonus: UserBonusModel,
      Currency: CurrencyModel
    } = db


    const losingBonusUnparsed = await BonusModel.findAll({
      where: {
        kind: 'losing',
        enabled: true
      },
      include: {
        model: LosingBonusSettingModel,
        where: {
          bonusClaimType: BONUS_CLAIM_TYPE.AUTOMATIC
        },
        include: {
          model: LosingBonusTierModel
        }
      }
    })

    if (!losingBonusUnparsed.length) {
      return {
        message: 'no user losing bonus'
      }
    }

    const userLosingBonus = await UserBonusModel.findAll({
      where: {
        kind: bonusType.LOSING,
        status: 'active'
      }
    })

    if (!userLosingBonus.length) {
      return {
        message: 'no user losing bonus'
      }
    }

    // create a job
    const jobTime = new Date()
    const uuid = uuidv4().replace(/-/g, '')
    const uniqueId = uuid.substr(uuid.length - 10)

    const queue = ActiveLosingBonusJobsQueue
    const queueName = ActiveLosingBonusJobs

    // create jobs
    if (losingBonusUnparsed) {
      const losingBonusCategorized = losingBonusUnparsed.forEach((cur, idx) => {
        const d = new Date()
        // check if bonus is expired or not
        if (cur.validUpto > d) {
          const type = cur.dataValues.LosingBonusSetting.claimIntervalType

          userLosingBonus.forEach((item) => {
            if (item.bonusId === cur.dataValues.id && item.expiresAt > d) {
              const validFrom = cur.dataValues.validFrom
              const activatedFrom = item.createdAt
              const validTill = cur.dataValues.validUpto
              const today = new Date()

              const timeDiff = today.getTime() - validFrom.getTime()
              const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));

              const claimDays = Math.floor((today.getTime() - activatedFrom.getTime()) / (1000 * 3600 * 24))
              const isAvailable = claimDays < cur.LosingBonusSetting.claimDays
              const isValid = Math.floor((validTill.getTime() - today.getTime()) / (1000 * 3600 * 24))

              let flag = false

              const queueParams = {}
              queueParams.time = jobTime
              queueParams.bonusType = type
              queueParams.bonusId = cur.id
              queueParams.userId = item.userId

              const yesterday = new Date(today)
              yesterday.setDate(today.getDate() - 1)
              yesterday.setHours(0, 0, 0, 0)

              queueParams.from = (daysDiff <= 1) ? item.createdAt : yesterday
              queueParams.to = today
              queueParams.isClaimed = !(isValid > 0)

              if (type === BONUS_INTERVAL_TYPE.DAILY) {
                flag = true
              }
              if (type === BONUS_INTERVAL_TYPE.WEEKLY && daysDiff % 7 === 0) {
                flag = true
              }
              if (type === BONUS_INTERVAL_TYPE.BIWEEKLY && daysDiff % 14 === 0) {
                flag = true
              }
              if (type === BONUS_INTERVAL_TYPE.MONTHLY && daysDiff % 30 === 0) {
                flag = true
              }

              if (flag) {
                queue.add(queueName,
                  queueParams,
                  {
                    jobId: `${cur.id}_${queueName}_${jobTime}_${item.userId}`,
                    removeOnComplete: true,
                    delay: 5
                  })
              }
            }
          })
        }
        return cur
      })
    }

    return losingBonusUnparsed
  }
}
