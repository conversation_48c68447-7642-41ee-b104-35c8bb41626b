import ServiceBase from "../../libs/serviceBase"
import ajv from "../../libs/ajv"
import { BONUS_CLAIM_TYPE, BONUS_INTERVAL_TYPE, bonusType } from "../../utils/constants/constant"
import { CreateJobService } from "../job"
import { v4 as uuidv4 } from 'uuid'
import db, { sequelize } from '../../db/models'
import { ActiveLosingBonusJobs, ActiveLosingBonusJobsQueue } from "../../queues/activeLosingBonusJob.queue"
import { Op, Sequelize } from 'sequelize'

const schema = {

}

const constraints = ajv.compile(schema)

export class AutomaticActiveLosingBonus extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      Bonus: BonusModel,
      LosingBonusSetting: LosingBonusSettingModel,
      UserBonus: UserBonusModel
    } = db

    const userBonuses = await UserBonusModel.findAll({
      attributes: ['userId'],
      where: {
        kind: bonusType.LOSING,
        status: 'active',
        expiresAt: { [Op.gt]: Sequelize.literal('CURRENT_TIMESTAMP') }
      },
      include: {
        model: BonusModel,
        attributes: ['validFrom', 'id', 'validUpto'],
        where: {
          kind: bonusType.LOSING,
          enabled: true,
          validUpto: { [Op.gt]: Sequelize.literal('CURRENT_TIMESTAMP') },
          validFrom: { [Op.lte]: Sequelize.literal('CURRENT_TIMESTAMP') }
        },
        required: true,
        include: [
          {
            model: LosingBonusSettingModel,
            attributes: ['weekDay', 'claimIntervalType'],
            where: {
              bonusClaimType: BONUS_CLAIM_TYPE.AUTOMATIC_ACTIVE
            },
            required: true,
          }
        ]
      },
      required: true
    })

    if (!userBonuses.length) {
      return {
        message: 'no user losing bonus found'
      }
    }

    // Function to find the first next occurrence of a specified weekday after a given date
    function findFirstNextWeekdayFromDate (date, weekday) {
      const startDate = new Date(date)
      const startWeekday = startDate.getDay()
      let dayDifference = weekday - startWeekday
      if (dayDifference < 0) dayDifference += 7
      const nextWeekdayDate = new Date(startDate)
      nextWeekdayDate.setDate(startDate.getDate() + dayDifference)

      return nextWeekdayDate
    }

    userBonuses.forEach(userBonus => {
      const today = new Date()
      const weekdaysMap = {
        sunday: 0, monday: 1, tuesday: 2, wednesday: 3, thursday: 4, friday: 5, saturday: 6
      }
      const specifiedWeekdayNum = weekdaysMap[userBonus?.Bonus?.LosingBonusSetting?.weekDay?.toLowerCase()]

      if (today.getDay() === specifiedWeekdayNum) {
        const firstComingDate = findFirstNextWeekdayFromDate((userBonus.Bonus.validFrom), specifiedWeekdayNum)

        const diffDays = Math.floor((today.getTime() - firstComingDate.getTime()) / (1000 * 60 * 60 * 24))
        let fromDate, toDate, flag

        if (today.getDate() === firstComingDate.getDate()) {
          fromDate = (userBonus.Bonus.validFrom)
          toDate = today
          flag = true
        }

        else if ((userBonus.Bonus.LosingBonusSetting.claimIntervalType === BONUS_INTERVAL_TYPE.BIWEEKLY) && (diffDays % 14 === 0)) {
          fromDate = new Date(today)
          fromDate.setDate(today.getDate() - 14)
          toDate = today
          flag = true
        }

        else if ((userBonus.Bonus.LosingBonusSetting.claimIntervalType === BONUS_INTERVAL_TYPE.WEEKLY) && (diffDays % 7 === 0)) {
          fromDate = new Date(today)
          fromDate.setDate(today.getDate() - 7)
          toDate = today
          flag = true
        }

        if (flag) {
          // create a job
          const jobTime = new Date()
          const uuid = uuidv4().replace(/-/g, '')
          const uniqueId = uuid.substr(uuid.length - 10)
          const queue = ActiveLosingBonusJobsQueue
          const queueName = ActiveLosingBonusJobs
          const queueParams = {}
          queueParams.time = jobTime
          queueParams.bonusType = userBonus.Bonus.LosingBonusSetting.claimIntervalType
          queueParams.bonusId = userBonus.Bonus.id
          queueParams.userId = userBonus.userId
          queueParams.from = fromDate
          queueParams.to = toDate
          const validTill = userBonus.Bonus.validUpto
          const isValid = Math.floor((validTill.getTime() - today.getTime()) / (1000 * 3600 * 24))
          queueParams.isClaimed = !(isValid > 0)

          queue.add(queueName,
            queueParams,
            {
              jobId: `${userBonus.Bonus.id}_${queueName}_${jobTime}_${userBonus.userId}`,
              removeOnComplete: true,
              delay: 5
            })
        }
      }
    })
  }
}
