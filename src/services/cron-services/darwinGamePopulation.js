import axios from 'axios'
import { Op, QueryTypes } from 'sequelize'
import { DARWIN_ICON, PROD_DARWIN_PROVIDER, STAGE_DARWIN_PROVIDER } from '../../common/constants'
import config from '../../configs/app.config'
import db, { sequelize } from '../../db/models'
import Logger from '../../libs/logger'
import ServiceBase from '../../libs/serviceBase'

export class DarwinGamePopulate extends ServiceBase {

  async run () {
    const sequelizeTransaction = await sequelize.transaction()

    try {
      const {
        TenantCredential: TenantCredentialModel,
        CasinoGame: CasinoGameModel,
        CasinoTable: CasinoTableModel,
        CasinoItem: CasinoItemModel,
        Page: PageModel,
        CasinoMenu: CasinoMenuModel,
        PageMenu: PageMenuModel,
        MenuItem: MenuItemModel
      } = db

      const casinoProviderId = config.get('env') === 'production' ? PROD_DARWIN_PROVIDER : STAGE_DARWIN_PROVIDER

      let tenantIds = await sequelize.query(`
        SELECT "tenant_id" AS "tenantId", ( SELECT "id" FROM "menu_tenant_setting" WHERE "menu_id" = (SELECT "id" FROM "menu_master" WHERE "name" = 'Casino')
        AND "tenant_id" = "TenantThemeSetting"."tenant_id" ) AS "casinoId"
        FROM "public"."tenant_theme_settings" AS "TenantThemeSetting" WHERE '${casinoProviderId}' = ANY (string_to_array(assigned_providers, ','))`,
        { type: QueryTypes.SELECT, useMaster: false })

        let superAdminMenuId = await sequelize.query(`SELECT "id" FROM "menu_master" WHERE "name" = 'Casino'`,
          { type: QueryTypes.SELECT, useMaster: false })
        // Temperory COMMIT for super admin seeding
        // tenantIds = [...tenantIds, { tenantId: 0, topCasinoId: superAdminMenuId[0].id }]
        tenantIds = [ { tenantId: 0, topCasinoId: superAdminMenuId[0].id }]


        for (let i = 0; i < tenantIds.length; i++){
          const tenantId = tenantIds[i].tenantId
          const topCasinoId = tenantIds[i].casinoId


      const creds = await TenantCredentialModel.findAll({
        attributes: ['key', 'value'],
        where: {
          key: ['DARWIN_GAME_LIST_URL'],
          tenantId
        },
        raw: true
      })

      // Getting data from the credentials table
      const {
        DARWIN_GAME_LIST_URL
      } = creds.reduce((acc, cur) => {
        return {
          ...acc,
          [cur.key]: cur.value
        }
      }, { DARWIN_GAME_LIST_URL: null })

      const currentDate = new Date().toISOString()

      const darwinGameData = await axios({
        url: `${DARWIN_GAME_LIST_URL}`,
        method: 'get',
        maxBodyLength: Infinity,
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (darwinGameData?.data === null) {
        throw new Error('darwinWorker: The data to be inserted in not present')
      }

      // Data for a page model
      const pageData = {
        title: 'darwin',
        enabled: true,
        order: null,
        createdAt: currentDate,
        updatedAt: currentDate,
        tenantId,
        topMenuId: topCasinoId,
        enableInstantGame: null,
        image: DARWIN_ICON
      }

      /**
      * Dataobject that returns the followin object
      *
      * @Object category @table casino_games
      * @object casinoTable @table casino_table
      * @object tableItems @table casino_item
      * */

      const darwinGameDataObject = darwinGameData?.data?.reduce(
        (acc, cur) => {
          if (!acc.category.length || !acc.category.includes(cur.type)) {
            acc.category.push(cur.type)
          }

          acc.casinoTable.push({
            name: cur.name,
            gameId: `${cur.type}`,
            createdAt: currentDate,
            updatedAt: currentDate,
            isLobby: true,
            tableId: '' + cur.identifier,
            providerId: casinoProviderId
          })

          acc.tableIds.push('' + cur.identifier)
          acc.name.push(cur.name)
          acc.tableItems.push({
            uuid: '' + cur.identifier,
            name: cur.name,
            image: `provider-images/darwin/thumbnail/${cur.identifier}.webp`,
            provider: casinoProviderId,
            active: cur.available,
            featured: true,
            createdAt: currentDate,
            updatedAt: currentDate,
            tenantId
          })

          return acc
        }, { category: [], casinoTable: [], tableIds: [], tableItems: [], name: [], menuItem: [] })

      // Object to be updated in casino games, if it does'nt already exists
      const dataToBeUpdatedIntoCasinoGames = darwinGameDataObject.category.map((item, idx) => {
        return {
          name: `${item}`,
          casinoProviderId,
          createdAt: currentDate,
          updatedAt: currentDate,
          gameId: `${item}`
        }
      })

      // Object to create or update into casino menu specific to a tenant
      const casinoMenuDataToBeUpdated = darwinGameDataObject.category.map((item, idx) => {
        return {
          name: `${item}`,
          menucategory: null,
          menuOrder: null,
          enabled: true,
          createdAt: currentDate,
          updatedAt: currentDate,
          tenantId,
          imageUrl: DARWIN_ICON
        }
      })

      // get existing data in CasinoGame table
      const casinoGamesData = await CasinoGameModel.findAll({
        attributes: ['gameId', 'id'],
        where: {
          gameId: {
            [Op.in]: dataToBeUpdatedIntoCasinoGames.map(item => item.gameId)
          }
        },
        raw: true
      })

      /**
       * @object name: casinoTableData
       * @gets the already existed data in the
       * @table CasinoTableModel
       */

      const casinoTableData = await CasinoTableModel.findAll({
        attributes: ['gameId', 'tableId', 'name'],
        where: {
          gameId: {
            [Op.in]: darwinGameDataObject.category.map(item => `${item}`)
          },
          providerId: casinoProviderId
        },
        raw: true
      })

      /**
       * @object name pagesData
       * @gets the already existing data in
       * @pages table
       */

      const pagesData = await PageModel.findAll({
        attributes: ['id', 'title', 'topMenuId'],
        where: {
          tenantId,
          title: 'darwin',
          topMenuId: topCasinoId
        },
        raw: true
      })

      /**
     * @object casinoItemData
     * @gets the data from table
     * @casinoItemModel
     */
      const casinoItemData = await CasinoItemModel.findAll({
        attributes: ['uuid', 'name', 'id', 'active'],
        where: {
          uuid: {
            [Op.in]: darwinGameDataObject.tableIds
          },
          name: {
            [Op.in]: darwinGameDataObject.name
          },
          tenantId
        },
        raw: true
      })

      /**
       * @object casinoMenuData
       * gets the data from table
       * @casinoMenu
       */

      const casinoMenuData = await CasinoMenuModel.findAll({
        attributes: ['name', 'id'],
        where: {
          tenantId,
          name: {
            [Op.in]: casinoMenuDataToBeUpdated.map(i => i.name)
          }
        },
        raw: true
      })

      // check update the casinoGamesData object responsible to maintain category based on new data
      const updatedDataToBeUpdatedIntoCasinoGames = dataToBeUpdatedIntoCasinoGames.filter(i => !casinoGamesData.map(i => i.gameId).includes(i.gameId))

      // update the casinoTableData object based on data from casinoTable table
      const updatedCasinoTableData = darwinGameDataObject.casinoTable.filter(i => !casinoTableData.map(i => i.tableId).includes(i.tableId))

      // update casinoItemData object based on data from casinoItem table
      const updatedCasinoItemData = darwinGameDataObject.tableItems.filter(i => !casinoItemData.map(i => i.uuid).includes(i.uuid))

      // existing data with diffrent active status from casinoItem table
      const existingCasinoItemData = casinoItemData.filter(i => darwinGameDataObject.tableItems.some(item => item.uuid === i.uuid && item.active !== i.active))

      Promise.all(existingCasinoItemData.map(async item =>{
        await CasinoItemModel.update({ active: !item.active }, { where: { id: item.id }, transaction: sequelizeTransaction })
        await MenuItemModel.update({ active: !item.active }, { where: { casinoItemId: item.id }, transaction: sequelizeTransaction })}
      ))
      // if pagesData doesnt exists then create the pageData
      const updatedPageData = pagesData.length ? pagesData : pageData

      // const updatedCasinoMenuData = casinoMenuData.length? casinoMenuData: casinoMenuDataToBeUpdated
      const updatedCasinoMenuData = casinoMenuDataToBeUpdated.filter(i => !casinoMenuData.map(i => i.name).includes(i.name))
      // Logger.info(`casinoTableData: ${JSON.stringify(updatedCasinoTableData)}`)

      // insert updated casino game data to table

      const insertedCasinoGamesData = await CasinoGameModel.bulkCreate(updatedDataToBeUpdatedIntoCasinoGames, { transaction: sequelizeTransaction })

      Logger.info(`insertedCasinoGamesData: ${insertedCasinoGamesData}`)

      const insertedCasinoTableData = await CasinoTableModel.bulkCreate(updatedCasinoTableData, { transaction: sequelizeTransaction })

      Logger.info(`insertedCasinoTableData: ${insertedCasinoTableData}`)

      const insertedCasinoItemData = await CasinoItemModel.bulkCreate(updatedCasinoItemData, { transaction: sequelizeTransaction })

      Logger.info(`insertedCasinoItemData: ${insertedCasinoItemData}`)

      const insertedPageData = !updatedPageData.length && await PageModel.create(updatedPageData, { transaction: sequelizeTransaction })

      Logger.info(`insertedPageData: ${insertedPageData}`)

      const insertedCasinoMenuData = await CasinoMenuModel.bulkCreate(updatedCasinoMenuData, { transaction: sequelizeTransaction })

      Logger.info(`insertedCasinoMenuData: ${insertedCasinoMenuData}`)

      const casinoGames = dataToBeUpdatedIntoCasinoGames.length ? dataToBeUpdatedIntoCasinoGames : insertedCasinoGamesData
      const pageId = pagesData.length ? pagesData[0].id : insertedPageData.id

      const casinoMenus = [
        ...casinoMenuData,
        ...insertedCasinoMenuData.map(item => ({ name: item.dataValues.name, id: item.dataValues.id }))
      ]

      const casinoTable = casinoTableData.length ? casinoTableData : insertedCasinoTableData

      const pageMenuDataToBeInserted = darwinGameDataObject.category.map((item, idx) => {
        return {
          pageId,
          casinoMenuId: casinoMenus[idx].id,
          name: `${item}`,
          menuOrder: null,
          createdAt: currentDate,
          updatedAt: currentDate
        }
      })

      const pageMenuData = await PageMenuModel.findAll({
        attributes: ['id', 'pageId', 'casinoMenuId', 'name'],
        where: {
          pageId
        },
        raw: true
      })

      // const updatedCasinoPageMenuData = pageMenuDataToBeInserted.filter(i => !pageMenuData.map(ii => ii.name).includes(i.name))
      const updatedCasinoPageMenuData = pageMenuDataToBeInserted.filter(i =>
        !pageMenuData.some(ii => ii.name === i.name && ii.casinoMenuId === i.casinoMenuId)
      )

      const insertedCasinoPageMenuData = updatedCasinoPageMenuData.length && await PageMenuModel.bulkCreate(updatedCasinoPageMenuData, { transaction: sequelizeTransaction })
      const casinoItem = casinoItemData.length ? casinoItemData : insertedCasinoItemData

      const pageMenu = (updatedCasinoPageMenuData.length === 0)
        ? pageMenuData
        : [
            ...pageMenuData,
            ...insertedCasinoPageMenuData.map(item => ({ name: item.dataValues.name, id: item.dataValues.id, casinoMenuId: item.dataValues.casinoMenuId, pageId: item.dataValues.pageId }))
          ]

      const menuItemsDataToBeInserted = casinoItem.map(item => {
        const findObj = casinoTable.find((i) => (i.name === item.name || i.uuid === item.tableId))
        const gameId = findObj.gameId
        const gameName = casinoGames.find(i => i.gameId === gameId).name
        const casinoMenuId = casinoMenus.find(i => i.name === gameName).id
        const pmId = pageMenu.find(i => i.casinoMenuId === casinoMenuId).id

        return {
          pageMenuId: pmId,
          casinoItemId: item.id,
          name: item.name,
          order: null,
          active: item.active,
          featured: true,
          createdAt: currentDate,
          updatedAt: currentDate,
          popular: true
        }
      })

      Logger.info(`menuItemsDataToBeInserted: ${menuItemsDataToBeInserted}`)

      const menuItemData = await MenuItemModel.findAll({
        attributes: ['id', 'pageMenuId', 'casinoItemId', 'name', 'active'],
        where: {
          casinoItemId: {
            [Op.in]: casinoItemData.map(i => i.id)
          }
        }
      })

      const updatedMenuItemsData = menuItemsDataToBeInserted.filter(i =>
        !menuItemData.some(ii => ii.casinoItemId === i.casinoItemId && ii.pageMenuId === i.pageMenuId)
      )


      const insertedMenuItemData = updatedMenuItemsData.length && await MenuItemModel.bulkCreate(updatedMenuItemsData, { transaction: sequelizeTransaction })

      Logger.info(`insertedMenuItemData: ${insertedMenuItemData}`)

       // disable the category if all games in that category are inactive
       const categoriesToDisable = await sequelize.query(
        ` UPDATE casino_menus cm
          SET enabled = subquery.result
          FROM (
            SELECT
              ct.game_id AS game_category,
              CASE
                WHEN COUNT(*) = COUNT(CASE WHEN ci.active = false THEN 1 END) THEN false
                ELSE true
              END AS result
            FROM
              casino_tables ct
            JOIN
              casino_items ci ON ct.table_id = ci.uuid
            WHERE
              ci.provider = '${STAGE_DARWIN_PROVIDER}'
              AND ci.tenant_id = '${tenantId}' AND ct.provider_id = '${STAGE_DARWIN_PROVIDER}'
            GROUP BY
              ct.game_id
          ) AS subquery
          WHERE cm.name = subquery.game_category AND cm.tenant_id = '${tenantId}';
        `

      );

    }
      await sequelizeTransaction.commit()
      return {
        success: true
      }
    } catch (error) {
      Logger.info(error, '========DARWIN game population error======')
      sequelizeTransaction.rollback()
      return {
        success: false,
        Error: {
          stack: error.stack
        }
      }
    }
  }
}
