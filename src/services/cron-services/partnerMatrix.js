import axios from 'axios';
import { Client as FTPClient } from "basic-ftp";
import * as moment from 'moment-timezone';
import { EXPORT_CSV_STATUS, PM_USER_LIMIT, PROD_TENANTS, STAGE_TENANTS, partnerMatrixFileName } from "../../common/constants";
import getPlayerRevenueReportDataRealTimeSyncV2 from '../../common/csv/reports/getPlayerRevenueReportDataRealTimeSyncV2';
import config from "../../configs/app.config";
import db, { sequelize } from '../../db/models';
import ajv from "../../libs/ajv";
import { s3 } from '../../libs/awsS3Config';
import ServiceBase from "../../libs/serviceBase";
const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')

const CSV_TYPES = {
  SALES: 'sales',
  REG: 'reg'
}

const schema = {
  type: 'object',
  properties: {
    tenantId: { type: 'number' },
    type: { enum: Object.values(CSV_TYPES) },
    queueId: { type: 'number' },
    csvDetail: { type: ['null', 'object'] }
  }
}

const constraints = ajv.compile(schema)

const convertToUTC = (inputDate, inputTimezone) => {
  const date = moment.tz(inputDate, inputTimezone);
  const utcDate = date.utc().format('YYYY-MM-DD HH:mm:ss');
  return utcDate;
}

const uploadToFTP = async (filePath, fileName, tenantId) => {
  const client = new FTPClient()
  client.ftp.verbose = true

  try {
    const tenants = config.get('env') === 'development' ? STAGE_TENANTS : PROD_TENANTS

    let host, user, password;
    if (tenants[tenantId].name == 'jeeto555') {
      host = config.get('partnerMatrix.host')
      user = config.get('partnerMatrix.user')
      password = config.get('partnerMatrix.password')
    }
    else if (tenants[tenantId].name == 'AceXBet365') {
      host = config.get('partnerMatrixAceXBet.host')
      user = config.get('partnerMatrixAceXBet.user')
      password = config.get('partnerMatrixAceXBet.password')
    }

    await client.access({
      host: host,
      user: user,
      password: password,
      secure: false
    })

    await client.uploadFrom(filePath, '/' + fileName)
    await client.close()
  }
  catch (e) {
    throw new Error(e)
  }
}

const uploadToS3Bucket = async (filePath, fileName, tenantId, type) => {
  const s3Config = config.getProperties().s3
  const fileContent = await fs.promises.readFile(filePath);
  const key = `tenants/${tenantId}/csv/${type}/${fileName}`
  const s3Params = {
    ACL: 'public-read',
    Bucket: s3Config.bucket,
    Key: key,
    Body: fileContent
  }

  const uploadedFile = await s3.upload(s3Params).promise()
  return uploadedFile
}

const getFileFromS3Bucket = async (file, fileName) => {
  const response = await axios.get(file, { responseType: 'arraybuffer' });
  const fileData = Buffer.from(response.data, 'binary');
  fs.writeFileSync(`/tmp/${fileName}`, fileData)
}

const getHeaders = (type) => {
  if (type === CSV_TYPES.REG) {
    return [
      { id: 'btag', title: 'BTAG' },
      { id: 'brand', title: 'BRAND' },
      { id: 'createdAt', title: 'ACCOUNT_OPENING_DATE' },
      { id: 'id', title: 'PLAYER_ID' },
      { id: 'userName', title: 'USERNAME' },
      { id: 'countryCode', title: 'COUNTRY' },
      { id: 'signInIp', title: 'PLAYER_IP' },
    ]
  } else {
    return [
      { id: 'btag', title: 'BTAG' },
      { id: 'brand', title: 'BRAND' },
      { id: 'transactionDate', title: 'TRANSACTION_DATE' },
      { id: 'playerId', title: 'PLAYER_ID' },
      { id: 'playerIP', title: 'PLAYER_IP' },
      { id: 'currency', title: 'CURRENCY' },
      { id: 'chargeback', title: 'Chargeback' },
      { id: 'deposits', title: 'DEPOSITS' },
      { id: 'depositCount', title: 'DEPOSITS_Count' },
      { id: 'casinoBets', title: 'CASINO_Bets' },
      { id: 'casinoRevenue', title: 'CASINO_revenue' },
      { id: 'casinoBonuses', title: 'CASINO_bonuses' },
      { id: 'casinoStake', title: 'CASINO_stake' },
      { id: 'casinoNGR', title: 'CASINO_NGR' },
      { id: 'sportsBonuses', title: 'SPORTS_BONUSES' },
      { id: 'sportsRevenue', title: 'SPORTS_REVENUE' },
      { id: 'sportsBets', title: 'SPORTS_BETS' },
      { id: 'sportsStake', title: 'SPORTS_STAKE' },
      { id: 'sportsNGR', title: 'SPORTS_NGR' },
    ]
  }
}

const getTransactionData = async (startTime, endTime, tenantId, tenantDetails, utcEndTime, utcStartTime, transactionDateFileName,csvDetails) => {
  const casinoData = await getTransactionDataCallDb(csvDetails.payload,'casino')
  const sportsData = await await getTransactionDataCallDb(csvDetails.payload, 'sports')


  let updatedDate = []

  const date = new Date(transactionDateFileName)
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const formattedCreatedAt = `${year}-${month}-${day}`

  const casinoUpdatedData = casinoData.reduce((acc, cur) => {
    if (cur?.affiliated_data) {
      const d = {
        brand: tenantDetails.name,
        // btag: `${tenantId}-${cur?.top?.hits?.hits[0]?._source?.player_details?.player_id}`,
        btag: cur?.affiliated_data,
        key: cur?.user_name+"-"+ cur?.code+"-"+tenantDetails.name,
        transactionDate: formattedCreatedAt,
        playerId: cur?.user_id,
        playerIP: '',
        currency: cur?.code,
        chargeback: 0,
        depositCount: cur?.deposit_count,
        deposits: cur?.total_deposit,
        casinoBets: cur?.bet_after_refund_count,
        casinoRevenue: cur?.ggr,
        casinoBonuses: cur?.bonus,
        casinoStake:  cur?.bet_after_refund,
        casinoNGR: cur?.ngr
      }
      return [...acc, d]
    }
    return acc
  }, [])


  const sportsUpdatedData = sportsData.reduce((acc, cur) => {

    if (cur?.affiliated_data) {
      const d = {
        brand: tenantDetails.name,
        btag: cur?.affiliated_data,
        transactionDate: formattedCreatedAt,
        playerId: cur?.user_id,
        playerIP: '',
        currency: cur?.code,
        chargeback: 0,
        key: cur?.user_name+"-"+ cur?.code+"-"+tenantDetails.name,
        sportsBets: cur?.bet_after_refund_count,
        sportsRevenue: cur?.ggr,
        sportsBonuses: cur?.bonus,
        sportsStake: cur?.bet_after_refund,
        sportsNGR: cur?.ngr

      }
      return [...acc, d]
    }
    return acc
  }, [])

  const joinedArray = casinoUpdatedData.map(item1 => {
    const matchingId = sportsUpdatedData.find(item2 => item2.key === item1.key) || {
      sportsBets: 0,
      sportsRevenue: 0,
      sportsBonuses: 0,
      sportsStake: 0,
      sportsNGR: 0
    }
    return { ...item1, ...matchingId }
  })

  const joinedSportArray = sportsUpdatedData.map(item1 => {
    const exists = joinedArray.some(item => item.key === item1.key)
    if (!exists) {
      item1.depositCount = 0
      item1.deposits = 0
      item1.casinoBets = 0
      item1.casinoRevenue = 0
      item1.casinoBonuses = 0
      item1.casinoStake = 0
      item1.casinoNGR = 0
      return item1
    }
  }).filter(item => item !== undefined)

  if (joinedSportArray.length > 0) {
    const mergedArray = [...joinedArray, ...joinedSportArray]
    return mergedArray
  }

  return joinedArray
}

const getUserData = async (UserModel, UserLoginHistoryModel, tenantId, tenantDetails, startTime, endTime) => {
  let users = []
  let offset = 0
  let usersBtach
  let whereCondition = ''

  if (startTime && endTime) {
    whereCondition = `(("User"."created_at" BETWEEN '${startTime}' and '${endTime}' ))`
  } else if (endTime) {
    whereCondition = `(("User"."created_at" <= '${endTime}'))`
  }
  do {

    const query = `
      SELECT
        "User"."id",
        "User"."created_at" AS "createdAt",
        "User"."user_name" AS "userName",
        "User"."country_code" AS "countryCode",
        "User"."affiliated_data" AS "btag",
        "UserLoginHistory"."ip" AS "signInIp"
      FROM
        "public"."users" AS "User"
      LEFT JOIN
      (
        SELECT "ip", "user_id", ROW_NUMBER() OVER (PARTITION BY "user_id" ORDER BY "updated_at" DESC) AS "rn" FROM "public"."user_login_history"
      ) as "UserLoginHistory"
      ON "User"."id" = "UserLoginHistory"."user_id" and "UserLoginHistory"."rn" = 1
      wHERE
        tenant_id = ${tenantId} AND
        active = true AND
          ${whereCondition} AND
        "User"."affiliated_data" !=''
      ORDER BY updated_at DESC
      limit ${PM_USER_LIMIT}
      offset ${offset}
    `
    usersBtach = await sequelize.query(query, {
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    })

    offset += 2000
    users = [...users, ...usersBtach]
  }
  while (usersBtach.length)

  const updatedUser = users.map(i => {
    const date = new Date(i.createdAt)
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    const formattedCreatedAt = `${year}-${month}-${day}`

    return {
      ...i,
      brand: tenantDetails.name,
      // btag: `${tenantId}-${i.id}`,
      // btag: 274938,
      createdAt: formattedCreatedAt
    }
  })

  return updatedUser
}

const deleteFile = async (filePath) => {
  fs.unlink(filePath, (err) => {
    if (err) {
      throw err
    }
  });
}

const writeCsvFile = async (filePath, data, headers) => {
  const csvWriter = createCsvWriter({
    path: filePath,
    header: headers
  });

  await csvWriter.writeRecords(data);
}

const getTransactionDataCall = async (startTime, endTime, tenantId, actionCategory, utcEndTime, utcStartTime) => {
  const url = config.get('partnerMatrix.API')
  const requestBody = {
    method: 'post',
    url,
    headers: {
      'Content-Type': 'application/json'
    },
    data: {
      size: 10,
      page: 1,
      search: "",
      agent_id: "",
      currency: "",
      player_type: "all",
      isDirectPlayer: "",
      time_zone: "UTC +05:30",
      time_zone_name: "Asia/Kolkata",
      tenant_id: tenantId,
      owner_id: "",
      action_type: "",
      game_provider: "",
      game_type: "",
      table_id: "",
      time_type: "custom",
      datetime: {
        start_date: utcStartTime,
        end_date: utcEndTime
      },
      time_period: {
        start_date: startTime,
        end_date: endTime
      },
      secret_token: config.get('queue_job.secretToken'),
      action_category: actionCategory
    }
  };
  try {
    const { data } = await axios(requestBody);
    await db.RequestResponseLog.create({
      requestJson: requestBody,
      responseJson: data,
      service: 'queue-partner-matrix',
      url: url,
      responseCode: data?.response?.status,
      tenantId: tenantId
    })
    return data
  } catch (e) {
    const { response } = e;
    await db.RequestResponseLog.create({
      requestJson: requestBody,
      responseJson: response?.data,
      service: 'queue-partner-matrix',
      url: url,
      responseCode: response?.status,
      tenantId: tenantId
    })
    throw e
  }
}

const getTransactionDataCallDb = async (data,actionCategory) => {
  try {



    let payload =  {
      "search": "",
      "ggr_ngr": "",
      "agent_id": "",
      "currency": "",
      "datetime": {
          "end_date": data.endTime,
          "start_date": data.startTime,
      },
      "owner_id": "",
      "game_type": "",
      "tenant_id": data.tenantId,
      "time_type": "real-time-sync",
      "time_zone": "UTC +00:00",
      "playerType": "all_players",
      "player_type": "all",
      "sbo_request": false,
      "time_period": {
          "end_date": data.utcEndTime,
          "start_date":data.utcStartTime,
      },
      "game_provider": "",
      "isDirectPlayer": "",
      "time_zone_name": "UTC +00:00",
      "action_category": actionCategory
  }

    let botUserJoin = ''
    let botUserSelect = ''
    const checkSboRequest = false
    let isSuperAdmin = false;
    // ==================== params gathering starts here ====================
    const params = {
      tenantId: 0,
      search: payload?.search ? payload?.search : '',
      // search_by_id: payload?.search_by_id ? payload?.search_by_id : '',
      currency_id: payload?.currency ? payload?.currency : '',
      date_range: payload?.date_range ? payload?.date_range : [],
      sort_by: payload?.sort_by ? payload?.sort_by : 'id',
      order: payload?.order ? payload?.order : 'desc',
      action_category: payload?.action_category,
      // sports_items: payload?.sports_items,
      game_type: payload?.game_type,
      // providers: payload?.providers,
      game_provider: payload?.game_provider,
      ggr_ngr: payload?.ggr_ngr
    }

    // Tenant Filter Gathering
    const tenantId = Number(payload.tenant_id)
    params.tenantId = [tenantId]
    isSuperAdmin = true

    // Agent Filter Gathering
    params.agentId = 0;
    const agentId = Number(payload.agent_id)



    // ==================== params gathering ends here ====================

    // ====================filters here ========================
    let users = [];
    payload.isSuperAdmin = true
    payload.playerCategory = "all_players"
    payload.checkSboRequest = checkSboRequest


      users = await getPlayerRevenueReportDataRealTimeSyncV2(payload, params, data);


    return users
  } catch (e) {
    return false
  }
}
export default class PartnerMatrix extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      User: UserModel,
      ExportCsvCenter: ExportCsvCenterModel,
      UserLoginHistory: UserLoginHistoryModel
    } = db

    const {
      tenantId,
      type,
      queueId,
      csvDetail
    } = this.args

    // get export center queue
    const exportCenterQueue = await ExportCsvCenterModel.findOne({
      where: {
        id: queueId
      }
    })

    if (!exportCenterQueue) {
      throw new Error("QueueLog doesn't exists")
    }

    try {
      const tenants = config.get('env') === 'development' ? STAGE_TENANTS : PROD_TENANTS
      const tenantDetails = tenants[tenantId]
      let data, fileName, filePath
      let startTime = new Date(exportCenterQueue?.payload?.startTime) || new Date();

      if (startTime < new Date('2024-07-18 18:30:00')) {
        startTime = new Date(exportCenterQueue?.payload?.endTime) || new Date();
      }
      const transactionDateFileName = startTime;

      // if csvUrl doesn't exists then create csv and upload it to s3 bucket
      if (!exportCenterQueue.csvUrl) {
        // get headers depending upon type
        const headers = getHeaders(type)

        // get file name of the said csv
        fileName = partnerMatrixFileName(
          tenantDetails.name,
          type,
          `${transactionDateFileName.getFullYear()}${String(transactionDateFileName.getMonth() + 1).padStart(2, '0')}${String(transactionDateFileName.getDate()).padStart(2, '0')}`
        )

        filePath = `/tmp/${fileName}`
        if (!exportCenterQueue?.payload?.startTime)
          exportCenterQueue.payload.startTime = '2024-03-01 00:00:00.0000';

        // fetch the data for given type
        if (type === CSV_TYPES.REG)
          data = await getUserData(UserModel, UserLoginHistoryModel, tenantId, tenantDetails, exportCenterQueue.payload.utcStartTime, exportCenterQueue.payload.utcEndTime)
        else
          data = await getTransactionData(
            exportCenterQueue.payload.startTime,
            exportCenterQueue.payload.endTime,
            tenantId,
            tenantDetails,
            exportCenterQueue.payload.utcEndTime,
            exportCenterQueue.payload.utcStartTime,
            transactionDateFileName,
            csvDetail
          )

        // create the csv file and store it in remote location
        await writeCsvFile(filePath, data, headers)

        // upload it to S3 bucket
        const uploadedFile = await uploadToS3Bucket(filePath, fileName, tenantId, 'users')

        // update the export center queue details
        exportCenterQueue.csvUrl = uploadedFile.Location || ''
        exportCenterQueue.status = EXPORT_CSV_STATUS.DONE
        await exportCenterQueue.save()
      }
      // if csvUrl already exists i.e. retrying the queue
      else {
        // get the file name and file path from csvUrl
        fileName = exportCenterQueue.csvUrl.split('/')[exportCenterQueue.csvUrl.split('/').length - 1]
        filePath = `/tmp/${fileName}`

        // get the stored csv file from s3 bucket
        await getFileFromS3Bucket(exportCenterQueue.csvUrl, fileName, tenantId, type)
      }

      // upload the csv to FTP
      const queueProcessFtpStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'partner-matrix-ftp-cron',
          status: 1
        },
        attributes: ['id']
      })
      if (queueProcessFtpStatus) {
        await uploadToFTP(filePath, fileName, tenantId)
      }

      // delete the local file after upload
      await deleteFile(filePath)

      return { data: true }
    }
    catch (e) {
      exportCenterQueue.status = EXPORT_CSV_STATUS.FAILED
      await exportCenterQueue.save()
      throw new Error(e)
    }
  }
}
