import axios from 'axios'
import { Op } from 'sequelize'
import SftpClient from 'ssh2-sftp-client'
import { AFFILIATE, EXPORT_CSV_STATUS, PM_USER_LIMIT, PROD_TENANTS, STAGE_TENANTS, wyntaFileName } from '../../common/constants'
import getPlayerRevenueReportDataRealTimeSyncV2 from '../../common/csv/reports/getPlayerRevenueReportDataRealTimeSyncV2'
import { getCountryNameByCode } from '../../common/helpers'
import config from '../../configs/app.config'
import db, { sequelize } from '../../db/models'
import ajv from '../../libs/ajv'
import { s3 } from '../../libs/awsS3Config'
import ServiceBase from '../../libs/serviceBase'
const createCsvWriter = require('csv-writer').createObjectCsvWriter
const fs = require('fs')

const CSV_TYPES = {
  SALES: 'sales',
  REG: 'reg'
}

const schema = {
  type: 'object',
  properties: {
    tenantId: { type: 'number' },
    type: { enum: Object.values(CSV_TYPES) },
    queueId: { type: 'number' },
    csvDetail: { type: ['null', 'object'] }
  }
}

const constraints = ajv.compile(schema)

const uploadToSFTP = async (filePath, fileName, tenantId) => {
  const sftp = new SftpClient()

  try {
    const tenants = config.get('env') === 'development' ? STAGE_TENANTS : PROD_TENANTS

    let host, username, password
   // if (tenants[tenantId].name === '88Punt') {
      host = config.get('wynta88Punt.host')
      username = config.get('wynta88Punt.user')
      password = config.get('wynta88Punt.password')
   // }

    await sftp.connect({
      host,
      port: 22,
      username,
      password
    })
    await sftp.put(filePath, `/uploads/${fileName}`)
    await sftp.end()
  } catch (e) {
    throw new Error(e)
  }
}

const uploadToS3Bucket = async (filePath, fileName, tenantId, type) => {
  const s3Config = config.getProperties().s3

  const fileContent = await fs.promises.readFile(filePath)
  const key = `tenants/${tenantId}/csv/${type}/${fileName}`
  const s3Params = {
    ACL: 'public-read',
    Bucket: s3Config.bucket,
    Key: key,
    Body: fileContent
  }

  const uploadedFile = await s3.upload(s3Params).promise()
  return uploadedFile
}

const getFileFromS3Bucket = async (file, fileName) => {
  const response = await axios.get(file, { responseType: 'arraybuffer' })
  const fileData = Buffer.from(response.data, 'binary')
  fs.writeFileSync(`/tmp/${fileName}`, fileData)
}

const getHeaders = (type) => {
  if (type === CSV_TYPES.REG) {
    return [
      { id: 'signInIp', title: 'ip' },
      { id: 'city', title: 'city' },
      { id: 'userName', title: 'alias' },
      { id: 'gender', title: 'gender' },
      { id: 'country', title: 'country' },
      { id: 'clickId', title: 'click_id' },
      { id: 'id', title: 'playerid' },
      { id: 'bonusCode', title: 'bonuscode' },
      { id: 'tenant', title: 'casinoname' },
      { id: 'dob', title: 'dateofbirth' },
      { id: 'updatedAt', title: 'lastupdated' },
      { id: 'createdAt', title: 'registrationdate' },
      { id: 'registeredplatform', title: 'registeredplatform' }
    ]
  } else {
    return [
      { id: 'transactionDate', title: 'date' },
      { id: 'voids', title: 'voids' },
      { id: 'totalBonuses', title: 'bonuses' },
      { id: 'totalRevenue', title: 'revenue' },
      { id: 'clickId', title: 'click_id' },
      { id: 'deposits', title: 'deposits' },
      { id: 'bonuscode', title: 'bonuscode' },
      { id: 'playerId', title: 'player_id' },
      { id: 'chargebacks', title: 'chargebacks' },
      { id: 'withdrawals', title: 'Paidcashouts' },
      { id: 'totalBets', title: 'Sidegamesbets' },
      { id: 'totalWin', title: 'sidegameswins' },
      { id: 'expiredbonuses', title: 'expiredbonuses' },
      { id: 'firstDepositDate', title: 'firstdepositdate' },
      { id: 'totalRefund', title: 'sidegamesrefunds' },
      { id: 'firstDepositAmount', title: 'firstdepositamount' },
      { id: 'reversechargebacks', title: 'reversechargebacks' },
      { id: 'jackpotcontribution', title: 'jackpotcontribution' },
      { id: 'country', title: 'country' },
      { id: 'casinoBonuses', title: 'casino_bonuses' },
      { id: 'sportsBonuses', title: 'sport_bonuses' },
      { id: 'casinoRevenue', title: 'casino_revenue' },
      { id: 'sportsRevenue', title: 'sport_revenue' },
      { id: 'casinoBets', title: 'casino_sidegamesbets' },
      { id: 'sportsBets', title: 'sport_sidegamesbets' },
      { id: 'casinoWin', title: 'casino_sidegameswins' },
      { id: 'sportsWin', title: 'sport_sidegameswins' },
      { id: 'casinoRefund', title: 'casino_sidegamesrefunds' },
      { id: 'sportsRefund', title: 'sport_sidegamesrefunds' },
    ]
  }
}

const getTransactionData = async (startTime, endTime, tenantId, tenantDetails, utcEndTime, utcStartTime, transactionDateFileName, csvDetails) => {
  const casinoData = await getTransactionDataCallDb(csvDetails.payload, 'casino')
  const sportsData = await getTransactionDataCallDb(csvDetails.payload, 'sports')

  const date = new Date(transactionDateFileName)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const formattedCreatedAt = `${year}-${month}-${day}`

  const casinoUpdatedData = casinoData.reduce((acc, cur) => {
    const d = {
      brand: tenantDetails.name,
      // btag: `${tenantId}-${cur?.top?.hits?.hits[0]?._source?.player_details?.player_id}`,
      btag: cur?.affiliated_data,
      key: cur?.user_name + '-' + cur?.code + '-' + tenantDetails.name,
      transactionDate: formattedCreatedAt,
      playerId: cur?.user_id,
      playerIP: '',
      currency: cur?.code,
      chargeback: 0,
      depositCount: cur?.deposit_count,
      deposits: cur?.total_deposit,
      withdrawals: cur?.total_withdraws,
      casinoBets: cur?.bet_after_refund,
      casinoWin: cur?.win_after_refund,
      casinoRevenue: cur?.ggr,
      casinoBonuses: cur?.bonus,
      casinoNGR: cur?.ngr
    }
    return [...acc, d]
  }, [])

  const sportsUpdatedData = sportsData.reduce((acc, cur) => {
    const d = {
      brand: tenantDetails.name,
      btag: cur?.affiliated_data,
      transactionDate: formattedCreatedAt,
      playerId: cur?.user_id,
      playerIP: '',
      currency: cur?.code,
      chargeback: 0,
      key: cur?.user_name + '-' + cur?.code + '-' + tenantDetails.name,
      sportsBets: cur?.bet_after_refund,
      sportsWin: cur?.win_after_refund,
      sportsRevenue: cur?.ggr,
      sportsBonuses: cur?.bonus,
      sportsNGR: cur?.ngr

    }
    return [...acc, d]
  }, [])

  const joinedArray = casinoUpdatedData.map(item1 => {
    const matchingId = sportsUpdatedData.find(item2 => item2.key === item1.key) || {
      sportsBets: 0,
      sportsWin: 0,
      sportsRevenue: 0,
      sportsBonuses: 0,
      sportsNGR: 0
    }
    return { ...item1, ...matchingId }
  })

  const joinedSportArray = sportsUpdatedData.map(item1 => {
    const exists = joinedArray.some(item => item.key === item1.key)
    if (!exists) {
      item1.depositCount = 0
      item1.deposits = 0
      item1.withdrawals = 0
      item1.casinoBets = 0
      item1.casinoWin = 0
      item1.casinoRevenue = 0
      item1.casinoBonuses = 0
      item1.casinoNGR = 0
      return item1
    }
  }).filter(item => item !== undefined)

  let mergedArray = []
  if (joinedSportArray.length > 0) {
    mergedArray = [...joinedArray, ...joinedSportArray]
  } else {
    mergedArray = joinedArray
  }
  const playerIds = mergedArray.map(i => i.playerId)
  let updatedUsers = []
  if (!playerIds.length){
    return updatedUsers
  }
  const wyntaPlayerClickIds = await getWyntaPlayerClickIds(playerIds)
  const firstDepositDetails = await getFirstDepositDetails(playerIds)
  const currenciesRates = await getConversionRate()

  const wyntaPlayerClickIdMap = wyntaPlayerClickIds.reduce((map, item) => {
    map[item.userId] = item.clickId
    return map
  }, {})

  updatedUsers = mergedArray
    .filter(item => wyntaPlayerClickIdMap[item.playerId]) // only include if userId exists in wyntaPlayerClickIds
    .map(item => {
      const match = firstDepositDetails.find(fd => fd.user_id === item.playerId.toString())

      return {
        ...item,
        clickId: wyntaPlayerClickIdMap[item.playerId], // add clickId
        voids: 0,
        bonuscode: '',
        chargebacks: 0,
        expiredbonuses: '',
        reversechargebacks: 0,
        jackpotcontribution: 0,
        totalRefund: 0,
        country : match ? getCountryNameByCode(match.country_code?match.country_code:'') : '',
        firstDepositAmount: convertINRtoUSD((match ? match.amount : 0),currenciesRates),
        firstDepositDate: match ? new Date(match.first_deposit_date).toISOString() : '',
        casinoBonuses: convertINRtoUSD(item.casinoBonuses,currenciesRates),
        casinoRevenue: convertINRtoUSD(item.casinoRevenue,currenciesRates),
        deposits: convertINRtoUSD(item.deposits,currenciesRates),
        withdrawals: convertINRtoUSD(item.withdrawals,currenciesRates),
        casinoBets: convertINRtoUSD(item.casinoBets,currenciesRates),
        totalBonuses: convertINRtoUSD((+item.casinoBonuses) + (+item.sportsBonuses),currenciesRates),
        totalRevenue: convertINRtoUSD((+item.casinoRevenue) + (+item.sportsRevenue),currenciesRates),
        totalBets: convertINRtoUSD((+item.casinoBets) + (+item.sportsBets),currenciesRates),
        totalWin: convertINRtoUSD((+item.casinoWin) + (+item.sportsWin),currenciesRates),
        sportsBonuses: convertINRtoUSD(item.sportsBonuses,currenciesRates),
        sportsRevenue: convertINRtoUSD(item.sportsRevenue,currenciesRates),
        sportsBets: convertINRtoUSD(item.sportsBets,currenciesRates),
        casinoBets: convertINRtoUSD(item.casinoBets,currenciesRates),
        casinoWin: convertINRtoUSD(item.casinoWin ,currenciesRates),
        sportsWin: convertINRtoUSD(+item.sportsWin,currenciesRates),
        casinoRefund: 0,
        sportsRefund: 0
      }
    })

  return updatedUsers
}

const getConversionRate = async () => {
  try {
    const currencies = await db.Currency.findAll({
      where: {
        code: ['INR', 'USD']
      },
      raw: true
    });

    return currencies;
  } catch (error) {
    console.error('Error fetching currencies:', error);
    throw error;
  }
};

const convertINRtoUSD = (inrAmount, currencies) => {
  const inr = currencies.find(c => c.code === 'INR');
  const usd = currencies.find(c => c.code === 'USD');

  if (!inr || !usd) {
    throw new Error('INR or USD exchange rate not found in the currencies array');
  }

  const convertedAmount = (inrAmount / inr.exchangeRate) * usd.exchangeRate;
  return convertedAmount;
};

const getWyntaPlayerClickIds = async (playerIds) => {
  const clickData = await db.UsersAffiliate.findAll({
    where: {
      userId: {
        [Op.in]: playerIds
      },
      affiliate: AFFILIATE.WYNTA
    },
    attributes: ['clickId', 'userId']
  })

  return clickData
}

const getFirstDepositDetails = async (playerIds) => {
  const query = `
      SELECT user_id , first_deposit_date, amount , users.country_code FROM
      user_first_deposit
      inner join users on users.id = user_first_deposit.user_id
      where user_id IN (${playerIds})
    `
  const users = await sequelize.query(query, {
    type: sequelize.QueryTypes.SELECT,
    useMaster: false
  })

  return users
}

const deleteFile = async (filePath) => {
  fs.unlink(filePath, (err) => {
    if (err) {
      throw err
    }
  })
}

const writeCsvFile = async (filePath, data, headers) => {
	try {
    // Create the header row using `title`
    const headerRow = headers.map(h => h.title).join('\t');

    // Create data rows using `id`
    const dataRows = data.map(row =>
      headers.map(h => row[h.id] ?? '').join('\t')
    );

    const tsvContent = [headerRow, ...dataRows].join('\n');

    fs.writeFileSync(filePath, tsvContent, 'utf8');

  } catch (error) {
    console.error('Error writing TSV file:', error);
  }
}

const getTransactionDataCallDb = async (data, actionCategory) => {
  try {
    const payload = {
      search: '',
      ggr_ngr: '',
      agent_id: '',
      currency: '',
      datetime: {
        end_date: data.endTime,
        start_date: data.startTime
      },
      owner_id: '',
      game_type: '',
      tenant_id: data.tenantId,
      time_type: 'real-time-sync',
      time_zone: 'UTC +00:00',
      playerType: 'all_players',
      player_type: 'all',
      sbo_request: false,
      time_period: {
        end_date: data.utcEndTime,
        start_date: data.utcStartTime
      },
      game_provider: '',
      isDirectPlayer: '',
      time_zone_name: 'UTC +00:00',
      action_category: actionCategory
    }

    const botUserJoin = ''
    const botUserSelect = ''
    const checkSboRequest = false
    let isSuperAdmin = false
    // ==================== params gathering starts here ====================
    const params = {
      tenantId: 0,
      search: payload?.search ? payload?.search : '',
      // search_by_id: payload?.search_by_id ? payload?.search_by_id : '',
      currency_id: payload?.currency ? payload?.currency : '',
      date_range: payload?.date_range ? payload?.date_range : [],
      sort_by: payload?.sort_by ? payload?.sort_by : 'id',
      order: payload?.order ? payload?.order : 'desc',
      action_category: payload?.action_category,
      // sports_items: payload?.sports_items,
      game_type: payload?.game_type,
      // providers: payload?.providers,
      game_provider: payload?.game_provider,
      ggr_ngr: payload?.ggr_ngr
    }

    // Tenant Filter Gathering
    const tenantId = Number(payload.tenant_id)
    params.tenantId = [tenantId]
    isSuperAdmin = true

    // Agent Filter Gathering
    params.agentId = 0
    const agentId = Number(payload.agent_id)

    // ==================== params gathering ends here ====================

    // ====================filters here ========================
    let users = []
    payload.isSuperAdmin = true
    payload.playerCategory = 'all_players'
    payload.checkSboRequest = checkSboRequest

    users = await getPlayerRevenueReportDataRealTimeSyncV2(payload, params, data)

    return users
  } catch (e) {
    return false
  }
}
const getUserData = async (UserModel, UserLoginHistoryModel, tenantId, tenantDetails, startTime, endTime) => {
  let users = []
  let offset = 0
  let usersBtach
  let whereCondition = ''

  if (startTime && endTime) {
    whereCondition = `(("User"."created_at" BETWEEN '${startTime}' and '${endTime}' ))`
  } else if (endTime) {
    whereCondition = `(("User"."created_at" <= '${endTime}'))`
  }
  do {
    const query = `
      SELECT
        "UserLoginHistory"."ip" AS "signInIp",
        "User"."city",
        "User"."user_name" AS "userName",
        "User"."country_code" AS "countryCode",
        "User"."gender" AS "gender",
        "User"."id",
        "User"."date_of_birth" AS "dob",
        currencies.code AS "currency",
        "User"."created_at" AS "createdAt",
        "User"."updated_at" AS "updatedAt",
       "UserLoginHistory"."device_type" AS "registeredplatform"
      FROM
        "public"."users" AS "User"
       INNER JOIN wallets on "User"."id" = wallets.owner_id
       INNER JOIN currencies on "wallets"."currency_id" = currencies.id

      LEFT JOIN
      (
        SELECT "ip", "user_id",device_type, ROW_NUMBER() OVER (PARTITION BY "user_id" ORDER BY "updated_at" DESC) AS "rn" FROM "public"."user_login_history"
      ) as "UserLoginHistory"
      ON "User"."id" = "UserLoginHistory"."user_id" and "UserLoginHistory"."rn" = 1
      wHERE
        wallets.owner_type = 'User' AND
        tenant_id = ${tenantId} AND
        "User"."active"  = true AND
          ${whereCondition}
      ORDER BY "User"."updated_at" DESC
      limit ${PM_USER_LIMIT}
      offset ${offset}
    `
    usersBtach = await sequelize.query(query, {
      type: sequelize.QueryTypes.SELECT,
      useMaster: false
    })

    offset += 2000
    users = [...users, ...usersBtach]
  }
  while (usersBtach.length)

  const playerIds = users.map(i => i.id)

  let updatedUser = []
  if (!playerIds.length){
   return updatedUser
  }

  const wyntaPlayerClickIds = await getWyntaPlayerClickIds(playerIds)

  const wyntaPlayerClickIdMap = wyntaPlayerClickIds.reduce((map, item) => {
    map[item.userId] = item.clickId
    return map
  }, {})

   updatedUser = users.filter(item => wyntaPlayerClickIdMap[item.id]) // only include if userId exists in wyntaPlayerClickIds
    .map(i => {
      return {
        ...i,
        tenant: tenantDetails.name,
        clickId: wyntaPlayerClickIdMap[i.id], // add clickId
        bonusCode: '',
        country: getCountryNameByCode(i.countryCode),
        dob: (i?.dob && i?.dob.length > 0) ? new Date(i.dob).toISOString() : '',
        createdAt: new Date(i.createdAt).toISOString(),
        updatedAt: new Date(i.updatedAt).toISOString()
      }
    })

  return updatedUser
}
export default class Wynta extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      User: UserModel,
      ExportCsvCenter: ExportCsvCenterModel,
      UserLoginHistory: UserLoginHistoryModel
    } = db

    const {
      tenantId,
      type,
      queueId,
      csvDetail
    } = this.args

    // get export center queue
    const exportCenterQueue = await ExportCsvCenterModel.findOne({
      where: {
        id: queueId
      }
    })

    if (!exportCenterQueue) {
      throw new Error("QueueLog doesn't exists")
    }

    try {
      const tenants = config.get('env') === 'development' ? STAGE_TENANTS : PROD_TENANTS
      const tenantDetails = tenants[tenantId]
      let data, fileName, filePath
      let startTime = new Date(exportCenterQueue?.payload?.startTime) || new Date()

      if (startTime < new Date('2024-07-18 18:30:00')) {
        startTime = new Date(exportCenterQueue?.payload?.endTime) || new Date()
      }
      const transactionDateFileName = startTime

      // if csvUrl doesn't exists then create csv and upload it to s3 bucket
      if (!exportCenterQueue.csvUrl) {
        // get headers depending upon type
        const headers = getHeaders(type)

        // get file name of the said csv
        fileName = wyntaFileName(
          tenantDetails.name,
          type,
          `${transactionDateFileName.getFullYear()}${String(transactionDateFileName.getMonth() + 1).padStart(2, '0')}${String(transactionDateFileName.getDate()).padStart(2, '0')}`
        )

        filePath = `/tmp/${fileName}`
        if (!exportCenterQueue?.payload?.startTime) { exportCenterQueue.payload.startTime = '2024-03-01 00:00:00.0000' }

        // fetch the data for given type
        if (type === CSV_TYPES.REG) {
          data = await getUserData(UserModel, UserLoginHistoryModel, tenantId, tenantDetails, exportCenterQueue.payload.utcStartTime, exportCenterQueue.payload.utcEndTime)
        } else {
          data = await getTransactionData(
            exportCenterQueue.payload.startTime,
            exportCenterQueue.payload.endTime,
            tenantId,
            tenantDetails,
            exportCenterQueue.payload.utcEndTime,
            exportCenterQueue.payload.utcStartTime,
            transactionDateFileName,
            csvDetail
          )
        }

        // create the csv file and store it in remote location
        await writeCsvFile(filePath, data, headers)

        // upload it to S3 bucket
        const uploadedFile = await uploadToS3Bucket(filePath, fileName, tenantId, 'users')

        // update the export center queue details
        exportCenterQueue.csvUrl = uploadedFile.Location || ''
        exportCenterQueue.status = EXPORT_CSV_STATUS.DONE
        await exportCenterQueue.save()
      } else {
        // get the file name and file path from csvUrl
        fileName = exportCenterQueue.csvUrl.split('/')[exportCenterQueue.csvUrl.split('/').length - 1]
        filePath = `/tmp/${fileName}`

        // get the stored csv file from s3 bucket
        await getFileFromS3Bucket(exportCenterQueue.csvUrl, fileName, tenantId, type)
      }

      // upload the csv to FTP
      const queueProcessFtpStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'wynta_ftp_cron',
          status: 1
        },
        attributes: ['id']
      })
      if (queueProcessFtpStatus) {
        await uploadToSFTP(filePath, fileName, tenantId)
      }

      // delete the local file after upload
      await deleteFile(filePath)

      return { data: true }
    } catch (e) {
      exportCenterQueue.status = EXPORT_CSV_STATUS.FAILED
      await exportCenterQueue.save()
      throw new Error(e)
    }
  }
}
