import md5 from 'md5'
import { Sequelize } from 'sequelize'
import { bonusQueueRollover } from '../../common/bonusQueueRollover'
import cancelDepositBonus from '../../common/cancelDepositBonus'
import cancelLosingBonus from '../../common/cancelLosingBonus'
import { ALLOWED_PERMISSIONS, DEPOSIT_REQUEST_STATUS, PAYMENT_GATEWAY_QUEUE_TYPE, QUEUE_WORKER_CONSTANT, SASPAY_INTEGRATION_CONSTANT, TABLES, TRANSACTION_TYPES } from '../../common/constants'
import { depositBonusCheck } from '../../common/depositBonusCheck'
import newCurrencyConversion from '../../common/newCurrencyConversion'
import { userFirstDeposit } from '../../common/userFirstDeposit'
import { verifyReferralCode } from '../../common/verifyReferralCode'
import db from '../../db/models'

const axios = require('axios')

async function saspayDepositStatusResolver (sequelizeTransaction, depositId) {

  try {
    const orderDetails = await db.DepositRequest.findOne(
      {
        where: {
          id: depositId
        },
        attributes: ['id', 'status', 'userId', 'paymentProviderId', 'tenantId', 'orderId'],
        transaction: sequelizeTransaction,
        lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.DepositRequest },
        skipLocked: false,
      },

    )
    await orderDetails.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.DepositRequest }, transaction: sequelizeTransaction })

    let user = await db.User.findOne({
      attributes: ['id', 'tenantId', 'userName', 'withdrawWagerAllowed', [Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id)`), 'exchangeRate']],
      where: {
        id: orderDetails?.userId
      },
      include: {
        model: db.Wallet,
        attributes: ['id', 'currencyId', 'amount', 'nonCashAmount'],
        required: true
      }
    })

    const url = SASPAY_INTEGRATION_CONSTANT.STATUS_CHECK_API
    const params = { order_id: orderDetails?.orderId }

    const response = await axios.get(url, { params })

    const reqLogObject = {
      requestJson: { body: url, params },
      service: PAYMENT_GATEWAY_QUEUE_TYPE.SASPAY_DEPOSIT_UPDATE_STATUS,
      url: 'queue-worker',
      responseJson: { data: response?.data },
      tenantId: orderDetails?.tenantId
    }
    const createLog = await db.RequestResponseLog.create(reqLogObject)

    if (!response)
      return { message: SASPAY_INTEGRATION_CONSTANT.RESPONSE_NOT_FOUND, data: null }

    if (response.data.status === SASPAY_INTEGRATION_CONSTANT.FAILED_STATUS) {
      await db.DepositRequest.update(
        { status: DEPOSIT_REQUEST_STATUS.FAILED },
        {
          where: {
            orderId: response?.data?.order_id,
            amount: response?.data?.amount,
            tenantId: orderDetails?.tenantId
          },
          transaction: sequelizeTransaction
        }
      )
      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED, orderDetails.id )

      return { message: SASPAY_INTEGRATION_CONSTANT.FAILED_STATUS_UPDATED, data: response?.data }
    }

    if (response.data.status === SASPAY_INTEGRATION_CONSTANT.SUCCESS_STATUS) {
      if (orderDetails.status === SASPAY_INTEGRATION_CONSTANT.COMPLETED_STATUS)
        return { message: SASPAY_INTEGRATION_CONSTANT.STATUS_ALREADY_UPDATED, data: response?.data }

      // duplicate transaction check
      const transactionExist = await db.Transaction.findOne({
        attributes: ['id'],
        where: {
          transactionId: response?.data?.payment_id,
          debitTransactionId: response?.data?.order_id,
          paymentProviderId: orderDetails?.paymentProviderId,
          tenantId: orderDetails?.tenantId
        },
        useMaster: true
      })

      if (transactionExist)
        return { message: SASPAY_INTEGRATION_CONSTANT.DUPLICATE_TRANSACTION, data: response?.data }

      // validation check
      const paymentProviders = await db.tenantPaymentConfiguration.findOne({
        attributes: ['providerKeyValues'],
        where: {
          active: true,
          tenantId: orderDetails?.tenantId,
          providerId: orderDetails?.paymentProviderId
        }
      })

      const secretKey = paymentProviders?.providerKeyValues?.secretKey
      const orderSignature = `${response.data.order_id}${response.data.status}${response.data.amount}${secretKey}`
      if (md5(orderSignature) !== response.data.order_signature) {
        return { message: SASPAY_INTEGRATION_CONSTANT.INVALID_ORDER_SIGNATURE, data: response.data }
      }

      const depositAmount = parseFloat(response?.data?.amount)
      const conversionRate = user?.dataValues?.exchangeRate
      let transactionObject = {
        targetWalletId: user?.Wallet?.id,
        targetCurrencyId: user?.Wallet?.currencyId,
        amount: depositAmount,
        conversionRate,
        actioneeId: orderDetails?.userId,
        actioneeType: 'User',
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        transactionId: response?.data?.payment_id,
        debitTransactionId: response?.data?.order_id,
        paymentMethod: SASPAY_INTEGRATION_CONSTANT.PAYMENT_METHOD,
        paymentProviderId: orderDetails?.paymentProviderId,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        status: 'success',
        success: true,
        comments: SASPAY_INTEGRATION_CONSTANT.TRANSACTION_COMMENTS
      }

      const userWallet = await db.Wallet.findOne({
        where: { ownerId: user.id, ownerType: TABLES.USER },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet
        },
        skipLocked: false
      })

      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet }, transaction: sequelizeTransaction })

      userWallet.amount = (parseFloat((userWallet.amount).toFixed(5)) + depositAmount)
      await userWallet.save({ transaction: sequelizeTransaction })
      user = { ...user.dataValues, Wallet: userWallet }

      transactionObject = await newCurrencyConversion(transactionObject, user.Wallet, depositAmount)
      transactionObject.targetAfterBalance = parseFloat(userWallet.amount)
      transactionObject.targetBeforeBalance = (parseFloat(userWallet.amount) - parseFloat(depositAmount))

      const txn = await db.Transaction.create(transactionObject, { transaction: sequelizeTransaction })

       // Meta data
       const trxnMetaData = {
        transactionId: txn.id,
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        metaData: { dateTime: response?.data?.date_time }
      }
      await db.TransactionsMetaData.create(trxnMetaData, { transaction: sequelizeTransaction })

      const txnIds = []
      if (txn) {
        await userFirstDeposit(sequelizeTransaction, txn)
        txnIds.push(txn.id)
      }

      const type = 'payin'
      await cancelLosingBonus(sequelizeTransaction, user.id, user.tenantId, type)
      await cancelDepositBonus(sequelizeTransaction, user.id, user.tenantId, type)
      const depositBonusID = await depositBonusCheck(depositAmount, user, txnIds, sequelizeTransaction, txn.id, orderDetails?.paymentProviderId)
      await bonusQueueRollover(depositAmount, txn.id, user.id, orderDetails?.paymentProviderId, depositBonusID, sequelizeTransaction)

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }
      const queueLog = await db.QueueLog.create(queueLogObject, { transaction: sequelizeTransaction })

      orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED
      await orderDetails.save({ transaction: sequelizeTransaction })
      await verifyReferralCode(sequelizeTransaction, txn, DEPOSIT_REQUEST_STATUS.COMPLETED,orderDetails.id, orderDetails?.tenantId)

      const tenantThemeSetting = await db.TenantThemeSetting.findOne({
        attributes: ['allowedModules'],
        where: { tenantId: orderDetails?.tenantId },
        raw: true
      });

      if (tenantThemeSetting && tenantThemeSetting?.allowedModules && tenantThemeSetting?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION) && !user?.withdrawWagerAllowed) {
        const depositWagerQueueLogObject = {
          type: QUEUE_WORKER_CONSTANT.DEPOSIT_WAGER_TRACKING,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [orderDetails?.id],
          tenantId: orderDetails?.tenantId
        }
        await db.QueueLog.create(depositWagerQueueLogObject, { transaction: sequelizeTransaction })
      }

    }
    return { message: SASPAY_INTEGRATION_CONSTANT.COMPLETED_STATUS_UPDATED, data: response?.data }
  } catch (error) {
    throw error
  }
}

export default saspayDepositStatusResolver
