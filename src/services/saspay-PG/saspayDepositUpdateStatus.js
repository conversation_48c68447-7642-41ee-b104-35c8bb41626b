import ServiceBase from '../../libs/serviceBase'
import ajv from '../../libs/ajv'
import { Sequelize, Op } from 'sequelize'
import db from '../../db/models'
import { CreateJobService } from '../job'
import { SASPAY_INTEGRATION_CONSTANT, PAYMENT_GATEWAY_QUEUE_TYPE } from '../../common/constants'
import { PaymentGateway, PaymentGatewayQueue } from '../../queues/paymentGateway.queue'
import { delay } from 'lodash'

const schema = {

}

const constraints = ajv.compile(schema)

export class SaspayDepositUpdateStatus extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const {
      paymentProviders: paymentProvidersModel,
      DepositRequest: DepositRequestModel,
      QueueProcessStatus: QueueProcessStatusModel,
    } = db

    const queueProcessStatus = await QueueProcessStatusModel.findOne({
      where: {
        service: 'update_saspay_deposit_records_cron',
        status: 1
      },
      attributes: ['id']
    })

    if (queueProcessStatus) {
      const records = await DepositRequestModel.findAll({
        where: {
          paymentProviderId: [Sequelize.literal(`(SELECT id FROM payment_providers WHERE provider_name = '${SASPAY_INTEGRATION_CONSTANT.PROVIDER_NAME}')`)],
          status: { [Sequelize.Op.not]: ['completed', 'failed'] },
          createdAt: {
            [Op.lt]: Sequelize.literal("NOW() - INTERVAL '24 hours'")
          }
        },
        attributes: ['id']
      })

      // create a job
      const queue = PaymentGatewayQueue
      const queueName = PaymentGateway
      const jobTime = new Date()
      const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
      // create jobs
      if (records.length > 0) {
        for (const txn of records) {
          await delay(1000)
          await Promise.all([
            await queue.add(
              queueName,
              {
                transactionId: txn.id,
                time: jobTime,
                transactionType: PAYMENT_GATEWAY_QUEUE_TYPE.SASPAY_DEPOSIT_UPDATE_STATUS,
              },
              {
                jobId: `${txn.id}_${queueName}`,
                // removeOnComplete: true,
                delay: 10,
              }
            ),
          ])
        }
      }
    }
  }
}
