import axios from 'axios';
import db from '../../db/models';

export default async (withdrawalDataParams) => {
  try {
    // Destructure withdrawalDataParams
    const {
      withdrawalRequest,
      tenantPaymentConfig,
    } = withdrawalDataParams;

    // Create Provider End Point
    const providerEndPoint = tenantPaymentConfig.providerKeyValues.end_point;

    /*
    // Convert Currency to INR if it is in LKR or chips
    const currencyCode = withdrawalRequest.User.Wallet.Currency.code;
    const currencyId = withdrawalRequest.User.Wallet.Currency.id;
    let paymentAmount = withdrawalRequest.amount;
    let paymentCurrency = 'INR';

    if (['chips', 'LKR'].includes(currencyCode)) {
      const conversionRate = withdrawalRequest.User.Wallet.Currency.exchangeRate;
      let currencyExchangeRateData = await db.Currency.findOne({
        where: {
          code: 'INR'
        },
        attributes: ['id', 'exchangeRate'],
        raw: true
      });
      paymentAmount = (paymentAmount * (currencyExchangeRateData.exchangeRate / conversionRate)).toFixed(5);
    }
    */

    // Generate Body
    const APIBody = {
      amount: withdrawalRequest.amount,
      currency: "INR",
      bank_account_number: withdrawalRequest.accountNumber?.trim(),
      bank_name: withdrawalRequest.bankName?.trim(),
      name: withdrawalRequest.User.firstName?.trim(),
      surname: withdrawalRequest.User.lastName?.trim(),
      organisation_name: tenantPaymentConfig.providerKeyValues.organisation_name,
      phone: withdrawalRequest.User.phone?.trim(),
      merchant_withdrawal_id: withdrawalRequest.fdTransactionId?.trim(),
      ifsc_number: withdrawalRequest.ifscCode?.trim(),
    };

    // Generate Access Token
    const { apiKey, tokenApi } = tenantPaymentConfig.providerKeyValues;
    const tokenApiBody = {
      'refresh_token': apiKey
    };
    const tokenResponse = await axios.post(tokenApi, tokenApiBody, {
      maxBodyLength: Infinity,
      headers: {
        maxBodyLength: Infinity,
        'Content-Type': 'application/x-www-form-urlencoded',
      }
    });
    const accessToken = tokenResponse.data.access_token;

    // Generate API Config
    const APIConfig = {
      maxBodyLength: Infinity,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      }
    };

    return {
      providerEndPoint,
      APIBody,
      APIConfig
    };

  } catch (error) {
    throw error;
  }
};
