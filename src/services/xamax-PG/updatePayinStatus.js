import qs from 'querystring'
import { Sequelize } from 'sequelize'
import cancelDepositBonus from '../../common/cancelDepositBonus'
import cancelLosingBonus from '../../common/cancelLosingBonus'
import { ALLOWED_PERMISSIONS, BONUS_TYPES, DEPOSIT_REQUEST_STATUS, PAYMENT_GATEWAY_QUEUE_TYPE, QUEUE_WORKER_CONSTANT, TABLES, TRANSACTION_TYPES, XAMAX_INTEGRATION_CONSTANT } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import v2CurrencyConversion from '../../common/newCurrencyConversion'
import { userFirstDeposit } from '../../common/userFirstDeposit'
import { verifyReferralCode } from '../../common/verifyReferralCode'
import db from '../../db/models'

const axios = require('axios')

async function xamaxUpdatePayinStatus (orderDetails, credentials, sequelizeTransaction) {
  let user
  try {
    const { apiKey, tokenApi, payinStatusCheckApi } = credentials

    // Generate Token:
    const tokenConfig = qs.stringify({
      'refresh_token': apiKey
    });
    let tokenResponse = {};
    try {
      tokenResponse = await axios.post(tokenApi, tokenConfig, {
        maxBodyLength: Infinity,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        }
      });
      tokenResponse = tokenResponse?.data
    } catch (error) {
      await ErrorLogHelper.logError(error, null, user)
      throw error
    }

    const requestData = {
      transaction_id: orderDetails?.orderId
    }

    // Axios configuration
    let axiosConfig = {
      maxBodyLength: Infinity,
      headers: {
        'Authorization': `Bearer ${tokenResponse.access_token}`
      },
      params: requestData,
    };

    let data = null;
    try {
      data = await axios.get(payinStatusCheckApi, { 
        ...axiosConfig,
        validateStatus: () => true
      });
    } catch (error) {
      await ErrorLogHelper.logError(error, null, user)
      throw error
    }

    const responseData = data.data
    const reqLogObject = {
      requestJson: { body: payinStatusCheckApi, requestData, axiosConfig },
      service: PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_DEPOSIT_REQUEST,
      url: 'queue-worker',
      responseJson: {
        data: {
          status: data.status,
          statusText: data.statusText,
          result:responseData,
        }
      },
      tenantId: orderDetails?.tenantId
    }

    await db.RequestResponseLog.create(reqLogObject)

    if (!data || (data?.status != XAMAX_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE && data?.status != XAMAX_INTEGRATION_CONSTANT.NOT_FOUND_STATUS_CODE)) {
      return { message: XAMAX_INTEGRATION_CONSTANT.INVALID_RESPONSE, response: data.data }
    }
    
    if (data?.status == XAMAX_INTEGRATION_CONSTANT.NOT_FOUND_STATUS_CODE || responseData?.status === XAMAX_INTEGRATION_CONSTANT.FAILED_TXNSTATUS || responseData?.status === XAMAX_INTEGRATION_CONSTANT.EXPIRED_TXNSTATUS || responseData?.status === XAMAX_INTEGRATION_CONSTANT.CANCEL_TXNSTATUS) {
      await db.DepositRequest.update(
        { status: DEPOSIT_REQUEST_STATUS.FAILED },
        {
          where: {
            orderId: orderDetails?.orderId,
            amount: orderDetails?.amount,
            tenantId: orderDetails?.tenantId
          },
          transaction: sequelizeTransaction
        }
      )
      const user = await db.User.findOne({
        attributes: ['id', 'tenantId'],
        where: {
          id: orderDetails?.userId,
          tenantId: orderDetails?.tenantId
        },
        raw : true
      })
      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED,orderDetails.id, orderDetails?.tenantId)

      return { message: XAMAX_INTEGRATION_CONSTANT.FAILED_STATUS_UPDATED, response: responseData }
    }

    if (responseData.status === XAMAX_INTEGRATION_CONSTANT.SUCCESS_TXNSTATUS) {
      if (orderDetails.status === XAMAX_INTEGRATION_CONSTANT.COMPLETED_STATUS) {
        return { message: XAMAX_INTEGRATION_CONSTANT.STATUS_ALREADY_UPDATED, response: data.data }
      }

      // duplicate transaction check
      const transactionExist = await db.Transaction.findOne({
        attributes: ['id'],
        where: {
          transactionId: responseData?.transaction_id,
          // debitTransactionId: responseData?.linked_utr,
          paymentProviderId: orderDetails?.paymentProviderId,
          tenantId: orderDetails?.tenantId
        },
        useMaster: true
      })

      if (transactionExist) { return { message: XAMAX_INTEGRATION_CONSTANT.DUPLICATE_TRANSACTION, response: responseData } }

      user = await db.User.findOne({
        attributes: ['id', 'tenantId', 'userName', 'withdrawWagerAllowed',[Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id AND wallets.owner_type = 'User')`), 'exchangeRate']
        ],
        where: {
          id: orderDetails?.userId,
          tenantId: orderDetails?.tenantId
        },
        include: {
          model: db.Wallet,
          attributes: ['id', 'currencyId', 'amount', 'nonCashAmount'],
          where: {
            ownerType: 'User'
          },
          required: true
        }
      })

      let depositAmount = parseFloat(responseData?.amount_delivered)
      const conversionRate = user?.dataValues?.exchangeRate
      let transactionObject = {
        targetWalletId: user?.Wallet?.id,
        targetCurrencyId: user?.Wallet?.currencyId,
        amount: depositAmount,
        conversionRate,
        actioneeId: orderDetails?.userId,
        actioneeType: 'User',
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        transactionId: responseData?.transaction_id,
        paymentMethod: XAMAX_INTEGRATION_CONSTANT.PAYMENT_METHOD,
        paymentProviderId: orderDetails?.paymentProviderId,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        status: 'success',
        success: true,
        comments: XAMAX_INTEGRATION_CONSTANT.TRANSACTION_COMMENTS
      }

      const userWallet = await db.Wallet.findOne({
        where: { ownerId: user.id, ownerType: TABLES.USER },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet
        },
        skipLocked: false
      })

      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet }, transaction: sequelizeTransaction })
      /*
      // Currency Conversion INR to base currency
      if (['IND'].includes(responseData.country) && ['INR'].includes(responseData.currency)) {
        let currencyExchangeRate = await db.Currency.findOne({
          where: {
            code: responseData.currency
          },
          attributes: ['id', 'exchange_rate'],
          raw: true
        })
        if (!currencyExchangeRate) {
          return setError(responseObject, `${responseData.currency} INR Currency Not Found`)
        }
        depositAmount = parseFloat((parseFloat(depositAmount) * (conversionRate / currencyExchangeRate.exchange_rate)).toFixed(4))
      }
     */
      // Logic to convert Crypto amount
      if (!['INR'].includes(responseData.currency)) {
        depositAmount = parseFloat((parseFloat(depositAmount) * responseData.crypto_ex_rate).toFixed(5))
      }
      userWallet.amount = (parseFloat((userWallet.amount).toFixed(5)) + depositAmount)
      await userWallet.save({ transaction: sequelizeTransaction })

      transactionObject = await v2CurrencyConversion(transactionObject, userWallet, depositAmount)
      transactionObject.targetAfterBalance = parseFloat(userWallet.amount)
      transactionObject.targetBeforeBalance = (parseFloat(userWallet.amount) - parseFloat(depositAmount))

      const txn = await db.Transaction.create(transactionObject, { transaction: sequelizeTransaction })

       // Meta data
       const trxnMetaData = {
        transactionId: txn.id,
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        metaData: {
          approved_amount: responseData?.amount_delivered,
        }
      }
      await db.TransactionsMetaData.create(trxnMetaData, { transaction: sequelizeTransaction })

      const txnIds = [], bulkData = []
      if (txn) {
        await userFirstDeposit(sequelizeTransaction, txn)
        txnIds.push(txn.id)
      }

      const type = 'payin'
      await cancelLosingBonus(sequelizeTransaction, user.id, user.tenantId, type)
      await cancelDepositBonus(sequelizeTransaction, user.id, user.tenantId, type)
      // Deposit Bonus Queue
      const depositBonusQueueLogObject = {
        type: 'bonus',
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [
          {
            amount: depositAmount,
            user,
            wallet: userWallet,
            transactionIds: txnIds,
            depositTransactionId: txn.id,
            paymentProviders: orderDetails?.paymentProviderId,
            bonusType: BONUS_TYPES.DEPOSIT
          }
        ]
      }
      bulkData.push(depositBonusQueueLogObject)
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }
      bulkData.push(queueLogObject)
      await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })

      orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED
      await orderDetails.save({ transaction: sequelizeTransaction })

      await verifyReferralCode(sequelizeTransaction, txn, DEPOSIT_REQUEST_STATUS.COMPLETED, orderDetails.id, orderDetails?.tenantId)

      const tenantThemeSetting = await db.TenantThemeSetting.findOne({
        attributes: ['allowedModules'],
        where: { tenantId: orderDetails?.tenantId },
        raw: true
      });

      if (tenantThemeSetting && tenantThemeSetting?.allowedModules && tenantThemeSetting?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION) && !user?.withdrawWagerAllowed) {
        const depositWagerQueueLogObject = {
          type: QUEUE_WORKER_CONSTANT.DEPOSIT_WAGER_TRACKING,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [orderDetails?.id],
          tenantId: orderDetails?.tenantId
        }
        await db.QueueLog.create(depositWagerQueueLogObject, { transaction: sequelizeTransaction })
      }

      return { message: XAMAX_INTEGRATION_CONSTANT.COMPLETED_STATUS_UPDATED, response: data.data }
    }
    return { message: XAMAX_INTEGRATION_CONSTANT.STATUS_NOT_UPDATED, response: data.data }
  } catch (error) {
    await ErrorLogHelper.logError(error, null, user)
    throw error
  }
}

export default xamaxUpdatePayinStatus
