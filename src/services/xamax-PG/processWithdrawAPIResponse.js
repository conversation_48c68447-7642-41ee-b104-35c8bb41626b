import db from '../../db/models';
import { QUEUE_WORKER_CONSTANT } from '../../common/constants';
import handleWithdrawRequestFailureCase from '../create-withdraw-request/handleWithdrawRequestFailureCase';

export default async (sequelizeTransaction, withdrawAPIResponse, withdrawalDataParams, pubsubObj) => {
  try {
    // Destructure withdrawalDataParams
    const {
      withdrawalRequest,
      authUser
    } = withdrawalDataParams;

    if (withdrawAPIResponse.status === 200) {
      if (withdrawAPIResponse.data.status === 'withdrawal_status_pending') {

        const xamaxGeneratedWithdrawId = withdrawAPIResponse.data.withdrawal_id;

        // Update Withdraw Data
        withdrawalRequest.status = 'pending_by_gateway';
        withdrawalRequest.payment_transaction_id = xamaxGeneratedWithdrawId;

        // Save In DB
        await withdrawalRequest.save({ transaction: sequelizeTransaction });

        // Update transaction
        withdrawalRequest.Transaction.transactionId = xamaxGeneratedWithdrawId;
        withdrawalRequest.Transaction.actioneeType = authUser.parentType;
        withdrawalRequest.Transaction.actioneeId = authUser.id;
        withdrawalRequest.Transaction.status = 'pending';
        withdrawalRequest.Transaction.comments = 'Pending by payment gateway';

        // Save In DB
        await withdrawalRequest.Transaction.save({ transaction: sequelizeTransaction });

        // Add Transaction in Queue Log
        const queueLog = await db.QueueLog.create({
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          ids: [String(withdrawalRequest.transactionId)]
        }, { transaction: sequelizeTransaction });

        // Add in pubsub object
        if (!pubsubObj.publishToQueueService) pubsubObj.publishToQueueService = [];
        pubsubObj.publishToQueueService.push({
          QueueLog: {
            queueLogId: queueLog.id,
          }
        });
      } else {
        await handleWithdrawRequestFailureCase(sequelizeTransaction, withdrawAPIResponse, withdrawalDataParams, pubsubObj);
      }
    } else {
      await handleWithdrawRequestFailureCase(sequelizeTransaction, withdrawAPIResponse, withdrawalDataParams, pubsubObj);
    }
  } catch (error) {
    throw error;
  }
};
