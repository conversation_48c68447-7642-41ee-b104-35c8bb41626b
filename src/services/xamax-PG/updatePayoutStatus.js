import cancelDepositBonus from "../../common/cancelDepositBonus"
import cancelLosingBonus from "../../common/cancelLosingBonus"
import { QUEUE_WORKER_CONSTANT, XAMAX_INTEGRATION_CONSTANT } from '../../common/constants'
import cancelTransactionLedger from "../../common/pendingPayout/cancelTransactionLedger"
import db from "../../db/models"
import { bulkDataForSmartico } from '../../common/commonFunctions'

export default async (withdrawalRequest, transaction, apiRes, sequelizeTransaction) => {
  try {
    const txnIds = []
    const bulkData = []

    if (apiRes.status == 200 && apiRes.data.status === XAMAX_INTEGRATION_CONSTANT.SUCCESS_TXNSTATUS) {
      txnIds.push(withdrawalRequest.transactionId);
      const type = 'payout'
      await cancelLosingBonus(sequelizeTransaction, withdrawalRequest.userId, withdrawalRequest.tenantId, type)
      await cancelDepositBonus(sequelizeTransaction, withdrawalRequest.userId, withdrawalRequest.tenantId, type)

      await db.WithdrawRequest.update(
        {
          withdrawal_type: "payment_gateway",
          actioned_at: new Date(),
          status: "approved",
        },
        {
          where: { id: withdrawalRequest.id },
          transaction: sequelizeTransaction,
        }
      )

      await db.Transaction.update(
        {
          status: "success",
          comments: "Approved By Payment gateway",
          createdAt: new Date().toISOString(),
        },
        {
          where: {
            id: withdrawalRequest.transactionId,
            tenantId: withdrawalRequest.tenantId,
          },
          transaction: sequelizeTransaction,
        }
      )

      if (withdrawalRequest.transactionId) {
        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds
        }
        bulkData.push(queueLogObject)
        bulkDataForSmartico(bulkData, withdrawalRequest.tenantId, true, withdrawalRequest.transactionId)
        const sendSmsAlerts = {
          type: QUEUE_WORKER_CONSTANT.DEP_WITHDRAW_SMS_OTP,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [{ "id": +withdrawalRequest.id }],
          tenantId: withdrawalRequest.tenantId
        }
        bulkData.push(sendSmsAlerts)
        await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })
      }

    } else if (apiRes.status == 500 || apiRes.data?.status === XAMAX_INTEGRATION_CONSTANT.FAILED_TXNSTATUS || apiRes.data?.status === XAMAX_INTEGRATION_CONSTANT.CANCEL_TXNSTATUS) {
      txnIds.push(withdrawalRequest.transactionId)
      await db.WithdrawRequest.update(
        {
          withdrawal_type: "payment_gateway",
          actioned_at: new Date(),
          status: 'rejected_by_gateway',
        },
        {
          where: { id: withdrawalRequest.id },
          transaction: sequelizeTransaction,
        }
      )
      await db.Transaction.update(
        {
          status: "cancelled",
          comments: "Rejected By payment Gateway",
          createdAt: new Date().toISOString(),
        },
        {
          where: {
            id: withdrawalRequest.transactionId,
            tenantId: withdrawalRequest.tenantId,
          },
          transaction: sequelizeTransaction,
        }
      )
      const userWalletMain = await db.Wallet.findOne({
        where: { id: transaction.sourceWalletId, ownerType: "User" },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet,
        },
        skipLocked: false,
      })
      await userWalletMain.reload({
        lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet },
        transaction: sequelizeTransaction,
      })

      userWalletMain.amount += parseFloat(transaction.amount)
      await userWalletMain.save({ transaction: sequelizeTransaction })

      const requestData = {
        transaction: transaction,
        withdrawRequest: withdrawalRequest,
        userWallet: userWalletMain,
      }
      const cancelTrnId = await cancelTransactionLedger(requestData)
      if (cancelTrnId) {
        txnIds.push(cancelTrnId)
      }
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.USER_TRANSACTION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [withdrawalRequest.userId]
      }

      bulkData.push(queueLogObject)

      if (withdrawalRequest.transactionId) {
        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds
        }
        bulkData.push(queueLogObject)
        bulkDataForSmartico(bulkData, withdrawalRequest?.tenantId, false, withdrawalRequest?.transactionId)
        const sendSmsAlerts = {
          type: QUEUE_WORKER_CONSTANT.DEP_WITHDRAW_SMS_OTP,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [{ "id": +withdrawalRequest.id }],
          tenantId: withdrawalRequest.tenantId
        }
        bulkData.push(sendSmsAlerts)
        await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })
      }
    }
  } catch (error) {
    console.log("=============== update xamax payout ===============", error)
    throw error
  }
}
