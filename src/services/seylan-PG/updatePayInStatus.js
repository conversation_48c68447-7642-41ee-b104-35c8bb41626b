import { Op, Sequelize } from 'sequelize'
import cancelDepositBonus from '../../common/cancelDepositBonus'
import cancelLosingBonus from '../../common/cancelLosingBonus'
import { ALLOWED_PERMISSIONS, BONUS_TYPES, DEPOSIT_REQUEST_STATUS, QUEUE_WORKER_CONSTANT, SEYLAN_INTEGRATION_CONSTANT, TABLES, TRANSACTION_TYPES } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import v2CurrencyConversion from '../../common/newCurrencyConversion'
import { userFirstDeposit } from '../../common/userFirstDeposit'
import { verifyReferralCode } from '../../common/verifyReferralCode'
import db from '../../db/models'

const axios = require('axios')

async function seylanPayUpdatePayinStatus (orderDetails, credentials, sequelizeTransaction) {
  let user
  try {

    const requestData = {
      order_id: orderDetails?.orderId
    }
    const { merchantId, password, apiUrl } = credentials
    const merchantUserName = `merchant.${merchantId}`
    const url = `${apiUrl}api/rest/version/73/merchant/${merchantId}/order/${orderDetails?.orderId}`
    // Axios configuration
    const axiosConfig = {
      method: 'get',
      maxBodyLength: Infinity,
      url: url,
      headers: {
        Authorization: 'Basic ' + Buffer.from(`${merchantUserName}:${password}`).toString('base64')
      }
    }

    let data
    try {
      const httpApiReponse = await axios.request(axiosConfig)
      data = httpApiReponse.data
      const reqLogObject = {
        requestJson: { body: url, requestData, axiosConfig },
        service: 'seylan',
        url: 'queue-worker',
        responseJson: { data },
        tenantId: orderDetails?.tenantId
      }
      await db.RequestResponseLog.create(reqLogObject)
    } catch (err) {
      if (err?.response) {
        if (err.response.status === 400) {
          await db.DepositRequest.update(
            { status: DEPOSIT_REQUEST_STATUS.FAILED },
            {
              where: {
                id: orderDetails?.id
              },
              transaction: sequelizeTransaction
            }
          )
          await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED, orderDetails.id, orderDetails?.tenantId)

          return { message: SEYLAN_INTEGRATION_CONSTANT.FAILED_STATUS_UPDATED, response: data }
        }
      }
    }

    // Check if response is valid
    if (!data || !data.status) {
      return { message: SEYLAN_INTEGRATION_CONSTANT.INVALID_RESPONSE, response: data }
    }

    const transactionData = data

    if (transactionData.status === 'FAILED' || transactionData.status === 'AUTHENTICATION_UNSUCCESSFUL') {
      await db.DepositRequest.update(
        { status: DEPOSIT_REQUEST_STATUS.FAILED },
        {
          where: {
            orderId: orderDetails?.orderId,
            amount: orderDetails?.amount,
            tenantId: orderDetails?.tenantId
          },
          transaction: sequelizeTransaction
        }
      )
      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED, orderDetails.id, orderDetails?.tenantId)

      return { message: SEYLAN_INTEGRATION_CONSTANT.FAILED_STATUS_UPDATED, response: data }
    }

    // Handle successful transactions
    if (transactionData.status === 'CAPTURED' && transactionData.result === 'SUCCESS') {
      if (orderDetails.status === DEPOSIT_REQUEST_STATUS.COMPLETED) {
        return { message: SEYLAN_INTEGRATION_CONSTANT.STATUS_ALREADY_UPDATED, response: data }
      }

      user = await db.User.findOne({
        attributes: ['id', 'tenantId', 'userName', 'withdrawWagerAllowed', 'createdAt', [Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id AND wallets.owner_type = 'User')`), 'exchangeRate']
        ],
        where: {
          id: orderDetails?.userId,
          tenantId: orderDetails?.tenantId
        },
        include: {
          model: db.Wallet,
          attributes: ['id', 'currencyId', 'amount', 'nonCashAmount'],
          where: {
            ownerType: 'User'
          },
          required: true
        }
      })

      // duplicate transaction check
      const transactionExist = await db.Transaction.findOne({
        attributes: ['id'],
        where: {
          transactionId: orderDetails?.orderId,
          paymentProviderId: orderDetails?.paymentProviderId,
          tenantId: orderDetails?.tenantId,
          createdAt: {
            [Op.gt]: user.createdAt
          }
        },
        useMaster: true
      })

      if (transactionExist) {
        return { message: SEYLAN_INTEGRATION_CONSTANT.DUPLICATE_TRANSACTION, response: data }
      }



      const depositAmount = parseFloat(transactionData?.amount)
      const conversionRate = user?.dataValues?.exchangeRate
      let transactionObject = {
        targetWalletId: user?.Wallet?.id,
        targetCurrencyId: user?.Wallet?.currencyId,
        amount: depositAmount,
        conversionRate,
        actioneeId: orderDetails?.userId,
        actioneeType: 'User',
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        transactionId: orderDetails?.orderId,
        debitTransactionId: transactionData?.authentication['3ds']?.transactionId,
        paymentMethod: SEYLAN_INTEGRATION_CONSTANT.PAYMENT_METHOD,
        paymentProviderId: orderDetails?.paymentProviderId,
        errorCode: 0,
        status: 'success',
        success: true,
        comments: SEYLAN_INTEGRATION_CONSTANT.TRANSACTION_COMMENTS
      }

      const userWallet = await db.Wallet.findOne({
        where: { ownerId: user.id, ownerType: TABLES.USER },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet
        },
        skipLocked: false
      })

      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet }, transaction: sequelizeTransaction })

      userWallet.amount = (parseFloat((userWallet.amount).toFixed(5)) + depositAmount)
      await userWallet.save({ transaction: sequelizeTransaction })

      transactionObject = await v2CurrencyConversion(transactionObject, userWallet, depositAmount)
      transactionObject.targetAfterBalance = parseFloat(userWallet.amount)
      transactionObject.targetBeforeBalance = (parseFloat(userWallet.amount) - parseFloat(depositAmount))

      const txn = await db.Transaction.create(transactionObject, { transaction: sequelizeTransaction })

      // Meta data
      const trxnMetaData = {
        transactionId: txn.id,
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        metaData: {
          transactionId: transactionData?.authentication['3ds']?.transactionId,
          amount: transactionData?.amount
        }
      }
      await db.TransactionsMetaData.create(trxnMetaData, { transaction: sequelizeTransaction })

      const txnIds = [], bulkData = []
      if (txn) {
        await userFirstDeposit(sequelizeTransaction, txn)
        txnIds.push(txn.id)
      }

      const type = 'payin'
      await cancelLosingBonus(sequelizeTransaction, user.id, user.tenantId, type)
      await cancelDepositBonus(sequelizeTransaction, user.id, user.tenantId, type)

      // Deposit Bonus Queue
      const depositBonusQueueLogObject = {
        type: 'bonus',
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [
          {
            amount: depositAmount,
            user,
            wallet: userWallet,
            transactionIds: txnIds,
            depositTransactionId: txn.id,
            paymentProviders: orderDetails?.paymentProviderId,
            bonusType: BONUS_TYPES.DEPOSIT
          }
        ]
      }
      bulkData.push(depositBonusQueueLogObject)

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }
      bulkData.push(queueLogObject)
      await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })

      orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED
      await orderDetails.save({ transaction: sequelizeTransaction })
      await verifyReferralCode(sequelizeTransaction, txn, DEPOSIT_REQUEST_STATUS.COMPLETED, orderDetails.id, orderDetails?.tenantId)

      const tenantThemeSetting = await db.TenantThemeSetting.findOne({
        attributes: ['allowedModules'],
        where: { tenantId: orderDetails?.tenantId },
        raw: true
      });

      if (tenantThemeSetting && tenantThemeSetting?.allowedModules && tenantThemeSetting?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION) && !user?.withdrawWagerAllowed) {
        const depositWagerQueueLogObject = {
          type: QUEUE_WORKER_CONSTANT.DEPOSIT_WAGER_TRACKING,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [orderDetails?.id],
          tenantId: orderDetails?.tenantId
        }
        await db.QueueLog.create(depositWagerQueueLogObject, { transaction: sequelizeTransaction })
      }

      return { message: SEYLAN_INTEGRATION_CONSTANT.COMPLETED_STATUS_UPDATED, response: data }
    }

    return { message: SEYLAN_INTEGRATION_CONSTANT.STATUS_NOT_UPDATED, response: data }
  } catch (error) {
    await ErrorLogHelper.logError(error, null, user)
    throw error
  }
}


export default seylanPayUpdatePayinStatus
