import { Sequelize } from 'sequelize'
import { MULTIPLE_PG_MAPPING, PAYMENT_GATEWAY_QUEUE_TYPE, CRON_LOG_STATUS } from '../../common/constants'
import db from '../../db/models'
import { PaymentGateway, PaymentGatewayQueue } from '../../queues/paymentGateway.queue'

export default async (data) => {
  const cronLog = {}
  cronLog.startTime = new Date()
  try {
    const transactionType = PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_DEPOSIT_REQUEST
    const queue = PaymentGatewayQueue
    const queueName = PaymentGateway
    const jobTime = new Date()

    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'pending_deposit_request',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      cronLog.cronId = queueProcessStatus?.id
      const paymentProviderCondition = MULTIPLE_PG_MAPPING[data]
        ? {
          [Sequelize.Op.in]: (await db.TenantCredential.findOne({
            where: { key: MULTIPLE_PG_MAPPING[data] }
          })).value.split(',')
        }
        : [Sequelize.literal(`(SELECT id FROM payment_providers WHERE provider_name = '${data}')`)]
      const records = await db.DepositRequest.findAll({
        attributes: ['id'],
        where: {
          paymentProviderId: paymentProviderCondition,
          status: { [Sequelize.Op.not]: ['completed', 'failed'] }
          // ,
          // createdAt: {
          //   [Op.lt]: Sequelize.literal("NOW() - INTERVAL '24 hours'")
          // }
        },
        raw: true
      })

      const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
      if (records.length > 0) {
        for (const txn of records) {
          await delay(1000)
          await Promise.all([
            await queue.add(
              queueName,
              {
                time: jobTime,
                transactionType: transactionType,
                transactionId: txn.id
              },
              {
                jobId: `${txn.id}_${queueName}`,
                removeOnComplete: true,
                delay: 10
              }
            )
          ])
        }
      }
    }
  } catch (e) {
    cronLog.status = CRON_LOG_STATUS.FAILED
    cronLog.errorMsg = e.message || null
    console.log('==========error in pending deposit queue handler==========', e)
  }
  cronLog.endTime = new Date()
  await db.CronLog.create(cronLog)
}
