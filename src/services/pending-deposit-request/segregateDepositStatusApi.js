import { PAYMENT_GATEWAY_NAMES } from '../../common/constants'
import db from '../../db/models'
import ackoPayUpdatePayinStatus from '../ackopay-PG/updatePayinStatus'
import cloudCashUpdatePayinStatus from '../cloudcash-PG/updatePayinStatus'
import jasPayUpdatePayinStatus from '../jaspay-PG/updatePayinStatus'
import paycookiesUpdatePayinStatus from '../paycookies-PG/updatePayinStatus'
import paywingsUpdatePayinStatus from '../paywings-PG/updatePayinStatus'
import peerPayUpdatePayinStatus from '../peerPay-PG/updatePayinStatus'
import sambhavPayUpdatePayinStatus from '../sambhavPay-PG/updatePayinStatus'
import seylanPayUpdatePayinStatus from '../seylan-PG/updatePayInStatus'
import techPayUpdatePayinStatus from '../techPay-PG/updatePayInStatus'
import xamaxUpdatePayinStatus from '../xamax-PG/updatePayinStatus'
import zenxPayUpdatePayinStatus from '../zenxpay-PG/updatePayInStatus'
const { Sequelize } = require('sequelize')

async function segregateDepositStatusApi (sequelizeTransaction, id) {
  try {
    const orderDetails = await db.DepositRequest.findOne(
      {
        where: {
          id
        },
        attributes: ['id', 'status', 'userId', 'paymentProviderId', 'tenantId', 'orderId', 'createdAt', 'amount', 'trackingId'],
        include: {
          model: db.paymentProviders,
          attributes: ['providerName'],
          // where: {
          //   active: true
          // },
          required: true,
          include: {
            model: db.tenantPaymentConfiguration,
            attributes: ['providerKeyValues'],
            where: {
              // active: true,
              tenantId: {
                [Sequelize.Op.eq]: Sequelize.col('DepositRequest.tenant_id')
              }
            },
            required: true
          }
        },
        transaction: sequelizeTransaction,
        lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.DepositRequest },
        skipLocked: false,
      })

    await orderDetails.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.DepositRequest }, transaction: sequelizeTransaction })

    const credentials = orderDetails.dataValues.paymentProvider.tenantPaymentConfigurations[0].providerKeyValues

    const paycookiesGateways = new Set([
      PAYMENT_GATEWAY_NAMES.PAYCOOKIES_PAYIN,
      PAYMENT_GATEWAY_NAMES.PAYCOOKIES_PAYIN2,
      PAYMENT_GATEWAY_NAMES.PAYCOOKIES_PAYIN3,
      PAYMENT_GATEWAY_NAMES.PAYCOOKIES_PAYIN4
    ])

    const peerPayGateways = new Set([
      PAYMENT_GATEWAY_NAMES.PEER_PAY_PAYIN,
      PAYMENT_GATEWAY_NAMES.PEER_PAY_PAYIN1
    ])

    if (orderDetails.paymentProvider.providerName === PAYMENT_GATEWAY_NAMES.PAYWINGS_PAYIN) {
      const response = await paywingsUpdatePayinStatus(orderDetails, credentials, sequelizeTransaction)
      return response
    }
    if (paycookiesGateways.has(orderDetails.paymentProvider.providerName)){
      const response = await paycookiesUpdatePayinStatus(orderDetails, credentials, sequelizeTransaction)
      return response
    }
    if (orderDetails.paymentProvider.providerName === PAYMENT_GATEWAY_NAMES.ACKO_PAYIN) {
      const response = await ackoPayUpdatePayinStatus(orderDetails, credentials, sequelizeTransaction)
      return response
    }
    if (orderDetails?.paymentProvider?.providerName === PAYMENT_GATEWAY_NAMES.SAMBHAVPAY_PAYIN) {
      const response = await sambhavPayUpdatePayinStatus(orderDetails, credentials, sequelizeTransaction)
      return response
    }
    if (orderDetails?.paymentProvider?.providerName === PAYMENT_GATEWAY_NAMES.JASPAY_PAYIN) {
      const response = await jasPayUpdatePayinStatus(orderDetails, credentials, sequelizeTransaction)
      return response
    }
    if (orderDetails?.paymentProvider?.providerName === PAYMENT_GATEWAY_NAMES.XAMAX_PAYIN) {
      const response = await xamaxUpdatePayinStatus(orderDetails, credentials, sequelizeTransaction)
      return response
    }
    if (orderDetails?.paymentProvider?.providerName === PAYMENT_GATEWAY_NAMES.CLOUDCASH_PAYIN) {
      const response = await cloudCashUpdatePayinStatus(orderDetails, credentials, sequelizeTransaction)
      return response
    }
    if (peerPayGateways.has(orderDetails?.paymentProvider?.providerName)) {
      const response = await peerPayUpdatePayinStatus(orderDetails, credentials, sequelizeTransaction)
      return response
    }
    if (orderDetails.paymentProvider.providerName === PAYMENT_GATEWAY_NAMES.ZENXPAY_PAYIN) {
      const response = await zenxPayUpdatePayinStatus(orderDetails, credentials, sequelizeTransaction)
      return response
    }
    if (orderDetails.paymentProvider.providerName === PAYMENT_GATEWAY_NAMES.SEYLAN_PAYIN) {
      const response = await seylanPayUpdatePayinStatus(orderDetails, credentials, sequelizeTransaction)
      return response
    }
     if (orderDetails.paymentProvider.providerName === PAYMENT_GATEWAY_NAMES.TECHPAY_PAYIN) {
      const response = await techPayUpdatePayinStatus(orderDetails, credentials, sequelizeTransaction)
      return response
    }
  } catch (error) {
    throw error
  }
}

export default segregateDepositStatusApi
