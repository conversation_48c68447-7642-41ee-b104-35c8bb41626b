import FormData from 'form-data';
import db from '../../db/models';
import { uuWalletEncryptByPublicKeyForOut } from '../../libs/encryption';

export default async (withdrawalDataParams) => {
  try {
    // Destructure withdrawalDataParams
    const {
      withdrawalRequest,
      tenantPaymentConfig,
    } = withdrawalDataParams;

    // Get User Details
    let user = await db.User.findOne({
      where: {
        id: withdrawalRequest.userId
      },
      include: {
        model: db.Wallet,
        attributes: ['id', 'currencyId', 'amount', 'nonCashAmount'],
        where: {
          ownerType: 'User'
        },
        include: [
          {
            model: db.Currency,
            attributes: ['id', 'code', 'exchangeRate', 'currencyResponseId'],
            required: true
          },
        ],
        required: true
      },
      attributes: ['id']
    })

    if (!user) {
      throw new Error(`User Not Found ${withdrawalRequest.userId}`);
    }

    // Create Provider End Point
    const { merchantId, apiUrl, apiKey, publicKey } = tenantPaymentConfig.providerKeyValues

    let withdrawAmount = parseFloat(withdrawalRequest?.amount);
    const symbol = withdrawalRequest.ifscCode?.trim()
    const chainName = withdrawalRequest.name?.trim()
    const toAddress = withdrawalRequest.accountNumber?.trim()

    const currencyResponse = await db.CurrencyResponse.findOne({
      where: {
        id: user?.Wallet?.Currency?.currencyResponseId
      },
      raw: true
    })

    if (!currencyResponse) {
      throw new Error('Conversion Rates Not Found');
    }
    const convertedAmount = await convertBaseCurrencyToCrypto(db.CryptoCurrency, withdrawAmount, user?.Wallet?.Currency?.exchangeRate, symbol)
    if (!convertedAmount) {
      throw new Error('Currency Conversion Failed.');
    }

    withdrawAmount = parseFloat((parseFloat(convertedAmount)).toFixed(5))


    const APIBody = {
      callBackId: withdrawalRequest.fdTransactionId?.trim(),
      userId: merchantId,
      tenantUserId: withdrawalRequest.userId,
      tenantType: 1,
      amount: withdrawAmount,
      symbol: symbol,
      chainName: chainName,
      toAddr: toAddress,
      tag: '',
    }

    const encryptedData = await uuWalletEncryptByPublicKeyForOut(APIBody, publicKey);
    if (!encryptedData) {
      throw new Error('Encryption Failed');
    }

    const form = new FormData()

    form.append('data', encryptedData)

    const providerEndPoint = `${apiUrl}/api/order/withdrawRequest`;

    const APIConfig = {
      method: 'post',
      url: providerEndPoint,
      headers: {
        'X-API-KEY': apiKey,
        ...form.getHeaders()
      },
    };

    return {
      providerEndPoint,
      APIBody: form,
      APIConfig,
      withdrawAmount
      // Sending from here to update crytoAmount
      //Wihtdraw request can't be updated here as it is locked in transaction
    };

  } catch (error) {
    throw error;
  }
};

async function convertBaseCurrencyToCrypto(CryptoCurrencyModel, amount, walletCurrencyExchangeRate, symbol) {
  const cryptoCurrency = await CryptoCurrencyModel.findOne({
    where: {
      code: symbol
    },
    raw: true
  });

  if (!cryptoCurrency) {
    return false;
  }

  const cryptoExchangeRate = cryptoCurrency.exchangeRate;

  if (cryptoExchangeRate) {
    // Convert user's currency amount to base currency amount
    const baseCurrencyAmount = amount / walletCurrencyExchangeRate;

    // Convert base currency amount to crypto
    const cryptoAmount = baseCurrencyAmount * cryptoExchangeRate;

    return cryptoAmount;
  }
  return false;
}
