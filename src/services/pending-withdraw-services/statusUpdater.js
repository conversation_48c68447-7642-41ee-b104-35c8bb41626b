import { Op } from "sequelize";
import { ACKOPAY_WITHDRAW, EASY_RUPIA_WITHDRAW, PAYCOOKIES_WITHDRAW, PAYOUT_WITHDRAW_PROVIDER, PAYWINGS_WITHDRAW, RPAYOUT_WITHDRAW, SKY_WITHDRAW, UU_WALLET_WITHDRAW_PROVIDER, XAMAX_WITHDRAW_PROVIDER } from "../../common/constants";
import pendingAxiosCall from "../../common/pendingPayout/pendingAxiosCall";
import processPayoutStatus from "../../common/pendingPayout/processPayoutStatus";
import db from "../../db/models";
import updateAckopayPayoutStatus from '../../services/ackopay-PG/updatePayoutStatus';
import updatePaycookiesPayoutStatus from '../../services/paycookies-PG/updatePayoutStatus';
import updateXamaxPayoutStatus from '../../services/xamax-PG/updatePayoutStatus';
import updatePaywingsPayoutStatus from '../../services/paywings-PG/updatePayoutStatus';
import updateRpayoutPayoutStatus from '../../services/rpayout-PG/updatePayoutStatus';
import updateUUWalletPayoutStatus from '../../services/UU-Wallet-PG/updatePayoutStatus';
import updatedPayoutStatus from "../easyrupia/updatedPayoutStatus";
async function statusUpdater(sequelizeTransaction, id) {
  try {
    //console.log("statusUpdater starts");
    const withdrawalRequest = await db.WithdrawRequest.findOne({
      where: {
        id: id,
        status: {
          [Op.notIn]: ['approved', 'cancelled', 'rejected', 'rejected_by_gateway']
        },
      },
      lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.WithdrawRequest },
      transaction: sequelizeTransaction,
      skipLocked: false,
    });

    if (!withdrawalRequest) {
      return false;
    } else {
      await withdrawalRequest.reload({
        lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.WithdrawRequest },
        transaction: sequelizeTransaction
      });
    }
    const transaction = await db.Transaction.findOne({
      where: {
        id: withdrawalRequest.transactionId,
        tenantId: withdrawalRequest.tenantId,
      },
      raw: true
    });
    if (!transaction) {
      return false
    }
    //@TODO----------------------statusUpdater calling status API----------------------------;
    let apiRes = await pendingAxiosCall(withdrawalRequest);
    if (withdrawalRequest.payment_provider_name === SKY_WITHDRAW) {
    }
    else if (withdrawalRequest.payment_provider_name === PAYOUT_WITHDRAW_PROVIDER) {
      await processPayoutStatus(withdrawalRequest, transaction, apiRes, sequelizeTransaction)
    }
    else if (withdrawalRequest.payment_provider_name === PAYWINGS_WITHDRAW) {
      await updatePaywingsPayoutStatus(withdrawalRequest, transaction, apiRes, sequelizeTransaction)
    }
    else if (withdrawalRequest.payment_provider_name === PAYCOOKIES_WITHDRAW) {
      await updatePaycookiesPayoutStatus(withdrawalRequest, transaction, apiRes, sequelizeTransaction)
    }
    else if (withdrawalRequest.payment_provider_name === ACKOPAY_WITHDRAW) {
      await updateAckopayPayoutStatus(withdrawalRequest, transaction, apiRes, sequelizeTransaction)
    }
    else if (withdrawalRequest.payment_provider_name === RPAYOUT_WITHDRAW) {
      await updateRpayoutPayoutStatus(withdrawalRequest, transaction, apiRes, sequelizeTransaction)
    } else if (withdrawalRequest.payment_provider_name === EASY_RUPIA_WITHDRAW) {
      await updatedPayoutStatus(withdrawalRequest, transaction, apiRes, sequelizeTransaction)
    } else if (withdrawalRequest.payment_provider_name === XAMAX_WITHDRAW_PROVIDER) {
      await updateXamaxPayoutStatus(withdrawalRequest, transaction, apiRes, sequelizeTransaction)
    }
    else if (withdrawalRequest.payment_provider_name === UU_WALLET_WITHDRAW_PROVIDER) {
      await updateUUWalletPayoutStatus(withdrawalRequest, transaction, apiRes, sequelizeTransaction)
    }
  } catch (error) {
    throw error
  }
}

export default statusUpdater
