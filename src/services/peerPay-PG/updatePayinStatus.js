import { Sequelize } from 'sequelize'
import cancelDepositBonus from '../../common/cancelDepositBonus'
import cancelLosingBonus from '../../common/cancelLosingBonus'
import { ALLOWED_PERMISSIONS, BONUS_TYPES, DEPOSIT_REQUEST_STATUS, PAYMENT_GATEWAY_QUEUE_TYPE, PEER_PAY_INTEGRATION_CONSTANT, QUEUE_WORKER_CONSTANT, TABLES, TRANSACTION_TYPES } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import v2CurrencyConversion from '../../common/newCurrencyConversion'
import { userFirstDeposit } from '../../common/userFirstDeposit'
import { verifyReferralCode } from '../../common/verifyReferralCode'
import db from '../../db/models'

const axios = require('axios')

async function peerPayUpdatePayinStatus (orderDetails, credentials, sequelizeTransaction) {
  let user
  try {
    const { token, payinStatusCheckApi } = credentials

    const requestData = {
      order_ids: orderDetails?.trackingId
    }

    // Axios configuration
    let axiosConfig = {
      headers: {
        'Authorization': `Bearer ${token}` // Currently fetching token from db
      },
      data: requestData, // Include requestData in the body
      // maxBodyLength: Infinity, // Optional: Adjust if needed for large payloads
    };

    let data = null;
    try {
      data = await axios.post(payinStatusCheckApi, axiosConfig.data, { headers: axiosConfig.headers });
    } catch (error) {
      const axiosResponse = error.response;
      const reqLogObject = {
        requestJson: { body: axiosConfig.data, headers: axiosConfig.headers },
        service: PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_DEPOSIT_REQUEST,
        url: payinStatusCheckApi,
        responseCode:axiosResponse?.status,
        responseJson: {
          data: {
            status: axiosResponse?.status,
            statusText: axiosResponse?.statusText,
            result: axiosResponse?.data
          }
        },
        tenantId: orderDetails?.tenantId
      }

      await db.RequestResponseLog.create(reqLogObject)
      await ErrorLogHelper.logError(error, null, user)
      throw error
    }

    const responseData = data?.data.data[0];

    const reqLogObject = {
      requestJson: { body: requestData, headers: axiosConfig.headers },
      service: PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_DEPOSIT_REQUEST,
      url: payinStatusCheckApi,
      responseJson: {
        data: {
          status: data.status,
          statusText: data.statusText,
          result: data.data,
        }
      },
      tenantId: orderDetails?.tenantId
    }

    await db.RequestResponseLog.create(reqLogObject)

    if (!data.data || data.data.code != PEER_PAY_INTEGRATION_CONSTANT.SUCCESS_STATUS_CODE) { return { message: PEER_PAY_INTEGRATION_CONSTANT.INVALID_RESPONSE, response: data?.data } }

    if (responseData.order_status === PEER_PAY_INTEGRATION_CONSTANT.FAILED_TXNSTATUS || responseData.order_status === PEER_PAY_INTEGRATION_CONSTANT.REJECTED_TXNSTATUS) {

      await db.DepositRequest.update(
        { status: DEPOSIT_REQUEST_STATUS.FAILED },
        {
          where: {
            id: orderDetails.id
          },
          transaction: sequelizeTransaction
        }
      )
      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED,orderDetails.id, orderDetails?.tenantId)

      return { message: PEER_PAY_INTEGRATION_CONSTANT.FAILED_STATUS_UPDATED, response: responseData }
    }

    if (responseData.order_status === PEER_PAY_INTEGRATION_CONSTANT.SUCCESS_TXNSTATUS || responseData.order_status === PEER_PAY_INTEGRATION_CONSTANT.MISMATCHED_TXNSTATUS) {
      if (orderDetails.status === PEER_PAY_INTEGRATION_CONSTANT.COMPLETED_STATUS) {
        return { message: PEER_PAY_INTEGRATION_CONSTANT.STATUS_ALREADY_UPDATED, response: data.data }
      }

      let depositAmount = parseFloat(responseData?.amount);

      if (responseData.order_status === PEER_PAY_INTEGRATION_CONSTANT.MISMATCHED_TXNSTATUS) {
        depositAmount = parseFloat(responseData?.amount_paid);
        await db.DepositRequest.update(
          { oldAmount: orderDetails?.amount, amount: responseData?.amount_paid },
          {
            where: {
              id: orderDetails.id
            },
            transaction: sequelizeTransaction
          }
        );
      }

      // duplicate transaction check
      const transactionExist = await db.Transaction.findOne({
        attributes: ['id'],
        where: {
          transactionId: String(responseData?.merc_ref_id),
          debitTransactionId: String(responseData?.order_id),
          paymentProviderId: orderDetails?.paymentProviderId,
          tenantId: orderDetails?.tenantId
        },
        useMaster: true
      })

      if (transactionExist) { return { message: PEER_PAY_INTEGRATION_CONSTANT.DUPLICATE_TRANSACTION, response: responseData } }

      user = await db.User.findOne({
        attributes: ['id', 'tenantId', 'userName', [Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id AND wallets.owner_type = 'User')`), 'exchangeRate']
        ],
        where: {
          id: orderDetails?.userId,
          tenantId: orderDetails?.tenantId
        },
        include: {
          model: db.Wallet,
          attributes: ['id', 'currencyId', 'amount', 'nonCashAmount'],
          where: {
            ownerType: 'User'
          },
          required: true
        }
      })

      const conversionRate = user?.dataValues?.exchangeRate
      let transactionObject = {
        targetWalletId: user?.Wallet?.id,
        targetCurrencyId: user?.Wallet?.currencyId,
        amount: depositAmount,
        conversionRate,
        actioneeId: orderDetails?.userId,
        actioneeType: 'User',
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        transactionId: responseData?.merc_ref_id,
        debitTransactionId: responseData?.order_id,
        paymentMethod: PEER_PAY_INTEGRATION_CONSTANT.PAYMENT_METHOD,
        paymentProviderId: orderDetails?.paymentProviderId,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        status: 'success',
        success: true,
        comments: PEER_PAY_INTEGRATION_CONSTANT.TRANSACTION_COMMENTS,
        timestamp: Date.now()
      }

      const userWallet = await db.Wallet.findOne({
        where: { ownerId: user.id, ownerType: TABLES.USER },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet
        },
        skipLocked: false
      })

      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet }, transaction: sequelizeTransaction })

      userWallet.amount = (parseFloat((userWallet.amount).toFixed(5)) + depositAmount)
      await userWallet.save({ transaction: sequelizeTransaction })

      transactionObject = await v2CurrencyConversion(transactionObject, userWallet, depositAmount)
      transactionObject.targetAfterBalance = parseFloat(userWallet.amount)
      transactionObject.targetBeforeBalance = (parseFloat(userWallet.amount) - parseFloat(depositAmount))

      const txn = await db.Transaction.create(transactionObject, { transaction: sequelizeTransaction })

       // Meta data
       const trxnMetaData = {
        transactionId: txn.id,
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        metaData: {
          name: responseData?.name,
          email: responseData?.email,
          mobile: responseData?.mobile,
          amount: responseData?.amount,
          amount_paid: responseData?.amount_paid,
          upi_id: responseData?.upi_id,
          order_status: responseData?.order_status,
          utr: responseData?.utr
        }
      }
      await db.TransactionsMetaData.create(trxnMetaData, { transaction: sequelizeTransaction })

      const txnIds = [], bulkData = []
      if (txn) {
        await userFirstDeposit(sequelizeTransaction, txn)
        txnIds.push(txn.id)
      }

      const type = 'payin'
      await cancelLosingBonus(sequelizeTransaction, user.id, user.tenantId, type)
      await cancelDepositBonus(sequelizeTransaction, user.id, user.tenantId, type)
      // Deposit Bonus Queue
      const depositBonusQueueLogObject = {
        type: 'bonus',
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [
          {
            amount: depositAmount,
            user,
            wallet: userWallet,
            transactionIds: txnIds,
            depositTransactionId: txn.id,
            paymentProviders: orderDetails?.paymentProviderId,
            bonusType: BONUS_TYPES.DEPOSIT
          }
        ]
      }
      bulkData.push(depositBonusQueueLogObject)
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }
      bulkData.push(queueLogObject)
      await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })

      orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED
      await orderDetails.save({ transaction: sequelizeTransaction })

      await verifyReferralCode(sequelizeTransaction, txn, DEPOSIT_REQUEST_STATUS.COMPLETED, orderDetails.id, orderDetails?.tenantId)

      const tenantThemeSetting = await db.TenantThemeSetting.findOne({
        attributes: ['allowedModules'],
        where: { tenantId: orderDetails?.tenantId },
        raw: true
      });

      if (tenantThemeSetting && tenantThemeSetting?.allowedModules && tenantThemeSetting?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION)) {
        const depositWagerQueueLogObject = {
          type: QUEUE_WORKER_CONSTANT.DEPOSIT_WAGER_TRACKING,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [orderDetails?.id],
          tenantId: orderDetails?.tenantId
        }
        await db.QueueLog.create(depositWagerQueueLogObject, { transaction: sequelizeTransaction })
      }

      return { message: PEER_PAY_INTEGRATION_CONSTANT.COMPLETED_STATUS_UPDATED, response: data.data }
    }
    return { message: PEER_PAY_INTEGRATION_CONSTANT.STATUS_NOT_UPDATED, response: data.data }
  } catch (error) {
    await ErrorLogHelper.logError(error, null, user)
    throw error
  }
}

export default peerPayUpdatePayinStatus
