import { Op, QueryTypes } from 'sequelize'
import { CRON_LOG_STATUS, SORT_TENANT_PROD, SORT_TENANT_STAGE } from '../../common/constants'
import config from '../../configs/app.config'
import db, { sequelize } from '../../db/models'
import Logger from '../../libs/logger'
import ServiceBase from '../../libs/serviceBase'
import { EMAIL_PROVIDERS, EMAIL_PROVIDERS_CREDENTIAL_KEYS, SUCCESS_MSG } from '../../utils/constants/constant'
import { ERROR_MSG } from '../../utils/constants/error'
import { createSnapshot, formatEURPrice, monthlyWeeklyChange, sendMail } from '../../utils/reports'
export class ComparativeReportData extends ServiceBase {
  async run () {
    const { Tenant: TenantModel, SuperAdminMailConfiguration: MailConfigModel, QueueProcessStatus: QueueProcessStatusModel } = db

    try {
      const queueProcessStatus = await QueueProcessStatusModel.findOne({
        where: {
          service: 'comparative_report_data_cron',
          status: 1
        },
        attributes: ['id']
      })

      if (queueProcessStatus) this.args.cronId = queueProcessStatus?.id // for cron logs

      const promises = []
      let casinoResultData = []
      const casinoResultObject = {}
      let message = SUCCESS_MSG.CREDENTIALS_REQUIRED

      const TenantEmailProvider = await db.TenantCredential.findOne({
        where: { key: 'APP_EMAIL_PROVIDER' },
        attributes: ['value'],
        raw: true
      })

      const emailProvider = (TenantEmailProvider?.value === 'MAIL_GUN') ? EMAIL_PROVIDERS.MAILGUN : EMAIL_PROVIDERS.SENDGRID
      const credentialKeys = EMAIL_PROVIDERS_CREDENTIAL_KEYS[emailProvider]

      const credentials = await MailConfigModel.findAll({
        where: {
          parentType: 'SuperAdminUser',
          value: { [Op.and]: [{ [Op.ne]: null }, { [Op.ne]: '' }] },
          keyProvidedByAccount: { [Op.in]: credentialKeys }
        },
        attributes: ['keyProvidedByAccount', 'value'],
        order: [['keyProvidedByAccount', 'ASC']],
        raw: true
      })

      if (credentials?.length === 5) {
        const date = new Date()
        promises.push(TenantModel.findAll({ where: { id: { [Op.ne]: 1 }, active: true }, attributes: ['id', 'name'], raw: true }))
        promises.push(sequelize.query('SELECT * from get_all_tenant_casino_data( :start_date);', { type: QueryTypes.SELECT, useMaster: false, replacements: { start_date: `${(new Date(date.getFullYear(), date.getMonth() - 1, 1)).toISOString().split('T')[0]} 00:00:00.000+00` } }))
        promises.push(sequelize.query('SELECT * from get_all_tenant_sports_data( :start_date);', { type: QueryTypes.SELECT, useMaster: false, replacements: { start_date: `${(new Date(date.getFullYear(), date.getMonth() - 1, 1)).toISOString().split('T')[0]} 00:00:00.000+00` } }))
        promises.push(sequelize.query('SELECT * from get_all_tenant_transactions_data( :start_date);', { type: QueryTypes.SELECT, useMaster: false, replacements: { start_date: `${(new Date(date.getFullYear(), date.getMonth() - 1, 1)).toISOString().split('T')[0]} 00:00:00.000+00` } }))
        promises.push(sequelize.query('SELECT * from get_all_tenant_casino_player_data( :start_date);', { type: QueryTypes.SELECT, useMaster: false, replacements: { start_date: `${(new Date(date.getFullYear(), date.getMonth() - 1, 1)).toISOString().split('T')[0]} 00:00:00.000+00` } }))
        promises.push(sequelize.query('SELECT * from get_all_tenant_sports_player_data( :start_date);', { type: QueryTypes.SELECT, useMaster: false, replacements: { start_date: `${(new Date(date.getFullYear(), date.getMonth() - 1, 1)).toISOString().split('T')[0]} 00:00:00.000+00` } }))

        const [tenantIds, casinoData, sportData, transactionsData, casinoPlayerCount, sportPlayerCount] = await Promise.all(promises)

        for (const tenant of tenantIds) {
          casinoResultObject[tenant.id] = {
            'Casino Total Bets EUR': { Name: tenant.name.toUpperCase(), Value: 'Casino Total Bets EUR', Yesterday: 0, 'Current Week': 0, 'Previous Week': 0, 'Current Month': 0, 'Previous Month': 0, 'Weekly Change': '0%', 'Monthly Change': '0%' },
            'Casino Total Wins EUR': { Name: tenant.name.toUpperCase(), Value: 'Casino Total Wins EUR', Yesterday: 0, 'Current Week': 0, 'Previous Week': 0, 'Current Month': 0, 'Previous Month': 0, 'Weekly Change': '0%', 'Monthly Change': '0%' },
            'Casino GGR EUR': { Name: tenant.name.toUpperCase(), Value: 'Casino GGR EUR', Yesterday: 0, 'Current Week': 0, 'Previous Week': 0, 'Current Month': 0, 'Previous Month': 0, 'Weekly Change': '0%', 'Monthly Change': '0%' },
            'Casino Total Unique Player Count': { Name: tenant.name.toUpperCase(), Value: 'Casino Total Unique Player Count', Yesterday: 0, 'Current Week': 0, 'Previous Week': 0, 'Current Month': 0, 'Previous Month': 0, 'Weekly Change': '0%', 'Monthly Change': '0%' },
            'Sports Total Bets EUR': { Name: tenant.name.toUpperCase(), Value: 'Sports Total Bets EUR', Yesterday: 0, 'Current Week': 0, 'Previous Week': 0, 'Current Month': 0, 'Previous Month': 0, 'Weekly Change': '0%', 'Monthly Change': '0%' },
            'Sports Total Wins EUR': { Name: tenant.name.toUpperCase(), Value: 'Sports Total Wins EUR', Yesterday: 0, 'Current Week': 0, 'Previous Week': 0, 'Current Month': 0, 'Previous Month': 0, 'Weekly Change': '0%', 'Monthly Change': '0%' },
            'Sports GGR EUR': { Name: tenant.name.toUpperCase(), Value: 'Sports GGR EUR', Yesterday: 0, 'Current Week': 0, 'Previous Week': 0, 'Current Month': 0, 'Previous Month': 0, 'Weekly Change': '0%', 'Monthly Change': '0%' },
            'Sports Total Unique Player Count': { Name: tenant.name.toUpperCase(), Value: 'Sports Total Unique Player Count', Yesterday: 0, 'Current Week': 0, 'Previous Week': 0, 'Current Month': 0, 'Previous Month': 0, 'Weekly Change': '0%', 'Monthly Change': '0%' },
            'Total Deposits EUR': { Name: tenant.name.toUpperCase(), Value: 'Total Deposits EUR', Yesterday: 0, 'Current Week': 0, 'Previous Week': 0, 'Current Month': 0, 'Previous Month': 0, 'Weekly Change': '0%', 'Monthly Change': '0%' },
            'Total Withdrawals EUR': { Name: tenant.name.toUpperCase(), Value: 'Total Withdrawals EUR', Yesterday: 0, 'Current Week': 0, 'Previous Week': 0, 'Current Month': 0, 'Previous Month': 0, 'Weekly Change': '0%', 'Monthly Change': '0%' }
          }
        }

        for (const data of casinoPlayerCount) {
          if (casinoResultObject[+data.tenantId]) {
            casinoResultObject[+data.tenantId]['Casino Total Unique Player Count'].Yesterday = +data?.yesterdayPlayerCount || 0
            casinoResultObject[+data.tenantId]['Casino Total Unique Player Count']['Current Week'] = +data?.currentWeekPlayerCount || 0
            casinoResultObject[+data.tenantId]['Casino Total Unique Player Count']['Previous Week'] = +data?.previousWeekPlayerCount || 0
            casinoResultObject[+data.tenantId]['Casino Total Unique Player Count']['Current Month'] = +data?.currentMonthPlayerCount || 0
            casinoResultObject[+data.tenantId]['Casino Total Unique Player Count']['Previous Month'] = +data?.previousMonthPlayerCount || 0
            casinoResultObject[+data.tenantId]['Casino Total Unique Player Count']['Weekly Change'] = monthlyWeeklyChange(+data?.currentWeekPlayerCount || 0, +data?.previousWeekPlayerCount || 0)
            casinoResultObject[+data.tenantId]['Casino Total Unique Player Count']['Monthly Change'] = monthlyWeeklyChange(+data?.currentMonthPlayerCount || 0, +data?.previousMonthPlayerCount || 0)
          }
        }

        for (const data of sportPlayerCount) {
          if (casinoResultObject[+data.tenantId]) {
            casinoResultObject[+data.tenantId]['Sports Total Unique Player Count'].Yesterday = +data?.yesterdayPlayerCount || 0
            casinoResultObject[+data.tenantId]['Sports Total Unique Player Count']['Current Week'] = +data?.currentWeekPlayerCount || 0
            casinoResultObject[+data.tenantId]['Sports Total Unique Player Count']['Previous Week'] = +data?.previousWeekPlayerCount || 0
            casinoResultObject[+data.tenantId]['Sports Total Unique Player Count']['Current Month'] = +data?.currentMonthPlayerCount || 0
            casinoResultObject[+data.tenantId]['Sports Total Unique Player Count']['Previous Month'] = +data?.previousMonthPlayerCount || 0
            casinoResultObject[+data.tenantId]['Sports Total Unique Player Count']['Weekly Change'] = monthlyWeeklyChange(+data?.currentWeekPlayerCount || 0, +data?.previousWeekPlayerCount || 0)
            casinoResultObject[+data.tenantId]['Sports Total Unique Player Count']['Monthly Change'] = monthlyWeeklyChange(+data?.currentMonthPlayerCount || 0, +data?.previousMonthPlayerCount || 0)
          }
        }

        for (const data of casinoData) {
          if (casinoResultObject[+data.tenantId]) {
            casinoResultObject[+data.tenantId]['Casino Total Bets EUR'].Yesterday = formatEURPrice(+data?.yesterdayBetsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino Total Bets EUR']['Current Week'] = formatEURPrice(+data?.currentWeekBetsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino Total Bets EUR']['Previous Week'] = formatEURPrice(+data?.previousWeekBetsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino Total Bets EUR']['Current Month'] = formatEURPrice(+data?.currentMonthBetsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino Total Bets EUR']['Previous Month'] = formatEURPrice(+data?.previousMonthBetsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino Total Bets EUR']['Weekly Change'] = monthlyWeeklyChange(+data?.currentWeekBetsEur || 0, +data?.previousWeekBetsEur || 0)
            casinoResultObject[+data.tenantId]['Casino Total Bets EUR']['Monthly Change'] = monthlyWeeklyChange(+data?.currentMonthBetsEur || 0, +data?.previousMonthBetsEur || 0)

            casinoResultObject[+data.tenantId]['Casino Total Wins EUR'].Yesterday = formatEURPrice(+data?.yesterdayWinsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino Total Wins EUR']['Current Week'] = formatEURPrice(+data?.currentWeekWinsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino Total Wins EUR']['Previous Week'] = formatEURPrice(+data?.previousWeekWinsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino Total Wins EUR']['Current Month'] = formatEURPrice(+data?.currentMonthWinsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino Total Wins EUR']['Previous Month'] = formatEURPrice(+data?.previousMonthWinsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino Total Wins EUR']['Weekly Change'] = monthlyWeeklyChange(+data?.currentWeekWinsEur || 0, +data?.previousWeekWinsEur || 0)
            casinoResultObject[+data.tenantId]['Casino Total Wins EUR']['Monthly Change'] = monthlyWeeklyChange(+data?.currentMonthWinsEur || 0, +data?.previousMonthWinsEur || 0)

            casinoResultObject[+data.tenantId]['Casino GGR EUR'].Yesterday = formatEURPrice(+data?.yesterdayGgrEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino GGR EUR']['Current Week'] = formatEURPrice(+data?.currentWeekGgrEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino GGR EUR']['Previous Week'] = formatEURPrice(+data?.previousWeekGgrEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino GGR EUR']['Current Month'] = formatEURPrice(+data?.currentMonthGgrEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino GGR EUR']['Previous Month'] = formatEURPrice(+data?.previousMonthGgrEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Casino GGR EUR']['Weekly Change'] = monthlyWeeklyChange(+data?.currentWeekGgrEur || 0, +data?.previousWeekGgrEur || 0)
            casinoResultObject[+data.tenantId]['Casino GGR EUR']['Monthly Change'] = monthlyWeeklyChange(+data?.currentMonthGgrEur || 0, +data?.previousMonthGgrEur || 0)
          }
        }

        for (const data of sportData) {
          if (casinoResultObject[+data.tenantId]) {
            casinoResultObject[+data.tenantId]['Sports Total Bets EUR'].Yesterday = formatEURPrice(+data?.yesterdaySportsBetsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports Total Bets EUR']['Current Week'] = formatEURPrice(+data?.currentWeekSportsBetsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports Total Bets EUR']['Previous Week'] = formatEURPrice(+data?.previousWeekSportsBetsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports Total Bets EUR']['Current Month'] = formatEURPrice(+data?.currentMonthSportsBetsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports Total Bets EUR']['Previous Month'] = formatEURPrice(+data?.previousMonthSportsBetsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports Total Bets EUR']['Weekly Change'] = monthlyWeeklyChange(+data?.currentWeekSportsBetsEur || 0, +data?.previousWeekSportsBetsEur || 0)
            casinoResultObject[+data.tenantId]['Sports Total Bets EUR']['Monthly Change'] = monthlyWeeklyChange(+data?.currentMonthSportsBetsEur || 0, +data?.previousMonthSportsBetsEur || 0)

            casinoResultObject[+data.tenantId]['Sports Total Wins EUR'].Yesterday = formatEURPrice(+data?.yesterdaySportsWinsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports Total Wins EUR']['Current Week'] = formatEURPrice(+data?.currentWeekSportsWinsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports Total Wins EUR']['Previous Week'] = formatEURPrice(+data?.previousWeekSportsWinsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports Total Wins EUR']['Current Month'] = formatEURPrice(+data?.currentMonthSportsWinsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports Total Wins EUR']['Previous Month'] = formatEURPrice(+data?.previousMonthSportsWinsEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports Total Wins EUR']['Weekly Change'] = monthlyWeeklyChange(+data?.currentWeekSportsWinsEur || 0, +data?.previousWeekSportsWinsEur || 0)
            casinoResultObject[+data.tenantId]['Sports Total Wins EUR']['Monthly Change'] = monthlyWeeklyChange(+data?.currentMonthSportsWinsEur || 0, +data?.previousMonthSportsWinsEur || 0)

            casinoResultObject[+data.tenantId]['Sports GGR EUR'].Yesterday = formatEURPrice(+data?.yesterdaySportsGgrEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports GGR EUR']['Current Week'] = formatEURPrice(+data?.currentWeekSportsGgrEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports GGR EUR']['Previous Week'] = formatEURPrice(+data?.previousWeekSportsGgrEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports GGR EUR']['Current Month'] = formatEURPrice(+data?.currentMonthSportsGgrEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports GGR EUR']['Previous Month'] = formatEURPrice(+data?.previousMonthSportsGgrEur.toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Sports GGR EUR']['Weekly Change'] = monthlyWeeklyChange(+data?.currentWeekSportsGgrEur || 0, +data?.previousWeekSportsGgrEur || 0)
            casinoResultObject[+data.tenantId]['Sports GGR EUR']['Monthly Change'] = monthlyWeeklyChange(+data?.currentMonthSportsGgrEur || 0, +data?.previousMonthSportsGgrEur || 0)
          }
        }

        for (const data of transactionsData) {
          if (casinoResultObject[+data.tenantId]) {
            casinoResultObject[+data.tenantId]['Total Deposits EUR'].Yesterday = formatEURPrice(+(+data?.yesterdayDepositsEur).toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Total Deposits EUR']['Current Week'] = formatEURPrice(+(+data?.currentWeekDepositsEur).toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Total Deposits EUR']['Previous Week'] = formatEURPrice(+(+data?.previousWeekDepositsEur).toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Total Deposits EUR']['Current Month'] = formatEURPrice(+(+data?.currentMonthDepositsEur).toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Total Deposits EUR']['Previous Month'] = formatEURPrice(+(+data?.previousMonthDepositsEur).toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Total Deposits EUR']['Weekly Change'] = monthlyWeeklyChange(+data?.currentWeekDepositsEur || 0, +data?.previousWeekDepositsEur || 0)
            casinoResultObject[+data.tenantId]['Total Deposits EUR']['Monthly Change'] = monthlyWeeklyChange(+data?.currentMonthDepositsEur || 0, +data?.previousMonthDepositsEur || 0)

            casinoResultObject[+data.tenantId]['Total Withdrawals EUR'].Yesterday = formatEURPrice(+(+data?.yesterdayWithdrawEur).toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Total Withdrawals EUR']['Current Week'] = formatEURPrice(+(+data?.currentWeekWithdrawEur).toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Total Withdrawals EUR']['Previous Week'] = formatEURPrice(+(+data?.previousWeekWithdrawEur).toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Total Withdrawals EUR']['Current Month'] = formatEURPrice(+(+data?.currentMonthWithdrawEur).toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Total Withdrawals EUR']['Previous Month'] = formatEURPrice(+(+data?.previousMonthWithdrawEur).toFixed(2) || 0)
            casinoResultObject[+data.tenantId]['Total Withdrawals EUR']['Weekly Change'] = monthlyWeeklyChange(+data?.currentWeekWithdrawEur || 0, +data?.previousWeekWithdrawEur || 0)
            casinoResultObject[+data.tenantId]['Total Withdrawals EUR']['Monthly Change'] = monthlyWeeklyChange(+data?.currentMonthWithdrawEur || 0, +data?.previousMonthWithdrawEur || 0)
          }
        }

        const sortTenants = (config.get('env') === 'production') ? SORT_TENANT_PROD : SORT_TENANT_STAGE
        const sortOrder = new Map(sortTenants.map((tenant, index) => [tenant.id, index]))

        const sortedTenants = [...Object.keys(casinoResultObject)].sort((a, b) => {
          const indexA = sortOrder.has(a) ? sortOrder.get(a) : Infinity
          const indexB = sortOrder.has(b) ? sortOrder.get(b) : Infinity
          return indexA - indexB
        })

        for (const tenant of sortedTenants) {
          casinoResultData = [...casinoResultData, ...Object.values(casinoResultObject[tenant])]
        }

        const casinoImage = createSnapshot({ jsonData: { resultData: casinoResultData }, headers: Object.keys(casinoResultData[0]) })

        if (casinoImage) {
          await sendMail({ credentials, emailProvider, casinoJpegStream: casinoImage })
          message = SUCCESS_MSG.EMAIL_SUCCESS
        } else message = ERROR_MSG.FAILED
      }

      return { success: true, message }
    } catch (error) {
      this.args.status = CRON_LOG_STATUS.FAILED
      this.args.errorMsg = error.message || null
      Logger.error(`Error in comparative report service - ${error}`)
      this.addError('InternalServerErrorType', error)
    }
  }
}
