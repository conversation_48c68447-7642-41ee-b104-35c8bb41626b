import { GAME_ID_PROVIDERS, NOT_SEAT_ID_PROVIDERS, TABLE_ID_PROVIDERS, CRON_LOG_STATUS } from '../../common/constants'
import { sequelize } from '../../db/models'
import Logger from '../../libs/logger'
import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG } from '../../utils/constants/constant'
import db from '../../db/models'

export class PremiumGameTransactionSummaryData extends ServiceBase {
  async run () {

    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'premium_game_transaction_summary_data_cron',
          status: 1
        },
        attributes: ['id']
      })

      if (queueProcessStatus) this.args.cronId = queueProcessStatus?.id  // for cron logs
      //console.log('caled=========================>PremiumGameTransactionSummaryData',`call premium_game_transactions_summary_data('${TABLE_ID_PROVIDERS}','${GAME_ID_PROVIDERS}','${NOT_SEAT_ID_PROVIDERS}');`)

      /**
       * If data exists in table, then run the query for last date else overall data will be calculated
       */
      await sequelize.query(`call premium_game_transactions_summary_data('${TABLE_ID_PROVIDERS}','${GAME_ID_PROVIDERS}','${NOT_SEAT_ID_PROVIDERS}');`)


      return { success: true, message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.args.status = CRON_LOG_STATUS.FAILED
      this.args.errorMsg = error.message || null
      Logger.error(`Error in updating premium game transactions summary - ${error}`)
      this.addError('InternalServerErrorType', error)
    }
  }
}
