import { CRON_LOG_STATUS } from '../../common/constants'
import db, { sequelize } from '../../db/models'
import Logger from '../../libs/logger'
import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG } from '../../utils/constants/constant'

export class BetTransactionsSummary extends ServiceBase {
  async run () {
    const { BetsTransactionSummary: BetsTransactionSummaryModel,QueueProcessStatus: QueueProcessStatusModel
     } = db

    try {
      console.log('------------------------BetTransactionsSummary_worker_called----------------------------------')
      const queueProcessStatus = await QueueProcessStatusModel.findOne({
        where: {
          service: 'bet_transactions_summary_cron',
          status: 1
        },
        attributes: ['id']
      })
      if (queueProcessStatus) this.args.cronId = queueProcessStatus?.id  // for cron logs
      console.log('------------------------BetTransactionsSummary_cron_active----------------------------------')
      const dataExists = await BetsTransactionSummaryModel.findOne({ attributes: ['id'] })

      /**
       * If data exists in table, then run the query for last date else overall data will be calculated
       */

      if (dataExists) {
        console.log('------------------------BetTransactionsSummary_dataExists----------------------------------')
        await sequelize.query('call sports_transactions_summary_data();')
      }

      return { success: true, message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      console.log('------------------------BetTransactionsSummary_ERROR----------------------------------',error)
      this.args.status = CRON_LOG_STATUS.FAILED
      this.args.errorMsg = error.message || null
      Logger.error(`Error in updating transactions summary - ${error}`)
      this.addError('InternalServerErrorType', error)
    }
  }
}
