import { GAME_ID_PROVIDERS, NOT_SEAT_ID_PROVIDERS, TABLE_ID_PROVIDERS } from '../../common/constants'
import db, { sequelize } from '../../db/models'
import Logger from '../../libs/logger'
import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG } from '../../utils/constants/constant'
export class TransactionSummaryData extends ServiceBase {
  async run () {
    const { TransactionsSummary: TransactionsSummaryModel } = db

    try {
      const dataExists = await TransactionsSummaryModel.findOne({ attributes: ['id'] })

      /**
       * If data exists in table, then run the query for last date else overall data will be calculated
       */

      if (dataExists) {
        await sequelize.query(`call casino_transactions_summary_data('${TABLE_ID_PROVIDERS}','${GAME_ID_PROVIDERS}','${NOT_SEAT_ID_PROVIDERS}');`)
      }

      await sequelize.query('REFRESH MATERIALIZED VIEW transactions_providers_list;')

      return { success: true, message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      Logger.error(`Error in updating transactions summary - ${error}`)
      this.addError('InternalServerErrorType', error)
    }
  }
}
