import { Sequelize } from 'sequelize'
import { bonusQueueRollover } from '../../common/bonusQueueRollover'
import cancelDepositBonus from '../../common/cancelDepositBonus'
import cancelLosingBonus from '../../common/cancelLosingBonus'
import { ALLOWED_PERMISSIONS, DEPOSIT_REQUEST_STATUS, PAYMENT_GATEWAY_QUEUE_TYPE, PAYWINGS_INTEGRATION_CONSTANT, QUEUE_WORKER_CONSTANT, TABLES, TRANSACTION_TYPES } from '../../common/constants'
import v2CurrencyConversion from '../../common/newCurrencyConversion'
import { userFirstDeposit } from '../../common/userFirstDeposit'
import { v2DepositBonusCheck } from '../../common/v2DepositBonusCheck'
import { verifyReferralCode } from '../../common/verifyReferralCode'
import db from '../../db/models'

const axios = require('axios')

async function paywingsUpdatePayinStatus (orderDetails, credentials, sequelizeTransaction) {
  try {
    const dateObj = new Date(orderDetails.createdAt)
    const formattedDate = dateObj.toISOString().split('T')[0]
    const requestData = {
      Order_ID: orderDetails?.orderId,
      TxnDate: formattedDate
    }

    // Credentials
    const merchantId = credentials.merchantId
    const payinApiKey = credentials.payinApiKey
    const basicAuth = Buffer.from(`${merchantId}:${payinApiKey}`).toString('base64')

    // Axios configuration
    const axiosConfig = {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Basic ${basicAuth}`
      }
    }

    const url = PAYWINGS_INTEGRATION_CONSTANT.PAYIN_STATUS_CHECK_API
    const { data } = await axios.post(url, requestData, axiosConfig)

    const reqLogObject = {
      requestJson: { body: url, requestData, axiosConfig },
      service: PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_DEPOSIT_REQUEST,
      url: 'queue-worker',
      responseJson: { data: data },
      tenantId: orderDetails?.tenantId
    }
    await db.RequestResponseLog.create(reqLogObject)

    if (!data) { return { message: PAYWINGS_INTEGRATION_CONSTANT.RESPONSE_NOT_FOUND, response: null } }

    if ((data.respCode === PAYWINGS_INTEGRATION_CONSTANT.SUCCESS_RESPCODE && data.data.StatusCode === PAYWINGS_INTEGRATION_CONSTANT.FAILED_STATUS) || (data.respCode === PAYWINGS_INTEGRATION_CONSTANT.FAILED_STATUS) || (data.respCode === PAYWINGS_INTEGRATION_CONSTANT.SUCCESS_RESPCODE && data.data.StatusCode === PAYWINGS_INTEGRATION_CONSTANT.REFUNDED_STATUS)) {
      await db.DepositRequest.update(
        { status: DEPOSIT_REQUEST_STATUS.FAILED },
        {
          where: {
            orderId: data?.data?.ORDER_ID,
            ...(data?.data?.GatewayTID !== null && { trackingId: data?.data?.GatewayTID }),
            ...(data?.data?.Amount !== null && { amount: data?.data?.Amount }),
            tenantId: orderDetails?.tenantId
          },
          transaction: sequelizeTransaction
        }
      )
      const user = await db.User.findOne({
        attributes: ['id', 'tenantId'],
        where: {
          id: orderDetails?.userId,
          tenantId: orderDetails?.tenantId
        },
        raw : true
      })
      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED,orderDetails.id, orderDetails?.tenantId)

      return { message: PAYWINGS_INTEGRATION_CONSTANT.FAILED_STATUS_UPDATED, response: data }
    }

    if (data.respCode === PAYWINGS_INTEGRATION_CONSTANT.SUCCESS_RESPCODE && data.data.StatusCode === PAYWINGS_INTEGRATION_CONSTANT.SUCCESS_STATUS) {
      if (orderDetails.status === PAYWINGS_INTEGRATION_CONSTANT.COMPLETED_STATUS) { return { message: PAYWINGS_INTEGRATION_CONSTANT.STATUS_ALREADY_UPDATED, response: data } }

      // duplicate transaction check
      const transactionExist = await db.Transaction.findOne({
        attributes: ['id'],
        where: {
          transactionId: data?.data?.GatewayTID,
          debitTransactionId: data?.data?.ORDER_ID,
          paymentProviderId: orderDetails?.paymentProviderId,
          tenantId: orderDetails?.tenantId
        },
        useMaster: true
      })

      if (transactionExist) { return { message: PAYWINGS_INTEGRATION_CONSTANT.DUPLICATE_TRANSACTION, response: data } }

      const user = await db.User.findOne({
        attributes: ['id', 'tenantId', 'userName', 'withdrawWagerAllowed', [Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id AND wallets.owner_type = 'User')`), 'exchangeRate']
        ],
        where: {
          id: orderDetails?.userId,
          tenantId: orderDetails?.tenantId
        },
        include: {
          model: db.Wallet,
          attributes: ['id', 'currencyId', 'amount', 'nonCashAmount'],
          where: {
            ownerType: 'User'
          },
          required: true
        }
      })

      const depositAmount = parseFloat(data?.data?.Amount)
      const conversionRate = user?.dataValues?.exchangeRate
      let transactionObject = {
        targetWalletId: user?.Wallet?.id,
        targetCurrencyId: user?.Wallet?.currencyId,
        amount: depositAmount,
        conversionRate,
        actioneeId: orderDetails?.userId,
        actioneeType: 'User',
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        transactionId: data?.data?.GatewayTID,
        debitTransactionId: data?.data?.ORDER_ID,
        paymentMethod: PAYWINGS_INTEGRATION_CONSTANT.PAYMENT_METHOD,
        paymentProviderId: orderDetails?.paymentProviderId,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        status: 'success',
        success: true,
        comments: PAYWINGS_INTEGRATION_CONSTANT.TRANSACTION_COMMENTS
      }

      const userWallet = await db.Wallet.findOne({
        where: { ownerId: user.id, ownerType: TABLES.USER },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet
        },
        skipLocked: false
      })

      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet }, transaction: sequelizeTransaction })

      userWallet.amount = (parseFloat((userWallet.amount).toFixed(5)) + depositAmount)
      await userWallet.save({ transaction: sequelizeTransaction })

      transactionObject = await v2CurrencyConversion(transactionObject, userWallet, depositAmount)
      transactionObject.targetAfterBalance = parseFloat(userWallet.amount)
      transactionObject.targetBeforeBalance = (parseFloat(userWallet.amount) - parseFloat(depositAmount))

      const txn = await db.Transaction.create(transactionObject, { transaction: sequelizeTransaction })

       // Meta data
       const trxnMetaData = {
        transactionId: txn.id,
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        metaData: { mdr: data?.data?.MDR, gst: data?.data?.GST, BankRefNum: data?.data?.GST }
      }
      await db.TransactionsMetaData.create(trxnMetaData, { transaction: sequelizeTransaction })

      const txnIds = []
      if (txn) {
        await userFirstDeposit(sequelizeTransaction, txn)
        txnIds.push(txn.id)
      }

      const type = 'payin'
      await cancelLosingBonus(sequelizeTransaction, user.id, user.tenantId, type)
      await cancelDepositBonus(sequelizeTransaction, user.id, user.tenantId, type)
      const depositBonusID = await v2DepositBonusCheck(sequelizeTransaction, depositAmount, user, userWallet, txnIds, txn.id, orderDetails?.paymentProviderId)
      await bonusQueueRollover(depositAmount, txn.id, user.id, orderDetails?.paymentProviderId, depositBonusID, sequelizeTransaction)

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }
      await db.QueueLog.create(queueLogObject, { transaction: sequelizeTransaction })


      orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED
      await orderDetails.save({ transaction: sequelizeTransaction })
      await verifyReferralCode(sequelizeTransaction, txn, DEPOSIT_REQUEST_STATUS.COMPLETED,orderDetails.id, orderDetails?.tenantId)

      const tenantThemeSetting = await db.TenantThemeSetting.findOne({
        attributes: ['allowedModules'],
        where: { tenantId: orderDetails?.tenantId },
        raw: true
      });

      if (tenantThemeSetting && tenantThemeSetting?.allowedModules && tenantThemeSetting?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION) && !user?.withdrawWagerAllowed) {
        const depositWagerQueueLogObject = {
          type: QUEUE_WORKER_CONSTANT.DEPOSIT_WAGER_TRACKING,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [orderDetails?.id],
          tenantId: orderDetails?.tenantId
        }
        await db.QueueLog.create(depositWagerQueueLogObject, { transaction: sequelizeTransaction })
      }

      return { message: PAYWINGS_INTEGRATION_CONSTANT.COMPLETED_STATUS_UPDATED, response: data }
    }
    return { message: PAYWINGS_INTEGRATION_CONSTANT.STATUS_NOT_UPDATED, response: data }
  } catch (error) {
    throw error
  }
}

export default paywingsUpdatePayinStatus
