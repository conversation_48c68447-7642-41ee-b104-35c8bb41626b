import axios from 'axios'
import { LOGIN_TYPE, PROD_TENANTS, STAGE_TENANTS } from '../common/constants'
import Error<PERSON>ogHelper from '../common/errorLog'
import config from "../configs/app.config"
import db from '../db/models'

export default async (data) => {
  try {
    const {
      WithdrawRequest: WithdrawRequestModel,
      TenantThemeSetting: TenantThemeSettingModel,
      UserPreferenceType: UserPreferenceTypeModel
    } = db

    const themeDetails = await TenantThemeSettingModel.findOne({
      attributes: ['smsEnable', 'allowedModules', 'smsGateway', 'userLoginType'],
      where: { tenantId: +data.tenantId }
    })

    if (!(themeDetails?.allowedModules && themeDetails?.allowedModules.split(',').includes('sendSmsAlert')) || !themeDetails.smsEnable) return

    const withdrawRequest = await WithdrawRequestModel.findOne({
      attributes: ['id', 'userId', 'status'],
      where: { id: data?.id?.id, tenantId: +data.tenantId }
    })
    if (!withdrawRequest) return

    const statusMapping = {
      approved: 'approve',
      rejected: 'reject', // rejected by admin
      rejected_by_gateway: 'reject', // rejected by gateway
      pending_by_gateway: 'withdrawInitiate',
      pending: 'initiateByUser' // when player initiate a withdraw request
    }
    const value = statusMapping[withdrawRequest.status]
    if (themeDetails?.smsEnable && themeDetails?.smsEnable.split(',').includes(value)) {

      let otpPreference = themeDetails?.userLoginType === LOGIN_TYPE.BOTH
        ? +(await UserPreferenceTypeModel.findOne({
          attributes: ['value'],
          where: { tenantId: +data.tenantId, userId: withdrawRequest?.userId, preferenceType: 'otp' }
        }))?.value
        : themeDetails?.userLoginType

      if (!otpPreference) {
        otpPreference = LOGIN_TYPE.MOBILE
      }

      if (themeDetails.smsGateway === 'MessageCentralSMS' && otpPreference === LOGIN_TYPE.MOBILE) return // not supported

      // call user be api for sending sms
      const axiosData = {
        userId: +withdrawRequest.userId,
        smsGateway: themeDetails.smsGateway,
        allowedModules: themeDetails?.allowedModules,
        smsPreference: otpPreference,
        value
      }

      const domain = config.get('env') === 'production' ? PROD_TENANTS[+data.tenantId]?.domain : STAGE_TENANTS[+data.tenantId]?.domain
      const url = 'https://api.' + `${domain}` + '/app/send-sms-alerts'
      const apiConfig = {
        method: "post",
        url,
        data: axiosData
      }

      await axios(apiConfig)
        .then(function (response) {
          return response
        })
        .catch(function (error, response) {
          return error
        })
    }
  } catch (error) {
    await ErrorLogHelper.logError(error, null, { tenantId: +data.tenantId })
    throw error
  }
}
