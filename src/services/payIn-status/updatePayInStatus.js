import { Op } from 'sequelize'
import { bonusQueueRollover } from '../../common/bonusQueueRollover'
import cancelDepositBonus from '../../common/cancelDepositBonus'
import cancelLosingBonus from '../../common/cancelLosingBonus'
import { ALLOWED_PERMISSIONS, DEPOSIT_REQUEST_STATUS, PAYIN_COMMENT, PAYIN_DEPOSIT_STATUS_URL, QUEUE_WORKER_CONSTANT, TRANSACTION_TYPES } from '../../common/constants'
import currencyConversion from '../../common/currencyConversion'
import { depositBonusCheck } from '../../common/depositBonusCheck'
import payInAxiosCall from '../../common/payIn/payInAxiosCall'
import userCurrencyExchange from '../../common/userCurrencyExchange'
import { userFirstDeposit } from '../../common/userFirstDeposit'
import { verifyReferralCode } from '../../common/verifyReferralCode'
import { walletLocking } from '../../common/walletLocking'
import db from '../../db/models'
import '../../libs/setUpGraceFullShutDown'

async function updatePayInStatus (sequelizeTransaction, depositId) {
  try {
    const depositRequest = await db.DepositRequest.findOne({
      where: {
        id: depositId,
        depositType: 'payment_providers',
        status: {[Op.ne]:'completed'}
      },
      transaction: sequelizeTransaction,
      lock: {
        level: sequelizeTransaction.LOCK.UPDATE,
        of: db.DepositRequest,
      },
      skipLocked: false,
    })
    if(depositRequest){
      await depositRequest.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.DepositRequest }, transaction: sequelizeTransaction })

      let user = await db.User.findOne({
        where: {
          id: depositRequest.userId
        },
        include: db.Wallet,
        attributes: ['id', 'userName', 'tenantId', 'withdrawWagerAllowed']
      })

      const url = `${PAYIN_DEPOSIT_STATUS_URL}${depositRequest.orderId}`
      const response = await payInAxiosCall(depositRequest)
      if (response.Result && response.Result.IsSuccess === true && response.Result.Status === "Success") {
        const result = response.Result
        result.Amount = +(result.Amount / 100)
        let transactionObject = {
          targetWalletId: user.Wallet.id,
          targetCurrencyId: user.Wallet.currencyId,
          amount: result.Amount,
          conversionRate: await userCurrencyExchange(db.Currency, user.Wallet.currencyId),
          comments: PAYIN_COMMENT,
          actioneeId: user.id,
          actioneeType: 'User',
          tenantId: user.tenantId,
          timestamp: new Date().getTime(),
          transactionType: TRANSACTION_TYPES.DEPOSIT,
          transactionId: result.TransactionId,
          debitTransactionId: result.OrderId,
          paymentMethod: result.PaymentMode,
          paymentProviderId: depositRequest.paymentProviderId,
          //errorDescription: 'Completed Successfully',
          errorCode: 0,
          status: 'success',
          success: true
        }
        const txnExist = await db.Transaction.findOne({
          where: {
            //debitTransactionId: result.OrderId,
            transactionId: result.TransactionId,
            tenantId: user.tenantId,
            status: 'success'
          },
          attributes: ['id'],
          raw: true,
          useMaster: true
        })
        if(txnExist){
          return { message: 'Success' }
        }
        const userWallet = await walletLocking(user, sequelizeTransaction)
        await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet }, transaction: sequelizeTransaction })

        userWallet.amount = parseFloat(userWallet.amount) + parseFloat(result.Amount)
        await userWallet.save({ transaction: sequelizeTransaction })

        user = { ...user.dataValues, Wallet: userWallet }

        transactionObject = await currencyConversion(transactionObject, user, result.Amount)
        transactionObject.targetAfterBalance = userWallet.amount
        transactionObject.targetBeforeBalance = parseFloat(userWallet.amount) - parseFloat(result.Amount)
        const txn2 = await db.Transaction.create(transactionObject, { transaction: sequelizeTransaction })

        const txnIds = []
        if (txn2) {
          await userFirstDeposit(sequelizeTransaction, txn2)
          txnIds.push(txn2.id)
        }

        const type = 'payin'
        await cancelLosingBonus(sequelizeTransaction, user.id, user.tenantId, type)
        await cancelDepositBonus(sequelizeTransaction, user.id, user.tenantId, type)

        const depositBonusID = await depositBonusCheck(result.Amount, user, txnIds, sequelizeTransaction, txn2.id, depositRequest.paymentProviderId)
        await bonusQueueRollover(result.Amount, txn2.id, user.id, depositRequest.paymentProviderId, depositBonusID, sequelizeTransaction)

        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds
        }
        await db.DepositRequest.update({
          status: DEPOSIT_REQUEST_STATUS.COMPLETED,
          remark: response.Status.returnMessage
        },
          {
            where: { id: depositId },
            transaction: sequelizeTransaction
          }
        )
      await verifyReferralCode(sequelizeTransaction, txn2, DEPOSIT_REQUEST_STATUS.COMPLETED,depositRequest.id, depositRequest?.tenantId)


        const tenantThemeSetting = await db.TenantThemeSetting.findOne({
          attributes: ['allowedModules'],
          where: { tenantId: user?.tenantId },
          raw: true
        });

        if (tenantThemeSetting && tenantThemeSetting?.allowedModules && tenantThemeSetting?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION) && !user?.withdrawWagerAllowed) {
          const depositWagerQueueLogObject = {
            type: QUEUE_WORKER_CONSTANT.DEPOSIT_WAGER_TRACKING,
            status: QUEUE_WORKER_CONSTANT.READY,
            ids: [depositId],
            tenantId: user?.tenantId
          }
          await db.QueueLog.create(depositWagerQueueLogObject, { transaction: sequelizeTransaction })
        }

        const queueLog = await db.QueueLog.create(queueLogObject, { transaction: sequelizeTransaction })

      } else if (response.Result && response.Result.IsSuccess === false && response.Result.Status === "ProviderCreated") {
        //update opened transaction to close
        await db.DepositRequest.update({
          status: DEPOSIT_REQUEST_STATUS.FAILED,
          remark: 'payment initiated'
        },
          {
            where: { id: depositId },
            transaction: sequelizeTransaction
          }
        )
      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED,depositRequest.id, depositRequest?.tenantId)

      } else if (response.Result && response.Result.IsSuccess === false && response.Result.Status === "Failed") {
        //update opened transaction to close
        await db.DepositRequest.update({
          status: DEPOSIT_REQUEST_STATUS.FAILED,
          remark: response.Status.returnMessage
        },
          {
            where: { id: depositId },
            transaction: sequelizeTransaction
          }
        )
      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED,depositRequest.id, depositRequest?.tenantId)


      } else if (response.Result && response.Result.IsSuccess === false && response.Result.Status === "Pending") {
      } else {
        await db.DepositRequest.update({
          status: DEPOSIT_REQUEST_STATUS.FAILED,
          remark: response.Status.returnMessage
        },
          {
            where: { id: depositId },
            transaction: sequelizeTransaction
          }
        )
      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED,depositRequest.id, depositRequest?.tenantId)

      }
    }

    return { message: 'Success' }
  } catch (error) {
    throw error
  }
}

export default updatePayInStatus
