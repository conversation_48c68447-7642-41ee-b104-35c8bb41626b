import { Sequelize } from 'sequelize'
import { v4 as uuidv4 } from 'uuid'
import { ALLOWED_PERMISSIONS, DEPOSIT_REQUEST_STATUS, INDIGRIT_INTEGRATION_CONSTANT, PAYMENT_GATEWAY_QUEUE_TYPE, QUEUE_WORKER_CONSTANT, TABLES, TRANSACTION_TYPES } from '../../common/constants'
import db from '../../db/models'

import newCurrencyConversion from '../../common/newCurrencyConversion'

import { bonusQueueRollover } from '../../common/bonusQueueRollover'
import { depositBonusCheck } from '../../common/depositBonusCheck'
import { userFirstDeposit } from '../../common/userFirstDeposit'
import { verifyReferralCode } from '../../common/verifyReferralCode'

const axios = require('axios')

async function statusResolver (sequelizeTransaction, transactionId, tenantId) {

  const paymentProviderData = await db.paymentProviders.findOne({
    where: {
      providerName: INDIGRIT_INTEGRATION_CONSTANT.PROVIDER_NAME
    },
    include: [
      {
        model: db.tenantPaymentConfiguration,
        where: {
          active: true,
          tenantId,
        },
        attributes: ['providerKeyValues'],
        required: true
      },
    ],
    raw: true,
    nest: true
  })

  if (!paymentProviderData)
    return { message: "Payment Provider not found", data: null }

  const providerKeyValues = paymentProviderData?.tenantPaymentConfigurations?.providerKeyValues
  const depositStatusApi = providerKeyValues?.depositStatusApi
  const url = `${depositStatusApi}${transactionId}`

  try {
    const response = await axios.get(url, {
      auth: {
        username: providerKeyValues?.secret_key,
        password: '',
      },
    })

    if(!response)
      return { message: "Response not found", data: null }

    const reqLogObject = {
      requestJson: { body: url },
      service: PAYMENT_GATEWAY_QUEUE_TYPE.INDIGRIT_DEPOSIT_UPDATE_STATUS ,
      url: 'queue-worker',
      responseJson: { data: response.data },
      tenantId
    }
    const createLog = await db.RequestResponseLog.create(reqLogObject)

    if (response.data.status === INDIGRIT_INTEGRATION_CONSTANT.FAILED_STATUS) {
      await db.DepositRequest.update(
        { status: response.data.status },
        {
          where: {
            orderId: response.data.deposit_id,
            trackingId: response.data.tracking_id,
            amount: response.data.amount,
            tenantId
          },
          transaction: sequelizeTransaction
        }
      )
      return { message: "Failed status updated successfully", data: response.data }
    }

    if (response.data.status === INDIGRIT_INTEGRATION_CONSTANT.SUCCESS_STATUS) {
      const orderDetails = await db.DepositRequest.findOne(
        {
          where: {
            orderId: response.data.deposit_id,
            trackingId: response.data.tracking_id,
            amount: response.data.amount,
            tenantId
          },
          attributes: ['id', 'status', 'userId', 'paymentProviderId']
        }
      )

      if(!orderDetails)
        return { message: "Order details not found", data: response.data }

      if (orderDetails.status === INDIGRIT_INTEGRATION_CONSTANT.COMPLETED_STATUS)
        return { message: "Already status updated", data: response.data }

      let user = await db.User.findOne({
        attributes: ['id', 'tenantId', 'userName', 'withdrawWagerAllowed', [Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id)`), 'exchangeRate']],
        where: {
          id: orderDetails.userId
        },
        include: {
          model: db.Wallet,
          attributes: ['id', 'currencyId', 'amount', 'nonCashAmount'],
          required: true
        }
      })

      if(!user)
        return { message: "User not found", data: response.data }

      const depositAmount = parseFloat(response.data.amount)
      const transactionId = uuidv4()
      const conversionRate = user?.dataValues?.exchangeRate
      let transactionObject = {
        targetWalletId: user?.Wallet?.id,
        targetCurrencyId: user?.Wallet?.currencyId,
        amount: depositAmount,
        conversionRate,
        actioneeId: orderDetails?.userId,
        actioneeType: 'User',
        tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        transactionId,
        debitTransactionId: response?.data?.deposit_id,
        paymentMethod: INDIGRIT_INTEGRATION_CONSTANT.PAYMENT_METHOD,
        paymentProviderId: orderDetails?.paymentProviderId,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        status: 'success',
        success: true,
        comments: INDIGRIT_INTEGRATION_CONSTANT.INDIGRIT_TRANSACTION_COMMENTS
      }

      const userWallet = await db.Wallet.findOne({
        where: { ownerId: user.id, ownerType: TABLES.USER },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet
        },
        skipLocked: false
      })
      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet }, transaction: sequelizeTransaction })
      user = { ...user.dataValues, Wallet: userWallet }

      userWallet.amount += parseFloat(depositAmount)

      await userWallet.save({ transaction: sequelizeTransaction })

      transactionObject = await newCurrencyConversion(transactionObject, user.Wallet, depositAmount)
      transactionObject.targetAfterBalance = parseFloat(userWallet.amount)
      transactionObject.targetBeforeBalance = (parseFloat(userWallet.amount) - parseFloat(depositAmount))

      const txn = await db.Transaction.create(transactionObject, { transaction: sequelizeTransaction })

      // Meta data
      const trxnMetaData = {
        transactionId: txn.id,
        tenantId: tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        metaData: { trackingId: response?.data?.tracking_id }
      }
      await db.TransactionsMetaData.create(trxnMetaData, { transaction: sequelizeTransaction })

      const txnIds = []
      if (txn) {
        await userFirstDeposit(sequelizeTransaction, txn)
        txnIds.push(txn.id)
      }

      const depositBonusID = await depositBonusCheck(depositAmount, user, txnIds, sequelizeTransaction, txn.id, orderDetails?.paymentProviderId)
       await bonusQueueRollover(depositAmount, txn.id, user.id, orderDetails?.paymentProviderId, depositBonusID, sequelizeTransaction)

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }
      const queueLog = await db.QueueLog.create(queueLogObject, { transaction: sequelizeTransaction })

      orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED
      await orderDetails.save({ transaction: sequelizeTransaction })

      await verifyReferralCode(sequelizeTransaction, txn, DEPOSIT_REQUEST_STATUS.COMPLETED,orderDetails.id, tenantId)


      const tenantThemeSetting = await db.TenantThemeSetting.findOne({
        attributes: ['allowedModules'],
        where: { tenantId },
        raw: true
      });

      if (tenantThemeSetting && tenantThemeSetting?.allowedModules && tenantThemeSetting?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION) && !user?.withdrawWagerAllowed) {
        const depositWagerQueueLogObject = {
          type: QUEUE_WORKER_CONSTANT.DEPOSIT_WAGER_TRACKING,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [orderDetails?.id],
          tenantId
        }
        await db.QueueLog.create(depositWagerQueueLogObject, { transaction: sequelizeTransaction })
      }

    }
    return { message: "Completed status updated successfully", data: response.data }
  } catch (error) {
    throw error
  }
}

export default statusResolver
