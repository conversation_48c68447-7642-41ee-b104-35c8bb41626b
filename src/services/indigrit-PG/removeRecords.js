import db from '../../db/models'
import { Sequelize, Op } from 'sequelize'
import { INDIGRIT_INTEGRATION_CONSTANT } from '../../common/constants'

export default async () => {
  try {
    const queueProcessStatus = await db.QueueProcessStatus.findOne({
      where: {
        service: 'remove_indigrit_records_cron',
        status: 1
      },
      attributes: ['id']
    })
    if (queueProcessStatus) {
      await db.DepositRequest.destroy({
        where: {
          paymentProviderId: [Sequelize.literal(`(SELECT id FROM payment_providers WHERE provider_name = '${INDIGRIT_INTEGRATION_CONSTANT.PROVIDER_NAME}')`)],
          paymentInitiate: false,
          status: { [Sequelize.Op.not]: ['completed', 'failed'] },
          createdAt: {
            [Op.lt]: Sequelize.literal("NOW() - INTERVAL '24 hours'")
          }
        }
      })
    }
  } catch (error) {
    throw error
  }

}
