import WorkerBase from '../../libs/workerBase'
import { updateSportsBetTransactionIndex, updateSportTransactionStatus } from '../../elastic-search'
import db, { sequelize } from '../../db/models'
import { isEligibleForLosingBonus } from '../../common/isEligibleForLosingBonus'
import { ggrBasedLosingBonus } from '../../common/ggrBasedLosingBonus'
import { BONUS_CLAIM_TYPE, BONUS_CALCULATION_TYPE, BONUS_INTERVAL_TYPE, BONUS_DATES } from '../../utils/constants/constant'
import { BONUS_TYPES, TRANSACTION_TYPES, TABLES, SUBSCRIPTION_CHANNEL, QUEUE_WORKER_CONSTANT, BONUS_STATUS, IST_AUTOMATIC_LOSING_BONUS_TENANTS } from '../../common/constants'
import generateUniqueTransactionIds from '../../common/generateUniqueTransactionIds'
import userCurrencyExchange from '../../common/userCurrencyExchange'
import currencyConversion from '../../common/newCurrencyConversion'
import beforeAfterBalance from '../../common/beforeAfterBalance'
import { Op } from 'sequelize'
import config from '../../configs/app.config'
import { Sequelize } from '../../db/models'

export default async (data) => {
  try {
    const { bonusType, bonusId, userId, from, to, isClaimed } = data
    const {
      Transaction: TransactionModel,
      Bonus: BonusModel,
      LosingBonusSetting: LosingBonusSettingModel,
      LosingBonusTier: LosingBonusTierModel,
      BetsTransaction: BetsTransactionModel,
      Wallet: WalletModel,
      Currency: CurrencyModel,
      UserLosingBonusClaimHistory: UserLosingBonusClaimHistoryModel,
      UserBonus: UserBonusModel,
      QueueLog: QueueLogModel
    } = db

    const losingBonus = await BonusModel.findOne({
      attributes: [`tenantId`, `kind`, `currencyId`, `id`, `percentage`, `validFrom`, `validUpto`],
      where: {
        id: bonusId,
      },
      include: {
        model: LosingBonusSettingModel,
        where: {
          [Op.or]: [{ bonusClaimType: BONUS_CLAIM_TYPE.AUTOMATIC_ACTIVE }, { bonusClaimType: BONUS_CLAIM_TYPE.AUTOMATIC }],
        },
        include: {
          model: LosingBonusTierModel
        }
      }
    })
    const userLosingBonus = await UserBonusModel.findOne({
      attributes: [`id`, `status`, `bonusAmount`, `rolloverBalance`, `userId`, `kind`, `bonusId`, 'claimedAt', `transactionId`, `createdAt`],
      where: {
        bonusId,
        userId
      }
    })

    const bonusClaimType = losingBonus.LosingBonusSetting.bonusClaimType
    const bonusTenantId = losingBonus.tenantId
    const enableTenants = config.get('env') === 'development' ? IST_AUTOMATIC_LOSING_BONUS_TENANTS.STAGE_TENANTS : IST_AUTOMATIC_LOSING_BONUS_TENANTS.PROD_TENANTS

    let startDate, endDate

    if (bonusClaimType === BONUS_CLAIM_TYPE.AUTOMATIC) {
      if (enableTenants.includes(+bonusTenantId)) {
        const validFrom = new Date(losingBonus?.validFrom)
        validFrom.setHours(validFrom.getHours() - 5, validFrom.getMinutes() - 30, 0, 0)
        const validTill = new Date(losingBonus?.validUpto)
        validTill.setHours(validTill.getHours() - 5, validTill.getMinutes() - 30, 0, 0)
        const today = new Date()
        const isValid = Math.floor((validTill.getTime() - today.getTime()) / (1000 * 3600 * 24))
        const yesterday = new Date(today)
        yesterday.setDate(today.getDate() - BONUS_DATES[bonusType])
        yesterday.setHours(-5, -30, 0, 0)
        startDate = (yesterday < userLosingBonus.createdAt) ? userLosingBonus.createdAt : yesterday
        endDate = isValid > 0 ? today : validTill
      }
      else {
        const validFrom = losingBonus.validFrom
        const activatedFrom = userLosingBonus.createdAt
        const validTill = losingBonus.validUpto
        const today = new Date()
        const timeDiff = today.getTime() - validFrom.getTime()
        const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));
        const claimDays = Math.floor((today.getTime() - activatedFrom.getTime()) / (1000 * 3600 * 24))
        const isValid = Math.floor((validTill.getTime() - today.getTime()) / (1000 * 3600 * 24))
        const yesterday = new Date(today)
        yesterday.setDate(today.getDate() - BONUS_DATES[bonusType])
        yesterday.setHours(0, 0, 0, 0)
        startDate = (claimDays < 1) ? userLosingBonus.createdAt : yesterday
        endDate = isValid > 0 ? today : validTill
      }
    }
    else if (bonusClaimType === BONUS_CLAIM_TYPE.AUTOMATIC_ACTIVE) {
      startDate = new Date(from)
      endDate = new Date(to)
    }

    const userWallet = await WalletModel.findOne({
      attributes: [`id`, 'amount', 'nonCashAmount', 'currencyId', 'ownerId'],
      where: {
        ownerId: userId,
        ownerType: 'User'
      },
      include: CurrencyModel
    })

    const dateObj = {
      createdAt: { [Op.gte]: startDate.toISOString(), [Op.lte]: endDate.toISOString() }
    }
    const dates = { startDate: startDate.toISOString(), endDate: endDate.toISOString() }
    let betAmountSumLoss
    const tenantId = losingBonus?.tenantId
    const bonusKind = userLosingBonus.kind

    // find casino providers and gameIds
    let casinoProviderIds = []
    let gameIds = []
    if (losingBonus?.LosingBonusSetting?.providerDetails) {
      await Promise.all(
        losingBonus.LosingBonusSetting.providerDetails.map(async (detail) => {
          if (detail.game_id?.length > 0) {
            gameIds.push(...detail.game_id)
          } else {
            const games = await sequelize.query(
              `SELECT transactions_providers_list.seat_ids AS "seatIds"
                  FROM transactions_providers_list
                  WHERE title = (
                  SELECT title
                  FROM pages
                  WHERE tenant_id = :tenantId
                    AND top_menu_id = :topMenuId
                    AND id = :id
                )`,
              {
                replacements: {
                  tenantId,
                  topMenuId: detail.top_menu_id,
                  id: detail.id,
                },
                type: Sequelize.QueryTypes.SELECT,
              }
            )

            if (games.length > 0) {
              gameIds.push(...games[0]?.seatIds)
            } else {
              const provider = await sequelize.query(
                `SELECT casino_providers.id
                    FROM casino_providers
                    INNER JOIN pages ON pages.title = casino_providers.name
                    WHERE tenant_id = :tenantId
                      AND pages.top_menu_id = :topMenuId
                      AND pages.id = :id`,
                {
                  replacements: {
                    tenantId,
                    topMenuId: detail.top_menu_id,
                    id: detail.id,
                  },
                  type: Sequelize.QueryTypes.SELECT,
                }
              )
              if (provider.length > 0) {
                casinoProviderIds.push(provider[0]?.id)
              }
            }
          }
        })
      )
    }

    if (losingBonus.LosingBonusSetting.bonusCalculationType === 'ngr') {
      betAmountSumLoss = await isEligibleForLosingBonus({
        TransactionModel,
        BetsTransactionModel,
        userWallet,
        userId,
        dateObj,
        tenantId,
        bonusKind,
        dates,
        gameIds,
        casinoProviderIds
      })
    } else {
      betAmountSumLoss = await ggrBasedLosingBonus({
        TransactionModel,
        userWallet,
        dateObj,
        tenantId,
        bonusKind,
        dates,
        gameIds,
        casinoProviderIds
      })
    }
    let amountDiff = Infinity
    let bonusObj = null
    losingBonus.LosingBonusSetting.LosingBonusTiers.forEach(ele => {
      if (betAmountSumLoss >= ele.minLosingAmount && amountDiff > Math.abs(ele.minLosingAmount - betAmountSumLoss)) {
        amountDiff = Math.abs(ele.minLosingAmount - betAmountSumLoss)
        bonusObj = ele
      }
    })
    if (!bonusObj) {
      return { message: 'Successfully updated', status: 'bonus is not there' }
    }
    // calculating losing bonus to credit
    let bonusToCredit
    if (bonusObj?.maxLosingAmount && bonusObj?.maxLosingAmount < betAmountSumLoss)
      bonusToCredit = bonusObj?.maxLosingAmount * (bonusObj?.percentage / 100)
    else
      bonusToCredit = betAmountSumLoss * (bonusObj?.percentage / 100)

    if (bonusObj?.maxBonus && bonusToCredit > bonusObj.maxBonus) {
      bonusToCredit = bonusObj.maxBonus
    }

    const transactionId = await generateUniqueTransactionIds(TransactionModel, losingBonus.tenantId)
    let transactionObj = {
      targetWalletId: userWallet.id,
      targetCurrencyId: userWallet.currencyId,
      amount: bonusToCredit,
      comments: '',
      actioneeId: userId,
      transactionId,
      paymentMethod: 'automatic',
      actioneeType: 'User',
      tenantId: losingBonus.tenantId,
      timestamp: new Date().getTime(),
      transactionType: TRANSACTION_TYPES.NON_CASH_BONUS_CLAIM,
      status: 'success'
    }

    userLosingBonus.bonusAmount = bonusToCredit
    userLosingBonus.userId = userId
    userLosingBonus.bonusId = bonusId
    //userLosingBonus.kind = BONUS_TYPES.LOSING
    if (losingBonus.LosingBonusSetting.claimDays) {
      userLosingBonus.status = BONUS_STATUS.CLAIMED
    }
    const claimedAt = Date.now()
    userLosingBonus.claimedAt = claimedAt

    const userLosingBonusClaimHistory = {}
    if (!losingBonus.LosingBonusSetting.claimDays) {
      userLosingBonusClaimHistory.bonusAmount = bonusToCredit
      userLosingBonusClaimHistory.userId = userId
      userLosingBonusClaimHistory.bonusId = bonusId
      userLosingBonusClaimHistory.claimedAt = claimedAt
    }

    const sequelizeTransaction = await sequelize.transaction()

     try {
      const userWallet = await WalletModel.findOne({
        where: { ownerId: userId, ownerType: TABLES.USER },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: WalletModel
        },
        skipLocked: false
      })

      userWallet.nonCashAmount += bonusToCredit
      const skipWalletHook = true
      await userWallet.save({ transaction: sequelizeTransaction, skipWalletHook })

      transactionObj.conversionRate = await userCurrencyExchange(CurrencyModel, userWallet.currencyId)
      transactionObj = await currencyConversion( transactionObj, userWallet, bonusToCredit)
      transactionObj = await beforeAfterBalance(transactionObj, userWallet.nonCashAmount, bonusToCredit)

      const skipTransactionHook = true
      const newTransaction = await TransactionModel.create(transactionObj, { transaction: sequelizeTransaction, skipTransactionHook })
      userLosingBonus.transactionId = newTransaction.id

      await userLosingBonus.save({ transaction: sequelizeTransaction })

      if (!losingBonus.LosingBonusSetting.claimDays) {
        userLosingBonusClaimHistory.transactionId = newTransaction.id
        await UserLosingBonusClaimHistoryModel.create(userLosingBonusClaimHistory, { transaction: sequelizeTransaction })
      }

      const txnIds = []

      if (newTransaction) {
        txnIds.push(newTransaction.id)
      }

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }

      const queueLog = await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })

      await sequelizeTransaction.commit()

      try {
        this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE, { UserWalletBalance: { walletBalance: userWallet.amount, userId, nonCashAmount: userWallet.nonCashAmount } })
        this.context.pubSub.publish(
          SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
          { QueueLog: { queueLogId: queueLog?.id } }
        )
      } catch (error) {
      }
    } catch (error) {
      await sequelizeTransaction.rollback()
      throw error
    }
  } catch (error) {
    throw error
  }
}
