import { Op } from 'sequelize'
import pushInQueue from '../../common/pushInQueue'
import db, { sequelize } from '../../db/models'
import ServiceBase from '../../libs/serviceBase'
import { PUSH_IN_QUEUE_CRON, PUSH_IN_QUEUE_PAGE_COUNT, SUCCESS_MSG, TRANSACTION_TYPE } from '../../utils/constants/constant'

export class BetTransactionData extends ServiceBase {
  async run () {
    const {
      QueueLog: QueueLogModel
    } = db

    try {
     if (PUSH_IN_QUEUE_CRON) {
        const {count} = await QueueLogModel.findOne({
          attributes: [
            [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          ],
          where: {
            [Op.or]: [
              { status: 0 },
              { status: 3 }
            ],
            ids: {
              [Op.ne]: []
            },
            type: TRANSACTION_TYPE.BET_TRANSACTION
          },
          raw: true
        })

        const pages = Math.ceil(count / PUSH_IN_QUEUE_PAGE_COUNT)

        for (let offset = 0; offset< pages; offset++){
          const queueLog = await db.QueueLog.findAll({
            attributes: ['id'],
            where: {
              [Op.or]: [
                { status: 0 },
                { status: 3 }
              ],
              ids: {
                [Op.ne]: []
              },
              type: TRANSACTION_TYPE.BET_TRANSACTION
            },
            limit: PUSH_IN_QUEUE_PAGE_COUNT,
            offset:offset,
            order: [
              [ 'id', 'ASC' ]
            ],
            raw: true
          })
          if (queueLog) {
            const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
            for (const queue of queueLog) {
              try {
                await delay(50)
                await pushInQueue(queue.id)
              } catch (error) {
                console.log('==========error=========', error)
                await db.QueueLog.update(
                  { status: 3 },
                  { where: { id: queue.id } }
                )
              }
            }
          }
        }
      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.addError('InternalServerErrorType', error)
    }
  }
}
