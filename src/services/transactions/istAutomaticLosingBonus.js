import { Op, Sequelize } from 'sequelize'
import { BONUS_TYPES, IST_AUTOMATIC_LOSING_BONUS_TENANTS, CRON_LOG_STATUS } from '../../common/constants'
import config from '../../configs/app.config'
import db from '../../db/models'
import ServiceBase from '../../libs/serviceBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'
import { BONUS_CLAIM_TYPE, BONUS_INTERVAL_TYPE, SUCCESS_MSG } from '../../utils/constants/constant'

export class IstAutomaticLosingBonus extends ServiceBase {
  async run () {

    try {
      const {
        Bonus: BonusModel,
        LosingBonusSetting: LosingBonusSettingModel,
        UserBonus: UserBonusModel,
        QueueProcessStatus: QueueProcessStatusModel
      } = db

      const enableTenants = config.get('env') === 'development' ? IST_AUTOMATIC_LOSING_BONUS_TENANTS.STAGE_TENANTS : IST_AUTOMATIC_LOSING_BONUS_TENANTS.PROD_TENANTS

      const queueProcessStatus = await QueueProcessStatusModel.findOne({
        attributes: ['id'],
        where: {
          service: 'ist_automatic_losing_bonus_cron',
          status: 1
        }
      })

      if (queueProcessStatus) {
        this.args.cronId = queueProcessStatus?.id  // for cron logs
        const userBonuses = await UserBonusModel.findAll({
          attributes: ['userId', 'expiresAt', 'createdAt'],
          where: {
            kind: {
              [Op.or]: [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING_BOTH]
            },
            status: 'active',
            expiresAt: { [Op.gt]: Sequelize.literal(`CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'`) }
          },
          include: {
            model: BonusModel,
            attributes: ['validFrom', 'id', 'validUpto'],
            where: {
              kind: {
                [Op.or]: [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING_BOTH]
              },
              enabled: true,
              validUpto: { [Op.gt]: Sequelize.literal(`CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'`) },
              validFrom: { [Op.lte]: Sequelize.literal(`CURRENT_TIMESTAMP AT TIME ZONE 'Asia/Kolkata'`) },
              tenantId: { [Op.in]: enableTenants }
            },
            required: true,
            include: [
              {
                model: LosingBonusSettingModel,
                attributes: ['claimIntervalType'],
                where: {
                  bonusClaimType: BONUS_CLAIM_TYPE.AUTOMATIC
                },
                required: true
              }
            ]
          },
          required: true
        })

        if (!userBonuses.length) {
          return {
            message: 'no user losing bonus found'
          }
        }

        // create a job
        const jobTime = new Date()
        const queue = CommonQueue
        const queueName = CommonJob

        userBonuses.forEach(userBonus => {
          const type = userBonus?.Bonus?.LosingBonusSetting?.claimIntervalType

          const validFrom = new Date(userBonus?.Bonus?.validFrom)
          validFrom.setHours(validFrom.getHours() - 5, validFrom.getMinutes() - 30, 0, 0)

          const validTill = new Date(userBonus?.Bonus?.validUpto)
          validTill.setHours(validTill.getHours() - 5, validTill.getMinutes() - 30, 0, 0)

          const today = new Date()

          const timeDiff = today.getTime() - validFrom.getTime()
          const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24))

          const isValid = Math.floor((validTill.getTime() - today.getTime()) / (1000 * 3600 * 24))

          let flag = false

          const queueParams = {}
          queueParams.time = jobTime
          queueParams.bonusType = type
          queueParams.bonusId = userBonus.Bonus.id
          queueParams.userId = userBonus.userId
          const yesterday = new Date(today)
          yesterday.setDate(today.getDate() - 1)
          yesterday.setHours(-5, -30, 0, 0)

          queueParams.from = (daysDiff <= 1) ? userBonus.createdAt : yesterday
          queueParams.to = today
          queueParams.isClaimed = !(isValid > 0)
          queueParams.transactionType = 'ist_automatic_losing_bonus'
          if (type === BONUS_INTERVAL_TYPE.DAILY) {
            flag = true
          }
          if (type === BONUS_INTERVAL_TYPE.WEEKLY && daysDiff % 7 === 0) {
            flag = true
          }
          if (type === BONUS_INTERVAL_TYPE.BIWEEKLY && daysDiff % 14 === 0) {
            flag = true
          }
          if (type === BONUS_INTERVAL_TYPE.MONTHLY && daysDiff % 30 === 0) {
            flag = true
          }
          if (flag) {
            queue.add(queueName,
              queueParams,
              {
                jobId: `${userBonus.Bonus.id}_${queueName}_${userBonus.userId}`,
                removeOnComplete: true,
                delay: 5
              })
          }
        })
      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.args.status = CRON_LOG_STATUS.FAILED
      this.args.errorMsg = error.message || null
      this.addError('InternalServerErrorType', error)
    }
  }
}
