import { DEPOSIT_REQUEST_STATUS } from '../../common/constants';
import config from '../../configs/app.config';
import db, { sequelize } from '../../db/models';
import { BUCKET_PATH, OCR_TRANSACTION_ACTION_BY, OCR_TRANSACTION_STATUS } from '../../utils/constants/constant';
const Tesseract = require('tesseract.js');

module.exports = async (data) => {
  try {
    const {
      DepositRequest: DepositRequestModel
    } = db

    const isProdEnv = config.get('env') === 'production';
    const prefix = (isProdEnv) ? BUCKET_PATH.PROD : BUCKET_PATH.STAGE
    const depositId = data.transactionId
    let endPoint = ''
    let tenantId = ''

    const depositRequest = await DepositRequestModel.findOne({
      where: {
        id: depositId, status: DEPOSIT_REQUEST_STATUS.OPEN,
      },
      attributes: ['id', 'transactionReceipt', 'tenantId', 'utrNumber'],
      raw: true
    })

    // Check the receipt is available or not
    if (depositRequest?.transactionReceipt) {
      endPoint = depositRequest.transactionReceipt
      tenantId = depositRequest.tenantId
    } else {
      throw new Error('No valid deposit request found or missing transaction receipt.');
    }

    // Image url
    const imageUrl = `${prefix}/${endPoint}`

    const result = await Tesseract.recognize(
      imageUrl, // Local path to the image
      'eng', // Language code
      // { logger: m => console.log(m) } // Optional logger for progress
    );

    const recognizedText = result.data.text

    const details = extractTransactionDetails(recognizedText);

    const utrNumber = details.utrNumber
    const transactionId = details.transactionId

    if (utrNumber === 'UTR not found') {
      updateOcrTransaction(depositId, OCR_TRANSACTION_STATUS.FAILED_TO_EXTRACT_UTR_NUMBER, OCR_TRANSACTION_ACTION_BY.THROUGH_OCR_SYSTEM, details)
      return true
    }

    if (utrNumber) {
      if (utrNumber?.toLowerCase() == depositRequest?.utrNumber?.toLowerCase()) {
        updateOcrTransaction(depositId, OCR_TRANSACTION_STATUS.VERIFIED_UTR_NUMBER, OCR_TRANSACTION_ACTION_BY.THROUGH_OCR_SYSTEM, details)
      } else {
        updateOcrTransaction(depositId, OCR_TRANSACTION_STATUS.UTR_NOT_MATCH, OCR_TRANSACTION_ACTION_BY.THROUGH_OCR_SYSTEM, details)
      }
    }
    else {
      updateOcrTransaction(depositId, OCR_TRANSACTION_STATUS.FAILED_TO_EXTRACT_UTR_NUMBER, OCR_TRANSACTION_ACTION_BY.THROUGH_OCR_SYSTEM, details)
    }

  } catch (error) {
    console.error('Error during OCR:', error);
    throw error;
  }
};

const extractTransactionDetails = (recognizedText) => {
  try {
    // Regular expressions for Transaction ID and UTR
    const transactionIdRegex = /Transaction ID[:\s]*([a-zA-Z0-9]+)/i;
    let utrRegex
    if (recognizedText.includes('UTR')) {
      utrRegex = /(?:UTR|UPI\s*Ref\s*No)[:\s]*([a-zA-Z0-9]+)/i;
    }
    else {
      utrRegex = /(?:UTR|UTR\s*Number|Ref\s*No|Transaction\s*ID|Reference\s*Number|Reference\s*No.)[:\s]*([a-zA-Z0-9]+)/i
    }
    const twelveDigitRegex = /\b\d{12}\b/;

    // Extract Transaction ID
    const transactionIdMatch = recognizedText.match(transactionIdRegex);
    const transactionId = transactionIdMatch ? transactionIdMatch[1] : null;

    // Extract UTR number
    const utrMatch = recognizedText.match(utrRegex);
    let utrNumber = utrMatch ? utrMatch[1] : null;

    if (utrNumber == null) {
      const twelveDigitMatch = recognizedText.match(twelveDigitRegex);
      if (twelveDigitMatch) {
        utrNumber = twelveDigitMatch[0]; // Consider this as the UTR number
      }
    }

    return {
      transactionId: transactionId || 'Transaction ID not found',
      utrNumber: utrNumber || 'UTR not found',
    };
  } catch (error) {
    console.error('Error extracting transaction details:', error);
    return { transactionId: null, utrNumber: null };
  }
};

const updateOcrTransaction = async (depositReqId, status, actionBy, ocrResult = {}) => {
  ocrResult = JSON.stringify(ocrResult)
  const query = `
    INSERT INTO public.ocr_transactions (
    deposit_request_id, status, action_by,ocr_result, created_at, updated_at)
    VALUES (
    :depositReqId, :status, :actionBy,:ocrResult, NOW(), NOW()
    )
    ON CONFLICT (deposit_request_id)
    DO UPDATE SET
    status = EXCLUDED.status,
    ocr_result = EXCLUDED.ocr_result,
    action_by = EXCLUDED.action_by,
    updated_at = NOW();
`;

  await sequelize.query(query, {
    replacements: { depositReqId, status, actionBy, ocrResult }
  });
}
