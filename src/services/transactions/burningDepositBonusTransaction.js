import { v4 as uuid } from 'uuid'
import { burningBonusAmountCheck } from '../../common/burningBonusAmountCheck'
import { burningCashBonusAmountCheck } from '../../common/burningCashBonusAmountCheck'
import { BONUS_COMMENT_ABBREVIATIONS, BONUS_TYPES, QUEUE_WORKER_CONSTANT, TABLES, TRANSACTION_TYPES } from '../../common/constants'
import userCurrencyExchange from '../../common/userCurrencyExchange'
import v3CurrencyConversion from '../../common/v3CurrencyConversion'
import db, { sequelize } from '../../db/models'

export default async (data) => {
  try {
    const {
      Transaction: TransactionModel,
      Wallet: WalletModel,
      Currency: CurrencyModel,
      QueueLog: QueueLogModel,
      UserBonus: UserBonusModel,
      InstantDepositBonusHistory: InstantDepositBonusHistoryModel
    } = db

    let burningAmount
    if (data.walletType === 0) burningAmount = await burningCashBonusAmountCheck(data)
    else burningAmount = await burningBonusAmountCheck(data)

    if (burningAmount > 0) {
      const sequelizeTransaction = await sequelize.transaction()

      try {
        const userId = data.userId
        const userWallet = await WalletModel.findOne({
          where: { ownerId: userId, ownerType: TABLES.USER },
          transaction: sequelizeTransaction,
          lock: {
            level: sequelizeTransaction.LOCK.UPDATE,
            of: WalletModel
          },
          skipLocked: false
        })

        const balanceField = data.walletType === 0 ? 'amount' : 'nonCashAmount'

        if (userWallet[balanceField] < burningAmount) {
          await sequelizeTransaction.rollback()
          return { message: 'Not enough balance in non cash wallet' }
        }
        userWallet[balanceField] -= burningAmount
        await userWallet.save({ transaction: sequelizeTransaction })

        let transactionObj = {
          sourceWalletId: userWallet.id,
          sourceCurrencyId: userWallet.currencyId,
          amount: burningAmount,
          comments: BONUS_COMMENT_ABBREVIATIONS.BBC,
          actioneeId: userId,
          actioneeType: 'User',
          transactionId: uuid(),
          tenantId: data.tenantId,
          timestamp: new Date().getTime(),
          transactionType: TRANSACTION_TYPES.BURNING_DEPOSIT_BONUS,
          //errorDescription: 'Completed Successfully',
          errorCode: 0,
          status: 'success',
          success: true
        }
        transactionObj.conversionRate = await userCurrencyExchange(CurrencyModel, userWallet.currencyId)
        transactionObj = await v3CurrencyConversion(sequelizeTransaction, transactionObj, userWallet.currencyId, data.tenantId, burningAmount)
        transactionObj.sourceAfterBalance = userWallet[balanceField]
        transactionObj.sourceBeforeBalance = userWallet[balanceField] + parseFloat(burningAmount)

        const { id } = await TransactionModel.create(transactionObj, { transaction: sequelizeTransaction })

        const txnIds = []
        if (id) txnIds.push(id)
        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds
        }
        await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })

        const modelToUpdate = [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_SPORTS, BONUS_TYPES.DEPOSIT_BOTH].includes(data.bonusKind)
          ? UserBonusModel
          : InstantDepositBonusHistoryModel

        await modelToUpdate.update(
          { burningTrxnId: id },
          {
            where: { id: data.id },
            transaction: sequelizeTransaction
          }
        )

        await sequelizeTransaction.commit()

      } catch (error) {
        console.log("--------error in burning deposit bonus transaction---------", error)
        await sequelizeTransaction.rollback()
      }
      return { message: 'Unutilized deposit bonus burn' }
    } else {
      return { message: 'Deposit bonus fully utilized' }
    }
  } catch (error) {
    throw error
  }
}
