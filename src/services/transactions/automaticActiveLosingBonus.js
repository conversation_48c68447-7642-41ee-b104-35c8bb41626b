import db from '../../db/models'
import { Op, Sequelize } from 'sequelize'
import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG, BONUS_CLAIM_TYPE, BONUS_INTERVAL_TYPE } from '../../utils/constants/constant'
import { CommonQueue, CommonJob } from '../../queues/common.queue'
import { BONUS_TYPES, CRON_LOG_STATUS } from '../../common/constants'

export class AutomaticActiveLosingBonus extends ServiceBase {
  async run () {
    try {
      const {
        Bonus: BonusModel,
        LosingBonusSetting: LosingBonusSettingModel,
        UserBonus: UserBonusModel,
        QueueProcessStatus: QueueProcessStatusModel
      } = db

      const queueProcessStatus = await QueueProcessStatusModel.findOne({
        where: {
          service: 'automatic_losing_bonus_cron',
          status: 1
        },
        attributes: ['id']
      })
      if (queueProcessStatus) {
        this.args.cronId = queueProcessStatus?.id  // for cron logs
        const userBonuses = await UserBonusModel.findAll({
          attributes: ['userId'],
          where: {
            kind: {
              [Op.or]: [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING_BOTH]
            },
            status: 'active',
            expiresAt: { [Op.gt]: Sequelize.literal('CURRENT_TIMESTAMP') }
          },
          include: {
            model: BonusModel,
            attributes: ['validFrom', 'id', 'validUpto'],
            where: {
              kind: {
                [Op.or]: [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING_BOTH]
              },
              enabled: true,
              validUpto: { [Op.gt]: Sequelize.literal('CURRENT_TIMESTAMP') },
              validFrom: { [Op.lte]: Sequelize.literal('CURRENT_TIMESTAMP') }
            },
            required: true,
            include: [
              {
                model: LosingBonusSettingModel,
                attributes: ['weekDay', 'claimIntervalType'],
                where: {
                  bonusClaimType: BONUS_CLAIM_TYPE.AUTOMATIC_ACTIVE
                },
                required: true
              }
            ]
          },
          required: true
        })

        if (!userBonuses.length) {
          return {
            message: 'no user losing bonus found'
          }
        }

        userBonuses.forEach(userBonus => {
          const today = new Date()
          const weekdaysMap = { sunday: 0, monday: 1, tuesday: 2, wednesday: 3, thursday: 4, friday: 5, saturday: 6 }
          const specifiedWeekdayNum = weekdaysMap[userBonus?.Bonus?.LosingBonusSetting?.weekDay?.toLowerCase()]

          if (today.getDay() === specifiedWeekdayNum) {
            const firstComingDate = findFirstNextWeekdayFromDate((userBonus.Bonus.validFrom), specifiedWeekdayNum)

            // Convert to UTC date-only (strip time)
            const todayDateOnly = new Date(today.toISOString().split('T')[0]);
            const targetDateOnly = new Date(firstComingDate.toISOString().split('T')[0]);

            // Get difference in milliseconds and convert to full days
            const timeDiffMs = todayDateOnly - targetDateOnly;
            const diffDays = Math.floor(timeDiffMs / (1000 * 60 * 60 * 24));

            let fromDate, toDate, flag

            if (today.getDate() === firstComingDate.getDate()) {
              fromDate = (userBonus.Bonus.validFrom)
              toDate = today
              flag = true
            } else if ((userBonus.Bonus.LosingBonusSetting.claimIntervalType === BONUS_INTERVAL_TYPE.BIWEEKLY) && (diffDays % 14 === 0)) {
              fromDate = new Date(today)
              fromDate.setDate(today.getDate() - 14)
              toDate = today
              flag = true
            } else if ((userBonus.Bonus.LosingBonusSetting.claimIntervalType === BONUS_INTERVAL_TYPE.WEEKLY) && (diffDays % 7 === 0)) {
              fromDate = new Date(today)
              fromDate.setDate(today.getDate() - 7)
              toDate = today
              flag = true
            }

            if (flag) {
              // create a job
              const jobTime = new Date()
              const queue = CommonQueue
              const queueName = CommonJob
              const queueParams = {}
              queueParams.time = jobTime
              queueParams.bonusType = userBonus.Bonus.LosingBonusSetting.claimIntervalType
              queueParams.bonusId = userBonus.Bonus.id
              queueParams.userId = userBonus.userId
              queueParams.from = fromDate
              queueParams.to = toDate
              queueParams.transactionType = 'automatic_active_losing_bonus'
              const validTill = userBonus.Bonus.validUpto
              const isValid = Math.floor((validTill.getTime() - today.getTime()) / (1000 * 3600 * 24))
              queueParams.isClaimed = !(isValid > 0)

              queue.add(queueName,
                queueParams,
                {
                  jobId: `${userBonus.Bonus.id}_${queueName}_${jobTime}_${userBonus.userId}`,
                  removeOnComplete: true,
                  delay: 5
                })
            }
          }
        })
      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.args.status = CRON_LOG_STATUS.FAILED
      this.args.errorMsg = error.message || null
      this.addError('InternalServerErrorType', error)
    }
  }
}


/**
 * Finds the next occurrence of a specified weekday,
 * based on a UTC datetime string, assuming it was created in IST (UTC+5:30).
 *
 * @param {string} dateStrUTC - The date string in UTC format (e.g., '2025-06-21T18:30:00Z')
 * @param {number} weekday - Target weekday (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
 * @returns {Date} - The next occurrence of the specified weekday at 00:00:00 UTC
 */
function findFirstNextWeekdayFromDate (dateStrUTC, weekday) {
  // Step 1: Convert input UTC date string to a Date object
  const original = new Date(dateStrUTC);

  // Step 2: Approximate local calendar day by shifting +5.5 hours (IST offset)
  //         This handles cases where UTC time like '18:30:00' was actually meant to be local midnight
  const approxIST = new Date(original.getTime() + 5.5 * 60 * 60 * 1000);

  // Step 3: Create a new UTC date representing 00:00:00 of the inferred local calendar day
  const localDateOnly = new Date(Date.UTC(
    approxIST.getUTCFullYear(),
    approxIST.getUTCMonth(),
    approxIST.getUTCDate()
  ));

  // Step 4: Get the weekday (0 = Sunday, ..., 6 = Saturday) of the local calendar day
  const currentWeekday = localDateOnly.getUTCDay();

  // Step 5: Calculate how many days to add to reach the *next* occurrence of the target weekday
  let dayDiff = (weekday - currentWeekday + 7) % 7;

  // Step 6: If it's the same weekday as today, skip to *next week's* occurrence
  if (dayDiff === 0) dayDiff = 7;

  // Step 7: Move forward by the calculated day difference
  localDateOnly.setUTCDate(localDateOnly.getUTCDate() + dayDiff);

  // Step 8: Return the resulting date (at 00:00:00 UTC)
  return localDateOnly;
}
