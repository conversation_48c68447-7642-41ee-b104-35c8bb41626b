import momentTimezone from 'moment-timezone';
import { Op, Sequelize } from "sequelize";
import { ALLOWED_PERMISSIONS, OFFER_FREQUENCY, PROD_TENANTS, STAGE_TENANTS } from '../../common/constants';
import ErrorLogHelper from '../../common/errorLog';
import { assignPrizes, calculateStartDate, jumbleFakeWinners } from '../../common/offerWinnersHelpers';
import config from '../../configs/app.config';
import db, { sequelize } from '../../db/models';
import ServiceBase from '../../libs/serviceBase';
import { SUCCESS_MSG } from '../../utils/constants/constant';

export class OfferWinnerAnnounce extends ServiceBase {
  async run () {
    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'offer_winner_announce_cron',
          status: 1
        },
        attributes: ['id']
      })

      if (!queueProcessStatus) return

      const {
        Offer: OfferModel,
        Prize: PrizeModel,
        OfferFakeWinner: OfferFakeWinnerModel,
        TenantThemeSetting: TenantThemeSettingModel,
        OfferProvider: OfferProviderModel } = db;

      const tenants = config.get('env') === 'development' ? STAGE_TENANTS : PROD_TENANTS

      for (const [tenantId, tenant] of Object.entries(tenants)) {

        const tenantThemeSetting = await TenantThemeSettingModel.findOne({
          attributes: ['tenantId', 'allowedModules'],
          where: {
            tenantId: tenantId
          },
          raw: true
        });

        if (!tenantThemeSetting) continue

        const { allowedModules } = tenantThemeSetting;

        // Check if the tenant has the playerCategory module enabled
        const hasOfferModule = allowedModules
          ? allowedModules.split(',').map(module => module.trim()).includes(ALLOWED_PERMISSIONS.OFFERS)
          : false;

        if (!hasOfferModule) continue

        const timezone = 'Asia/kolkata';
        const dateFormat = 'YYYY-MM-DD';

        const offersList = await OfferModel.findAll({
          attributes: ['id', 'tenantId', 'frequency', 'dayOfWeek', 'winningType', 'validFrom', 'validTo', 'createdAt'],
          where: {
            tenantId,
            status: true,
            [Op.and]: [
              Sequelize.literal(`
                date("Offer"."valid_from" AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}') <=
                date(NOW()::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}') - INTERVAL '1 DAY'
              `),
              Sequelize.literal(`
                date("Offer"."valid_to" AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}') >=
                date(NOW()::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}') - INTERVAL '1 DAY'
              `),
            ],
            [Op.or]: [
              // Daily frequency: Fetch offers created before today
              {
                frequency: OFFER_FREQUENCY.DAILY
              },
              // Weekly frequency: Fetch offers ending on the specified day of the previous week
              {
                frequency: OFFER_FREQUENCY.WEEKLY,
                [Op.and]: [
                  // Compare the dayOfWeek column with the extracted day of the previous day (consider timezone)
                  Sequelize.where(
                    Sequelize.col('day_of_week'),
                    Sequelize.literal(`
                      EXTRACT(DOW FROM
                        date((NOW()::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}') - INTERVAL '1 DAY')
                      )
                    `)
                  ),
                  Sequelize.literal(`
                    to_char("Offer"."valid_from" AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}', 'IYYY-IW') <=
                    to_char(date((NOW()::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}')), 'IYYY-IW')
                  `), // Compare current and previous ISO week
                ],
              },
              // Monthly frequency: Announce winners for last month
              {
                frequency: OFFER_FREQUENCY.MONTHLY,
                [Op.and]: [
                  Sequelize.literal(`
                    DATE_TRUNC('day', (NOW()::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}')::date - INTERVAL '1 DAY') =
                    DATE_TRUNC(
                      'day',
                      (
                        date(("Offer"."valid_from"::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}'))
                        + INTERVAL '1 MONTH' * FLOOR(
                          DATE_PART(
                            'month',
                            AGE(
                              DATE_TRUNC(
                                'day', (NOW()::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}')::date - INTERVAL '1 DAY'
                              ),
                              date(("Offer"."valid_from"::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}'))
                            )
                          )
                        )
                      )
                    )
                  `)
                ],
              },
              // End of campaign period: Check for offers that are valid until yesterday
              {
                frequency: OFFER_FREQUENCY.END_OF_CAMPAIGN_PERIOD,
              }
            ],
          },
          include: [
            {
              model: PrizeModel,
              as: 'prizes',
              attributes: ['id', 'title', 'prizesCount'],
              required: false,
            },
            {
              model: OfferFakeWinnerModel,
              attributes: ['id', 'user_name', 'percentage', 'position'],
              required: false,
            },
            {
              model: OfferProviderModel,
              attributes: ['providerId'],
              required: false,
            }
          ],
        });

        // Iterate over offersList, calculate `totalPrizesCount`, and fetch winners dynamically
        for (let offer of offersList) {

          // Calculate totalPrizesCount for the current offer
          const totalPrizesCount = offer.prizes.reduce((sum, prize) => {
            const count = parseInt(prize.prizesCount, 10) || 0;
            return sum + count;
          }, 0);

          // // If prizes count is 0 then skip
          if (!totalPrizesCount) continue;

          // Determine the field to calculate based on winningType
          const winnerField = {
            0: `SUM(CASE WHEN type = 20 THEN psc.amount ELSE 0 END)`, // Rollover Won
            1: `SUM(CASE WHEN type = 21 THEN psc.amount ELSE 0 END) `, // GGR
            2: `(
                  SUM(CASE WHEN type = 21 THEN psc.amount ELSE 0 END)
                  - SUM(CASE WHEN type = 31 THEN psc.amount ELSE 0 END)
                )`, // NGR
            3: `SUM(CASE WHEN type = 29 THEN psc.amount ELSE 0 END)` // Total Wagered Amount (Bet Amount)
          }[offer?.winningType];

          if (!winnerField) {
            throw new Error('Invalid winningType specified');
          }

          // Calculate the start and end dates
          const endDate = momentTimezone().tz(timezone).subtract(1, 'day').format(dateFormat); // End of yesterday
          let startDate = calculateStartDate(offer, timezone, dateFormat)

          // Check if a winner is already announced for this offer_id and endDate
          const [existingWinner] = await sequelize.query(`
            SELECT 1
            FROM offer_winners
            WHERE offer_id = :offerId AND winner_announce_date = :winnerAnnounceDate
            LIMIT 1;
          `, {
            replacements: {
              offerId: offer.id,
              winnerAnnounceDate: endDate,
            },
            type: sequelize.QueryTypes.SELECT,
          });

          if (existingWinner) continue;

          // Find tenant base currency
          const findTenantBaseCurrencyQuery = `
                  select
                    c.id as currency_id
                  from
                    tenant_credentials tc
                    join currencies c on (tc.value = c.code)
                  where
                    key = 'TENANT_BASE_CURRENCY'
                    and tenant_id = :tenantId;`;

          const tenantBaseCurrencyData = await sequelize.query(findTenantBaseCurrencyQuery, {
            replacements: {
              tenantId,
            },
            type: sequelize.QueryTypes.SELECT
          });

          const tenantBaseCurrencyId = tenantBaseCurrencyData[0]?.currency_id;

          // Get provider IDs for this offer
          const providerIds = offer.OfferProviders?.map(op => op.providerId) || [];

          // Build provider filter condition
          const providerCondition = providerIds.length > 0
            ? `AND psp.provider_id IN (${providerIds.join(',')})`
            : '';

          const [results] = await sequelize.query(`
            SELECT
              psp.user_id AS user_id,
              u.user_name AS user_name,
              ${winnerField} AS winner_value
            FROM
              player_summary_provider_wise psp
            JOIN
              player_provider_other_currency psc
              ON psp.id = psc.player_summary_provider_id AND psc.currency_id = :tenantBaseCurrencyId
            JOIN
              users u
              ON psp.user_id = u.id
            WHERE
              psp.tenant_id = :tenantId
              AND psp.date BETWEEN :startDate AND :endDate
              ${providerCondition}
            GROUP BY
              psp.user_id, u.user_name
            HAVING
              ${winnerField} > 0
            ORDER BY
              winner_value DESC
            LIMIT :totalPrizesCount;
          `, {
            replacements: {
              tenantId,
              startDate,
              endDate,
              tenantBaseCurrencyId,
              totalPrizesCount,
              ...(providerIds.length > 0 && { providerIds })
            },
          });

          if (results.length) {
            const finalWinners = jumbleFakeWinners(results, offer);
            const updatedWinners = assignPrizes(finalWinners, offer);

            // // Start a transaction
            const transaction = await sequelize.transaction();

            try {
              // Loop through each winner and insert into the `offer_winners` table
              for (const winner of updatedWinners) {
                await sequelize.query(`
                    INSERT INTO offer_winners (
                      user_name, offer_id, tenant_id, winning_type, winning_value,
                      prize_title, frequency, winner_announce_date, is_fake_user,
                      created_at, updated_at
                    )
                    VALUES (
                      :userName, :offerId, :tenantId, :winningType, :winningValue,
                      :prizeTitle, :frequency, :winnerAnnounceDate, :isFakeUser, NOW(), NOW()
                    );
                  `,
                  {
                    replacements: {
                      userName: winner.user_name,
                      offerId: offer.id,
                      tenantId: offer.tenantId,
                      winningType: offer.winningType,
                      winningValue: winner.winner_value,
                      prizeTitle: winner.prize_title,
                      frequency: offer.frequency,
                      winnerAnnounceDate: endDate,
                      isFakeUser: winner.is_fake_user || false,
                    },
                    transaction,
                  }
                );
              }

              // Commit the transaction after all operations succeed
              await transaction.commit();
            } catch (error) {
              // Rollback the transaction in case of an error
              if (transaction) {
                await transaction.rollback();
              }
            }
          }
        }
      }

      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    }
    catch (error) {
      await ErrorLogHelper.logError(error, null, null)
      this.addError('InternalServerErrorType', error)
    }
  }
}
