import { ALLOWED_PERMISSIONS, CONFIGURATION_BACKUP_MODULE, PROD_TENANTS, STAGE_TENANTS } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import config from '../../configs/app.config'
import db, { sequelize } from '../../db/models'
import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG } from '../../utils/constants/constant'

export class ConfigurationBackup extends ServiceBase {
  async run () {
    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'configuration_backup_cron',
          status: 1
        },
        attributes: ['id']
      })

      if (queueProcessStatus) {

        const tenants = config.get('env') === 'development' ? STAGE_TENANTS : PROD_TENANTS

        for (const [tenantId, tenant] of Object.entries(tenants)) {
          // Initial fetch to avoid duplicate calls
          const tenantThemeSetting = await db.TenantThemeSetting.findOne({
            where: { tenantId },
            attributes: { exclude: ['id', 'tenantId', 'updatedAt', 'createdAt'] },
            raw: true,
          });

          if (!tenantThemeSetting) continue;

          const { allowedModules } = tenantThemeSetting;
          const hasPermission = allowedModules
            ? allowedModules.split(',').map(module => module.trim()).includes(ALLOWED_PERMISSIONS.CONFIGURATION_BACKUP)
            : false;

          if (!hasPermission) continue;

          // Step 1: Registration Fields Backup
          const tenantUserRegisterFieldJson = await db.TenantUserRegistrationFields.findOne({
            where: { tenantId },
            attributes: { exclude: ['id', 'tenantId', 'updatedAt', 'createdAt'] },
            raw: true
          });

          // Step 2: Configuration Variables Backup
          const tenantCredentials = await db.TenantCredential.findAll({
            where: { tenantId },
            attributes: { exclude: ['id', 'tenantId', 'updatedAt', 'createdAt'] },
            raw: true
          });

          const credentialsJson = tenantCredentials.reduce((result, credential) => {
            result[credential.key] = {
              value: credential.value,
              description: credential.description,
            };
            return result;
          }, {});

          // Step 3: Theme Settings Backup
          const basicSettings = {
            layoutId: tenantThemeSetting.layoutId,
            languageId: tenantThemeSetting.languageId,
            whatsappNumber: tenantThemeSetting.whatsappNumber,
            callingNumber: tenantThemeSetting.callingNumber,
            logoUrl: tenantThemeSetting.logoUrl,
            fabIconUrl: tenantThemeSetting.fabIconUrl
          };

          const theme = tenantThemeSetting?.theme;
          const callingsNumbers = tenantThemeSetting?.callingNumbers && Object.keys(tenantThemeSetting?.callingNumbers).length ? { calling_numbers: tenantThemeSetting?.callingNumbers } : {};

          const excludedKeys = ['layoutId', 'languageId', 'whatsappNumber', 'callingNumber', 'logoUrl', 'fabIconUrl',
            'theme', 'callingNumbers'];

          const configurationSettings = Object.fromEntries(
            Object.entries(tenantThemeSetting).filter(([key]) => !excludedKeys.includes(key))
          );

          // Run all backups concurrently using Promise.all
          await Promise.all([
            tenantUserRegisterFieldJson && Object.keys(tenantUserRegisterFieldJson).length > 0
              ? sequelize.query('CALL backup_configuration(:tenantId, :moduleType, :currentData)', {
                replacements: {
                  tenantId,
                  moduleType: CONFIGURATION_BACKUP_MODULE.REGISTRATION_FIELDS,
                  currentData: JSON.stringify(tenantUserRegisterFieldJson)
                },
              })
              : null,
            credentialsJson && Object.keys(credentialsJson).length > 0
              ? sequelize.query('CALL backup_configuration(:tenantId, :moduleType, :currentData)', {
                replacements: {
                  tenantId,
                  moduleType: CONFIGURATION_BACKUP_MODULE.CONFIGURATION_VARIABLE,
                  currentData: JSON.stringify(credentialsJson)
                },
              })
              : null,
            configurationSettings && Object.keys(configurationSettings).length > 0
              ? sequelize.query('CALL backup_configuration(:tenantId, :moduleType, :currentData)', {
                replacements: {
                  tenantId,
                  moduleType: CONFIGURATION_BACKUP_MODULE.CONFIGURATION_SETTINGS,
                  currentData: JSON.stringify(configurationSettings)
                },
              })
              : null,
            basicSettings && Object.keys(basicSettings).length > 0
              ? sequelize.query('CALL backup_configuration(:tenantId, :moduleType, :currentData)', {
                replacements: {
                  tenantId,
                  moduleType: CONFIGURATION_BACKUP_MODULE.THEME_GENERAL_SETTINGS,
                  currentData: JSON.stringify(basicSettings)
                },
              })
              : null,
            callingsNumbers && Object.keys(callingsNumbers).length > 0
              ? sequelize.query('CALL backup_configuration(:tenantId, :moduleType, :currentData)', {
                replacements: {
                  tenantId,
                  moduleType: CONFIGURATION_BACKUP_MODULE.HELPLINE_NUMBERS,
                  currentData: JSON.stringify(callingsNumbers)
                },
              })
              : null,
            theme
              ? sequelize.query('CALL backup_configuration(:tenantId, :moduleType, :currentData)', {
                replacements: {
                  tenantId,
                  moduleType: CONFIGURATION_BACKUP_MODULE.THEME,
                  currentData: JSON.stringify(theme)
                },
              })
              : null,
          ].filter(Boolean)); // Remove any null values from the array
        }
      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    }
    catch (error) {
      await ErrorLogHelper.logError(error, null, null)
      this.addError('InternalServerErrorType', error)
    }
  }
}
