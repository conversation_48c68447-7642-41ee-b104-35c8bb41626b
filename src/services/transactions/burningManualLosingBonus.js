import ErrorLogHelper from '../../common/errorLog'
import db, { sequelize } from '../../db/models'
import ServiceBase from '../../libs/serviceBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'
import { SUCCESS_MSG } from '../../utils/constants/constant'
import { CRON_LOG_STATUS } from '../../common/constants'

export class BurningManualLosingBonus extends ServiceBase {
  async run () {
    const cronLog = {}
    cronLog.startTime = new Date()
    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'burning_manual_losing_bonus_cron',
          status: 1
        },
        attributes: ['id']
      })

      if (queueProcessStatus) {
        if (queueProcessStatus) cronLog.cronId = queueProcessStatus?.id  // for cron logs

        const today = new Date()
        const currentDate = today.toISOString()
        const currentDateMinus2Hours = new Date(today - 2 * 60 * 60 * 1000).toISOString() // cron is running in every 2 hours

        const queries = {
          claimDaysBonus: `
            SELECT
              ub.id,
              ub.user_id AS "userId",
              ub.claimed_at AS "createdAt",
              ub.bonus_amount AS "bonusAmount",
              b.id AS "bonusId",
              b.tenant_id AS "tenantId",
              lbs.burning_days AS "burningDays",
              lbs.claim_days AS "claimDays"
            FROM
              user_bonus ub
            JOIN
              bonus b ON ub.bonus_id = b.id
            JOIN
              losing_bonus_settings lbs ON b.id = lbs.bonus_id
            WHERE
              b.kind IN ('losing', 'losing_sport', 'losing_both')
              AND ub.status = 'claimed'
              AND lbs.burning_days IS NOT NULL
              AND lbs.claim_days IS NOT NULL
              AND ub.burning_trxn_id IS NULL
              AND (ub.claimed_at + INTERVAL '1 day' * lbs.burning_days) <= :currentDate
              AND (ub.claimed_at + INTERVAL '1 day' * lbs.burning_days) > :currentDateMinus2Hours
          `,
          claimIntervalBonus: `
            SELECT
              ulbch.id,
              ulbch.user_id AS "userId",
              ulbch.created_at AS "createdAt",
              ulbch.bonus_amount AS "bonusAmount",
              b.tenant_id AS "tenantId",
              b.id AS "bonusId",
              lbs.burning_days AS "burningDays",
              lbs.claim_days AS "claimDays"
            FROM
              user_losing_bonus_claim_history ulbch
            JOIN
              bonus b ON ulbch.bonus_id = b.id
            JOIN
              losing_bonus_settings lbs ON b.id = lbs.bonus_id
            WHERE
              b.kind IN ('losing', 'losing_sport', 'losing_both')
              AND lbs.bonus_claim_type = 'manual'
              AND lbs.claim_days IS NULL
              AND lbs.claim_interval_type IS NOT NULL
              AND lbs.burning_days IS NOT NULL
              AND ulbch.burning_trxn_id IS NULL
              AND (ulbch.created_at + INTERVAL '1 day' * lbs.burning_days) <= :currentDate
              AND (ulbch.created_at + INTERVAL '1 day' * lbs.burning_days) > :currentDateMinus2Hours
          `
        }

        const commonReplacements = {
          currentDate,
          currentDateMinus2Hours,
        }

        const fetchAndProcessBonusHistory = async (queryKey) => {
          const bonusHistory = await sequelize.query(queries[queryKey], {
            replacements: commonReplacements,
            type: sequelize.QueryTypes.SELECT,
          })

          if (bonusHistory.length) {
            const queue = CommonQueue
            const queueName = CommonJob

            bonusHistory.forEach((history) => {
              const queueParams = {
                id: history.id,
                userId: history.userId,
                createdAt: history.createdAt,
                bonusAmount: history.bonusAmount,
                bonusId: history.bonusId,
                tenantId: history.tenantId,
                burningDays: history.burningDays,
                claimDays: history.claimDays,
                transactionType: 'burning_manual_losing_bonus'
              }
              queue.add(
                queueName,
                queueParams,
                {
                  jobId: `${history.id}_${queueName}_${history.userId}`,
                  removeOnComplete: true,
                  delay: 5
                }
              )
            })
          }
        }

        await Promise.all([
          fetchAndProcessBonusHistory('claimDaysBonus'),
          fetchAndProcessBonusHistory('claimIntervalBonus')
        ])
      }
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message || null
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      await ErrorLogHelper.logError(error, null, null)
      this.addError('InternalServerErrorType', error)
    }
  }
}
