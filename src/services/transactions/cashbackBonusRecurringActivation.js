import moment from 'moment';
import { Op, Sequelize } from "sequelize";
import { ALLOWED_PERMISSIONS, AUTO_ACTIVATION_TYPE, BONUS_CREATION_TYPE, BONUS_TYPES, PROD_TENANTS, QUEUE_WORKER_CONSTANT, RECURRING_SCHEDULE_DAYS, RECURRING_SCHEDULE_TYPE, STAGE_TENANTS, } from '../../common/constants';
import <PERSON>rrorLogHelper from '../../common/errorLog';
import config from '../../configs/app.config';
import db, { sequelize } from '../../db/models';
import ServiceBase from '../../libs/serviceBase';
import { SUCCESS_MSG } from '../../utils/constants/constant';

export class CashbackBonusRecurringActivation extends ServiceBase {
  async run () {
    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'cashback_bonus_recurring_activation_cron',
          status: 1
        },
        attributes: ['id']
      })

      if (!queueProcessStatus) return

      const {
        BonusRecurringSchedule: BonusRecurringScheduleModel,
        Bonus: BonusModel,
        BonusCategory: BonusCategoryModel,
        LosingBonusSetting: LosingBonusSettingModel,
        LosingBonusTier: LosingBonusTierModel,
        QueueLog: QueueLogModel,
        TenantThemeSetting: TenantThemeSettingModel } = db;

      const tenants = config.get('env') === 'development' ? STAGE_TENANTS : PROD_TENANTS

      for (const [tenantId, tenant] of Object.entries(tenants)) {

        const tenantThemeSetting = await TenantThemeSettingModel.findOne({
          attributes: ['tenantId', 'allowedModules'],
          where: {
            tenantId
          },
          raw: true
        });

        if (!tenantThemeSetting) continue

        const { allowedModules } = tenantThemeSetting;

        // Check if the tenant has the playerCategory module enabled
        const hasRecurringActivationModule = allowedModules
          ? allowedModules.split(',').map(module => module.trim()).includes(ALLOWED_PERMISSIONS.CASHBACK_BONUS_RECURRING_ACTIVATION)
          : false;

        if (!hasRecurringActivationModule) continue;

        // Check if the tenant has the playerCategory module enabled
        const hasPlayerCategory = allowedModules
          ? allowedModules.split(',').map(module => module.trim()).includes(ALLOWED_PERMISSIONS.ENABLE_PLAYER_CATEGORY)
          : false;

        const timezone = 'Asia/kolkata';

        // Fetching the list of active bonus recurring schedules for the current day
        const bonusRecurringScheduleList = await BonusRecurringScheduleModel.findAll({
          where: {
            tenantId, // Filter by tenant ID to get schedules specific to the current tenant
            active: true, // Only consider active recurring schedules

            // Ensure a new bonus is created only if lastCreatedBonusId is NULL or the last created bonus wasn't created today
            [Op.and]: [
              {
                [Op.or]: [
                  { lastCreatedBonusId: null },
                  Sequelize.literal(
                    `NOT EXISTS (
                        SELECT 1 FROM bonus
                        WHERE bonus.id = "BonusRecurringSchedule"."last_created_bonus_id"
                        AND (
                            DATE((bonus.valid_from::timestamp AT TIME ZONE 'UTC') AT TIME ZONE '${timezone}') =
                            DATE((CURRENT_TIMESTAMP AT TIME ZONE '${timezone}'))

                            OR

                            DATE((bonus.created_at::timestamp AT TIME ZONE 'UTC') AT TIME ZONE '${timezone}') =
                            DATE((CURRENT_TIMESTAMP AT TIME ZONE '${timezone}'))
                        )
                    )`
                  )
                ]
              },
              {
                [Op.or]: [
                  {
                    // Beginning of the Month: Run on the 1st day of the month
                    recurringType: RECURRING_SCHEDULE_TYPE.BEGINNING_OF_MONTH,
                    [Op.and]: [Sequelize.literal(`EXTRACT(DAY FROM (NOW()::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}')) = ${RECURRING_SCHEDULE_DAYS.BEGINNING_OF_MONTH}`)]
                  },
                  {
                    // First Half of the Month: Run on the 14th day of the month
                    recurringType: RECURRING_SCHEDULE_TYPE.FIRST_HALF_MONTH,
                    [Op.and]: [Sequelize.literal(`EXTRACT(DAY FROM (NOW()::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}')) = ${RECURRING_SCHEDULE_DAYS.FIRST_HALF_MONTH}`)]
                  },
                  {
                    // Mid to Late Month: Run on the 16th day of the month
                    recurringType: RECURRING_SCHEDULE_TYPE.MID_LATE_MONTH,
                    [Op.and]: [Sequelize.literal(`EXTRACT(DAY FROM (NOW()::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}')) = ${RECURRING_SCHEDULE_DAYS.MID_LATE_MONTH}`)]
                  },
                  {
                    // Second Half of the Month: Run on the second-to-last day of the month
                    recurringType: RECURRING_SCHEDULE_TYPE.SECOND_HALF_MONTH,
                    [Op.and]: [
                      Sequelize.literal(
                        `EXTRACT(DAY FROM (NOW()::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}')) = EXTRACT(DAY FROM (DATE_TRUNC('MONTH', (NOW()::timestamp AT TIME ZONE 'UTC' AT TIME ZONE '${timezone}')) + INTERVAL '1 MONTH - ${RECURRING_SCHEDULE_DAYS.DAYS_BEFORE_MONTH_END} day'))`
                      )
                    ]
                  }
                ]
              }
            ]
          },

          // Include related models for additional details
          include: [
            {
              attributes: [
                'id','code','percentage','enabled','kind','currencyId','promotionTitle','image','termsAndConditions', 'vipLevels',
                'tenantId','flat','walletType','bonusCancellationType','depositBonusType','burningDays','promoCodes',
                'referType', 'referValue','autoActivateBonus', 'enablePastDate', 'creationType',

                // Fix timezone conversion
                [Sequelize.literal(`valid_from AT TIME ZONE 'UTC'`), 'validFrom'],
                [Sequelize.literal(`valid_upto AT TIME ZONE 'UTC'`), 'validUpto']
              ],
              model: BonusModel,
              required: true, // Ensures only schedules with valid bonuses are fetched
              include: [
                {
                  model: LosingBonusSettingModel, // Fetch losing bonus settings linked to the bonus
                  include: [
                    {
                      model: LosingBonusTierModel, // Fetch the tiers for losing bonus settings
                    },
                  ],
                },
                // Conditionally add BonusCategoryModel if hasPlayerCategory is true
                ...(hasPlayerCategory
                  ? [
                    {
                      model: BonusCategoryModel,
                      attributes: ['category'],
                      as: 'bonusCategory',
                      required: false, // Ensures bonuses without a category are also included
                    }
                  ]
                  : [])
              ],
            },
          ],
        });

        // Only proceed if there are schedules to process
        if (bonusRecurringScheduleList.length > 0) {
          // Start a transaction
          const transaction = await sequelize.transaction();

          try {
            for (const recurringSchedule of bonusRecurringScheduleList) {
              const bonusEntity = recurringSchedule?.Bonus;
              const bonusSettingEntity = bonusEntity?.LosingBonusSetting;
              const bonusSettingTierEntity = bonusSettingEntity?.LosingBonusTiers ?? [];
              const bonusCategoryEntity = bonusEntity?.bonusCategory ?? [];
              const monthAbbr = moment().format('MMM').toUpperCase(); // e.g., "FEB"
              const uniqueCode = `${monthAbbr}${+bonusEntity?.tenantId}${bonusEntity?.code}`;

              const insertData = {
                code: uniqueCode,
                percentage: bonusEntity?.percentage,
                enabled: bonusEntity?.enabled,
                validFrom: bonusEntity?.validFrom,
                validUpto: bonusEntity?.validUpto,
                kind: bonusEntity?.kind,
                currencyId: bonusEntity?.currencyId,
                promotionTitle: JSON.stringify(bonusEntity?.promotionTitle),
                image: bonusEntity?.image,
                termsAndConditions: JSON.stringify(bonusEntity?.termsAndConditions),
                vipLevels: bonusEntity?.vipLevels,
                tenantId: bonusEntity?.tenantId,
                flat: bonusEntity?.flat,
                walletType: bonusEntity?.walletType,
                bonusCancellationType: bonusEntity?.bonusCancellationType,
                depositBonusType: bonusEntity?.depositBonusType,
                burningDays: bonusEntity?.burningDays,
                promoCodes: bonusEntity?.promoCodes,
                referType: bonusEntity?.referType,
                referValue: bonusEntity?.referValue,
                autoActivateBonus: true,
                enablePastDate: !!bonusSettingEntity?.claimDays,
                creationType: BONUS_CREATION_TYPE.AUTOMATIC
              };

              // Step 1: Insert new bonus
              const newBonus = await BonusModel.create(insertData, { transaction });


              // Step 2: Reset auto-activated bonuses for the same tenant & currency
              await BonusModel.update(
                { autoActivateBonus: false },
                {
                  where: {
                    tenantId,
                    autoActivateBonus: true,
                    currencyId: bonusEntity?.currencyId,
                    creationType: BONUS_CREATION_TYPE.AUTOMATIC,
                    id: { [Op.ne]: newBonus?.id }
                  },
                  transaction
                }
              );

              // Step 3: Insert Losing Bonus Settings (linked to bonus) only if it exists
              let newSettings = null;

              if (bonusSettingEntity) {
                const settingsData = {
                  bonusId: newBonus.id,
                  claimDays: bonusSettingEntity.claimDays,
                  bonusClaimType: bonusSettingEntity.bonusClaimType,
                  bonusCalculationType: bonusSettingEntity.bonusCalculationType,
                  claimIntervalType: bonusSettingEntity.claimIntervalType,
                  weekDay: bonusSettingEntity.weekDay,
                  burningDays: bonusSettingEntity.burningDays,
                  burnType: bonusSettingEntity.burnType,
                  providerDetails: bonusSettingEntity.providerDetails
                };

                newSettings = await LosingBonusSettingModel.create(settingsData, { transaction });
              }

              // Step 4: Insert Losing Bonus Tiers (linked to settings) only if newSettings exists
              if (newSettings) {
                for (const tier of bonusSettingTierEntity) {
                  const losingBonusTierToInsert = {
                    losingBonusSettingId: newSettings.id,
                    minLosingAmount: tier?.minLosingAmount,
                    maxLosingAmount: tier?.maxLosingAmount,
                    percentage: tier?.percentage
                  };

                  await LosingBonusTierModel.create(losingBonusTierToInsert, { transaction });
                }
              }

              // Step 5: Insert Bonus Categories (linked to bonus) only if it exists
              if (bonusCategoryEntity && bonusCategoryEntity.length) {
                for (const categoryRow of bonusCategoryEntity) {
                  const bonusCategoryToInsert = {
                    bonusId: newBonus.id,
                    tenantId,
                    category: categoryRow?.category
                  };

                  await BonusCategoryModel.create(bonusCategoryToInsert, { transaction });
                }
              }

              // Step 6: Check if a QueueLog entry already exists
              const existingQueueLog = await QueueLogModel.findOne({
                attributes: ['id'],
                where: {
                  type: QUEUE_WORKER_CONSTANT.BONUS,
                  tenantId,
                  status: QUEUE_WORKER_CONSTANT.READY,
                  ids: {
                    [Sequelize.Op.contains]: [{ bonusId: newBonus.id, type: AUTO_ACTIVATION_TYPE.AUTO }]
                  }
                },
                transaction
              });

              // Step 7: Insert into QueueLog if no existing entry is found
              if (!existingQueueLog) {
                await QueueLogModel.create(
                  {
                    type: QUEUE_WORKER_CONSTANT.BONUS,
                    status: QUEUE_WORKER_CONSTANT.READY,
                    ids: [{ bonusType: BONUS_TYPES.AUTO_BONUS_ACTIVATE, bonusId: newBonus.id, timezone, type: AUTO_ACTIVATION_TYPE.AUTO }],
                    tenantId
                  },
                  { transaction }
                );
              }

              // Step 8: Update lastCreatedBonusId in the BonusRecurringSchedule table
              await BonusRecurringScheduleModel.update(
                { lastCreatedBonusId: newBonus.id },
                { where: { id: recurringSchedule.id }, transaction }
              );
            }

            // Commit the transaction after all operations succeed
            await transaction.commit();
          } catch (error) {
            // Rollback the transaction in case of an error
            if (transaction) {
              await transaction.rollback();
            }

            await ErrorLogHelper.logError(error, null, null);
            this.addError('InternalServerErrorType', error);
          }
        }
      }

      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    }
    catch (error) {
      await ErrorLogHelper.logError(error, null, null)
      this.addError('InternalServerErrorType', error)
    }
  }
}
