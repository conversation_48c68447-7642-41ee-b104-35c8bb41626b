import ErrorLogHelper from '../../common/errorLog'
import { sequelize } from '../../db/models'

export default async (data) => {
  try {
    const { tenantId, topMenuId } = data
    const cteQuery = `
      WITH page_menus_cte AS (
      SELECT
        LOWER(pm.name) AS "pageMenuName",
        pm.id AS "pageMenuId",
        COALESCE(MAX(cm.image_url), NULL) AS "casinoMenuImageUrl",
        cm.tenant_id AS "tenantId"
      FROM "page_menus" pm
      JOIN "pages" p
        ON p.id = pm.page_id
        AND p.enabled = TRUE
        AND p.top_menu_id = :topMenuId
        AND p.tenant_id = :tenantId
      JOIN "casino_menus" cm
        ON cm.id = pm.casino_menu_id
        AND cm.enabled = TRUE
        AND cm.tenant_id = :tenantId
      GROUP BY LOWER(pm.name), pm.id, cm.tenant_id
    ),
    grouped_page_menus AS (
    SELECT
      "pageMenuName",
      array_agg("pageMenuId") AS "pageMenuIds",
      MAX("casinoMenuImageUrl") AS "casinoMenuImageUrl",
      MAX("tenantId") AS "tenantId"
    FROM page_menus_cte
    GROUP BY "pageMenuName"
    ),
    menu_items_cte AS (
      SELECT
        mi.id AS "menuItemId",
        mi.name AS "menuItemName",
        mi.featured,
        mi.page_menu_id,
        ci.id AS "casinoItemId",
        ci.uuid,
        ci.image,
        ci.has_lobby,
        cp.name AS "providerName",
        p.title AS "pageTitle",
        COUNT(*) OVER (PARTITION BY gpm."pageMenuName") AS total_items
      FROM "menu_items" mi
      INNER JOIN "casino_items" ci
        ON ci.id = mi.casino_item_id
        AND ci.tenant_id = :tenantId
      JOIN "casino_providers" cp
        ON cp.id = CAST(ci.provider AS bigint)
      JOIN grouped_page_menus gpm
        ON mi.page_menu_id = ANY(gpm."pageMenuIds")
      JOIN "page_menus" pm
        ON pm.id = mi.page_menu_id
      JOIN "pages" p
        ON p.id = pm.page_id
        AND p.enabled = TRUE
      WHERE mi.active = TRUE
      ORDER BY mi.featured DESC, mi."order" ASC, mi.id ASC
    ),
    total_items_cte AS (
    SELECT
      gpm."pageMenuName",
      MAX(lm.total_items) AS total_items
    FROM grouped_page_menus gpm
    JOIN menu_items_cte lm
      ON lm.page_menu_id = ANY(gpm."pageMenuIds")
    GROUP BY gpm."pageMenuName"
    ),
    upserted_records AS (
    INSERT INTO combined_menu_games (
      name, casino_menu, menu_items, total_menu_item, tenant_id, top_menu_id, created_at, updated_at
    )
    SELECT
      gpm."pageMenuName" AS name,
      json_build_object(
        'id', gpm."pageMenuIds"[1]::int,
        'name', INITCAP(gpm."pageMenuName"),
        'imageUrl', gpm."casinoMenuImageUrl",
        'tenantId', gpm."tenantId"::int
      ) AS casino_menu,
      json_agg(json_build_object(
        'id', lm."menuItemId"::int,
        'name', lm."menuItemName",
        'featured', lm.featured,
        'pageMenuName', lm."pageTitle",
        'casinoItem', json_build_object(
          'id', lm."casinoItemId"::int,
          'uuid', lm.uuid,
          'image', lm.image,
          'hasLobby', lm.has_lobby,
          'providerName', lm."providerName"
        )
      )) AS menu_items,
      json_build_array(json_build_object(
        'count', MAX(ti.total_items)
      )) AS total_menu_item,
      gpm."tenantId"::int AS tenant_id,
      :topMenuId AS top_menu_id,
      NOW() AS created_at,
      NOW() AS updated_at
    FROM grouped_page_menus gpm
      JOIN menu_items_cte lm
      ON lm.page_menu_id = ANY(gpm."pageMenuIds")
      JOIN total_items_cte ti
      ON ti."pageMenuName" = gpm."pageMenuName"
      GROUP BY gpm."pageMenuName", gpm."pageMenuIds", gpm."casinoMenuImageUrl", gpm."tenantId"
    ON CONFLICT (tenant_id, top_menu_id, name)
    DO UPDATE SET
      casino_menu = EXCLUDED.casino_menu,
      menu_items = EXCLUDED.menu_items,
      total_menu_item = EXCLUDED.total_menu_item,
      updated_at = NOW()
    RETURNING name, tenant_id, top_menu_id
    )
    DELETE FROM combined_menu_games
    WHERE tenant_id = :tenantId
      AND top_menu_id = :topMenuId
      AND is_custom_category = false
      AND (name, tenant_id, top_menu_id) NOT IN (
      SELECT name, tenant_id, top_menu_id FROM upserted_records
    );
    `

    await sequelize.query(cteQuery, {
      replacements: {
        tenantId: +tenantId,
        topMenuId
      }
    })

  } catch (error) {
    await ErrorLogHelper.logError(error, null, null)
    throw error
  }
}
