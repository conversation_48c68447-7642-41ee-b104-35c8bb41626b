import db, { sequelize } from '../../db/models'
import { Op, Sequelize } from 'sequelize'
import ServiceBase from '../../libs/serviceBase'
import burningLosingBonusTransaction from './burningLosingBonusTransaction'
import { SUCCESS_MSG, BONUS_CLAIM_TYPE, bonusType } from '../../utils/constants/constant'
import pushInQueue from '../../common/pushInQueue'
import { CRON_LOG_STATUS } from '../../common/constants'

export class BurningLosingBonus extends ServiceBase {
  async run () {

    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'burning_losing_bonus_cron',
          status: 1
        },
        attributes: ['id']
      })
      if (queueProcessStatus) {
        this.args.cronId = queueProcessStatus?.id  // for cron logs
        const today = new Date()
        const todayDate = today.toISOString().split('T')[0]
        const userLosingBonusHistory = await sequelize.query(`
      SELECT
        ulbch.id,
        ulbch.user_id AS "userId",
        ulbch.created_at AS "createdAt",
        ulbch.bonus_amount AS "bonusAmount",
        b.id AS "bonusId",
        b.tenant_id AS "tenantId",
        lbs.burning_days AS "burningDays"
      FROM
        user_losing_bonus_claim_history ulbch
      JOIN
        bonus b ON ulbch.bonus_id = b.id
      JOIN
        losing_bonus_settings lbs ON b.id = lbs.bonus_id
      WHERE
        b.kind = :losingBonusKind
        AND lbs.bonus_claim_type = :automaticActiveClaimType
        AND lbs.burning_days IS NOT NULL
        AND ulbch.burning_trxn_id IS NULL
        AND DATE(ulbch.created_at + INTERVAL '1 day' * lbs.burning_days) = :todayDate
    `, {
          replacements: {
            losingBonusKind: bonusType.LOSING,
            automaticActiveClaimType: BONUS_CLAIM_TYPE.AUTOMATIC_ACTIVE,
            todayDate: todayDate
          },
          type: sequelize.QueryTypes.SELECT
        })

        if (userLosingBonusHistory) {
          const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
          const queueEntries = userLosingBonusHistory.map(entry => ({
            status: 0,
            type: 'burning_losing_bonus',
            ids: [entry]
          }))
          try {
            const queues = await db.QueueLog.bulkCreate(queueEntries, { returning: true })
            for (const queue of queues) {
              await delay(50)
              await pushInQueue(queue.id)
            }
          } catch (error) {
            console.log('==========error in burning losing bonus=========', error)
          }
        }
      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.args.status = CRON_LOG_STATUS.FAILED
      this.args.errorMsg = error.message || null
      this.addError('InternalServerErrorType', error)
    }
  }
}
