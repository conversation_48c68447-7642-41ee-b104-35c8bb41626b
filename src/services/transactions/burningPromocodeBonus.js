import { BONUS_STATUS, CRON_LOG_STATUS } from '../../common/constants'
import db, { sequelize } from '../../db/models'
import ServiceBase from '../../libs/serviceBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'
import { SUCCESS_MSG } from '../../utils/constants/constant'

export class BurningPromocodeBonus extends ServiceBase {
  async run () {
    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'burning_promo_code_bonus_cron',
          status: 1
        },
        attributes: ['id']
      })

      if (queueProcessStatus) {
        this.args.cronId = queueProcessStatus?.id  // for cron logs
        const today = new Date()
        const userPromocodeBonusHistory = await sequelize.query(`
          SELECT
            ub.id,
            ub.user_id AS "userId",
            ub.created_at AS "createdAt",
            ub.bonus_amount AS "bonusAmount",
            b.id AS "bonusId",
            b.tenant_id AS "tenantId",
            b.burning_days AS "burningDays"
          FROM
            user_promo_code_bonus ub
          JOIN
            tenant_promo_codes b ON ub.promo_code_id = b.id
          WHERE
            ub.status = :claimedStatus
            AND b.burning_days IS NOT NULL
            AND ub.burning_trxn_id IS NULL
            AND (ub.created_at + INTERVAL '1 day' * b.burning_days) <= :currentDate
            AND (ub.created_at + INTERVAL '1 day' * b.burning_days) > :currentDateMinus2Hours
        `, {
          replacements: {
            claimedStatus: BONUS_STATUS.CLAIMED,
            currentDate: today.toISOString(),
            currentDateMinus2Hours: new Date(today - 2 * 60 * 60 * 1000).toISOString()  // cron is running in every 2 hours
          },
          type: sequelize.QueryTypes.SELECT
        })

        if (userPromocodeBonusHistory) {
          userPromocodeBonusHistory.forEach(userPromocodeBonusHistory => {
            const queue = CommonQueue
            const queueName = CommonJob
            const queueParams = {}

            queueParams.id = userPromocodeBonusHistory.id
            queueParams.userId = userPromocodeBonusHistory.userId
            queueParams.createdAt = userPromocodeBonusHistory.createdAt
            queueParams.bonusAmount = userPromocodeBonusHistory.bonusAmount
            queueParams.bonusId = userPromocodeBonusHistory.bonusId
            queueParams.tenantId = userPromocodeBonusHistory.tenantId
            queueParams.burningDays = userPromocodeBonusHistory.burningDays
            queueParams.transactionType = 'burning_promo_code_bonus'
            queue.add(queueName,
              queueParams,
              {
                jobId: `${userPromocodeBonusHistory.id}_${queueName}_${userPromocodeBonusHistory.userId}`,
                removeOnComplete: true,
                delay: 5
              })
          }
          )
        }
      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.args.status = CRON_LOG_STATUS.FAILED
      this.args.errorMsg = error.message || null
      this.addError('InternalServerErrorType', error)
    }
  }
}
