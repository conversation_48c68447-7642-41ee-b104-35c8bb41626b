import { PutObjectCommand } from '@aws-sdk/client-s3'
import archiver from 'archiver'
import { createObjectCsvStringifier } from 'csv-writer'
import moment from 'moment'
import { Sequelize } from 'sequelize'
import serialize from 'serialize-javascript'
import { v4 as uuidv4 } from 'uuid'
import { CRON_LOG_STATUS, CSV_LOG_TYPE } from '../../common/constants'
import config from '../../configs/app.config'
import db from '../../db/models'
import { s3 } from '../../libs/awsS3ConfigV2'
import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG } from '../../utils/constants/constant'

export class RequestResponseLogData extends ServiceBase {
  async run () {
    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'delete_request_response_logs_cron',
          status: 1
        },
        attributes: ['id']
      })
      if (queueProcessStatus) {
        this.args.cronId = queueProcessStatus?.id  // for cron logs
        const params = ''
        let startDate, endDate
        if (params) {
          startDate = `${params.startDate} 00:00:00.0000`
          endDate = params.endDate
        } else {
          startDate = moment()
          endDate = moment()
          endDate = startDate.subtract(15, "days").format("YYYY-MM-DD")
          startDate = `${endDate} 00:00:00`
        }
        endDate = `${endDate} 23:59:59.9999`
        const tenantDetails = await db.RequestResponseLogs.findAll({
          where: {
            createdAt: Sequelize.literal(`created_at < (CURRENT_DATE - INTERVAL'14 day') :: TIMESTAMPTZ`)
          },
          attributes: [[Sequelize.fn('DISTINCT', Sequelize.col('tenant_id')), 'tenantId']],
        })

        const csvStringifier = createObjectCsvStringifier({
          header: [
            { id: 'id', title: 'ID' },
            { id: 'request_json', title: 'Request Json' },
            { id: 'response_json', title: 'Response Json' },
            { id: 'service', title: 'Service' },
            { id: 'url', title: 'Url' },
            { id: 'tenant_id', title: 'Tenant Id' },
            { id: 'response_code', title: 'Response Code' },
            { id: 'response_status', title: 'Response Status' },
            { id: 'error_code', title: 'Error Code' },
            { id: 'created_at', title: 'Created At' },
            { id: 'updated_at', title: 'Updated At' }
          ]
        })
        if (tenantDetails.length > 0) {
          for (const tenant of tenantDetails) {
            const reqBackup = {
              startDate: startDate,
              endDate: endDate,
              tenantId: tenant.tenantId,
              type: CSV_LOG_TYPE.REQUEST_RESPONSE_LOG
            }
            const backupLogData = await db.RequestResponseLogsBackup.create(reqBackup)

            let chunkSize = 5000
            let fullCsvContent = ''
            const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
            const totalRecords = await db.RequestResponseLogs.count({
              where: {
                tenantId: tenant.tenantId,
                createdAt: Sequelize.literal(`created_at < (CURRENT_DATE - INTERVAL'14 day') :: TIMESTAMPTZ`)
              }
            })
            const totalChunks = Math.ceil(totalRecords / chunkSize)
            for (let i = 0; i < totalChunks; i++) {
              const offset = i * chunkSize
              const reqResData = await db.RequestResponseLogs.findAll({
                where: {
                  tenantId: tenant.tenantId,
                  createdAt: Sequelize.literal(`created_at < (CURRENT_DATE - INTERVAL'14 day') :: TIMESTAMPTZ`)
                },
                offset: offset,
                limit: chunkSize,
              })
              let mainArr = []
              if (reqResData.length > 0) {
                for (const logs of reqResData) {
                  const object = {
                    id: logs.id,
                    request_json: serialize(logs.requestJson),
                    response_json: serialize(logs.responseJson),
                    service: logs.service,
                    url: logs.url,
                    tenant_id: logs.tenantId,
                    response_code: logs.responseCode,
                    response_status: logs.responseStatus,
                    error_code: logs.errorCode,
                    created_at: logs.createdAt.toISOString().replace("T", " ").substring(0, 19),
                    updated_at: logs.updatedAt.toISOString().replace("T", " ").substring(0, 19)
                  }
                  mainArr = [...mainArr, object]
                }
              }
              const csvData = csvStringifier.stringifyRecords(mainArr)
              if (i === 0) {
                fullCsvContent += csvStringifier.getHeaderString() // Add header once
              }

              fullCsvContent += csvData
              await delay(1000)
            }

            const uuid = uuidv4().replace(/-/g, '')
            const s3Config = config.getProperties().s3
            const key = `csv/request_response_logs/request_response_log_${uuid}.zip`
            const zipBuffer = await createZipBuffer('logs.csv', fullCsvContent)

            await s3.send(new PutObjectCommand({
              Bucket: s3Config.bucket,
              Key: key, // Replace .csv with .zip
              Body: zipBuffer,
              ContentType: 'application/zip',
              ContentLength: zipBuffer.length
            }))

            const updateReqBackup = {
              csvUrl: key,
              updatedAt: new Date()
            }

            await db.RequestResponseLogsBackup.update(updateReqBackup, {
              where: { id: backupLogData.id }
            });
          }
          // delete records
          await db.RequestResponseLogs.destroy({
            where: {
              createdAt: Sequelize.literal(`created_at < (CURRENT_DATE - INTERVAL'14 day') :: TIMESTAMPTZ`)
            }
          })
        }

      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.args.status = CRON_LOG_STATUS.FAILED
      this.args.errorMsg = error.message || null
      this.addError('InternalServerErrorType', error)
    }
  }
}

function createZipBuffer (filename, content) {
  return new Promise((resolve, reject) => {
    const archive = archiver('zip', { zlib: { level: 9 } })
    const chunks = []

    archive.on('data', chunk => chunks.push(chunk))
    archive.on('end', () => resolve(Buffer.concat(chunks)))
    archive.on('error', err => reject(err))

    archive.append(content, { name: filename }) // e.g., report.csv inside zip
    archive.finalize()
  })
}
