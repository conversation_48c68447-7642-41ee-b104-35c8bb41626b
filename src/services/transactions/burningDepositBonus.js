import db, { sequelize } from '../../db/models'
import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG } from '../../utils/constants/constant'
import { BONUS_TYPES, BONUS_STATUS, CRON_LOG_STATUS } from '../../common/constants'
import { CommonQueue, CommonJob } from '../../queues/common.queue'

export class BurningDepositBonus extends ServiceBase {
  async run () {
    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'burning_deposit_bonus_cron',
          status: 1
        },
        attributes: ['id']
      })

      if (queueProcessStatus) {
        this.args.cronId = queueProcessStatus?.id  // for cron logs
        const today = new Date()
        const currentDate = today.toISOString()
        const currentDateMinus2Hours = new Date(today - 2 * 60 * 60 * 1000).toISOString()

        const queries = {
          depositBonus: `
            SELECT
              ub.id,
              ub.user_id AS "userId",
              ub.claimed_at AS "createdAt",
              ub.bonus_amount AS "bonusAmount",
              b.id AS "bonusId",
              b.tenant_id AS "tenantId",
              dbs.burning_days AS "burningDays",
              b.wallet_type AS "walletType",
              b.kind AS "bonusKind"
            FROM
              user_bonus ub
            JOIN
              bonus b ON ub.bonus_id = b.id
            JOIN
              deposit_bonus_settings dbs ON b.id = dbs.bonus_id
            WHERE
              b.kind IN ('deposit', 'deposit_sport', 'deposit_both')
              AND ub.status = 'claimed'
              AND dbs.burning_days IS NOT NULL
              AND ub.burning_trxn_id IS NULL
              AND (ub.claimed_at + INTERVAL '1 day' * dbs.burning_days) <= :currentDate
              AND (ub.claimed_at + INTERVAL '1 day' * dbs.burning_days) > :currentDateMinus2Hours
          `,
          instantDepositBonus: `
            SELECT
              idbh.id,
              idbh.user_id AS "userId",
              idbh.created_at AS "createdAt",
              idbh.bonus_amount AS "bonusAmount",
              b.tenant_id AS "tenantId",
              b.id AS "bonusId",
              dbs.burning_days AS "burningDays",
              b.wallet_type AS "walletType",
              b.kind AS "bonusKind"
            FROM
              instant_deposit_bonus_history idbh
            JOIN
              bonus b ON idbh.bonus_id = b.id
            JOIN
              deposit_bonus_settings dbs ON b.id = dbs.bonus_id
            WHERE
              b.kind = 'deposit_instant'
              AND dbs.burning_days IS NOT NULL
              AND idbh.burning_trxn_id IS NULL
              AND (idbh.created_at + INTERVAL '1 day' * dbs.burning_days) <= :currentDate
              AND (idbh.created_at + INTERVAL '1 day' * dbs.burning_days) > :currentDateMinus2Hours
          `
        }

        const commonReplacements = {
          currentDate,
          currentDateMinus2Hours,
        }

        const fetchAndProcessBonusHistory = async (queryKey) => {
          const bonusHistory = await sequelize.query(queries[queryKey], {
            replacements: commonReplacements,
            type: sequelize.QueryTypes.SELECT,
          })

          if (bonusHistory.length) {
            const queue = CommonQueue
            const queueName = CommonJob

            bonusHistory.forEach((history) => {
              const jobTime = new Date()
              const queueParams = {
                id: history.id,
                userId: history.userId,
                createdAt: history.createdAt,
                bonusAmount: history.bonusAmount,
                bonusId: history.bonusId,
                tenantId: history.tenantId,
                burningDays: history.burningDays,
                walletType: history.walletType,
                bonusKind: history.bonusKind,
                transactionType: 'burning_deposit_bonus'
              }
              queue.add(
                queueName,
                queueParams,
                {
                  jobId: `${history.id}_${queueName}_${jobTime}_${history.userId}`,
                  removeOnComplete: true,
                  delay: 5
                }
              )
            })
          }
        }

        await Promise.all([
          fetchAndProcessBonusHistory('depositBonus'),
          fetchAndProcessBonusHistory('instantDepositBonus')
        ])
      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.args.status = CRON_LOG_STATUS.FAILED
      this.args.errorMsg = error.message || null
      this.addError('InternalServerErrorType', error)
    }
  }
}
