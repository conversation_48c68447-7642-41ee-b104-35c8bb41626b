import db, { sequelize } from '../../db/models'
import { Op, Sequelize } from 'sequelize'
import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG, BONUS_CLAIM_TYPE, BONUS_INTERVAL_TYPE, bonusType } from '../../utils/constants/constant'
import { CommonQueue, CommonJob } from '../../queues/common.queue'
import { v4 as uuidv4 } from 'uuid'
import { BONUS_TYPES, IST_AUTOMATIC_LOSING_BONUS_TENANTS, CRON_LOG_STATUS } from '../../common/constants'
import config from '../../configs/app.config'

export class AutomaticLosingBonus extends ServiceBase {
  async run () {

    try {
      const {
        Bonus: BonusModel,
        LosingBonusSetting: LosingBonusSettingModel,
        Wallet: WalletModel,
        Transaction: TransactionModel,
        BetsTransaction: BetsTransactionModel,
        LosingBonusTier: LosingBonusTierModel,
        UserBonus: UserBonusModel,
        Currency: CurrencyModel,
        QueueProcessStatus: QueueProcessStatusModel
      } = db

      const queueProcessStatus = await QueueProcessStatusModel.findOne({
        where: {
          service: 'automatic-losing-bonus-cron',
          status: 1
        },
        attributes: ['id']
      })

      if (queueProcessStatus) this.args.cronId = queueProcessStatus?.id  // for cron logs
      const disableTenants = config.get('env') === 'development' ? IST_AUTOMATIC_LOSING_BONUS_TENANTS.STAGE_TENANTS : IST_AUTOMATIC_LOSING_BONUS_TENANTS.PROD_TENANTS

      const losingBonusUnparsed = await BonusModel.findAll({
        where: {
          kind: {
            [Op.or]: [BONUS_TYPES.LOSING , BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING_BOTH]
          },
          enabled: true,
          validUpto: { [Op.gt]: Sequelize.literal('CURRENT_TIMESTAMP') },
          validFrom: { [Op.lte]: Sequelize.literal('CURRENT_TIMESTAMP') },
          tenantId: { [Op.notIn]: disableTenants }
        },
        include: {
          model: LosingBonusSettingModel,
          where: {
            bonusClaimType: BONUS_CLAIM_TYPE.AUTOMATIC
          },
          include: {
            model: LosingBonusTierModel
          }
        }
      })
      if (!losingBonusUnparsed.length) {
        return {
          message: 'no user losing bonus'
        }
      }

      const userLosingBonus = await UserBonusModel.findAll({
        where: {
          kind: {
            [Op.or]: [BONUS_TYPES.LOSING , BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.LOSING_BOTH]
          },
          status: 'active',
          expiresAt: { [Op.gt]: Sequelize.literal('CURRENT_TIMESTAMP') }
        }
      })
      if (!userLosingBonus.length) {
        return {
          message: 'no user losing bonus'
        }
      }
      // create a job
      const jobTime = new Date()
      const uuid = uuidv4().replace(/-/g, '')
      const uniqueId = uuid.substr(uuid.length - 10)
      const queue = CommonQueue
      const queueName = CommonJob

      // create jobs
      if (losingBonusUnparsed) {
          const losingBonusCategorized = losingBonusUnparsed.forEach((cur, idx) => {
          const d = new Date()
          // check if bonus is expired or not
          if (cur.validUpto > d) {
            const type = cur.dataValues.LosingBonusSetting.claimIntervalType
            userLosingBonus.forEach((item) => {
              if (item.bonusId === cur.dataValues.id && item.expiresAt > d) {

                const validFrom = cur.dataValues.validFrom
                const activatedFrom = item.createdAt
                const validTill = cur.dataValues.validUpto
                const today = new Date()

                const timeDiff = today.getTime() - validFrom.getTime()
                const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));

                const claimDays = Math.floor((today.getTime() - activatedFrom.getTime()) / (1000 * 3600 * 24))
                const isAvailable = claimDays < cur.LosingBonusSetting.claimDays
                const isValid = Math.floor((validTill.getTime() - today.getTime()) / (1000 * 3600 * 24))

                let flag = false

                const queueParams = {}
                queueParams.time = jobTime
                queueParams.bonusType = type
                queueParams.bonusId = cur.id
                queueParams.userId = item.userId
                const yesterday = new Date(today)
                yesterday.setDate(today.getDate() - 1)
                yesterday.setHours(0, 0, 0, 0)

                queueParams.from = (daysDiff <= 1) ? item.createdAt : yesterday
                queueParams.to = today
                queueParams.isClaimed = !(isValid > 0)
                queueParams.transactionType = 'active_losing_bonus'
                if (type === BONUS_INTERVAL_TYPE.DAILY) {
                  flag = true
                }
                if (type === BONUS_INTERVAL_TYPE.WEEKLY && daysDiff % 7 === 0) {
                  flag = true
                }
                if (type === BONUS_INTERVAL_TYPE.BIWEEKLY && daysDiff % 14 === 0) {
                  flag = true
                }
                if (type === BONUS_INTERVAL_TYPE.MONTHLY && daysDiff % 30 === 0) {
                  flag = true
                }
                if (flag) {
                  queue.add(queueName,
                    queueParams,
                    {
                      jobId: `${cur.id}_${queueName}_${jobTime}_${item.userId}`,
                      removeOnComplete: true,
                      delay: 5
                    })
                }
              }
            })
          }
          return cur
        })
      }

     // return losingBonusUnparsed
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.args.status = CRON_LOG_STATUS.FAILED
      this.args.errorMsg = error.message || null
      this.addError('InternalServerErrorType', error)
    }
  }
}
