import { Sequelize } from 'sequelize';
import { BONUS_TYPES } from '../../common/constants';
import <PERSON><PERSON><PERSON><PERSON>ogHelper from '../../common/errorLog';
import db, { sequelize } from '../../db/models';
import ServiceBase from '../../libs/serviceBase';
import { MULTI_BONUS_STATUS, SUCCESS_MSG } from '../../utils/constants/constant';

export class MultipleBonusActivation extends ServiceBase {
  async run () {
    const sequelizeTransaction = await sequelize.transaction();
    try {
      const {
        UserBonusQueue: UserBonusQueueModel,
      } = db;
      // Check if there is a queue process running for multiple_bonus_activation_cron
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'multiple_bonus_activation_cron',
          status: 1
        },
        attributes: ['id']
      });

      if (!queueProcessStatus) return;

      const tenantList = await sequelize.query(`WITH tenants AS (
          SELECT
            tenant_id as id,
            CASE
              WHEN 'multiBonusAllowance' = ANY(string_to_array(allowed_modules, ',')) THEN true
              ELSE false
            END AS multibonusallowance,
            CASE
              WHEN 'parallelBonus' = ANY(string_to_array(allowed_modules, ',')) THEN true
              ELSE false
            END AS parallelbonus
          FROM
            tenant_theme_settings
        )
        SELECT *
        FROM tenants where multibonusallowance = true
        `, {
          raw: true,
          type: Sequelize.QueryTypes.SELECT,
        });

      const excludedTenantIds = tenantList.map(tenant => tenant.id);

      // Update all pending bonus queue status to failed for tenants that are not allowed to run multi bonus
      await UserBonusQueueModel.update(
        { status: MULTI_BONUS_STATUS.FAILED },
        {
          where: {
            status: MULTI_BONUS_STATUS.PENDING,
            ...(excludedTenantIds.length > 0 && {
              bonus_id: {
                [Sequelize.Op.in]: Sequelize.literal(`
                  (SELECT id FROM bonus WHERE tenant_id NOT IN (${excludedTenantIds.join(',')}))
                `),
              },
            }),
          },
          transaction: sequelizeTransaction,
        }
      );

      const [noParallelTenantIds, parallelTenantIds] = tenantList.reduce((acc, tenant) => {
        if (tenant.parallelbonus) {
          acc[1].push(tenant.id);
        } else {
          acc[0].push(tenant.id);
        }
        return acc;
      }, [[], []]);

      const insertQueueLogs = async (tenantIds, bonusKinds) => {
        if (tenantIds.length > 0) {
          await sequelize.query(`
            INSERT INTO queue_logs (type, status, ids, created_at, updated_at, tenant_id)
            SELECT
              'bonus' AS type,
              0 AS status,
              jsonb_agg(
                jsonb_build_object(
                  'type', 'queue',
                  'bonusId', subquery.bonus_id,
                  'userIds', subquery.user_ids,
                  'bonusType', '${BONUS_TYPES.AUTO_BONUS_ACTIVATE}'
                )
              ) AS ids,
              now() AS created_at,
              now() AS updated_at,
              NULL AS tenant_id
            FROM (
              SELECT
                bonus_id,
                ARRAY_AGG(user_id) AS user_ids
              FROM (
                SELECT  DISTINCT ON (user_id) user_bonus_queue.ordering, user_id, bonus_id
                FROM user_bonus_queue JOIN bonus bb ON bb.id = user_bonus_queue.bonus_id
                WHERE status = 0 and bb.kind IN (${bonusKinds.map(kind => `'${kind}'`).join(',')})
                  AND user_id IN (
                    SELECT DISTINCT ub.user_id
                    FROM user_bonus ub
                    WHERE ub.user_id IN (
                      SELECT DISTINCT ubq.user_id
                      FROM user_bonus_queue ubq
                      JOIN bonus ON bonus.id = ubq.bonus_id
                      WHERE ubq.status = 0 AND bonus.tenant_id IN (${tenantIds.join(',')}) and bonus.kind IN (${bonusKinds.map(kind => `'${kind}'`).join(',')})
                    )
                    AND ub.user_id NOT IN (
                      SELECT user_id
                      FROM user_bonus
                      JOIN bonus ON bonus.id = user_bonus.bonus_id
                      WHERE user_bonus.status = 'active' AND bonus.kind IN (${bonusKinds.map(kind => `'${kind}'`).join(',')})
                    )
                  )
                ORDER BY user_id, ordering
              ) subquery
              GROUP BY bonus_id
            ) subquery
            GROUP BY subquery.bonus_id;
          `, { transaction: sequelizeTransaction });
        }
      };

      //  Insert non-parallel with all bonus types
      await insertQueueLogs(noParallelTenantIds, [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_BOTH, BONUS_TYPES.LOSING_SPORT, BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_BOTH, BONUS_TYPES.DEPOSIT_INSTANT, BONUS_TYPES.DEPOSIT_SPORTS]);

      // Insert parallel bonus with losing bonus types
      await insertQueueLogs(parallelTenantIds, [BONUS_TYPES.LOSING, BONUS_TYPES.LOSING_BOTH, BONUS_TYPES.LOSING_SPORT]);

      // Insert parallel bonus with deposit bonus types
      await insertQueueLogs(parallelTenantIds, [BONUS_TYPES.DEPOSIT, BONUS_TYPES.DEPOSIT_BOTH, BONUS_TYPES.DEPOSIT_INSTANT, BONUS_TYPES.DEPOSIT_SPORTS]);

      await sequelizeTransaction.commit();

      return { message: SUCCESS_MSG.CREATE_SUCCESS };
    } catch (error) {
      await sequelizeTransaction.rollback();
      await ErrorLogHelper.logError(error, null, null);
      this.addError('InternalServerErrorType', error);
    }
  }
}
