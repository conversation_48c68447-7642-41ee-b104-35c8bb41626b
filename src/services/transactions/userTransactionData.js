import { Sequelize } from 'sequelize'
import { QUEUE_WORKER_CONSTANT } from '../../common/constants'
import { publishToRedis } from '../../common/queueService/publishToRedis'
import db, { sequelize } from '../../db/models'
import ServiceBase from '../../libs/serviceBase'
import { PUSH_IN_QUEUE_CRON } from '../../utils/constants/constant'

// function getESclient () {
//   const elasticUrl = config.get('elastic.url') + ':' + config.get('elastic.port') || 'http://elasticsearch:9200'
//   const protocol = config.get('elastic.protocal')
//   const esClient = new Client({ node: protocol + config.get('elastic.user') + ':' + config.get('elastic.password') + '@' + elasticUrl })
//   return esClient;
// }

async function getUserStatusString (status) {
  let str = ''
  switch (status) {
    case false:
      str = 'InActive'
      break;
    case true:
      str = 'Active'
      break;
    default:
      str = 'NA'
  }
  return str
}

export class UserTransactionData extends ServiceBase {
  async run () {
    const {
      QueueLog: QueueLogModel
    } = db

    try {
      // let client = getESclient();
      if (PUSH_IN_QUEUE_CRON) {
        await sequelize.query(`
        delete
        FROM "queue_logs"
        WHERE  "type" = 'user_transaction' and (status = 0 or status = 3 )  and "id" not  in(SELECT
            min (queue_logs.id)
        FROM
            queue_logs, jsonb_array_elements(ids) as elem where type ='user_transaction'
                and (status = 0 or status = 3 )
        GROUP BY elem )`
        )

        const updateQueueTxn = await sequelize.query(`
        UPDATE queue_logs SET status = 2 WHERE id in
        (SELECT id
        FROM (
          SELECT id, (
            SELECT jsonb_agg(DISTINCT e.val)
            FROM jsonb_array_elements(ids) AS e(val)
          ) AS doc
          FROM queue_logs where type ='user_transaction' and (status = 0 or status = 3 )
        ) AS t
        ORDER BY doc ASC LIMIT 1000 OFFSET 0 );`
        )
        const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
        await delay(60000)
        const userTxn = await sequelize.query(`SELECT * from get_list_user_information();`,
          {
            type: Sequelize.QueryTypes.SELECT
          })
        // let newUserList = []
        let playerList = []
        if (userTxn && userTxn.length > 0) {
          for (const data of userTxn) {
            // const modifiedUser = {
            //   player_id: +data.user_id,
            //   player_id_s: data.user_id,
            //   vip_level: data.vip_level,
            //   tenant_id: +data.tenant_id,
            //   nick_name: data.nick_name,
            //   user_name: data.user_name,
            //   email: data.email,
            //   phone: data.phone,
            //   phone_code: data.phone_code,
            //   agent_full_name: data.agent_first_name + ' ' + data.agent_last_name,
            //   agent_name: data.agent_name,
            //   last_login: data.last_login_date,
            //   country: data.country_code,
            //   creation_date: data.created_at,
            //   real_balance: parseFloat(data.wallet_amount),
            //   non_cash_balance: parseFloat(data.wallet_non_cash_amount),
            //   one_time_bonus_amount: parseFloat(data.one_time_bonus_amount) || parseFloat(0),
            //   total_balance: totalBalance,
            //   currency: data.currency,
            //   wallet_id: parseInt(data.wallet_id),
            //   total_bets: data?.dist_transaction_id_count ? parseInt(data?.dist_transaction_id_count) : 0,
            //   total_bet_amount: data?.total_bet_amount ? parseFloat(data?.total_bet_amount) : 0,
            //   total_sport_bets: parseInt(sportBetCount),
            //   total_sport_bet_amount: parseFloat(sportBetAmount),
            //   parent_id: parseInt(data.parent_id),
            //   parent_type: data.parent_type,
            //   parent_chain_ids: data?.user_ids?.split(","),
            //   kyc_done: data.kyc_done ? "true" : "false",
            //   demo: data.demo ? "true" : "false",
            //   status: data.active,
            //   first_deposit_amount: parseFloat(firstDepositAmount),
            //   first_deposit_amount_transaction_id: firstDepositTxnId.toString(),
            //   first_deposit_amount_date_time: firstDepositDateTime,
            //   category_type: +data.category_type
            // }

            // Push the modified user object into the new array
            // newUserList.push(modifiedUser)

            const playerData = {
              userId: +data.user_id,
              tenantId: +data.tenant_id
            }
            playerList.push(playerData)

          }
          // const operations = newUserList.flatMap(doc => [{ index: { _index: 'users_development', _id: doc.player_id } }, doc])
          // const bulkResponse = await client.bulk({ refresh: true, body: operations })
          // if (bulkResponse.errors) {
          //   const erroredDocuments = []

          //   bulkResponse.items.forEach((action, i) => {
          //     const operation = Object.keys(action)[0]
          //     if (action[operation].error) {
          //       erroredDocuments.push({

          //         status: action[operation].status,
          //         error: action[operation].error,
          //         operation: operations[i * 2],
          //         document: operations[i * 2 + 1]
          //       })
          //     }
          //   })
          //   console.log("====ErrorInBulkReindex",erroredDocuments)
          // }

          //player Report

          if (Array.isArray(playerList) && playerList.length > 0) {
            const playerPromises = playerList.map(async (player) => {
              // Insert a Smartico queue log for this user
              const smarticoQueueLogObject = {
                type: QUEUE_WORKER_CONSTANT.SMARTICO_WALLET_UPDATE,
                status: QUEUE_WORKER_CONSTANT.READY,
                ids: [+player.userId], // Assuming 'user' has the 'id' after upsert
                tenantId: +player.tenantId,
              };

              const smarticoQueueLog = await QueueLogModel.create(smarticoQueueLogObject);

              // Publish to Redis
              try {
                await publishToRedis.publishToQueueService({ QueueLog: { queueLogId: smarticoQueueLog.id } });
              } catch (error) {
                console.error("Error while publishing Smartico queue", error);
              }
            });

            await Promise.all(playerPromises);
          }

          //player Report

        }

        await sequelize.query(`
        UPDATE queue_logs SET status = 1 WHERE id in
        (SELECT id
        FROM (
          SELECT id, (
            SELECT jsonb_agg(DISTINCT e.val)
            FROM jsonb_array_elements(ids) AS e(val)
          ) AS doc
          FROM queue_logs where type ='user_transaction' and (status = 2)
        ) AS t
        );`
        )
      }
      // return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      console.log("=======error", error)
      this.addError('InternalServerErrorType', error)
      throw new Error(error)
    }
  }
}
