import { JETFAIR_INTEGRATION_CONSTANT, paymentForCodes, QUEUE_WORKER_CONSTANT, SPORT_PROVIDER, SPORTS_PROVIDER_PROD, SPORTS_PROVIDERS_STAGE } from '../../common/constants'
import config from '../../configs/app.config'
import db, { sequelize } from '../../db/models'
import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG } from '../../utils/constants/constant'

export class SportCasinoTransactionData extends ServiceBase {
  async run () {
    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'sport_to_casino_cron',
          status: 1
        },
        attributes: ['id']
      })
      if(queueProcessStatus){
        const CONSTANTS = {
          PROVIDERS: config.get('env') === 'production' ? JSON.stringify(SPORTS_PROVIDER_PROD): JSON.stringify(SPORTS_PROVIDERS_STAGE),
          JETFAIR_INTEGRATION_CONSTANT: JSON.stringify(JETFAIR_INTEGRATION_CONSTANT),
          SPORT_PROVIDER: JSON.stringify(SPORT_PROVIDER),
          QUEUE_WORKER_CONSTANT: JSON.stringify(QUEUE_WORKER_CONSTANT),
          PAYMENT_FOR_CODES: JSON.stringify(paymentForCodes),
        };

        // Call stored procedure
        const callSportCasinoTxTemplateWorkerSP = `CALL process_sport_casino_tx_template_worker(
          :providers,
          :jetfair_integration_constant,
          :sport_provider,
          :queue_worker_constant,
          :payment_for_codes
        )`
        await sequelize.query(callSportCasinoTxTemplateWorkerSP, {
          replacements:{
            providers: CONSTANTS.PROVIDERS,
            jetfair_integration_constant: CONSTANTS.JETFAIR_INTEGRATION_CONSTANT,
            sport_provider: CONSTANTS.SPORT_PROVIDER,
            queue_worker_constant: CONSTANTS.QUEUE_WORKER_CONSTANT,
            payment_for_codes: CONSTANTS.PAYMENT_FOR_CODES,
          }
        })
      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.addError('InternalServerErrorType', error)
    }
  }
}
