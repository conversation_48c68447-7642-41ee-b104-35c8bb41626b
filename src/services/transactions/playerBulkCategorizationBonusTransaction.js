import { v4 as uuid } from 'uuid'
import { BONUS_COMMENT_ABBREVIATIONS, DEPOSIT_WITHDRAW_JOB_STATUS, DEPOSIT_WITHDRAW_USER_STATUS, QUEUE_WORKER_CONSTANT, TABLES, TRANSACTION_TYPES } from '../../common/constants'
import userCurrencyExchange from '../../common/userCurrencyExchange'
import v3CurrencyConversion from '../../common/v3CurrencyConversion'
import db, { sequelize } from '../../db/models'

export default async (data) => {

  const {
    Transaction: TransactionModel,
    Wallet: WalletModel,
    Currency: CurrencyModel,
    QueueLog: QueueLogModel,
    DepositWithdrawUser: DepositWithdrawUserModel,
    DepositWithdrawJob: DepositWithdrawJobModel
  } = db

  let jobId = null;

  try {
    const sequelizeTransaction = await sequelize.transaction()

    try {
      const depositWithdrawUserId = data.transactionId

      const depositWithdrawUser = await DepositWithdrawUserModel.findOne({
        where: {
          id: depositWithdrawUserId
        },
        include: [
          {
            model: DepositWithdrawJobModel,
            attributes: ['id', 'type', 'requestObject', 'tenantId']
          }
        ]
      })

      const bonusAmount = Number(depositWithdrawUser?.amount)

      if (bonusAmount <= 0) {
        await sequelizeTransaction.rollback();
        return;
      }

      const userId = depositWithdrawUser.userId
      jobId = depositWithdrawUser?.DepositWithdrawJob?.id;

      // Update job status to IN_PROGRESS
      await DepositWithdrawJobModel.update(
        { status: DEPOSIT_WITHDRAW_JOB_STATUS.IN_PROGRESS },
        { where: { id: jobId, status: DEPOSIT_WITHDRAW_JOB_STATUS.NOT_STARTED } }
      );

      const userWallet = await WalletModel.findOne({
        attributes: ['id', 'currencyId', 'nonCashAmount'],
        where: { ownerId: userId, ownerType: TABLES.USER },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: WalletModel
        },
        skipLocked: false
      })


      userWallet.nonCashAmount = parseFloat(userWallet.nonCashAmount) + parseFloat(bonusAmount);
      await userWallet.save({ transaction: sequelizeTransaction })

      const tenantId = depositWithdrawUser?.DepositWithdrawJob?.tenantId

      let transactionObj = {
        sourceWalletId: userWallet.id,
        sourceCurrencyId: userWallet.currencyId,
        amount: bonusAmount,
        comments: BONUS_COMMENT_ABBREVIATIONS.LBC,
        actioneeId: userId,
        actioneeType: 'User',
        transactionId: uuid(),
        tenantId: tenantId,
        timestamp: new Date().getTime(),
        transactionType: TRANSACTION_TYPES.PLAYER_CATEGORIZATION_BONUS,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        status: 'success',
        success: true
      }

      transactionObj.conversionRate = await userCurrencyExchange(CurrencyModel, userWallet.currencyId)
      transactionObj = await v3CurrencyConversion(sequelizeTransaction, transactionObj, userWallet.currencyId, tenantId, bonusAmount)
      transactionObj.sourceAfterBalance = userWallet.nonCashAmount
      transactionObj.sourceBeforeBalance = userWallet.nonCashAmount - bonusAmount

      const { id } = await TransactionModel.create(transactionObj, { transaction: sequelizeTransaction })

      const txnIds = []
      if (id) txnIds.push(id)
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }

      await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })

      await DepositWithdrawUserModel.update({ status: DEPOSIT_WITHDRAW_USER_STATUS.COMPLETED },
        { where: { id: depositWithdrawUserId } })

      const pendingTasks = await DepositWithdrawUserModel.count({
        where: {
          jobId: jobId,
          status: DEPOSIT_WITHDRAW_USER_STATUS.NOT_STARTED
        }
      });

      if (pendingTasks === 0) {
        const failedTasks = await DepositWithdrawUserModel.count({
          where: {
            jobId: jobId,
            status: DEPOSIT_WITHDRAW_USER_STATUS.FAILED
          }
        });

        const jobStatus = failedTasks === 0 ? DEPOSIT_WITHDRAW_JOB_STATUS.COMPLETED : DEPOSIT_WITHDRAW_JOB_STATUS.PARTIALLY_FAILED;

        await DepositWithdrawJobModel.update({ status: jobStatus }, { where: { id: jobId } });
      }

      await sequelizeTransaction.commit()

    } catch (error) {
      console.log("--------Error in player category bonus transaction---------", error);
      await sequelizeTransaction.rollback();

      // Update the job status to PARTIALLY_FAILED in case of any error
      if (jobId) {
        await DepositWithdrawJobModel.update({ status: DEPOSIT_WITHDRAW_JOB_STATUS.PARTIALLY_FAILED }, { where: { id: jobId } });
      }
    }

  } catch (error) {
    throw error
  }
}
