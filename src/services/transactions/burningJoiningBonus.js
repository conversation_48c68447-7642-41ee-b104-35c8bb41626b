import db, { sequelize } from '../../db/models'
import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG } from '../../utils/constants/constant'
import { BONUS_TYPES, BONUS_STATUS, CRON_LOG_STATUS } from '../../common/constants'
import { CommonQueue, CommonJob } from '../../queues/common.queue'

export class BurningJoiningBonus extends ServiceBase {
  async run () {
    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'burning_joining_bonus_cron',
          status: 1
        },
        attributes: ['id']
      })

      if (queueProcessStatus) {
        this.args.cronId = queueProcessStatus?.id  // for cron logs
        const today = new Date()
        const userJoiningBonusHistory = await sequelize.query(`
          SELECT
            ub.id,
            ub.user_id AS "userId",
            ub.created_at AS "createdAt",
            ub.bonus_amount AS "bonusAmount",
            b.id AS "bonusId",
            b.tenant_id AS "tenantId",
            b.burning_days AS "burningDays"
          FROM
            user_bonus ub
          JOIN
            bonus b ON ub.bonus_id = b.id
          WHERE
            b.kind = :joiningBonusKind
            AND ub.status = :claimedStatus
            AND b.burning_days IS NOT NULL
            AND ub.burning_trxn_id IS NULL
            AND (ub.created_at + INTERVAL '1 day' * b.burning_days) <= :currentDate
            AND (ub.created_at + INTERVAL '1 day' * b.burning_days) > :currentDateMinus2Hours
        `, {
          replacements: {
            joiningBonusKind: BONUS_TYPES.JOINING,
            claimedStatus: BONUS_STATUS.CLAIMED,
            currentDate: today.toISOString() ,
            currentDateMinus2Hours: new Date(today - 2 * 60 * 60 * 1000).toISOString()  // cron is running in every 2 hours
          },
          type: sequelize.QueryTypes.SELECT
        })

        if (userJoiningBonusHistory) {
          userJoiningBonusHistory.forEach(userJoiningBonusHistory => {
            const jobTime = new Date()
            const queue = CommonQueue
            const queueName = CommonJob
            const queueParams = {}

            queueParams.id = userJoiningBonusHistory.id
            queueParams.userId = userJoiningBonusHistory.userId
            queueParams.createdAt = userJoiningBonusHistory.createdAt
            queueParams.bonusAmount = userJoiningBonusHistory.bonusAmount
            queueParams.bonusId = userJoiningBonusHistory.bonusId
            queueParams.tenantId = userJoiningBonusHistory.tenantId
            queueParams.burningDays = userJoiningBonusHistory.burningDays
            queueParams.transactionType = 'burning_joining_bonus'
            queue.add(queueName,
              queueParams,
              {
                jobId: `${userJoiningBonusHistory.id}_${queueName}_${jobTime}_${userJoiningBonusHistory.userId}`,
                removeOnComplete: true,
                delay: 5
              })
          }
          )
        }
      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.args.status = CRON_LOG_STATUS.FAILED
      this.args.errorMsg = error.message || null
      this.addError('InternalServerErrorType', error)
    }
  }
}
