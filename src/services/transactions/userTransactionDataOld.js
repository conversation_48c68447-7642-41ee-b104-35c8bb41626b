import db, { sequelize } from '../../db/models'
import { Op, Sequelize } from 'sequelize'
import ServiceBase from '../../libs/serviceBase'
import pushInQueue from '../../common/pushInQueue'
import { SUCCESS_MSG, TRANSACTIONS, PUSH_IN_QUEUE_PAGE_COUNT, PUSH_IN_QUEUE_CRON } from '../../utils/constants/constant'

export class UserTransactionData extends ServiceBase {
  async run () {
    const {
      QueueLog: QueueLogModel
    } = db

    try {
      if (PUSH_IN_QUEUE_CRON) {
        const userTxn = await sequelize.query(`
                          SELECT DISTINCT ON (doc) doc,id
                          FROM (
                            SELECT id, (
                              SELECT jsonb_agg(DISTINCT e.val)
                              FROM jsonb_array_elements(ids) AS e(val)
                            ) AS doc
                            FROM queue_logs where type ='user_transaction' and ("status" = 0 or "status" = 3 )
                          ) AS t
                          ORDER BY doc ASC LIMIT 1000 OFFSET 0;`,
          { type: Sequelize.QueryTypes.SELECT }
        )
        if (userTxn && userTxn.length > 0) {
          const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
          for (const queue of userTxn) {
            try {
              await delay(50)
              await pushInQueue(queue.id)
            } catch (error) {
              await QueueLogModel.update(
                { status: 3 },
                { where: { id: queue.id } }
              )
            }
            await QueueLogModel.update(
              { status: 1 },
              { where: { ids: [queue.doc], type: 'user_transaction' } }
            )
          }
        }
      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.addError('InternalServerErrorType', error)
    }
  }
}
