import axios from 'axios'
import { Op } from 'sequelize'
import { v4 as uuidv4 } from 'uuid'
import config from '../../../src/configs/app.config'
import { JOB_CONSTANT, OTB_TENANTS_CONFIG, PROD_ALLOWED_TENANTS_SMARTICO, PROD_TENANTS, SMARTICO_EVENT_TYPES_MAPPING, STAG_ALLOWED_TENANTS_SMARTICO, STAGE_TENANTS } from '../../common/constants'
import { getEzugiWalletUpdateBulk, getUserDataEzugi, getUserLoginStats } from '../../common/smartiCoUtils'
import db from '../../db/models'
import ServiceBase from '../../libs/serviceBase'
import { PUSH_IN_QUEUE_CRON } from '../../utils/constants/constant'

export class SmartiCoEventsData extends ServiceBase {
  async run () {
    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'smartico_events_data_cron',
          status: 1
        },
        attributes: ['id']
      })

      if (!queueProcessStatus || !PUSH_IN_QUEUE_CRON) {
        throw new Error('Queue Service Status Stopped  (smartico_events_data_cron)')
      }
      const bulkInsertEventTypes = ['smartico_user', 'smartico_login_stats', 'smartico_wallet_update_bulk']
      const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
      const allowedTenantSmartico = config.get('env') === 'development' ? STAG_ALLOWED_TENANTS_SMARTICO : PROD_ALLOWED_TENANTS_SMARTICO
      const isDevelopmentEnv = config.get('env') === 'development'

      for (const tenantId of allowedTenantSmartico) {
        let isMarina888Tenant = false
        let isOTBEnabled = false
        let tenantName

        if (isDevelopmentEnv) {
          tenantName = STAGE_TENANTS[tenantId].name
          if (tenantId === 54) isMarina888Tenant = true // Marina888 staging tenantId
          isOTBEnabled = !!OTB_TENANTS_CONFIG.STAGE[tenantId] // check if one time bonus balance is enabled for this tenant
        } else {
          tenantName = PROD_TENANTS[tenantId].name
          if (tenantId === 86) isMarina888Tenant = true // Marina888 production tenantId
          isOTBEnabled = !!OTB_TENANTS_CONFIG.PROD[tenantId] // check if one time bonus balance is enabled for this tenant
        }
        for (const eventType of bulkInsertEventTypes) {
          const queueLogs = await db.QueueLog.findAll({
            where: {
              type: eventType,
              tenantId,
              status: {
                [Op.in]: [0, 3] // ready, failed
              }
            },
            attributes: ['id', 'ids'],
            order: [['id', 'ASC']],
            offset: 0,
            limit: 1000 // in batches of 1000 per request
          })

          if (queueLogs.length === 0) continue

          const queueLogIds = queueLogs.map(log => log.id)

          let eventIds = []
          if (['smartico_user', 'smartico_login_stats', 'smartico_wallet_update_bulk'].includes(eventType)) {
            eventIds = [...new Set(queueLogs.flatMap(log => log.ids))]
          } else {
            eventIds = []
            for (const log of queueLogs) {
              eventIds.push(log.ids)
            }
          }

          // Batch update QueueLogs to status 2
          await updateQueueLogStatus(queueLogIds, 2) // active state
          let data = []
          try {
            switch (eventType) {
              case 'smartico_user':
                data = await getUserDataEzugi(eventIds, SMARTICO_EVENT_TYPES_MAPPING[eventType], tenantId, tenantName, isMarina888Tenant, isOTBEnabled)
                break
              case 'smartico_login_stats':
                data = await getUserLoginStats(eventIds, SMARTICO_EVENT_TYPES_MAPPING[eventType], tenantId, tenantName, isMarina888Tenant, isOTBEnabled)
                break
              case 'smartico_wallet_update_bulk':
                data = await getEzugiWalletUpdateBulk(eventIds, SMARTICO_EVENT_TYPES_MAPPING[eventType], tenantId, tenantName, isMarina888Tenant, isOTBEnabled)
                break
              default:
                throw new Error(`Unhandled event type: ${eventType}`)
            }
            if (!data || data?.length === 0) {
              throw new Error(`No data returned for event type: ${eventType}`)
            }
          } catch (error) {
            await updateQueueLogStatus(queueLogIds, 3)
            throw error
          }
          if (eventType === 'smartico_user') {
            const updateProfileData = data.map(item => ({
              ...item,
              payload: {},
              eid: uuidv4(),
              event_type: 'update_profile'
            }))
            await sendSmarticoRequest(updateProfileData, tenantId, [])
          }
          await sendSmarticoRequest(data, tenantId, queueLogIds)

          await delay(50) // delay between requests
        }
        await delay(2000) // delay between tenants
      }
      return { success: true }
    } catch (error) {
      this.addError('InternalServerErrorType', error)
      throw new Error(error)
    }
  }
}

async function updateQueueLogStatus (ids, status) {
  await db.QueueLog.update(
    { status },
    {
      where: {
        id: {
          [Op.in]: ids
        }
      }
    }
  )
}

const sendSmarticoRequest = async (data, tenantId, queueLogIds) => {
  const url = config.get('smartiCo.url')
  const token = config.get('smartiCo.token')

  const requestBody = {
    method: JOB_CONSTANT.METHOD,
    maxBodyLength: JOB_CONSTANT.MAX_BODY_LENGTH,
    url,
    headers: {
      ...JOB_CONSTANT.HEADERS,
      Authorization: token
    },
    data
  }

  try {
    const { data } = await axios(requestBody)
    await db.RequestResponseLog.create({
      requestJson: requestBody,
      responseJson: data,
      service: 'queue-smartico-bulk',
      url: url,
      responseCode: data?.response?.status,
      tenantId: tenantId
    })
    if (queueLogIds.length > 0) {
      await updateQueueLogStatus(queueLogIds, 1) // finished state
    }
    return data
  } catch (e) {
    const { response } = e
    await db.RequestResponseLog.create({
      requestJson: requestBody,
      responseJson: response?.data,
      service: 'queue-smartico-bulk',
      url: url,
      responseCode: response?.status,
      tenantId: tenantId
    })
    if (queueLogIds.length > 0) {
      await updateQueueLogStatus(queueLogIds, 3) // failed state
    }
    throw e
  }
}
