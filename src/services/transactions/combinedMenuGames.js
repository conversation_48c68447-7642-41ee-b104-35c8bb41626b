import ErrorLogHelper from '../../common/errorLog'
import db, { sequelize } from '../../db/models'
import ServiceBase from '../../libs/serviceBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'
import { SUCCESS_MSG } from '../../utils/constants/constant'
import {  CRON_LOG_STATUS } from '../../common/constants'

export class CombinedMenuGames extends ServiceBase {
  async run () {
    const cronLog = {}
    cronLog.startTime = new Date()
    try {
      const queueProcessStatus = await db.QueueProcessStatus.findOne({
        where: {
          service: 'combined_menu_games_cron',
          status: 1
        },
        attributes: ['id']
      })

      if (queueProcessStatus) {
        if (queueProcessStatus) cronLog.cronId = queueProcessStatus?.id  // for cron logs

        // topmenuid and tenantid fetch
        const results = await sequelize.query(`
          SELECT p.top_menu_id AS "topMenuId", p.tenant_id AS "tenantId"
            FROM pages p
            INNER JOIN tenants t ON p.tenant_id = t.id
            WHERE p.enabled = true
            AND t.active = true
            AND p.top_menu_id IN (
              SELECT mts.id
              FROM menu_tenant_setting mts
              WHERE mts.status = true
            )
        `, {
          type: sequelize.QueryTypes.SELECT
        })

        if (results) {
          results.forEach(result => {
            const { topMenuId, tenantId } = result
            const queue = CommonQueue
            const queueName = CommonJob
            const queueParams = {
              topMenuId,
              tenantId,
              transactionType: 'combined_menu_games'
            }
            queue.add(
              queueName,
              queueParams,
              {
                jobId: `${topMenuId}_${queueName}_${tenantId}`,
                removeOnComplete: true,
                delay: 5
              }
            )
          })
        }
      }
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    }
    catch (error) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message || null
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      await ErrorLogHelper.logError(error, null, null)
      this.addError('InternalServerErrorType', error)
    }
  }
}
