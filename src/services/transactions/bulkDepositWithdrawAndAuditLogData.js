import db, { sequelize } from '../../db/models'
import { Op, Sequelize } from 'sequelize'
import ServiceBase from '../../libs/serviceBase'
import pushInQueue from '../../common/pushInQueue'
import { SUCCESS_MSG, PUSH_IN_QUEUE_PAGE_COUNT, PUSH_IN_QUEUE_CRON, TRANSACTION_TYPE } from '../../utils/constants/constant'

export class BulkDepositWithdrawAndAuditLogData extends ServiceBase {
  async run () {
    const {
      QueueLog: QueueLogModel
    } = db

    try {
     if (PUSH_IN_QUEUE_CRON) {
        const {count} = await QueueLogModel.findOne({
          attributes: [
            [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
          ],
          where: {
            [Op.or]: [
              { status: 0 },
              { status: 3 }
            ],
            ids: {
              [Op.ne]: []
            },
            type: {
              [Op.in]: [TRANSACTION_TYPE.BULK_DEPOSIT_WITHDRAW, TRANSACTION_TYPE.PLAYER_COMMISION, TRANSACTION_TYPE.AUDIT_LOG]
            }
          },
          raw: true
        })

        const pages = Math.ceil(count / PUSH_IN_QUEUE_PAGE_COUNT)

        for (let offset = 0; offset< pages; offset++){
          const queueLog = await db.QueueLog.findAll({
            attributes: ['id'],
            where: {
              [Op.or]: [
                { status: 0 },
                { status: 3 }
              ],
              ids: {
                [Op.ne]: []
              },
              type: {
                [Op.in]: [TRANSACTION_TYPE.BULK_DEPOSIT_WITHDRAW, TRANSACTION_TYPE.PLAYER_COMMISION, TRANSACTION_TYPE.AUDIT_LOG]
              }
            },
            limit: PUSH_IN_QUEUE_PAGE_COUNT,
            offset:offset,
            order: [
              [ 'id', 'ASC' ]
            ],
            raw: true,
            useMaster: true
          })
          if (queueLog) {
            const delay = ms => new Promise(resolve => setTimeout(resolve, ms))
            for (const queue of queueLog) {
              try {
                await delay(50)
                await pushInQueue(queue.id)
              } catch (error) {
                console.log('==========error=========', error)
                await db.QueueLog.update(
                  { status: 3 },
                  { where: { id: queue.id } }
                )
              }
            }
          }
        }
      }
      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      this.addError('InternalServerErrorType', error)
    }
  }
}
