import db from '../../db/models';
import { EVENT, EVENT_TYPE, QUEUE_WORKER_CONSTANT } from '../../common/constants';
import cancelTransactionLedger from "../../common/pendingPayout/cancelTransactionLedger";
import { checkTenantPermissions } from "../../common/findAdminRole";

export default async (sequelizeTransaction, withdrawAPIResponse, withdrawalDataParams, pubsubObj) => {
  try {
    // Destructure withdrawalDataParams
    const {
      withdrawalRequest,
      tenantPaymentConfig,
      authUser,
      clientIP
    } = withdrawalDataParams;

    const xamaxGeneratedWithdrawId = withdrawAPIResponse.data.withdrawal_id;

    // --------------------------- START: Update Withdraw Data ---------------------------
    withdrawalRequest.status = 'rejected_by_gateway';
    withdrawalRequest.payment_transaction_id = xamaxGeneratedWithdrawId;
    withdrawalRequest.actionedAt = new Date();
    withdrawalRequest.withdrawal_type = 'payment_gateway';
    withdrawalRequest.mode = tenantPaymentConfig.providerKeyValues.paymentMode ?? 'IMPS';
    withdrawalRequest.remark = withdrawAPIResponse.data.message || withdrawAPIResponse.data.error;
    withdrawalRequest.processedBankDetails = null;

    // Save In DB
    await withdrawalRequest.save({ transaction: sequelizeTransaction });

    // --------------------------- END: Update Withdraw Data ---------------------------

    // --------------------------- START: Update transaction Data ---------------------------

    withdrawalRequest.Transaction.status = 'rejected';
    withdrawalRequest.Transaction.comments = 'rejected by payment gateway';
    withdrawalRequest.Transaction.actioneeType = authUser.parentType;
    withdrawalRequest.Transaction.actioneeId = authUser.id;

    // Save In DB
    await withdrawalRequest.Transaction.save({ transaction: sequelizeTransaction });

    // --------------------------- END: Update transaction Data ---------------------------

    // ---------------- START: Apply lock on wallet, Credit amount to user wallet and Create Cancel Transaction ----------------

    const userWalletMain = await db.Wallet.findOne({
      where: { id: withdrawalRequest.Transaction.sourceWalletId, ownerType: "User" },
      transaction: sequelizeTransaction,
      lock: {
        level: sequelizeTransaction.LOCK.UPDATE,
        of: db.Wallet,
      },
      skipLocked: false,
    });
    await userWalletMain.reload({
      lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet },
      transaction: sequelizeTransaction,
    });

    userWalletMain.amount += parseFloat(withdrawalRequest.amount);
    await userWalletMain.save({ transaction: sequelizeTransaction });

    // Create cancel transaction entry
    const cancelTrnId = await cancelTransactionLedger({
      withdrawRequest: {
        ...withdrawalRequest.dataValues,
        actionableType: authUser.parentType,
        actionableId: authUser.id,
      },
      transaction: withdrawalRequest.Transaction,
      userWallet: userWalletMain,
      sequelizeTransaction
    });

    // ---------------- END: Apply lock on wallet and Credit amount to user wallet and Create Cancel Transaction ----------------

    // ---------------- START: Queue Log Transactions and Publish To Redis ----------------

    const txnIds = [];
    const bulkData = [];

    txnIds.push(String(withdrawalRequest.transactionId));
    if (cancelTrnId) {
      txnIds.push(cancelTrnId);
    }

    const queueLogObject = {
      type: QUEUE_WORKER_CONSTANT.USER_TRANSACTION,
      status: QUEUE_WORKER_CONSTANT.READY,
      ids: [String(withdrawalRequest.userId)]
    };

    bulkData.push(queueLogObject);

    if (withdrawalRequest.transactionId) {
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds,
        tenantId: authUser.tenantId,
      };
      bulkData.push(queueLogObject);

      const createdQueueLogs = await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction });

      // Add in pubsub object
      if (!pubsubObj.publishToQueueService) pubsubObj.publishToQueueService = [];
      createdQueueLogs.forEach(createdQueueLog => {
        pubsubObj.publishToQueueService.push(
          { QueueLog: { queueLogId: createdQueueLog.id } }
        );
      });

      // ---------------- END: Queue Log Transactions and Publish To Redis ----------------

      // ---------------- START: Create Notifications and Publish To Redis ----------------

      const notificationStatus = 'rejected by gateway';
      const notificationDetails = {
        message: `Your withdrawal request of ${withdrawalRequest.amount} has been ${notificationStatus}`,
        userId: withdrawalRequest.userId,
        senderType: "AdminUser",
        referenceId: withdrawalRequest.userId,
        referenceType: "User",
        type: 'withdraw-request',
        value: [withdrawalRequest.amount, notificationStatus]
      };

      const notification = await db.Notification.create({
        senderType: authUser.parentType || 0,
        senderId: authUser.id || 0,
        referenceType: notificationDetails.referenceType,
        referenceId: notificationDetails.referenceId,
        message: notificationDetails.message,
        type: notificationDetails.type,
        value: JSON.stringify(notificationDetails.value)
      }, { transaction: sequelizeTransaction });

      const notificationReceiver = await db.NotificationReceiver.create({
        notificationId: notification.id,
        isRead: false,
        receiverType: "User",
        receiverId: withdrawalRequest.userId
      }, { transaction: sequelizeTransaction });

      // Add in pubsub object
      if (!pubsubObj.publishToPlayerNotification) pubsubObj.publishToPlayerNotification = [];
      pubsubObj.publishToPlayerNotification.push({
        PlayerNotification: {
          ...notificationDetails,
          userId: String(notificationDetails.userId),
          id: notificationReceiver.id,
        }
      });

      // ---------------- END: Create Notifications and Publish To Redis ----------------
    }

    // -------------------------- START: Audit log --------------------------

    const hasPermission = await checkTenantPermissions(sequelizeTransaction, 'player_activity_log', 'R', authUser.tenantId);

    if (
      hasPermission &&
      authUser.parentType !== "SuperAdminUser" &&
      authUser.parentType !== "Manager"
    ) {
      const auditData = {
        tenantId: authUser.tenantId,
        actioneeId: authUser.id,
        eventType: EVENT_TYPE.player,
        event: EVENT.toggle,
        eventId: withdrawalRequest.userId,
        actioneeIp: clientIP,
        action: 'Updated withdrawal request status.',
        previousData: { id: withdrawalRequest.id, verify_status: withdrawalRequest.verify_status },
        modifiedData: { id: withdrawalRequest.id, verify_status: 'rejected', remark: withdrawalRequest.remark, status: 'rejected by gateway' },
      };

      const auditLog = await db.AuditLog.create(auditData, { transaction: sequelizeTransaction });
      await db.QueueLog.create({ type: 'audit_log', ids: [String(auditLog.id)] }, { transaction: sequelizeTransaction });
    }

    // -------------------------- END: Audit log --------------------------

  } catch (error) {
    throw error;
  }
};
