import { QUEUE_WORKER_CONSTANT, UU_WALLET_WITHDRAW_PROVIDER, XAMAX_WITHDRAW_PROVIDER } from '../../common/constants';
import db from '../../db/models';
import { uuWalletDecryptByPublicKeyForOut } from '../../libs/encryption';
import uuGenerateWithdrawReqPayload from '../UU-Wallet-PG/uuGenerateWithdrawReqPayload';
import uuProcessWithdrawAPIResponse from '../UU-Wallet-PG/uuProcessWithdrawAPIResponse';
import generateXamaxWithdrawRequestPayload from "../xamax-PG/generateWithdrawRequestPayload";
import processXamaxWithdrawAPIResponse from "../xamax-PG/processWithdrawAPIResponse";
const axios = require('axios');

async function createWithdrawRequest(sequelizeTransaction, jobData, pubsubObj) {
  try {
    // Get withdrawal data from job data
    let jobWithdrawalData = jobData.withdrawalData[0];
    const { withdrawRequestId, clientIP } = jobWithdrawalData;

    // --------------- START: Find Withdraw request, Transaction, Request User, Actionee User and Tenant Payment Config ---------------------

    // Find withdraw request
    const withdrawalRequest = await db.WithdrawRequest.findOne({
      where: {
        id: withdrawRequestId
      },
      include: [
        {
          model: db.User,
          required: true,
          include: [
            {
              model: db.Wallet,
              required: true,
              attributes: ['id', 'currencyId'],
              where: { ownerType: 'User' },
              include: [
                {
                  model: db.Currency,
                  attributes: ['id', 'code', 'exchangeRate'],
                  required: true
                }
              ]
            }
          ]
        },
        {
          model: db.Transaction,
          required: true,
        }
      ],
      transaction: sequelizeTransaction,
      lock: {
        level: sequelizeTransaction.LOCK.UPDATE,
        of: db.WithdrawRequest,
      },
      skipLocked: false,
    });

    // If withdraw request not found then return
    if (!withdrawalRequest) {
      return { message: "Withdrawal Request not Found", data: null };
    } else {
      await withdrawalRequest.reload({
        transaction: sequelizeTransaction,
        lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.WithdrawRequest },
      });
    }

    // Find Payment Configurations for tenant
    let tenantPaymentConfig = await db.tenantPaymentConfiguration.findOne({
      raw: true,
      attributes: ['providerKeyValues'],
      where: {
        tenantId: withdrawalRequest.tenantId,
        providerId: withdrawalRequest.paymentProviderId
      },
      include: [
        {
          attributes: ['providerName'],
          model: db.paymentProviders,
          required: true,
          where: {
            providerType: 'withdrawal'
          }
        }
      ],
    });
    tenantPaymentConfig = {
      ...tenantPaymentConfig,
      providerName: tenantPaymentConfig['paymentProvider.providerName']
    };
    delete tenantPaymentConfig['paymentProvider.providerName'];

    // If Payment Provider not found then return
    if (!tenantPaymentConfig) {
      return { message: "Payment Provider Not Found", data: null };
    }

    // Find Actionee User
    const authUser = await db.AdminUser.findOne({
      raw: true,
      attributes: ['id', 'parentType', 'tenantId'],
      where: {
        id: withdrawalRequest.makerData.id
      },
    });

    // If Actionee User not found then return
    if (!authUser) {
      return { message: "Actionee User Not Found", data: null };
    }

    const allWithdrawalRelatedData = {
      withdrawalRequest,
      tenantPaymentConfig,
      authUser,
      clientIP
    };

    // --------------- END: Find Withdraw request, Transaction, Request User, Actionee User and Tenant Payment Config ---------------------

    // --------------- START: Generate Withdraw Request API Parameters Based On Provider ---------------------

    let APIData;
    const providerName = tenantPaymentConfig.providerName;

    if (providerName === XAMAX_WITHDRAW_PROVIDER) {
      APIData = await generateXamaxWithdrawRequestPayload(allWithdrawalRelatedData);
    }

    if (providerName === UU_WALLET_WITHDRAW_PROVIDER) {
      APIData = await uuGenerateWithdrawReqPayload(allWithdrawalRelatedData);
    }

    // --------------- END: Generate Withdraw Request API Parameters Based On Provider ---------------------

    // --------------- START: Call Withdraw Request API Using Above Generated Parameters ---------------------

    const {
      providerEndPoint,
      APIBody,
      APIConfig
    } = APIData;

    let withdrawAPIResponse = await axios.post(
      providerEndPoint,
      APIBody,
      {
        ...APIConfig,
        validateStatus: function (status) {
          // Accept any status code (2xx, 3xx, 4xx, 5xx, etc.)
          return true; // Return true to resolve the promise for all status codes
        }
      }
    );

    if (providerName === UU_WALLET_WITHDRAW_PROVIDER && withdrawAPIResponse?.data?.code == 0) {
      let responseData = withdrawAPIResponse.data
      const decryptedResponseData = await uuWalletDecryptByPublicKeyForOut(responseData?.data, tenantPaymentConfig?.providerKeyValues?.publicKey);
      withdrawAPIResponse.data.data = decryptedResponseData
    }

    // Log request response
    await db.RequestResponseLog.create({
      requestJson: {
        headers: APIConfig.headers,
        body: APIBody,
      },
      url: providerEndPoint,
      responseJson: withdrawAPIResponse?.data,
      service: "create-withdraw-request",
      tenantId: withdrawalRequest.tenantId,
    });

    // --------------- END: Call Withdraw Request API Using Above Generated Parameters ---------------------

    // --------------- START: Process Withdraw API Response Based On Provider ---------------------

    if (providerName === XAMAX_WITHDRAW_PROVIDER) {
      await processXamaxWithdrawAPIResponse(sequelizeTransaction, withdrawAPIResponse, allWithdrawalRelatedData, pubsubObj);
    }

    if (providerName === UU_WALLET_WITHDRAW_PROVIDER) {
      await uuProcessWithdrawAPIResponse(sequelizeTransaction, withdrawAPIResponse, allWithdrawalRelatedData, pubsubObj, APIData.withdrawAmount);
    }

    // --------------- END: Process Withdraw API Response Based On Provider ---------------------

    // --------------- START: Queue Log for DEP_WITHDRAW_SMS_OTP ---------------------

      const allowedModulesData = await db.TenantThemeSetting.findOne({
        where: {
          tenantId: authUser.tenantId
        },
        attributes: ['allowedModules', 'smsEnable'],
      });

      if (allowedModulesData?.allowedModules?.trim()) {
        const allowedModules = allowedModulesData.allowedModules.trim().split(',');
        const smsEnabledFor = allowedModulesData.smsEnable ? allowedModulesData.smsEnable.split(',') : [];

        // Check conditions for sending SMS
        if (allowedModules.includes("sendSmsAlert") && smsEnabledFor.includes("withdrawInitiate")) {
          const withdrawRequestData = { id: withdrawalRequest.id };

          // Create a new QueueLog entry
          await db.QueueLog.create({
            type: QUEUE_WORKER_CONSTANT.DEP_WITHDRAW_SMS_OTP,
            ids: [withdrawRequestData],
            tenantId: authUser.tenantId,
          }, { transaction: sequelizeTransaction });
        }
      }

    // --------------- END: Queue Log for DEP_WITHDRAW_SMS_OTP ---------------------

  } catch (error) {
    throw error
  }
}

export default createWithdrawRequest
