import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG } from '../../utils/constants/constant'

const schema = {
  type: 'object',
  properties: {
    types: {
      type: 'array',
      items: {
        type: 'string'
      }
    }
  },
  required: ['types']
}

//const constraints = ajv.compile(schema)

export class FindJobService extends ServiceBase {
  // get constraints () {
  //   return constraints
  // }

  async run () {
    const date = '2025-06-19 18:30:00'
    const startDate = new Date(date)
    console.log("=======startDate", startDate)
    const { types } = this.args
     // CasinoTransactionData.execute()
    // await getQueueData()
    // await backupLogData()
    // await updatePayInStatus()
    // console.log("============herer")
    // await getQueueData()
    // await updatingPendingTransactions(SKY_WITHDRAW) // testing purpose
    // await backupLogData()
    // await updateDepositRequest()
    // await deleteTokenData()
    // await deleteQueueLogsData()
    // await deleteReqResLogData()
    // await deleteUserLoginHistoryData()
    /*const InvalidTypes = []
    for (const type of types) {
      switch (type) {
        case 'insertMarina888Users':
          insertMarina888Users()
          break
        case 'insertMarina888Agents':
          insertMarina888Agents()
          break
        case 'insertMarina888AgentTransaction':
          insertMarina888AgentTransaction()
          break
        case 'insertMarina888UserTransaction':
          insertMarina888UserTransaction()
          break
        case 'insertMarina888Bonus':
          insertMarina888Bonus()
          breakSUCCESS_MSG
        case 'insertMarina888UserBonus':
          insertMarina888UserBonus()
          break
        case 'insertMarina888UserBets':
          insertMarina888UserBets()
          break
        default:
          console.log(`Unknown type: ${type}`)
          InvalidTypes.push(type)
      }
    }

    if (InvalidTypes.length) {
      return { message: 'Invalid Types Provided', InvalidTypes }
    }*/

   // return { message: SUCCESS_MSG.CREATE_SUCCESS, InvalidTypes }
    return { message: SUCCESS_MSG.CREATE_SUCCESS }
  }
}
