import ServiceBase from '../../libs/serviceBase'
import { SUCCESS_MSG } from '../../utils/constants/constant'
import axios from "axios"

export class PayInStatusService extends ServiceBase {
  // get constraints () {
  //   return constraints
  // }

  async run () {
    try{
      const { url, method, headers, body } = this.args
      let apiConfig = {
        method: method,
        maxBodyLength: Infinity,
        url: url,
        headers: headers,
        data: body
      }
      console.log("=============",apiConfig)
      const apiResponse = await axios(apiConfig)
        .then(function (response) {
          return response.data
        })
        .catch(function (error, response) {
          return error;
        });

      return { response:apiResponse }
    }catch(e){
      console.log("=============error",e)
    }

  }
}
