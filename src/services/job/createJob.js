import ServiceBase from '../../libs/serviceBase'
import ajv from '../../libs/ajv'
// import db, { sequelize } from '../../db/models'
import { CasinoTransactionQueue, CasinoTransaction } from '../../queues/casinoTransaction.queue'
import { BetTransactionQueue, BetTransaction } from '../../queues/betTransaction.queue'
import { DepositTransactionQueue, DepositTransaction } from '../../queues/depositTransaction.queue'
import { UserTransactionQueue, UserTransaction } from '../../queues/userTransaction.queue'
import { WithdrawTransactionQueue, WithdrawTransaction } from '../../queues/withdrawlTransaction.queue'
import { EmailTemplateJobStatus, SUCCESS_MSG } from '../../utils/constants/constant'
import { updateEntity } from '../../utils/crud'
import { log } from 'winston'
import { updateSportsBetTransactionIndex } from '../../elastic-search'
import getQueueData from '../../common/getQueueData'
import { v4 as uuidv4 } from 'uuid'


const schema = {
  type: 'object',
  properties: {
    transactionType: { type: 'number' }
  },
  required: ['transactionType']
}

const constraints = ajv.compile(schema)
export class CreateJobService extends ServiceBase {
  get constraints () {
    return constraints
  }

  async run () {
    const { transactionType, transactionId } = this.args

    try {
      let queue
      let queueName
      switch (transactionType) {
        case 1:
          queue = CasinoTransactionQueue
          queueName = CasinoTransaction
          break
        case 2:
          queue = BetTransactionQueue
          queueName = BetTransaction
          break
        // To do implement later
        // case 3:
        //   queue = DepositTransactionQueue
        //   queueName = DepositTransaction
        //   break
        case 4:
          queue = UserTransactionQueue
          queueName = UserTransaction
          break
        // To do implement later
        // case 5:
        //   queue = WithdrawTransactionQueue
        //   queueName = WithdrawTransaction
        //   break
      }
      // console.log(queue, "-----------------------queue------------------------\n")
      const jobTime = new Date()
      const uuid = uuidv4().replace(/-/g, '')
      const uniqueId = uuid.substr(uuid.length - 10)

      if (transactionId.length > 0) {
        for (const txnId of transactionId) {
          queue.add(queueName,
            {
              time: jobTime,
              transactionType: transactionType,
              transactionId: txnId
            },
            {
              jobId: `${txnId}_${queueName}_${jobTime}_${uniqueId}`,
              // removeOnComplete: false,
              delay: 5
            })
        }
      }

      return { message: SUCCESS_MSG.CREATE_SUCCESS }
    } catch (error) {
      console.log(error, "------------error-=--------------------\n")
      this.addError('InternalServerErrorType', error)
    }
  }
}
