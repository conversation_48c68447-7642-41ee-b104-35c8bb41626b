import { Sequelize } from 'sequelize'
import PgDepositNotification from '../../common/PgDepositNotifications'
import { bonusQueueRollover } from '../../common/bonusQueueRollover'
import cancelDepositBonus from '../../common/cancelDepositBonus'
import cancelLosingBonus from '../../common/cancelLosingBonus'
import { ALLOWED_PERMISSIONS, DEPOSIT_REQUEST_STATUS, PAYMENT_GATEWAY_QUEUE_TYPE, QUEUE_WORKER_CONSTANT, SAMBHAVPAY_PG_INTEGRATION_CONSTANT, TABLES, TRANSACTION_TYPES } from '../../common/constants'
import { userFirstDeposit } from '../../common/userFirstDeposit'
import { v2DepositBonusCheck } from '../../common/v2DepositBonusCheck'
import v3CurrencyConversion from '../../common/v3CurrencyConversion'
import { verifyReferralCode } from '../../common/verifyReferralCode'
import db from '../../db/models'
import { getCheckSum, getEncrypt, getResponse } from './helperFunctions'

const axios = require('axios')

async function sambhavPayUpdatePayinStatus (orderDetails, credentials, sequelizeTransaction) {
  try {
    let params = {
      Mid: credentials.mid,
      TotalAmount: parseFloat(orderDetails.amount)*100,
      TxnRefNo: orderDetails.trackingId,
      OrderNo: orderDetails.orderId
    }

    const encryptStatus = await getEncrypt(params, credentials)
    params = {
      mid: credentials.mid,
      amount: parseFloat(orderDetails.amount) * 100,
      txnRefNo: orderDetails.trackingId,
      orderNo: orderDetails.orderId
    }
    const statusCheckSum = await getCheckSum(params, credentials.saltKey)

    // Axios configuration
    const axiosConfig = {
      headers: {
        'Content-Type': 'application/json'
      }
    }
    const requestData = {
      encryptStatus,
      statusCheckSum,
      mid: credentials.mid
    }

    const { data } = await axios.post(credentials.payinStatusApi, requestData, axiosConfig)

    let finalResponse = await getResponse(data, credentials)

    const reqLogObject = {
      requestJson: { body: credentials.payinStatusApi, params, requestData, axiosConfig },
      service: PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_DEPOSIT_REQUEST,
      url: 'queue-worker',
      responseJson: { data: finalResponse , encrptData: data},
      tenantId: orderDetails?.tenantId
    }
    await db.RequestResponseLog.create(reqLogObject)

    if (!finalResponse) { return { message: SAMBHAVPAY_PG_INTEGRATION_CONSTANT.RESPONSE_NOT_FOUND, response: finalResponse } }

    if (finalResponse.Error){
      return { message: finalResponse.Error, response: finalResponse }
    }

    if (finalResponse.RespCode === SAMBHAVPAY_PG_INTEGRATION_CONSTANT.FAILED_STATUS || finalResponse.RespCode === SAMBHAVPAY_PG_INTEGRATION_CONSTANT.PENDING_FOR_AUTHORIZATION_STATUS) {
      await db.DepositRequest.update(
        { status: DEPOSIT_REQUEST_STATUS.FAILED },
        {
          where: {
            orderId: finalResponse?.OrderNo,
            trackingId: finalResponse?.TxnRefNo,
            amount: parseFloat(finalResponse?.PayAmount),
            tenantId: orderDetails?.tenantId
          },
          transaction: sequelizeTransaction
        }
      )
      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED,orderDetails.id, orderDetails?.tenantId)

      return { message: SAMBHAVPAY_PG_INTEGRATION_CONSTANT.FAILED_STATUS_UPDATED, response: finalResponse }
    }

    if (finalResponse.RespCode === SAMBHAVPAY_PG_INTEGRATION_CONSTANT.SUCCESS_STATUS) {
      if (orderDetails.status === SAMBHAVPAY_PG_INTEGRATION_CONSTANT.COMPLETED_STATUS) { return { message: SAMBHAVPAY_PG_INTEGRATION_CONSTANT.STATUS_ALREADY_UPDATED, response: finalResponse } }
      // duplicate transaction check
      const transactionExist = await db.Transaction.findOne({
        attributes: ['id'],
        where: {
          transactionId: finalResponse?.TxnRefNo,
          debitTransactionId: finalResponse?.OrderNo,
          paymentProviderId: orderDetails?.paymentProviderId,
          tenantId: orderDetails?.tenantId
        },
        useMaster: true
      })

      if (transactionExist) { return { message: SAMBHAVPAY_PG_INTEGRATION_CONSTANT.DUPLICATE_TRANSACTION, response: finalResponse } }

      const user = await db.User.findOne({
        attributes: ['id', 'tenantId', 'userName', 'withdrawWagerAllowed', [Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id AND wallets.owner_type = 'User')`), 'exchangeRate']
        ],
        where: {
          id: orderDetails?.userId,
          tenantId: orderDetails?.tenantId
        },
        include: {
          model: db.Wallet,
          attributes: ['id', 'currencyId', 'amount', 'nonCashAmount'],
          where: {
            ownerType: 'User'
          },
          required: true
        }
      })

      const depositAmount = parseFloat(finalResponse?.PayAmount)
      const conversionRate = user?.dataValues?.exchangeRate
      let transactionObject = {
        targetWalletId: user?.Wallet?.id,
        targetCurrencyId: user?.Wallet?.currencyId,
        amount: depositAmount,
        conversionRate,
        actioneeId: orderDetails?.userId,
        actioneeType: 'User',
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        transactionId: finalResponse?.TxnRefNo,
        debitTransactionId: finalResponse?.OrderNo,
        paymentMethod: SAMBHAVPAY_PG_INTEGRATION_CONSTANT.PAYMENT_METHOD,
        paymentProviderId: orderDetails?.paymentProviderId,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        status: 'success',
        success: true,
        comments: SAMBHAVPAY_PG_INTEGRATION_CONSTANT.TRANSACTION_COMMENTS
      }

      const userWallet = await db.Wallet.findOne({
        where: { ownerId: user.id, ownerType: TABLES.USER },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet
        },
        skipLocked: false
      })

      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet }, transaction: sequelizeTransaction })

      userWallet.amount = (parseFloat((userWallet.amount).toFixed(5)) + depositAmount)
      await userWallet.save({ transaction: sequelizeTransaction })

      transactionObject = await v3CurrencyConversion(sequelizeTransaction, transactionObject, user?.Wallet?.currencyId, orderDetails?.tenantId, depositAmount)
      transactionObject.targetAfterBalance = parseFloat(userWallet.amount)
      transactionObject.targetBeforeBalance = (parseFloat(userWallet.amount) - parseFloat(depositAmount))

      const txn = await db.Transaction.create(transactionObject, { transaction: sequelizeTransaction })

      // Meta data
      const trxnMetaData = {
        transactionId: txn.id,
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        metaData: {
          AddField1: finalResponse?.AddField1,
          AddField2: finalResponse?.AddField2 ,
          AddField3: finalResponse?.AddField3,
          AddField4: finalResponse?.AddField4,
          AddField5: finalResponse?.AddField5 ,
          AddField6: finalResponse?.AddField6,
          AddField7: finalResponse?.AddField7,
          AddField8: finalResponse?.AddField8,
          AddField9: finalResponse?.AddField9,
          AddField10: finalResponse?.AddField10,
          TxnRespDate: finalResponse?.TxnRespDate,
          TotalAmount: finalResponse?.TotalAmount
         }
      }
      await db.TransactionsMetaData.create(trxnMetaData, { transaction: sequelizeTransaction })

      const txnIds = []
      if (txn) {
        await userFirstDeposit(sequelizeTransaction, txn)
        txnIds.push(txn.id)
      }

      const type = 'payin'
      await cancelLosingBonus(sequelizeTransaction, user.id, user.tenantId, type)
      await cancelDepositBonus(sequelizeTransaction, user.id, user.tenantId, type)
      const depositBonusID = await v2DepositBonusCheck(sequelizeTransaction, depositAmount, user, userWallet, txnIds, txn.id, orderDetails?.paymentProviderId)
      await bonusQueueRollover(depositAmount, txn.id, user.id, orderDetails?.paymentProviderId, depositBonusID, sequelizeTransaction)

      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }
      await db.QueueLog.create(queueLogObject, { transaction: sequelizeTransaction })

      orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED
      await orderDetails.save({ transaction: sequelizeTransaction })
      await verifyReferralCode(sequelizeTransaction, txn, DEPOSIT_REQUEST_STATUS.COMPLETED,orderDetails.id, orderDetails?.tenantId)

      const tenantThemeSetting = await db.TenantThemeSetting.findOne({
        attributes: ['allowedModules'],
        where: { tenantId: orderDetails?.tenantId },
        raw: true
      });

      if (tenantThemeSetting && tenantThemeSetting?.allowedModules && tenantThemeSetting?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION) && !user?.withdrawWagerAllowed) {
        const depositWagerQueueLogObject = {
          type: QUEUE_WORKER_CONSTANT.DEPOSIT_WAGER_TRACKING,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [orderDetails?.id],
          tenantId: orderDetails?.tenantId
        }
        await db.QueueLog.create(depositWagerQueueLogObject, { transaction: sequelizeTransaction })
      }

      await PgDepositNotification(orderDetails.id, sequelizeTransaction, user.id, depositAmount)
      return { message: SAMBHAVPAY_PG_INTEGRATION_CONSTANT.COMPLETED_STATUS_UPDATED, response: finalResponse }
    }
    return { message: SAMBHAVPAY_PG_INTEGRATION_CONSTANT.STATUS_NOT_UPDATED, response: finalResponse }
  } catch (error) {
    throw error
  }
}

export default sambhavPayUpdatePayinStatus
