import { Sequelize } from 'sequelize'
import { bonusQueueRollover } from '../../common/bonusQueueRollover'
import cancelDepositBonus from '../../common/cancelDepositBonus'
import cancelLosingBonus from '../../common/cancelLosingBonus'
import { ACKOPAY_INTEGRATION_CONSTANT, ALLOWED_PERMISSIONS, DEPOSIT_REQUEST_STATUS, PAYMENT_GATEWAY_QUEUE_TYPE, QUEUE_WORKER_CONSTANT, TABLES, TRANSACTION_TYPES } from '../../common/constants'
import v2CurrencyConversion from '../../common/newCurrencyConversion'
import { userFirstDeposit } from '../../common/userFirstDeposit'
import { v2DepositBonusCheck } from '../../common/v2DepositBonusCheck'
import { verifyReferralCode } from '../../common/verifyReferralCode'
import db from '../../db/models'

const axios = require('axios')

async function ackoPayUpdatePayinStatus (orderDetails, credentials, sequelizeTransaction) {
  try {
    const requestData = {
      clientId: credentials?.clientId,
      secretKey: credentials?.secretKey,
      orderId: orderDetails?.trackingId
    }

    // Axios configuration
    const axiosConfig = {
      headers: {
        'Content-Type': 'application/json'
      }
    }

    const url = credentials?.payinStatusCheckApi
    const { data } = await axios.post(url, requestData, axiosConfig)

    const reqLogObject = {
      requestJson: { body: url, requestData, axiosConfig },
      service: PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_DEPOSIT_REQUEST,
      url: 'queue-worker',
      responseJson: { data: data },
      tenantId: orderDetails?.tenantId
    }
    await db.RequestResponseLog.create(reqLogObject)

    if (!data) { return { message: ACKOPAY_INTEGRATION_CONSTANT.RESPONSE_NOT_FOUND, response: null } }

    if (data.statusCode === ACKOPAY_INTEGRATION_CONSTANT.STATUS_CODE_SUCCESS &&
      (data.status === ACKOPAY_INTEGRATION_CONSTANT.FAILED_TXNSTATUS ||
        data.status === ACKOPAY_INTEGRATION_CONSTANT.REFUND_TXNSTATUS ||
        data.status === ACKOPAY_INTEGRATION_CONSTANT.EXPIRED_TXNSTATUS ||
        data.status === ACKOPAY_INTEGRATION_CONSTANT.PARTIALPAID_TXNSTATUS ||
        data.status === ACKOPAY_INTEGRATION_CONSTANT.CANCELLED_TXNSTATUS
      )) {
      await db.DepositRequest.update(
        { status: DEPOSIT_REQUEST_STATUS.FAILED },
        {
          where: {
            trackingId: data?.orderId,
            amount: data?.amount,
            tenantId: orderDetails?.tenantId
          },
          transaction: sequelizeTransaction
        }
      )
      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED,orderDetails.id, orderDetails?.tenantId)

      return { message: ACKOPAY_INTEGRATION_CONSTANT.FAILED_STATUS_UPDATED, response: data }
    }

    if (data.statusCode === ACKOPAY_INTEGRATION_CONSTANT.STATUS_CODE_SUCCESS && data.status === ACKOPAY_INTEGRATION_CONSTANT.PAID_TXNSTATUS) {
      if (orderDetails.status === ACKOPAY_INTEGRATION_CONSTANT.COMPLETED_STATUS) { return { message: ACKOPAY_INTEGRATION_CONSTANT.STATUS_ALREADY_UPDATED, response: data } }

      // duplicate transaction check
      const transactionExist = await db.Transaction.findOne({
        attributes: ['id'],
        where: {
          transactionId: data?.orderId,
          debitTransactionId: data?.clientOrderId,
          paymentProviderId: orderDetails?.paymentProviderId,
          tenantId: orderDetails?.tenantId
        },
        useMaster: true
      })

      if (transactionExist) { return { message: ACKOPAY_INTEGRATION_CONSTANT.DUPLICATE_TRANSACTION, response: data } }

      const user = await db.User.findOne({
        attributes: ['id', 'tenantId', 'userName', 'withdrawWagerAllowed',[Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id AND wallets.owner_type = 'User')`), 'exchangeRate']
        ],
        where: {
          id: orderDetails?.userId,
          tenantId: orderDetails?.tenantId
        },
        include: {
          model: db.Wallet,
          attributes: ['id', 'currencyId', 'amount', 'nonCashAmount'],
          where: {
            ownerType: 'User'
          },
          required: true
        }
      })

      const depositAmount = parseFloat(data?.amount)
      const conversionRate = user?.dataValues?.exchangeRate
      let transactionObject = {
        targetWalletId: user?.Wallet?.id,
        targetCurrencyId: user?.Wallet?.currencyId,
        amount: depositAmount,
        conversionRate,
        actioneeId: orderDetails?.userId,
        actioneeType: 'User',
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        transactionId: data?.orderId,
        debitTransactionId: data?.clientOrderId,
        paymentMethod: ACKOPAY_INTEGRATION_CONSTANT.PAYMENT_METHOD,
        paymentProviderId: orderDetails?.paymentProviderId,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        status: 'success',
        success: true,
        comments: ACKOPAY_INTEGRATION_CONSTANT.TRANSACTION_COMMENTS
      }

      const userWallet = await db.Wallet.findOne({
        where: { ownerId: user.id, ownerType: TABLES.USER },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet
        },
        skipLocked: false
      })

      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet }, transaction: sequelizeTransaction })

      userWallet.amount = (parseFloat((userWallet.amount).toFixed(5)) + depositAmount)
      await userWallet.save({ transaction: sequelizeTransaction })

      transactionObject = await v2CurrencyConversion(transactionObject, userWallet, depositAmount)
      transactionObject.targetAfterBalance = parseFloat(userWallet.amount)
      transactionObject.targetBeforeBalance = (parseFloat(userWallet.amount) - parseFloat(depositAmount))

      const txn = await db.Transaction.create(transactionObject, { transaction: sequelizeTransaction })

       // Meta data
       const trxnMetaData = {
        transactionId: txn.id,
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        metaData: { bankRefNo: data?.bankRefNo, userName: data?.userName }
      }
      await db.TransactionsMetaData.create(trxnMetaData, { transaction: sequelizeTransaction })

      const txnIds = []
      if (txn) {
        await userFirstDeposit(sequelizeTransaction, txn)
        txnIds.push(txn.id)
      }

      const type = 'payin'
      await cancelLosingBonus(sequelizeTransaction, user.id, user.tenantId, type)
      await cancelDepositBonus(sequelizeTransaction, user.id, user.tenantId, type)
     const depositBonusID = await v2DepositBonusCheck(sequelizeTransaction, depositAmount, user, userWallet, txnIds, txn.id, orderDetails?.paymentProviderId)
     await bonusQueueRollover(depositAmount, txn.id, user.id, orderDetails?.paymentProviderId, depositBonusID, sequelizeTransaction)


      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }
      await db.QueueLog.create(queueLogObject, { transaction: sequelizeTransaction })

      orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED
      await orderDetails.save({ transaction: sequelizeTransaction })
      await verifyReferralCode(sequelizeTransaction, txn, DEPOSIT_REQUEST_STATUS.COMPLETED,orderDetails.id, orderDetails?.tenantId)


      const tenantThemeSetting = await db.TenantThemeSetting.findOne({
        attributes: ['allowedModules'],
        where: { tenantId: orderDetails?.tenantId },
        raw: true
      });

      if (tenantThemeSetting && tenantThemeSetting?.allowedModules && tenantThemeSetting?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION) && !user?.withdrawWagerAllowed) {
        const depositWagerQueueLogObject = {
          type: QUEUE_WORKER_CONSTANT.DEPOSIT_WAGER_TRACKING,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [orderDetails?.id],
          tenantId: orderDetails?.tenantId
        }
        await db.QueueLog.create(depositWagerQueueLogObject, { transaction: sequelizeTransaction })
      }

      return { message: ACKOPAY_INTEGRATION_CONSTANT.COMPLETED_STATUS_UPDATED, response: data }
    }
    return { message: ACKOPAY_INTEGRATION_CONSTANT.STATUS_NOT_UPDATED, response: data }
  } catch (error) {
    throw error
  }
}

export default ackoPayUpdatePayinStatus
