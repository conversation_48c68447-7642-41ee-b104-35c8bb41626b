import crypto from 'crypto'
import { Sequelize } from 'sequelize'
import cancelDepositBonus from '../../common/cancelDepositBonus'
import cancelLosingBonus from '../../common/cancelLosingBonus'
import { ALLOWED_PERMISSIONS, BONUS_TYPES, CLOUDCASH_PG, DEPOSIT_REQUEST_STATUS, PAYMENT_GATEWAY_QUEUE_TYPE, QUEUE_WORKER_CONSTANT, TABLES, TRANSACTION_TYPES } from '../../common/constants'
import ErrorLogHelper from '../../common/errorLog'
import { userFirstDeposit } from '../../common/userFirstDeposit'
import v3CurrencyConversion from '../../common/v3CurrencyConversion'
import { verifyReferralCode } from '../../common/verifyReferralCode'
import db from '../../db/models'
import toFixedDecimal from '../../libs/toFixedDecimal'
import xmlT<PERSON><PERSON><PERSON> from '../../libs/xmlToJson'

const axios = require('axios')

async function cloudCashUpdatePayinStatus (orderDetails, credentials, sequelizeTransaction) {
  let user
  try {

    const { merchant, payinStatusCheckApi, securityCode } = credentials
    const statusCheckUrl = `${payinStatusCheckApi}/Services/Merchants/${merchant}/TransferStatus/${orderDetails?.orderId}`

    let statusCheckResponse;
    try {
      statusCheckResponse = await axios.post(statusCheckUrl);
    } catch (error) {
      throw error
    }

    let responseData;
    try {
      responseData = await xmlToJson(statusCheckResponse.data)
    } catch (error) {
      throw error
    }

    const requestData = {
      merchant: merchant,
      Reference: orderDetails.orderId
    }
    const reqLogObject = {
      requestJson: { body: payinStatusCheckApi, requestData },
      service: PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_DEPOSIT_REQUEST,
      url: 'queue-worker',
      responseJson: {
        data: {
          status: statusCheckResponse?.status,
          statusText: responseData.Message,
          result: responseData,
        }
      },
      tenantId: orderDetails?.tenantId
    }

    await db.RequestResponseLog.create(reqLogObject)

    const payloadString = `${merchant}${orderDetails.orderId}${responseData.CustomerID}${toFixedDecimal(responseData.Amount)}${responseData.CurrencyCode}${responseData.StatusCode}${securityCode}`
    // toUpperCase() because in status response we are getting key in uppercase
    const stringToHash = crypto.createHash('md5').update(payloadString).digest('hex').toUpperCase()

    if (!responseData?.Key) {
      throw { message: "Key not present", response: '' }
    }
    responseData.Key = responseData.Key.toString()
    if (stringToHash != responseData?.Key?.toUpperCase()) {
      throw { message: "Invalid Key.", response: '' }
    }

    if (!statusCheckResponse || statusCheckResponse?.status != CLOUDCASH_PG.SUCCESS_STATUS_CODE) {
      return { message: CLOUDCASH_PG.INVALID_RESPONSE, response: responseData }
    }

    if ([CLOUDCASH_PG.FAILED_TXNSTATUS, CLOUDCASH_PG.REJECTED_TXNSTATUS, CLOUDCASH_PG.CANCELLED_TXNSTATUS].includes(responseData.StatusCode)) {

      await db.DepositRequest.update(
        { status: DEPOSIT_REQUEST_STATUS.FAILED },
        {
          where: {
            orderId: responseData?.Reference,
            amount: orderDetails?.amount,
            tenantId: orderDetails?.tenantId
          },
          transaction: sequelizeTransaction
        }
      )

      await verifyReferralCode(sequelizeTransaction, '', DEPOSIT_REQUEST_STATUS.FAILED, orderDetails.id, orderDetails?.tenantId)

      return { message: CLOUDCASH_PG.FAILED_STATUS_UPDATED, response: responseData }
    }
    if (responseData.StatusCode === CLOUDCASH_PG.APPROVED_TXNSTATUS) {
      if (orderDetails.status === CLOUDCASH_PG.COMPLETED_STATUS) {
        return { message: CLOUDCASH_PG.STATUS_ALREADY_UPDATED, response: responseData }
      }

      // duplicate transaction check
      const transactionExist = await db.Transaction.findOne({
        attributes: ['id'],
        where: {
          transactionId: responseData?.Reference,
          debitTransactionId: responseData?.ID,
          paymentProviderId: orderDetails?.paymentProviderId,
          tenantId: orderDetails?.tenantId
        },
        useMaster: true
      })

      if (transactionExist) {
        return { message: CLOUDCASH_PG.DUPLICATE_TRANSACTION, response: responseData }
      }

      user = await db.User.findOne({
        attributes: ['id', 'tenantId', 'userName', [Sequelize.literal(`(SELECT currencies.exchange_rate FROM currencies, wallets WHERE currencies.id = wallets.currency_id AND wallets.owner_id = "User".id AND wallets.owner_type = 'User')`), 'exchangeRate']
        ],
        where: {
          id: orderDetails?.userId,
          tenantId: orderDetails?.tenantId
        },
        include: {
          model: db.Wallet,
          attributes: ['id', 'currencyId', 'amount', 'nonCashAmount'],
          where: {
            ownerType: 'User'
          },
          required: true
        }
      })

      let depositAmount = parseFloat(responseData?.Amount)
      const conversionRate = user?.dataValues?.exchangeRate
      let transactionObject = {
        targetWalletId: user?.Wallet?.id,
        targetCurrencyId: user?.Wallet?.currencyId,
        amount: depositAmount,
        conversionRate,
        actioneeId: orderDetails?.userId,
        actioneeType: 'User',
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        transactionId: responseData?.Reference,
        paymentMethod: '',
        paymentProviderId: orderDetails?.paymentProviderId,
        //errorDescription: 'Completed Successfully',
        errorCode: 0,
        status: 'success',
        success: true,
        comments: CLOUDCASH_PG.TRANSACTION_COMMENTS
      }

      const userWallet = await db.Wallet.findOne({
        where: { ownerId: user.id, ownerType: TABLES.USER },
        transaction: sequelizeTransaction,
        lock: {
          level: sequelizeTransaction.LOCK.UPDATE,
          of: db.Wallet
        },
        skipLocked: false
      })

      await userWallet.reload({ lock: { level: sequelizeTransaction.LOCK.UPDATE, of: db.Wallet }, transaction: sequelizeTransaction })
      userWallet.amount = (parseFloat((userWallet.amount).toFixed(5)) + depositAmount)
      await userWallet.save({ transaction: sequelizeTransaction })

      transactionObject = await v3CurrencyConversion(sequelizeTransaction, transactionObject, userWallet.currencyId, orderDetails?.tenantId, depositAmount)
      transactionObject.targetAfterBalance = parseFloat(userWallet.amount)
      transactionObject.targetBeforeBalance = (parseFloat(userWallet.amount) - parseFloat(depositAmount))

      const txn = await db.Transaction.create(transactionObject, { transaction: sequelizeTransaction })

      // Meta data
      const trxnMetaData = {
        transactionId: txn.id,
        tenantId: orderDetails?.tenantId,
        transactionType: TRANSACTION_TYPES.DEPOSIT,
        metaData: {
          approved_amount: responseData?.Amount,
        }
      }
      await db.TransactionsMetaData.create(trxnMetaData, { transaction: sequelizeTransaction })

      const txnIds = [], bulkData = []
      if (txn) {
        await userFirstDeposit(sequelizeTransaction, txn)
        txnIds.push(txn.id)
      }

      const type = 'payin'
      await cancelLosingBonus(sequelizeTransaction, user.id, user.tenantId, type)
      await cancelDepositBonus(sequelizeTransaction, user.id, user.tenantId, type)
      // Deposit Bonus Queue
      const depositBonusQueueLogObject = {
        type: 'bonus',
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: [
          {
            amount: depositAmount,
            user,
            wallet: userWallet,
            transactionIds: txnIds,
            depositTransactionId: txn.id,
            paymentProviders: orderDetails?.paymentProviderId,
            bonusType: BONUS_TYPES.DEPOSIT
          }
        ]
      }
      bulkData.push(depositBonusQueueLogObject)
      const queueLogObject = {
        type: QUEUE_WORKER_CONSTANT.TYPE,
        status: QUEUE_WORKER_CONSTANT.READY,
        ids: txnIds
      }
      bulkData.push(queueLogObject)
      await db.QueueLog.bulkCreate(bulkData, { transaction: sequelizeTransaction })

      orderDetails.status = DEPOSIT_REQUEST_STATUS.COMPLETED
      await orderDetails.save({ transaction: sequelizeTransaction })

      await verifyReferralCode(sequelizeTransaction, txn, DEPOSIT_REQUEST_STATUS.COMPLETED, orderDetails.id, orderDetails?.tenantId)

      const tenantThemeSetting = await db.TenantThemeSetting.findOne({
        attributes: ['allowedModules'],
        where: { tenantId: orderDetails?.tenantId },
        raw: true
      });

      if (tenantThemeSetting && tenantThemeSetting?.allowedModules && tenantThemeSetting?.allowedModules.split(',').includes(ALLOWED_PERMISSIONS.ENABLE_FRAUD_DETECTION)) {
        const depositWagerQueueLogObject = {
          type: QUEUE_WORKER_CONSTANT.DEPOSIT_WAGER_TRACKING,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: [orderDetails?.id],
          tenantId: orderDetails?.tenantId
        }
        await db.QueueLog.create(depositWagerQueueLogObject, { transaction: sequelizeTransaction })
      }

      return { message: CLOUDCASH_PG.COMPLETED_STATUS_UPDATED, response: responseData }
    }
    return { message: CLOUDCASH_PG.STATUS_NOT_UPDATED, response: responseData }
  } catch (error) {
    await ErrorLogHelper.logError(error, null, user)
    throw error
  }
}

export default cloudCashUpdatePayinStatus
