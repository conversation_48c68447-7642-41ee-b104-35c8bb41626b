import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { GameTransactionSummaryData } from '../../services/reports/gameTransactionSummaryData'
import db from '../../db/models'
import { CRON_LOG_STATUS } from '../../common/constants'

class GameTransactionSummaryWorker extends WorkerBase {
  async run () {
    const cronLog = {}
    cronLog.startTime = new Date()
    try {
      await GameTransactionSummaryData.execute(cronLog)
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      return { message: 'Game Transactions summary created successfully' }
    } catch (error) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message || null
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      Logger.error(`Transactions summary worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await GameTransactionSummaryWorker.run({ job })
  return result
}
