import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { SmartiCoEventsTransactionData } from '../../services/transactions/smartiCoEventsTransactionData'

class SmartiCoEventsTransactionScheduleWorker extends WorkerBase {
  async run () {
    try {
      await SmartiCoEventsTransactionData.execute()
      return { message: 'Smartico Events Transaction Job created successfully' }
    } catch (error) {
      Logger.error(`Smartico Events Transactions summary worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await SmartiCoEventsTransactionScheduleWorker.run({ job })
  return result
}
