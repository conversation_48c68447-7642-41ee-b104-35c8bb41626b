import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { UserTransactionData } from '../../services/transactions/userTransactionData'

class UserTransactionScheduleWorker extends WorkerBase {
  async run () {
    try {
      await UserTransactionData.execute()
      return { message: 'User Transaction Job created successfully' }
    } catch (error) {
      Logger.error(`User Transactions schedule worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await UserTransactionScheduleWorker.run({ job })
  return result
}
