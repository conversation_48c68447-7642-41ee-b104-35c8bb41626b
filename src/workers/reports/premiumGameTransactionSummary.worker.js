import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { PremiumGameTransactionSummaryData } from '../../services/reports/premiumGameTransactionSummaryData'
import db from '../../db/models'
import { CRON_LOG_STATUS } from '../../common/constants'

class PremiumGameTransactionSummaryWorker extends WorkerBase {
  async run () {
    const cronLog = {}
    cronLog.startTime = new Date()
    try {
      await PremiumGameTransactionSummaryData.execute(cronLog)
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      return { message: 'Premium  Game Transactions summary created successfully' }
    } catch (error) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message || null
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      Logger.error(`Premium Game Transactions summary worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await PremiumGameTransactionSummaryWorker.run({ job })
  return result
}
