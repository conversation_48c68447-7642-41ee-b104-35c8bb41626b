import path from 'path'
// import { Marina888Job, Marina888Queue } from '../../queues/marina888.queue'
import { BonusEngineQueue, CashbackBonusRecurringActivationQueueSchedule, MultipleBonusActivationQueueSchedule } from '../../queues/bonusEngine.queue'
import {
  AutomaticActiveLosingBonusQueueSchedule, AutomaticLosingBonusQueueSchedule, BetsTransactionsSummary, BulkDepositWithdrawAndAuditLogQueueSchedule, BurningDepositBonusQueueSchedule, BurningJoiningBonusQueueSchedule, BurningLosingBonusQueueSchedule, BurningManualLosingBonusQueueSchedule, BurningPromocodeBonusQueueSchedule,
  CombinedMenusGamesQueueSchedule,
  CommonQueue, ComparativeReport,
  ConfigurationBackupQueueSchedule,
  GameTransactionsSummary, IstAutomaticLosingBonusQueueSchedule,
  LotteryGenerateWinCallbacksSchedule,
  OfferWinnerAnnounceQueueSchedule,
  PremiumGameTransactionsSummary, RequestResponseLogSchedule,
  UpdateRiskAndFraudIndicatorsJob,
  UpdateTodayPlayerRevenueJob, UpsertMostPlayedGamesJob
} from '../../queues/common.queue'
import { SmartiCoEventsDataQueueSchedule, SmartiCoEventsTransactionSchedule, SmartiCoJob, SmartiCoQueue } from '../../queues/smartiCo.queue'
import { BetTransactionSchedule, CasinoTransactionSchedule, RollbackTransactionSchedule, SportToCasinoTransactionSchedule, TransactionQueue, UserTransactionSchedule } from '../../queues/transaction.queue'

CommonQueue.process(UpdateRiskAndFraudIndicatorsJob, 1, path.join(__dirname, '../transaction-workers/updateRiskAndFraudSchedule.worker.js'))

CommonQueue.process(UpdateTodayPlayerRevenueJob, 1, path.join(__dirname, '../transaction-workers/todayPlayerRevenueSchedule.worker.js'))

CommonQueue.process(UpsertMostPlayedGamesJob, 1, path.join(__dirname, '../transaction-workers/upsertMostPlayedGamesSchedule.worker.js'))

CommonQueue.process(GameTransactionsSummary, 1, path.join(__dirname, './gameTransactionSummary.worker.js'))

CommonQueue.process(PremiumGameTransactionsSummary, 1, path.join(__dirname, './premiumGameTransactionSummary.worker.js'))

CommonQueue.process(BetsTransactionsSummary, 1, path.join(__dirname, './betsTransactionsSummary.worker'))

CommonQueue.process(ComparativeReport, 1, path.join(__dirname, './comparativeReport.worker.js'))

CommonQueue.process(BulkDepositWithdrawAndAuditLogQueueSchedule, 1, path.join(__dirname, '../transaction-workers/bulkDepositWithdrawAndAuditLogSchedule.worker.js'))

CommonQueue.process(BurningLosingBonusQueueSchedule, 1, path.join(__dirname, '../transaction-workers/burningLosingBonusSchedule.worker.js'))

CommonQueue.process(AutomaticLosingBonusQueueSchedule, 1, path.join(__dirname, '../transaction-workers/automaticLosingBonusSchedule.worker.js'))

CommonQueue.process(AutomaticActiveLosingBonusQueueSchedule, 1, path.join(__dirname, '../transaction-workers/automaticActiveLosingBonusSchedule.worker.js'))

CommonQueue.process(RequestResponseLogSchedule, 1, path.join(__dirname, '../transaction-workers/requestResponseLogSchedule.worker.js'))

CommonQueue.process(BurningJoiningBonusQueueSchedule, 1, path.join(__dirname, '../transaction-workers/burningJoiningBonusSchedule.worker.js'))

CommonQueue.process(BurningDepositBonusQueueSchedule, 1, path.join(__dirname, '../transaction-workers/burningDepositBonusSchedule.worker.js'))

SmartiCoQueue.process(SmartiCoEventsDataQueueSchedule, 1, path.join(__dirname, './smartiCoEventsDataSchedule.worker.js'))

SmartiCoQueue.process(SmartiCoJob, 1, path.join(__dirname, './smartiCoJob.worker.js'))

CommonQueue.process(IstAutomaticLosingBonusQueueSchedule, 1, path.join(__dirname, '../transaction-workers/istAutomaticLosingBonusQueueSchedule.worker.js'))

SmartiCoQueue.process(SmartiCoEventsTransactionSchedule, 1, path.join(__dirname, './smartiCoEventsTransactionSchedule.worker.js'))

// Marina888Queue.process(Marina888Job, 1, path.join(__dirname, './marina888Job.worker.js'))

CommonQueue.process(BurningPromocodeBonusQueueSchedule, 1, path.join(__dirname, '../transaction-workers/burningPromocodeBonusSchedule.worker.js'))

TransactionQueue.process(UserTransactionSchedule, 1, path.join(__dirname, '../transaction-workers/userTransactionSchedule.worker.js'))
TransactionQueue.process(BetTransactionSchedule, 1, path.join(__dirname, '../transaction-workers/betTransactionSchedule.worker.js'))
TransactionQueue.process(CasinoTransactionSchedule, 1, path.join(__dirname, '../transaction-workers/casinoTransactionSchedule.worker.js'))
TransactionQueue.process(SportToCasinoTransactionSchedule, 1, path.join(__dirname, '../transaction-workers/sportCasinoTransactionSchedule.worker.js'))
CommonQueue.process(BurningManualLosingBonusQueueSchedule, 1, path.join(__dirname, '../transaction-workers/burningManualLosingBonusSchedule.worker.js'))
CommonQueue.process(CombinedMenusGamesQueueSchedule, 1, path.join(__dirname, '../transaction-workers/combinedMenusGamesQueueSchedule.worker.js'))
CommonQueue.process(ConfigurationBackupQueueSchedule, 1, path.join(__dirname, '../transaction-workers/configurationBackupSchedule.worker.js'))

CommonQueue.process(OfferWinnerAnnounceQueueSchedule, 1, path.join(__dirname, '../transaction-workers/offerWinnerAnnounceSchedule.worker.js'))

BonusEngineQueue.process(MultipleBonusActivationQueueSchedule, 1, path.join(__dirname, '../transaction-workers/multipleBonusActivationSchedule.worker.js'))

BonusEngineQueue.process(CashbackBonusRecurringActivationQueueSchedule, 1, path.join(__dirname, '../transaction-workers/cashbackBonusRecurringActivationSchedule.worker.js'))

CommonQueue.process(LotteryGenerateWinCallbacksSchedule, 1, path.join(__dirname, '../transaction-workers/lotteryWinCallbacksDataSchedule.worker.js'))

TransactionQueue.process(RollbackTransactionSchedule, 1, path.join(__dirname, '../transaction-workers/rollbackTransactionSchedule.worker.js'))
