import marina888ProcessHandler from '../../common/marina888/marina888ProcessHandler'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'

class Marina888JobWorker extends WorkerBase {
  async run () {
    try {
      const result = await marina888ProcessHandler(this.args.job.data)
      if (result.status) {
        return { message: 'Job successfully done' }
      }
      return { message: 'Job failed' }
    } catch (error) {
      console.log('======Common job worker error', error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await Marina888JobWorker.run({ job })
  return result
}
