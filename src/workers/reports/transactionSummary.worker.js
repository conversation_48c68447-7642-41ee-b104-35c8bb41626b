import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { TransactionSummaryData } from '../../services/reports/transactionSummaryData'

class TransactionSummaryWorker extends WorkerBase {
  async run () {
    try {
      await TransactionSummaryData.execute()
      return { message: 'Transactions summary created successfully' }
    } catch (error) {
      Logger.error(`Transactions summary worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await TransactionSummaryWorker.run({ job })
  return result
}
