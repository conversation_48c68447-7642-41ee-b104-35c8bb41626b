import config from '../../../src/configs/app.config'
import { DEMO_USER_ID, ERROR_LOG_TYPE, SMARTICO_EVENT_TYPES } from '../../common/constants'
import smartiCoAxiosCall from '../../common/smartiCoAxiosCall'
import db from '../../db/models'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { SmartiCoEventsData } from '../../services/transactions/smartiCoEventsData'

class CustomError extends Error {
  constructor (message) {
    super(message)
    this.name = 'CustomError'
    // Optionally, prevent stack trace from being included
    this.stack = '' // Clears the stack trace
  }
}

class SmartiCoJobWorker extends WorkerBase {
  async run () {
    let transType
    try {
      const transactionType = this.args.job.data.transactionType
      transType = transactionType

      if (transType === 'smarticoEventsDataCron') {
        await SmartiCoEventsData.execute()
        return { message: 'Job successfully done' }
      } else if ([
        SMARTICO_EVENT_TYPES.UPDATE_USER,
        SMARTICO_EVENT_TYPES.WITHDRAWAL_REQUSTED,
        SMARTICO_EVENT_TYPES.WITHDRAWAL_APPROVED,
        SMARTICO_EVENT_TYPES.WITHDRAWAL_CANCELLED,
        SMARTICO_EVENT_TYPES.DEPOSIT_APPROVED,
        SMARTICO_EVENT_TYPES.CASINO_WIN,
        SMARTICO_EVENT_TYPES.LOGIN_STATS,
        SMARTICO_EVENT_TYPES.WALLET_UPDATE
      ].includes(transactionType)) {

        const ids = this.args.job.data.data.ids
        if(ids.includes(Number(DEMO_USER_ID)) ){
          return { message: 'Job successfully done' }
        }

        await smartiCoAxiosCall(this.args.job.data)
      }
      return { message: 'Job successfully done' }
    } catch (error) {
      const isProduction = config.get('env') === 'production'

      const tenantId = isProduction ? 86 : 54
      const tenantName = 'Marina888'
      const payload = JSON.stringify({
        jobData: {
          ...this.args.job.data,
          tenantName,
          tenantId
        }
      })
      const errorLogData = {
        errorMessage: `InvalidTransactionType :${transType} `,
        fileName: payload,
        stackTrace: error?.stack ? JSON.stringify(error?.stack) : null,
        exceptionCode: 400,
        tenantId: tenantId,
        type: ERROR_LOG_TYPE.QUEUE
      }
      const errorLog = await db.ErrorLog.create(errorLogData)
      throw new CustomError(`LOGID: ${errorLog.id}`)
    }
  }
}

export default async (job) => {
  const result = await SmartiCoJobWorker.run({ job })
  return result
}
