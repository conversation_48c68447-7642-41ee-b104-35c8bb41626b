import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { SmartiCoJob, SmartiCoQueue } from '../../queues/smartiCo.queue'

class SmartiCoEventsDataScheduleWorker extends WorkerBase {
  async run () {
    try {
      const queueName = 'SmartiCoJobQueue'
      const transactionType = 'smarticoEventsDataCron'
      const time = new Date().getTime()
      await Promise.all([
        SmartiCoQueue.add(SmartiCoJob,
          {
            time,
            transactionType: transactionType
          },
          {
            jobId: `${transactionType}_${time}_${queueName}`,
            removeOnComplete: true,
            delay: 2
          })
      ])
      return { message: 'SmartiCo EventsData Job created successfully' }
    } catch (error) {
      Logger.error(`SmartiCo EventsData worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await SmartiCoEventsDataScheduleWorker.run({ job })
  return result
}
