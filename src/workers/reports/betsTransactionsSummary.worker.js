import db from '../../db/models'
import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { BetTransactionsSummary } from '../../services/reports/betTransactionsSummary'
import { CRON_LOG_STATUS } from '../../common/constants'

class BetsTransactionSummaryWorker extends WorkerBase {
  async run () {
    const cronLog = {}
    cronLog.startTime = new Date()
    try {
      await BetTransactionsSummary.execute(cronLog)
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      return { message: 'Bets transactions summary created successfully' }
    } catch (error) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message || null
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      Logger.error(`Bets transactions summary worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await BetsTransactionSummaryWorker.run({ job })
  return result
}
