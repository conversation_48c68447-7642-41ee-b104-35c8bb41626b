import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'

class OfferWinnerAnnounceScheduleWorker extends WorkerBase {
  async run () {
    try {
      const queueName = CommonQueue
      const transactionType = 'offerWinnerAnnounceCron'
      const time = new Date().getTime()
      await Promise.all([
        CommonQueue.add(CommonJob,
          {
            time,
            transactionType: transactionType
          },
          {
            jobId: `${transactionType}_${time}_${queueName.name}`,
            removeOnComplete: true,
            delay: 2
          })
      ])

      return { message: 'Offer Winner Announce Job created successfully' }
    } catch (error) {
      Logger.error(`Offer winner announce worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await OfferWinnerAnnounceScheduleWorker.run({ job })
  return result
}
