import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { BulkDepositWithdrawAndAuditLogData } from '../../services/transactions/bulkDepositWithdrawAndAuditLogData'

class BulkDepositWithdrawAndAuditLogScheduleWorker extends WorkerBase {
  async run () {
    try {
      await BulkDepositWithdrawAndAuditLogData.execute()
      return { message: 'Bulk deposit withdraw and audit log Job created successfully' }
    } catch (error) {
      Logger.error(`Bulk deposit withdraw and audit log worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await BulkDepositWithdrawAndAuditLogScheduleWorker.run({ job })
  return result
}
