import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { TransactionJob, TransactionQueue } from '../../queues/transaction.queue'

class CasinoTransactionScheduleWorker extends WorkerBase {
  async run () {
    try {
      const queueName = 'TransactionQueue'
      const transactionType = 'casinoTransactionData'
      const time = new Date().getTime()
      await Promise.all([
        TransactionQueue.add(TransactionJob,
        {
          time,
          transactionType: transactionType
        },
        {
          jobId: `${transactionType}_${time}_${queueName}`,
          removeOnComplete: true,
          delay: 2
        })
      ])
      return { message: 'Casino Transaction Job created successfully' }
    } catch (error) {
      Logger.error(`Casino Transactions summary worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await CasinoTransactionScheduleWorker.run({ job })
  return result
}
