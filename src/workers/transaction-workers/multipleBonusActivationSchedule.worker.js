import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { BonusEngine, BonusEngineQueue } from '../../queues/bonusEngine.queue'

class MultipleBonusActivationQueueScheduleWorker extends WorkerBase {
  async run () {
    try {

      const queueName = 'BonusEngineQueue'
      const transactionType = 'multipleBonusActivationCron'
      const time = new Date().getTime()
      await Promise.all([
        BonusEngineQueue.add(BonusEngine,
          {
            time,
            transactionType: transactionType
          },
          {
            jobId: `${transactionType}_${time}_${queueName.name}`,
            removeOnComplete: true,
            delay: 2
          })
      ])

      return { message: 'multi Bonus Job created successfully ' }
    } catch (error) {
      Logger.error(`multi Bonus Job worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await MultipleBonusActivationQueueScheduleWorker.run({ job })
  return result
}
