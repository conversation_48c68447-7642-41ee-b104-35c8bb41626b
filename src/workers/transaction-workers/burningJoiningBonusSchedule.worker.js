import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { BurningJoiningBonus } from '../../services/transactions/burningJoiningBonus'
import db from '../../db/models'
import { CRON_LOG_STATUS } from '../../common/constants'

class BurningJoiningBonusScheduleWorker extends WorkerBase {
  async run () {
    const cronLog = {}
    cronLog.startTime = new Date()
    try {
      await BurningJoiningBonus.execute(cronLog)
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      return { message: 'Burning joining bonus Job created successfully' }
    } catch (error) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message || null
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      Logger.error(`Burning joining bonus worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await BurningJoiningBonusScheduleWorker.run({ job })
  return result
}
