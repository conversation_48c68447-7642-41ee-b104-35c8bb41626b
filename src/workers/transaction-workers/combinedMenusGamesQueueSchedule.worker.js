import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'

class CombinedMenuGamesScheduleWorker extends WorkerBase {
  async run () {
    try {
      const queueName = CommonQueue
      const transactionType = 'combinedMenusDataCron'
      const time = new Date().getTime()
      await Promise.all([
        CommonQueue.add(CommonJob,
          {
            time,
            transactionType: transactionType
          },
          {
            jobId: `${transactionType}_${time}_${queueName}`,
            removeOnComplete: true,
            delay: 2
          })
      ])
      return { message: 'Combined menu games bonus Job created successfully' }
    } catch (error) {
      Logger.error(`Combined menu games bonus worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await CombinedMenuGamesScheduleWorker.run({ job })
  return result
}
