import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { TransactionJob, TransactionQueue } from '../../queues/transaction.queue'

class RollbackTransactionScheduleWorker extends WorkerBase {
  async run () {
    try {
      const queueName = 'TransactionQueue'
      const transactionType = 'rollbackTransactionData'
      const time = new Date().getTime()
      await Promise.all([
        TransactionQueue.add(TransactionJob,
        {
          time,
          transactionType: transactionType
        },
        {
          jobId: `${transactionType}_${time}_${queueName}`,
          removeOnComplete: true,
          delay: 2
        })
      ])
      return { message: 'Rollback Transaction Job created successfully' }
    } catch (error) {
      Logger.error(`Rollback Transactions worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await RollbackTransactionScheduleWorker.run({ job })
  return result
}
