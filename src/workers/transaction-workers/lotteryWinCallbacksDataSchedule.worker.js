import { Op, QueryTypes } from 'sequelize'
import { LOTTERY_GAMES_CREDENTIALS, PROD_LOTTERY777_GAMES_PROVIDER, STAGE_LOTTERY777_GAMES_PROVIDER, TRANSACTION_TYPES } from '../../common/constants'
import config from '../../configs/app.config'
import db, { sequelize } from '../../db/models'
import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'

class lotteryWinCallbacksDataScheduleWorker extends WorkerBase {
  async run () {
    try {
      const queueName = 'CommonQueue'
      const transactionType = 'lottery_win_callback'
      const jobTime = new Date().getTime()

      const beforeDate = new Date()

      beforeDate.setMinutes(beforeDate.getMinutes() - 30)

      const afterDate = new Date()

      afterDate.setHours(afterDate.getHours() - 36)

      const lotteryProviderId = config.get('env') === 'production' ? PROD_LOTTERY777_GAMES_PROVIDER : STAGE_LOTTERY777_GAMES_PROVIDER

      const keys = [
        LOTTERY_GAMES_CREDENTIALS.LOTTERY_GAMES_AGENT_ID_INR,
        LOTTERY_GAMES_CREDENTIALS.LOTTERY_GAMES_AGENT_ID_EUR,
        LOTTERY_GAMES_CREDENTIALS.LOTTERY_GAMES_AGENT_ID_USD,
        LOTTERY_GAMES_CREDENTIALS.LOTTERY_GAMES_AGENT_ID_LKR
      ]

      const tenants = await sequelize.query(`
        SELECT "tenant_id" AS "tenantId", ( SELECT "id" FROM "menu_tenant_setting" WHERE "menu_id" = (SELECT "id" FROM "menu_master" WHERE "name" = 'Lottery')
        AND "tenant_id" = "TenantThemeSetting"."tenant_id" ) AS "casinoId"
        FROM "public"."tenant_theme_settings" AS "TenantThemeSetting" WHERE '${lotteryProviderId}' = ANY (string_to_array(assigned_providers, ','))`,
        { type: QueryTypes.SELECT, useMaster: false })

      for (const tenant of tenants) {
        const tenantId = tenant.tenantId
        const credentials = await db.TenantCredential.findAll({
          attributes: ['key', 'value'],
          where: {
            tenantId: tenantId,
            key: { [Op.in]: keys }
          },
          raw: true
        })

        const clientCredentials = credentials.reduce((acc, config) => {
          acc.push(config.value)
          return acc
        }, [])

        if (!clientCredentials?.length) {
          continue
        }

        const anyAgentId = clientCredentials[0]

        const query = `
            SELECT id, actionee_id, transaction_id, seat_id
            FROM "transactions"
            WHERE
                "transaction_type" IN (:transactionTypes)
                AND "created_at" BETWEEN :afterDate AND :beforeDate
                AND "transaction_id" NOT IN (
                    SELECT "debit_transaction_id" FROM "transactions"
                    WHERE "debit_transaction_id" IS NOT NULL AND "created_at" >= :afterDate
                )
                AND "transaction_id" NOT IN (
                    SELECT "cancel_transaction_id" FROM "transactions"
                    WHERE "cancel_transaction_id" IS NOT NULL AND "created_at" >= :afterDate
                )
                AND "tenant_id" = :tenantId
                AND "provider_id" = :lotteryProviderId;
        `;

        const allTransactions = await sequelize.query(query, {
          replacements: {
            transactionTypes:[TRANSACTION_TYPES.DEBIT, TRANSACTION_TYPES.DEBIT_NO_CASH, TRANSACTION_TYPES.DEBIT_OTB_CASH],
            afterDate: afterDate,
            beforeDate: beforeDate,
            tenantId,
            lotteryProviderId
          },
          type: QueryTypes.SELECT
        });

        const uniqueTransactionIds = []
        for (const transaction of allTransactions) {
          await sleep(5000)
          if (uniqueTransactionIds.includes(transaction.transaction_id)) {
            continue;
          }

          CommonQueue.add(CommonJob,
            {
              time: jobTime,
              transactionType: transactionType,
              data: {
                ids: transaction.id,
                transactionId: transaction.transaction_id,
                userId: `${anyAgentId}_${transaction.actionee_id}`,
                tenantId: tenantId,
                gameId: transaction.seat_id,
                betOrderId: `${anyAgentId}_${transaction.transaction_id}`
              }
            },
            {
              jobId: `${transactionType}_${transaction.transaction_id}_${queueName}`,
              removeOnComplete: true,
              delay: 5
            })

          uniqueTransactionIds.push(transaction.transaction_id)
        }
      }

      return { message: 'Lottery 0 win callbacks data generated.' }
    } catch (error) {
      Logger.error(`Lottery 0 win callbacks data worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await lotteryWinCallbacksDataScheduleWorker.run({ job })
  return result
}
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))
