import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'

class UpdateRiskAndFraudWorker extends WorkerBase {
  async run () {
    try {
      const queueName = 'CommonQueue'
      const transactionType = 'updateRiskAndFraudData'
      const time = new Date().getTime()
      await Promise.all([
        CommonQueue.add(CommonJob,
        {
          time,
          transactionType: transactionType
        },
        {
          jobId: `${transactionType}_${time}_${queueName}`,
          removeOnComplete: true,
          delay: 2
        })
      ])
      return { message: 'Update Risk and Fruad Data job created successfully' }
    } catch (error) {
      Logger.error(`risk_and_fraud_data_update_worker_error-${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await UpdateRiskAndFraudWorker.run({ job })
  return result
}
