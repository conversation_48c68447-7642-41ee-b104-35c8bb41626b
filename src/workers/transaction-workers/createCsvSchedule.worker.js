import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { CreateCsv, CreateCsvQueue } from '../../queues/createCsv.queue'

class CreateCsvScheduleWorker extends WorkerBase {
  async run () {
    try {
      const queueName = 'CreateCsvQueue'
      const transactionType = 'createCsvData'
      const time = new Date().getTime()
      await Promise.all([
        CreateCsvQueue.add(CreateCsv,
        {
          time,
          transactionType: transactionType
        },
        {
          jobId: `${transactionType}_${time}_${queueName}`,
          removeOnComplete: true,
          delay: 2
        })
      ])
      return { message: 'Create csv Job created successfully' }
    } catch (error) {
      Logger.error(`Casino Transactions summary worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await CreateCsvScheduleWorker.run({ job })
  return result
}
