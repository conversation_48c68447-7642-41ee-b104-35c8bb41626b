import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { TransactionJob, TransactionQueue } from '../../queues/transaction.queue'

class SportCasinoTransactionScheduleWorker extends WorkerBase {
  async run () {
    try {
      const queueName = 'TransactionQueue'
      const transactionType = 'sportToCasinoTransactionData'
      const time = new Date().getTime()
      await Promise.all([
        TransactionQueue.add(TransactionJob,
        {
          time,
          transactionType: transactionType
        },
        {
          jobId: `${transactionType}_${time}_${queueName}`,
          removeOnComplete: true,
          delay: 2
        })
      ])
      return { message: 'Bet Transaction Job created successfully' }
    } catch (error) {
      Logger.error(`Bet Transactions summary worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await SportCasinoTransactionScheduleWorker.run({ job })
  return result
}
