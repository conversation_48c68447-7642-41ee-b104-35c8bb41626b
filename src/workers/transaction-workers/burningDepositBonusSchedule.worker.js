import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { BurningDepositBonus } from '../../services/transactions/burningDepositBonus'
import db from '../../db/models'
import { CRON_LOG_STATUS } from '../../common/constants'

class BurningDepositBonusScheduleWorker extends WorkerBase {
  async run () {
    const cronLog = {}
    cronLog.startTime = new Date()
    try {
      await BurningDepositBonus.execute(cronLog)
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      return { message: 'Burning deposit bonus Job created successfully' }
    } catch (error) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message || null
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      Logger.error(`Burning deposit bonus worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await BurningDepositBonusScheduleWorker.run({ job })
  return result
}
