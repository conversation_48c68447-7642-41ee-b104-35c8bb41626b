import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'

class UpsertMostPlayedGamesWorker extends WorkerBase {
  async run () {
    try {
      const queueName = 'CommonQueue'
      const transactionType = 'upsertMostPlayedGamesData'
      const time = new Date().getTime()
      await Promise.all([
        CommonQueue.add(CommonJob,
        {
          time,
          transactionType: transactionType
        },
        {
          jobId: `${transactionType}_${time}_${queueName}`,
          removeOnComplete: true,
          delay: 2
        })
      ])
      return { message: 'Upsert Most Played Games Data job created successfully' }
    } catch (error) {
      Logger.error(`Upsert Most Played Games worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await UpsertMostPlayedGamesWorker.run({ job })
  return result
}
