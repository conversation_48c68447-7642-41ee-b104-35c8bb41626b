import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'

class BurningManualLosingBonusScheduleWorker extends WorkerBase {
  async run () {
    try {
      const queueName = CommonQueue
      const transactionType = 'burningManualLosingBonusCron'
      const time = new Date().getTime()
      await Promise.all([
        CommonQueue.add(CommonJob,
          {
            time,
            transactionType: transactionType
          },
          {
            jobId: `${transactionType}_${time}_${queueName}`,
            removeOnComplete: true,
            delay: 2
          })
      ])
      return { message: 'Burning manual losing bonus Job created successfully' }
    } catch (error) {
      Logger.error(`Burning manual losing bonus worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await BurningManualLosingBonusScheduleWorker.run({ job })
  return result
}
