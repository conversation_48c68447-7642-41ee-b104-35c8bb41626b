import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { AutomaticLosingBonus } from '../../services/transactions/automaticLosingBonus'
import db from '../../db/models'
import { CRON_LOG_STATUS } from '../../common/constants'

class AutomaticLosingBonusScheduleWorker extends WorkerBase {
  async run () {
    const cronLog = {}
    cronLog.startTime = new Date()
    try {
      await AutomaticLosingBonus.execute(cronLog)
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      return { message: 'Burning losing bonus Job created successfully' }
    } catch (error) {
      cronLog.status = CRON_LOG_STATUS.FAILED
      cronLog.errorMsg = error.message || null
      cronLog.endTime = new Date()
      await db.CronLog.create(cronLog)
      Logger.error(`Burning losing bonus worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await AutomaticLosingBonusScheduleWorker.run({ job })
  return result
}
