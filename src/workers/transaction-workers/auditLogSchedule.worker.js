import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { AuditLogData } from '../../services/transactions/auditLogData'

class AuditLogScheduleWorker extends WorkerBase {
  async run () {
    try {
      await AuditLogData.execute()
      return { message: 'Bet Transaction Job created successfully' }
    } catch (error) {
      Logger.error(`Bet Transactions summary worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await AuditLogScheduleWorker.run({ job })
  return result
}
