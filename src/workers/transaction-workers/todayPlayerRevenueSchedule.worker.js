import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'

class TodayPlayerRevenueWorker extends WorkerBase {
  async run () {
    try {
      const queueName = 'CommonQueue'
      const transactionType = 'updateTodayPlayerRevenueData'
      const time = new Date().getTime()
      await Promise.all([
        CommonQueue.add(CommonJob,
        {
          time,
          transactionType: transactionType
        },
        {
          jobId: `${transactionType}_${time}_${queueName}`,
          removeOnComplete: true,
          delay: 2
        })
      ])
      return { message: 'Update Today Player Revenue Data job created successfully' }
    } catch (error) {
      Logger.error(`Today Player Revenue worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await TodayPlayerRevenueWorker.run({ job })
  return result
}
