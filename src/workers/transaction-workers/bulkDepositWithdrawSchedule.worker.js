import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { BulkDepositWithdrawData } from '../../services/transactions/bulkDepositWithdrawData'

class BulkDepositWithdrawScheduleWorker extends WorkerBase {
  async run () {
    try {
      await BulkDepositWithdrawData.execute()
      return { message: 'Bet Transaction Job created successfully' }
    } catch (error) {
      Logger.error(`Bet Transactions summary worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await BulkDepositWithdrawScheduleWorker.run({ job })
  return result
}
