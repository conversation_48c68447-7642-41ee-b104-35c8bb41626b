import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { BonusEngine, BonusEngineQueue } from '../../queues/bonusEngine.queue'

class BonusEngnineScheduleWorker extends WorkerBase {
  async run () {
    try {
      const queueName = 'BonusEngineQueue'
      const transactionType = 'bonusEngineData'
      const time = new Date().getTime()
      await Promise.all([
        BonusEngineQueue.add(BonusEngine,
        {
          time,
          transactionType: transactionType
        },
        {
          jobId: `${transactionType}_${time}_${queueName}`,
          removeOnComplete: true,
          delay: 2
        })
      ])
      return { message: 'Bonus Engine Job created successfully' }
    } catch (error) {
      Logger.error(`Casino Transactions summary worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await BonusEngnineScheduleWorker.run({ job })
  return result
}
