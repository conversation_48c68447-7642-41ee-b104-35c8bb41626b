import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { CommonJob, CommonQueue } from '../../queues/common.queue'

class ConfigurationBackupScheduleWorker extends WorkerBase {
  async run () {
    try {
      const queueName = CommonQueue
      const transactionType = 'configurationBackupCron'
      const time = new Date().getTime()
      await Promise.all([
        CommonQueue.add(CommonJob,
          {
            time,
            transactionType: transactionType
          },
          {
            jobId: `${transactionType}_${time}_${queueName.name}`,
            removeOnComplete: true,
            delay: 2
          })
      ])

      return { message: 'Configuration Backup Job created successfully' }
    } catch (error) {
      Logger.error(`Configuration backup worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await ConfigurationBackupScheduleWorker.run({ job })
  return result
}
