import Logger from '../../libs/logger'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { BonusEngine, BonusEngineQueue } from '../../queues/bonusEngine.queue'

class CashbackBonusRecurringActivationQueueScheduleWorker extends WorkerBase {
  async run () {
    try {

      const queueName = BonusEngineQueue
      const transactionType = 'cashbackBonusRecurringActivationCron'
      const time = new Date().getTime()
      await Promise.all([
        BonusEngineQueue.add(BonusEngine,
          {
            time,
            transactionType: transactionType
          },
          {
            jobId: `${transactionType}_${time}_${queueName.name}`,
            removeOnComplete: true,
            delay: 2
          })
      ])

      return { message: 'Cashback Bonus Recurring Activation Job created successfully ' }
    } catch (error) {
      Logger.error(`Cashback Bonus Recurring Activation Job worker error - ${JSON.stringify(error)}`)
      throw error
    }
  }
}

export default async (job) => {
  const result = await CashbackBonusRecurringActivationQueueScheduleWorker.run({ job })
  return result
}
