import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { signupUserIndex, updateTransactionIndex } from '../../elastic-search'
import { sequelize } from '../../db/models'
import { TRANSACTION_TYPES } from '../../common/constants'

class UserTransactionWorker extends WorkerBase {
  async run () {
    try{
      const userId = this.args.job.data.transactionId
      await signupUserIndex(userId, sequelize)
      return { message: 'User created successfully' }
    }catch(error){
      console.log("======User transaction worker error", error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await UserTransactionWorker.run({ job })
  return result
}
