import autoActivateBonus from '../../common/bonusEngine/autoActivateBonus'
import depositBonusRolloverCheck from '../../common/bonusEngine/depositBonusRolloverAmountCheck'
import depositV2Bonus from '../../common/bonusEngine/depositV2Bonus'
import joiningBonus from '../../common/bonusEngine/joiningBonus'
import promoCodeBonus from '../../common/bonusEngine/promoCodeBonus'
import referralCodeBonus from '../../common/bonusEngine/referralCodeBonus'
import singleTimeUseBonus from '../../common/bonusEngine/singleTimeUseBonus'
import { bonusQueueRollover } from '../../common/bonusQueueRollover'
import { BONUS_ENGINE_BONUS_TYPE, BONUS_TYPES, CRON_TYPE } from '../../common/constants'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { BonusEngineData } from '../../services/transactions/bonusEngineData'
import { CashbackBonusRecurringActivation } from '../../services/transactions/cashbackBonusRecurringActivation'
import { MultipleBonusActivation } from '../../services/transactions/multipleBonusActivation'

class BonusEngineWorker extends WorkerBase {
  async run () {
    try{
      const bonusData = this.args.job.data.bonusData
      const transactionType = this.args.job.data.transactionType
      if (transactionType === CRON_TYPE.BONUS_ENGINE_DATA) {
        await BonusEngineData.execute()
      } else if (transactionType === CRON_TYPE.MULTIPLE_BONUS_ACTIVATION_CRON){
        await MultipleBonusActivation.execute();
      } else if (transactionType === CRON_TYPE.CASHBACK_BONUS_RECURRING_ACTIVATION) {
        await CashbackBonusRecurringActivation.execute();
      } else if (bonusData[0]?.bonusType === BONUS_TYPES.PROMO_CODE) {
        await promoCodeBonus(bonusData)
      } else if(bonusData[0]?.bonusType === BONUS_TYPES.JOINING) {
        await joiningBonus(bonusData)
      } else if(bonusData[0]?.bonusType === BONUS_TYPES.DEPOSIT) {
        const depositBonusID = await depositV2Bonus(bonusData)
        await bonusQueueRollover(bonusData[0]?.amount, bonusData[0]?.depositTransactionId, bonusData[0]?.user.id, bonusData[0]?.paymentProviders, depositBonusID, null)
      } else if (bonusData[0]?.bonusType === BONUS_ENGINE_BONUS_TYPE.DEPOSIT_ROLLOVER) {
        await depositBonusRolloverCheck(bonusData)
      } else if (bonusData[0]?.bonusType === BONUS_ENGINE_BONUS_TYPE.SINGLE_TIME_USE_BONUS) {
        await singleTimeUseBonus(bonusData)
      } else if (bonusData[0]?.bonusType === BONUS_TYPES.REFERRAL_CODE) {
        await referralCodeBonus(bonusData)
      } else if (bonusData[0]?.bonusType === BONUS_TYPES.AUTO_BONUS_ACTIVATE) {
        await autoActivateBonus(bonusData)
      }
      return { message: 'Bonus Engine run successfully' }
    }catch(error){
      console.log("======Bonus Engine worker error", error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await BonusEngineWorker.run({ job })
  return result
}
