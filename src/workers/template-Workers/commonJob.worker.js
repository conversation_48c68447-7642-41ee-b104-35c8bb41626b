
import addInPlayerRevenueReportForToday from '../../common/addInPlayerRevenueReportForToday'
import alanbaseAxiosCall from '../../common/alanbaseAxiosCall'
import axiosCall from '../../common/axiosCall'
import processBulkDepositStatus from '../../common/bulkProcessing/processBulkDepositStatus'
import processBulkWithdrawalStatus from '../../common/bulkProcessing/processBulkWithdrawalStatus'
import { ALANBASE_EVENT_TYPES, EXPORT_CSV_TYPE, MARINA888_TYPES } from '../../common/constants'
import createThumbnail from '../../common/createThumbnail'
import processDepositWagerTracking from '../../common/depositRequest/processDepositWagerTracking'
import gameSeedManual from '../../common/gameSeedManual'
import lotteryExecuteWinCallbacks from '../../common/lotteryExecuteWinCallbacks'
import marina888R<PERSON>ord<PERSON>and<PERSON> from '../../common/marina888/marina888RecordHandler'
import marina888AxiosCall from '../../common/marina888AxiosCall'
import reportMailSender from '../../common/reportMailSender'
import syncCategory from '../../common/syncCategory'
import updateRiskAndFraudIndicatorsData from '../../common/updateRiskAndFraudIndicatorsData'
import upsertMostPlayedGamesData from '../../common/upsertMostPlayedGamesData'
import wyntaNewUserResync from '../../common/wyntaNewUserResync'
import db, { sequelize } from '../../db/models'
import { deleteDepositBonusTranx } from '../../elastic-search'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import sendSmsAlerts from '../../services/sendSmsAlerts'
import automaticLosingBonusTransaction from '../../services/transactions/automaticLosingBonusTransaction'
import burningDepositBonusTransaction from '../../services/transactions/burningDepositBonusTransaction'
import burningJoiningBonusTransactions from '../../services/transactions/burningJoiningBonusTransactions'
import burningLosingBonusTransaction from '../../services/transactions/burningLosingBonusTransaction'
import { BurningManualLosingBonus } from '../../services/transactions/burningManualLosingBonus'
import burningManualLosingBonusTransaction from '../../services/transactions/burningManualLosingBonusTransaction'
import burningPromocodeBonusTransaction from '../../services/transactions/burningPromocodeBonusTransaction'
import { CombinedMenuGames } from '../../services/transactions/combinedMenuGames'
import combinedMenuGamesTransaction from '../../services/transactions/combinedMenuGamesTransaction'
import { ConfigurationBackup } from '../../services/transactions/configurationBackup'
import ocrTransaction from '../../services/transactions/ocrTransaction'
import { OfferWinnerAnnounce } from '../../services/transactions/offerWinnerAnnounce'
import playerBulkCategorizationBonusTransaction from '../../services/transactions/playerBulkCategorizationBonusTransaction'

class CommonJobWorker extends WorkerBase {
  async run () {
    try {
      const transactionType = this.args.job.data.transactionType

      if (transactionType === 'audit_log') {
        // const auditLogId = this.args.job.data.transactionId
        // await updateAuditLog(auditLogId, sequelize)
      return { message: 'Job successfully done' }
      }
       else if (transactionType === 'burning_losing_bonus') {
        await burningLosingBonusTransaction(this.args.job.data.transactionId)
      } else if (transactionType === 'active_losing_bonus' || transactionType === 'automatic_active_losing_bonus' || transactionType === 'ist_automatic_losing_bonus') {
        await automaticLosingBonusTransaction(this.args.job.data)
      } else if (transactionType === 'burning_joining_bonus') {
        await burningJoiningBonusTransactions(this.args.job.data)
      } else if (transactionType === 'player_bulk_categorization_bonus') {
        await playerBulkCategorizationBonusTransaction(this.args.job.data)
      } else if (transactionType === MARINA888_TYPES.MARINA_888_AGENT ||
        transactionType === MARINA888_TYPES.MARINA_888_USER ||
        transactionType === MARINA888_TYPES.MARINA_888_USER_TRANSACTIONS ||
        transactionType === MARINA888_TYPES.MARINA_888_AGENT_TRANSACTIONS ||
        transactionType === MARINA888_TYPES.MARINA_888_BONUS ||
        transactionType === MARINA888_TYPES.MARINA_888_USER_BONUS ||
        transactionType === MARINA888_TYPES.MARINA_888_USER_BETS) {
        await marina888AxiosCall(this.args.job.data)
      } else if (transactionType === MARINA888_TYPES.MARINA_888_MIGRATION_CRON) {
        await marina888RecordHandler(this.args.job.data)
      } else if (transactionType === 'burning_deposit_bonus') {
        await burningDepositBonusTransaction(this.args.job.data)
      } else if (transactionType === MARINA888_TYPES.MARINA_888_USER_PASSWORD_CHANGE) {
        await marina888AxiosCall(this.args.job.data)
      } else if (transactionType === 'bulk_request_deposit_withdraw') {
        await processBulkWithdrawalStatus(this.args.job.data)
      } else if (transactionType === 'bulk_deposit_request') {
        await processBulkDepositStatus(this.args.job.data)
      } else if (transactionType === EXPORT_CSV_TYPE.MAIL_REPORT_CSV) {
        await reportMailSender(this.args.job.data)
      } else if (transactionType === 'burning_promo_code_bonus') {
        await burningPromocodeBonusTransaction(this.args.job.data)
      } else if (transactionType === 'deposit_wager_tracking') {
        await processDepositWagerTracking(this.args.job.data)
      } else if (transactionType === 'burning_manual_losing_bonus') {
        await burningManualLosingBonusTransaction(this.args.job.data)
      } else if (transactionType === 'dep_withdraw_sms_otp') {
        await sendSmsAlerts(this.args.job.data)
      } else if (transactionType === 'combined_menu_games') {
        await combinedMenuGamesTransaction(this.args.job.data)
      } else if (transactionType === 'combinedMenusDataCron') {
        await CombinedMenuGames.execute()
      } else if (transactionType === 'burningManualLosingBonusCron') {
        await BurningManualLosingBonus.execute()
      } else if (transactionType === 'configurationBackupCron') {
        await ConfigurationBackup.execute();
      } else if (transactionType === 'offerWinnerAnnounceCron') {
        await OfferWinnerAnnounce.execute();
      } else if (transactionType == 'ocr_transaction') {
        ocrTransaction(this.args.job.data)
      } else if ([
        ALANBASE_EVENT_TYPES.REGISTRATION,
        ALANBASE_EVENT_TYPES.DEPOSIT,
        ALANBASE_EVENT_TYPES.WITHDRAWAL_COMPLETED,
        ALANBASE_EVENT_TYPES.ALANBASE_WIN,
        ALANBASE_EVENT_TYPES.ALANBASE_SB_PLACE_BET,
        ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_PLACE_BET,
        ALANBASE_EVENT_TYPES.ALANBASE_SB_MARKET_CANCEL,
        ALANBASE_EVENT_TYPES.ALANBASE_SB_SETTLE_MARKET,
        ALANBASE_EVENT_TYPES.ALANBASE_SB_RESETTLE_MARKET,
        ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_SETTLE_MARKET,
        ALANBASE_EVENT_TYPES.ALANBASE_SB_CASHOUT,
        ALANBASE_EVENT_TYPES.ALANBASE_SB_CANCEL_CASHOUT
      ].includes(transactionType)) {
        await alanbaseAxiosCall(this.args.job.data)
      } else if (transactionType === 'elastic_record_deletion') {
        await deleteDepositBonusTranx(this.args.job.data, sequelize)
      } else if (transactionType === 'updateTodayPlayerRevenueData') {
        await addInPlayerRevenueReportForToday()
      } else if (transactionType === 'updateRiskAndFraudData') {
        await updateRiskAndFraudIndicatorsData()
      } else if (transactionType === 'upsertMostPlayedGamesData') {
        await upsertMostPlayedGamesData()
      } else if (transactionType === 'sync_category') {
        await syncCategory(this.args.job.data)
      } else if (transactionType === 'game_seed_manual') {
        await gameSeedManual(this.args.job.data)
      }
      else if (transactionType === 'lottery_win_callback') {
        await lotteryExecuteWinCallbacks(this.args.job.data)
      }
      else if (transactionType === 'wynta_new_user_resync') {
        await wyntaNewUserResync(this.args.job.data)
      }
      else if (transactionType === 'create_thumbnail') {
        await createThumbnail(this.args.job.data)
      }
      else {
        const dipositWithdrawUserId = this.args.job.data.transactionId
        const dipositWithdrawUser = await db.DepositWithdrawUser.findOne({
          where: {
            id: dipositWithdrawUserId
          },
          useMaster: true
        })
        await axiosCall(dipositWithdrawUser.jobId, dipositWithdrawUser.userId)
      }
      return { message: 'Job successfully done' }
    } catch (error) {
      console.log('======Common job worker error', error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await CommonJobWorker.run({ job })
  return result
}
