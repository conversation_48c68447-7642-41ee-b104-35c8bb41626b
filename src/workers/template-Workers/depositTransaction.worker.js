import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'

class DepositTransactionWorker extends WorkerBase {
  async run () {
    try {
      return { message: 'Success' }
    } catch (error) {
      console.log("===========error", error)
      throw error
    }

  }
}

export default async (job) => {
  const result = await DepositTransactionWorker.run({ job })
  return result
}
