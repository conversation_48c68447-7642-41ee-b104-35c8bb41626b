import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { updateSportsBetTransactionIndex, updateSportTransactionStatus } from '../../elastic-search'
import db, { sequelize } from '../../db/models'


class BetTransactionWorker extends WorkerBase {
  async run () {
    try {
      const transactionId = this.args.job.data.transactionId
      await updateSportsBetTransactionIndex(transactionId, sequelize)
      return { message: 'Bet transaction updated successfully' }
    } catch (error) {
      console.log("======Bet transaction worker error", error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await BetTransactionWorker.run({ job })
  return result
}
