import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import db, { sequelize } from '../../db/models'
import { updateAuditLog } from '../../elastic-search'

class AuditLogWorker extends WorkerBase {
  async run () {
    const auditLogId = this.args.job.data.transactionId
    try{
      await updateAuditLog(auditLogId, sequelize)
      return { message: 'Audit Log successfully' }

    }catch(error){
      console.log("======Audit log worker error", error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await AuditLogWorker.run({ job })
  return result
}
