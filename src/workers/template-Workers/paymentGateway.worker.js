import pendingAxiosCall from '../../common/pendingPayout/pendingAxiosCall';
import db, { sequelize } from '../../db/models'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { INDIGRIT_WITHDRAW, SKY_WITHDRAW, SUBSCRIPTION_CHANNEL, PAYMENT_GATEWAY_QUEUE_TYPE, PAYMENT_GATEWAY_NAMES } from '../../common/constants'
import { walletLocking } from '../../common/walletLocking'
import cancelTransactionLedger from '../../common/pendingPayout/cancelTransactionLedger';
import statusResolver from '../../services/indigrit-PG/statusResolver'
import statusUpdater from '../../services/pending-withdraw-services/statusUpdater';
import updatePayInStatus from '../../services/payIn-status/updatePayInStatus'
import saspayDepositStatusResolver from '../../services/saspay-PG/saspayDepositStatusResolver'
import segregateDepositStatusApi from '../../services/pending-deposit-request/segregateDepositStatusApi'
import createWithdrawRequest from '../../services/create-withdraw-request/createWithdrawRequest'
import { publishToRedis } from '../../common/queueService/publishToRedis';

class PaymentGatewayWorker extends WorkerBase {
  async run () {
    const sequelizeTransaction = await sequelize.transaction()
    try {
      const pubsubObj = {};
      let response = {}
      if (this.args.job.data.transactionType === PAYMENT_GATEWAY_QUEUE_TYPE.INDIGRIT_DEPOSIT_UPDATE_STATUS) {
        const { transactionId, tenantId } = this.args.job.data
        response = {...await statusResolver(sequelizeTransaction, transactionId, tenantId), ...response}
      }

      else if (this.args.job.data.transactionType === PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_WITHDRAWAL_TRANSACTION || this.args.job.data.transactionType === PAYMENT_GATEWAY_QUEUE_TYPE.PAYOUT_WITHDRAW_UPDATE_STATUS) {
        const id = this.args.job.data.transactionId
        await statusUpdater(sequelizeTransaction,id)
      }
      else if (this.args.job.data.transactionType === PAYMENT_GATEWAY_QUEUE_TYPE.PAYIN_DEPOSIT_UPDATE_STATUS) {
        const id = this.args.job.data.transactionId
        await updatePayInStatus(sequelizeTransaction,id)
      }
      else if (this.args.job.data.transactionType === PAYMENT_GATEWAY_QUEUE_TYPE.SASPAY_DEPOSIT_UPDATE_STATUS) {
        const { transactionId } = this.args.job.data
        response = {...await saspayDepositStatusResolver(sequelizeTransaction, transactionId), ...response}
      }
      else if (this.args.job.data.transactionType === PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_DEPOSIT_REQUEST) {
        const { transactionId } = this.args.job.data
        response = { ...await segregateDepositStatusApi(sequelizeTransaction, transactionId), ...response }
      }
      else if (this.args.job.data.transactionType === PAYMENT_GATEWAY_QUEUE_TYPE.CREATE_WITHDRAW_REQUEST) {
        await createWithdrawRequest(sequelizeTransaction, this.args.job.data, pubsubObj);
      }

      await sequelizeTransaction.commit()

      // Publish to redis
      try {
        const pubsubMethodNames = Object.keys(pubsubObj);
        if (pubsubMethodNames.length) {
          for (const methodName of pubsubMethodNames) {
            for (const objectToPublish of pubsubObj[methodName]) {
              await publishToRedis[methodName](objectToPublish);
            }
          }
        }
      } catch (error) {}

      if ([PAYMENT_GATEWAY_QUEUE_TYPE.INDIGRIT_DEPOSIT_UPDATE_STATUS, PAYMENT_GATEWAY_QUEUE_TYPE.SASPAY_DEPOSIT_UPDATE_STATUS, PAYMENT_GATEWAY_QUEUE_TYPE.PENDING_DEPOSIT_REQUEST].includes(this.args.job.data.transactionType) )
        return response

      return { message: 'success' }
    }
    catch (error) {
      await sequelizeTransaction.rollback()
      console.log("======Pending Payment Gateway transaction worker error", error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await PaymentGatewayWorker.run({ job })
  return result
}
