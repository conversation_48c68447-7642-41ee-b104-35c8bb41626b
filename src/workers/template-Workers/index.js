import path from 'path'
// import { DepositTransactionQueue, DepositTransaction } from '../../queues/depositTransaction.queue'
// import { WithdrawTransactionQueue, WithdrawTransaction } from '../../queues/withdrawlTransaction.queue'
import { ActiveLosingBonusJobs, ActiveLosingBonusJobsQueue } from '../../queues/activeLosingBonusJob.queue'
import { AuditLog, AuditLogQueue } from '../../queues/auditLog.queue'
import { BonusEngine, BonusEngineQueue } from '../../queues/bonusEngine.queue'
import { BulkDepositWithdrawTransaction, BulkDepositWithdrawTransactionQueue } from '../../queues/bulkDepositWithdrawlTransaction.queue'
import { CommonJob, CommonQueue } from '../../queues/common.queue'
import { CreateCsv, CreateCsvQueue } from '../../queues/createCsv.queue'
import { PaymentGateway, PaymentGatewayQueue } from '../../queues/paymentGateway.queue'
import { TransactionJob, TransactionQueue } from '../../queues/transaction.queue'
import { ExportCsv, ExportCsvQueue } from '../../queues/exportCsv.queue'
// BetTransactionQueue.process(BetTransaction, 2, path.join(__dirname, './betTransaction.worker.js'))
// CasinoTransactionQueue.process(CasinoTransaction, 2, path.join(__dirname, './casinoTransaction.worker.js'))
// DepositTransactionQueue.process(DepositTransaction, 1, path.join(__dirname, './depositTransaction.worker.js'))
// UserTransactionQueue.process(UserTransaction, 5, path.join(__dirname, './userTransaction.worker.js'))
// WithdrawTransactionQueue.process(WithdrawTransaction, 1, path.join(__dirname, './withdrawTransaction.worker.js'))
BulkDepositWithdrawTransactionQueue.process(BulkDepositWithdrawTransaction, 1, path.join(__dirname, './bulkDepositWithdrawTransaction.worker.js'))
// SportsCasinoTransactionQueue.process(SportsCasinoTransaction, 4, path.join(__dirname, './sportsCasinoTransaction.worker.js'))
// CreateCsvQueue.process(CreateCsv, {
//   concurrency: 2,
//   lockDuration: 86400000
// }, path.join(__dirname, './createCsv.worker.js'))
CreateCsvQueue.process(CreateCsv, 5, path.join(__dirname, './createCsv.worker.js'))
ExportCsvQueue.process(ExportCsv, 1, path.join(__dirname, './exportCsv.worker.js'))
ActiveLosingBonusJobsQueue.process(ActiveLosingBonusJobs, 1, path.join(__dirname, './activeLosingBonusJob.worker.js'))
AuditLogQueue.process(AuditLog, 1, path.join(__dirname, './auditLog.worker.js'))
PaymentGatewayQueue.process(PaymentGateway, 1, path.join(__dirname, './paymentGateway.worker.js'))
CommonQueue.process(CommonJob, 1, path.join(__dirname, './commonJob.worker.js'))
BonusEngineQueue.process(BonusEngine, 1, path.join(__dirname, './bonusEngine.worker.js'))
TransactionQueue.process(TransactionJob, 1, path.join(__dirname, './transaction.worker.js'))
