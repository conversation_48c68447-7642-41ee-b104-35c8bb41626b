import WorkerBase from '../../libs/workerBase'
import { updateSportsBetTransactionIndex, updateSportTransactionStatus } from '../../elastic-search'
import db, { sequelize } from '../../db/models'
import { isEligibleForLosingBonus } from '../../common/isEligibleForLosingBonus'
import { ggrBasedLosingBonus } from '../../common/ggrBasedLosingBonus'
import { BONUS_CLAIM_TYPE, BONUS_CALCULATION_TYPE, BONUS_INTERVAL_TYPE, BONUS_DATES } from '../../utils/constants/constant'
import { BONUS_TYPES, TRANSACTION_TYPES, TABLES, SUBSCRIPTION_CHANNEL, QUEUE_WORKER_CONSTANT, BONUS_STATUS } from '../../common/constants'
import generateUniqueTransactionIds from '../../common/generateUniqueTransactionIds'
import userCurrencyExchange from '../../common/userCurrencyExchange'
import currencyConversion from '../../common/newCurrencyConversion'
import beforeAfterBalance from '../../common/beforeAfterBalance'
import { Op } from 'sequelize'

class ActiveLosingBonusWorker extends WorkerBase {
  async run () {
    try {
      const { bonusType, bonusId, userId, from, to, isClaimed } = this.args.job.data
      const {
        Transaction: TransactionModel,
        Bonus: BonusModel,
        LosingBonusSetting: LosingBonusSettingModel,
        LosingBonusTier: LosingBonusTierModel,
        BetsTransaction: BetsTransactionModel,
        Wallet: WalletModel,
        Currency: CurrencyModel,
        UserLosingBonusClaimHistory: UserLosingBonusClaimHistoryModel,
        UserBonus: UserBonusModel,
        QueueLog: QueueLogModel
      } = db

      const losingBonus = await BonusModel.findOne({
        attributes: [`tenantId`, `kind`, `currencyId`, `id`, `percentage`, `validFrom`, `validUpto`],
        where: {
          id: bonusId,
        },
        include: {
          model: LosingBonusSettingModel,
          where: {
            [Op.or]: [{ bonusClaimType: BONUS_CLAIM_TYPE.AUTOMATIC_ACTIVE }, { bonusClaimType: BONUS_CLAIM_TYPE.AUTOMATIC }],
          },
          include: {
            model: LosingBonusTierModel
          }
        }
      })

      const userLosingBonus = await UserBonusModel.findOne({
        attributes: [`id`, `status`, `bonusAmount`, `rolloverBalance`, `userId`, `kind`, `bonusId`, 'claimedAt', `transactionId`, `createdAt`],
        where: {
          bonusId,
          userId,
          kind: BONUS_TYPES.LOSING
        }
      })

      const bonusClaimType = losingBonus.LosingBonusSetting.bonusClaimType
      let startDate, endDate

      if (bonusClaimType === BONUS_CLAIM_TYPE.AUTOMATIC) {
      const validFrom = losingBonus.validFrom
      const activatedFrom = userLosingBonus.createdAt
      const validTill = losingBonus.validUpto
      const today = new Date()

      const timeDiff = today.getTime() - validFrom.getTime()
      const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));

      const claimDays = Math.floor((today.getTime() - activatedFrom.getTime()) / (1000 * 3600 * 24))
      const isValid = Math.floor((validTill.getTime() - today.getTime()) / (1000 * 3600 * 24))

      const yesterday = new Date(today)
      yesterday.setDate(today.getDate() - BONUS_DATES[bonusType])
      yesterday.setHours(0, 0, 0, 0)

      startDate = (claimDays < 1) ? userLosingBonus.createdAt : yesterday
      endDate = isValid > 0 ? today: validTill
      }
      else if (bonusClaimType === BONUS_CLAIM_TYPE.AUTOMATIC_ACTIVE) {
        startDate = new Date(from)
        endDate = new Date(to)
      }

      const userWallet = await WalletModel.findOne({
        attributes: [`id`, 'amount', 'nonCashAmount', 'currencyId', 'ownerId'],
        where: {
          ownerId: userId,
          ownerType: 'User'
        },
        include: CurrencyModel
      })

      const dateObj = {
        createdAt: { [Op.gte]: startDate.toISOString(), [Op.lte]: endDate.toISOString() }
      }

      let betAmountSumLoss
      const tenantId = losingBonus?.tenantId
      if (losingBonus.LosingBonusSetting.bonusCalculationType === 'ngr') {
        betAmountSumLoss = await isEligibleForLosingBonus({
          TransactionModel,
          BetsTransactionModel,
          userWallet,
          userId,
          dateObj,
          tenantId
        })
      } else {
        betAmountSumLoss = await ggrBasedLosingBonus({
          TransactionModel,
          userWallet,
          dateObj,
          tenantId
        })
      }

      let amountDiff = Infinity
      let bonusObj = null
      losingBonus.LosingBonusSetting.LosingBonusTiers.forEach(ele => {
        if (betAmountSumLoss >= ele.minLosingAmount && amountDiff > Math.abs(ele.minLosingAmount - betAmountSumLoss)) {
          amountDiff = Math.abs(ele.minLosingAmount - betAmountSumLoss)
          bonusObj = ele
        }
      })
      if (!bonusObj) {
        return { message: 'Successfully updated', status: 'bonus is not there' }
      }
      // calculating losing bonus to credit
      let bonusToCredit
      if (bonusObj?.maxLosingAmount && bonusObj?.maxLosingAmount < betAmountSumLoss)
        bonusToCredit = bonusObj?.maxLosingAmount * (bonusObj?.percentage / 100)
      else
        bonusToCredit = betAmountSumLoss * (bonusObj?.percentage / 100)

      if (bonusObj?.maxBonus && bonusToCredit > bonusObj.maxBonus) {
        bonusToCredit = bonusObj.maxBonus
      }

      const transactionId = await generateUniqueTransactionIds(TransactionModel, losingBonus.tenantId)
      let transactionObj = {
        targetWalletId: userWallet.id,
        targetCurrencyId: userWallet.currencyId,
        amount: bonusToCredit,
        comments: '',
        actioneeId: userId,
        transactionId,
        paymentMethod: 'automatic',
        actioneeType: 'User',
        tenantId: losingBonus.tenantId,
        timestamp: new Date().getTime(),
        transactionType: TRANSACTION_TYPES.NON_CASH_BONUS_CLAIM,
        status: 'success'
      }

      userLosingBonus.bonusAmount = bonusToCredit
      userLosingBonus.userId = userId
      userLosingBonus.bonusId = bonusId
      userLosingBonus.kind = BONUS_TYPES.LOSING
      if (losingBonus.LosingBonusSetting.claimDays) {
        userLosingBonus.status = BONUS_STATUS.CLAIMED
      }
      const claimedAt = Date.now()
      userLosingBonus.claimedAt = claimedAt

      const userLosingBonusClaimHistory = {}
      if (!losingBonus.LosingBonusSetting.claimDays) {
        userLosingBonusClaimHistory.bonusAmount = bonusToCredit
        userLosingBonusClaimHistory.userId = userId
        userLosingBonusClaimHistory.bonusId = bonusId
        userLosingBonusClaimHistory.claimedAt = claimedAt
      }

      const sequelizeTransaction = await sequelize.transaction()

      try {
        const userWallet = await WalletModel.findOne({
          where: { ownerId: userId, ownerType: TABLES.USER },
          transaction: sequelizeTransaction,
          lock: {
            level: sequelizeTransaction.LOCK.UPDATE,
            of: WalletModel
          },
          skipLocked: false
        })

        userWallet.nonCashAmount += bonusToCredit
        const skipWalletHook = true
        await userWallet.save({ transaction: sequelizeTransaction, skipWalletHook })

        transactionObj.conversionRate = await userCurrencyExchange(CurrencyModel, userWallet.currencyId)
        transactionObj = await currencyConversion( transactionObj, userWallet, bonusToCredit)
        transactionObj = await beforeAfterBalance(transactionObj, userWallet.nonCashAmount, bonusToCredit)

        const skipTransactionHook = true
        const newTransaction = await TransactionModel.create(transactionObj, { transaction: sequelizeTransaction, skipTransactionHook })
        userLosingBonus.transactionId = newTransaction.id

        await userLosingBonus.save({ transaction: sequelizeTransaction })

        if (!losingBonus.LosingBonusSetting.claimDays) {
          userLosingBonusClaimHistory.transactionId = newTransaction.id
          await UserLosingBonusClaimHistoryModel.create(userLosingBonusClaimHistory, { transaction: sequelizeTransaction })
        }

        // commenting extra code
        // if (!losingBonus.LosingBonusSetting.claimDays) {
        //   userLosingBonusClaimHistory.transactionId = newTransaction.id
        //   await UserLosingBonusClaimHistoryModel.create(userLosingBonusClaimHistory, { transaction: sequelizeTransaction })
        // }

        const txnIds = []

        if (newTransaction) {
          txnIds.push(newTransaction.id)
        }

        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: txnIds
        }

        const queueLog = await QueueLogModel.create(queueLogObject, { transaction: sequelizeTransaction })

        await sequelizeTransaction.commit()

        try {
          this.context.pubSub.publish(SUBSCRIPTION_CHANNEL.USER_WALLET_BALANCE, { UserWalletBalance: { walletBalance: userWallet.amount, userId, nonCashAmount: userWallet.nonCashAmount } })
          this.context.pubSub.publish(
            SUBSCRIPTION_CHANNEL.QUEUE_WORKER,
            { QueueLog: { queueLogId: queueLog?.id } }
          )
        } catch (error) {
        }
      } catch (error) {
        await sequelizeTransaction.rollback()
      }
      return { message: 'Automatic losing bonus completed'}
    } catch (error) {
      console.log("======Bet transaction worker error", error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await ActiveLosingBonusWorker.run({ job })
  return result
}
