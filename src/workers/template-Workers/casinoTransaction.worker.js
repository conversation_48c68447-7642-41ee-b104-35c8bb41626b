import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'

class CasinoTransactionWorker extends WorkerBase {
  async run () {
    try{
      const transactionId = this.args.job.data.transactionId
      //await updateTransactionIndex(transactionId, sequelize)
      return { message: 'Casino transaction index created successfully' }
    }catch(error){
      console.log("====casino transaction worker error", error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await CasinoTransactionWorker.run({ job })
  return result
}
