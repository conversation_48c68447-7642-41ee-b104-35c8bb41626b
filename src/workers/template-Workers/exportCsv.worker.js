import { EXPORT_CSV_STATUS } from '../../common/constants'
import exportTransactionCsvDbData from '../../common/csv/exportTransactionCsvDbData'
import db from '../../db/models'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'

class ExportCsvWorker extends WorkerBase {
  async run () {
    const queueLogId = this.args.job.data.queueLogId

    try {
      const queueData = await db.QueueLog.findOne({
        where: {
          id: queueLogId
        },
        useMaster: true,
        raw: true
      })
      if (queueData) {
        await exportTransactionCsvDbData(queueData)

        return { message: 'Csv created successfully' }
      } else {
        throw new Error('No data found')
      }
    } catch (error) {
      await db.QueueLog.update({
        status: EXPORT_CSV_STATUS.FAILED
      },
      {
        where: { id: queueLogId }
      }
      )
      console.log('======Create csv worker error', error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await ExportCsvWorker.run({ job })
  return result
}
