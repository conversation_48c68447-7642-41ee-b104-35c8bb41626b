import axiosCall from '../../common/axiosCall'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import db from '../../db/models'

class BulkDepositWithdrawTransactionWorker extends WorkerBase {
  async run () {
    try{
      const dipositWithdrawUserId = this.args.job.data.transactionId
      const dipositWithdrawUser = await db.DepositWithdrawUser.findOne({
        where: {
          id: dipositWithdrawUserId
        },
        useMaster: true
      })
      const response = await axiosCall(dipositWithdrawUser.jobId, dipositWithdrawUser.userId)
      if(response){
        return { message: 'Success' }
      }
    }catch(error){
      throw error
      console.log("========bulk deposit withdraw worker error",error)
    }


  }
}

export default async (job) => {
  const result = await BulkDepositWithdrawTransactionWorker.run({ job })
  return result
}
