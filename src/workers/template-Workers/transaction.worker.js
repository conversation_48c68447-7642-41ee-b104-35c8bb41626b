import checkRollbackWithAxios from '../../common/checkRollbackWithAxios'
import { sequelize } from '../../db/models'
import { signupUserIndex } from '../../elastic-search'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import { BetTransactionData } from '../../services/transactions/betTransactionData'
import { CasinoTransactionData } from '../../services/transactions/casinoTransactionData'
import { RollbackTransactionData } from '../../services/transactions/rollbackTransactionData'
import { SportCasinoTransactionData } from '../../services/transactions/sportCasinoTransactionData'
import { UserTransactionData } from '../../services/transactions/userTransactionData'

class TransactionWorker extends WorkerBase {
  async run () {
    try {
      const transactionType = this.args.job.data.transactionType
      const transactionId = this.args.job.data.transactionId
      const tenantId = this.args.job.data.tenantId
      if(transactionType === 'casino_transaction'){
      //  await updateTransactionIndex(transactionId, sequelize)
      }else if(transactionType === 'bet_transaction'){
      //  await updateSportsBetTransactionIndex(transactionId, sequelize)
      }else if(transactionType === 'user_transaction'){
        await signupUserIndex(transactionId, sequelize)
      }else if(transactionType == 'casinoTransactionData'){
        await CasinoTransactionData.execute()
      }else if(transactionType == 'userTransactionData'){
        await UserTransactionData.execute()
      }else if(transactionType == 'betTransactionData'){
        await BetTransactionData.execute()
      }else if(transactionType == 'sportToCasinoTransactionData'){
        await SportCasinoTransactionData.execute()
      }else if(transactionType == 'rollbackTransactionData'){
        await RollbackTransactionData.execute()
      }else if(transactionType == 'rollback_transaction'){
        await checkRollbackWithAxios(transactionId, tenantId)
      }else if('sport_casino_transaction'){

      }
      return { message: 'Casino transaction index created successfully' }
    } catch (error) {
      console.log('======Common job worker error', error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await TransactionWorker.run({ job })
  return result
}
