import { EXPORT_CSV_STATUS, EXPORT_CSV_TYPE, PROD_TENANTS, QUEUE_WORKER_CONSTANT, STAGE_TENANTS, WYNTA_SITES } from '../../common/constants'
import getDbQueryStreamData from '../../common/csv/getQueryStreamData'
import getTransactionCsvData from '../../common/csv/getTransactionCsvData'
import getTransactionCsvDbData from '../../common/csv/getTransactionCsvDbData'
import getAgentRevenueReportData from '../../common/csv/reports/getAgentRevenueReportData'
import getAgentRevenueReportV3Data from '../../common/csv/reports/getAgentRevenueReportV3Data'
import getBetReportCsvData from '../../common/csv/reports/getBetReportCsvData'
import getBetReportDbCsvData from '../../common/csv/reports/getBetReportDbCsvData'
import getBiggestWinnerReportData from '../../common/csv/reports/getBiggestWinnerReportData'
import getCashiersBetWinReport from '../../common/csv/reports/getCashiersBetWinReport'
import getDepositUserList from '../../common/csv/reports/getDepositUserList'
import getFirstTimeDepositPlayerReportCsvData from '../../common/csv/reports/getFirstTimeDepositPlayerReportCsvData'
import getGameTransactionDbReportData from '../../common/csv/reports/getGameTransactionDbReportData'
import getGameTransactionReportData from '../../common/csv/reports/getGameTransactionReportData'
import getLanguageStrings from '../../common/csv/reports/getLanguageStrings'
import getPlayerFilterReportData from '../../common/csv/reports/getPlayerFilterReportData'
import getPlayerFinancialDbReportCsvData from '../../common/csv/reports/getPlayerFinancialDbReportCsvData'
import getPlayerFinancialReportCsvData from '../../common/csv/reports/getPlayerFinancialReportCsvData'
import getPlayerListData from '../../common/csv/reports/getPlayerListData'
import getPlayerNcbDbReportData from '../../common/csv/reports/getPlayerNcbDbReportData'
import getPlayerNcbReportData from '../../common/csv/reports/getPlayerNcbReportData'
import getPlayerPerformanceAndFinancialReport from '../../common/csv/reports/getPlayerPerformanceAndFinancialReport'
import getPlayerReportCsvData from '../../common/csv/reports/getPlayerReportCsvData'
import getPlayerReportCsvDataDb from '../../common/csv/reports/getPlayerReportCsvDataDb'
import getPlayerRevenueReportData from '../../common/csv/reports/getPlayerRevenueReportData'
import getPowerplaySettlementReportCsvData from '../../common/csv/reports/getPowerplaySettlementReportCsvData'
import getPromoCodeUserList from '../../common/csv/reports/getPromoCodeUserList'
import getRiskEvaluationReportCsvData from '../../common/csv/reports/getRiskEvaluationReportCsvData'
import getSampleDepositList from '../../common/csv/reports/getSampleDepositList'
import getSampleUserRfidList from '../../common/csv/reports/getSampleUserRfidList'
import getSampleWithdrawalList from '../../common/csv/reports/getSampleWithdrawalList'
import getSportTransactionCsvData from '../../common/csv/reports/getSportTransactionCsvData'
import getSportTransactionDbCsvData from '../../common/csv/reports/getSportTransactionDbCsvData'
import getUnifiedTransactionDbReportData from '../../common/csv/reports/getUnifiedTransactionDbReportData'
import getUnifiedTransactionReportData from '../../common/csv/reports/getUnifiedTransactionReportData'
import getUserLoginHistory from '../../common/csv/reports/getUserLoginHistory'
import getWithdrawlList from '../../common/csv/reports/getWithdrawlList'
import config from '../../configs/app.config'
import db from '../../db/models'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'
import PartnerMatrix from '../../services/cron-services/partnerMatrix'
import Wynta from '../../services/cron-services/wynta'
import { CreateCsvData } from '../../services/transactions/createCsvData'

const CSV_TYPES = {
  SALES: 'sales',
  REG: 'reg'
}
class CreateCsvWorker extends WorkerBase {
  async run () {
    const csvId = this.args.job.data.transactionId
    const transactionType = this.args.job.data.transactionType
    try {
      if(transactionType === 'createCsvData'){
        await CreateCsvData.execute()
        return
      }
      const csvDetail = await db.ExportCsvCenter.findOne({
        where: {
          id: csvId
        },
        useMaster: true,
        raw: true
      })
      if (csvDetail) {
        if (csvDetail.type == EXPORT_CSV_TYPE.CASINO_TRANSACTION || csvDetail.type == EXPORT_CSV_TYPE.ACCOUNT_SUMMARY) {
          const success = await getTransactionCsvData(csvDetail)
        }else if(csvDetail.type == EXPORT_CSV_TYPE.CASINO_TRANSACTION_DB || csvDetail.type == EXPORT_CSV_TYPE.ACCOUNT_SUMMARY_DB){
          await getTransactionCsvDbData(csvDetail)
        } else if(csvDetail.type == 'query_stream'){
          await getDbQueryStreamData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.PLAYER_REPORT) {
          await getPlayerReportCsvData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.PLAYER_REPORT_DB) {
          await getPlayerReportCsvDataDb(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.PLAYER_FINANCIAL_REPORT || csvDetail.type == EXPORT_CSV_TYPE.FINANCIAL) {
          await getPlayerFinancialReportCsvData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.PLAYER_FINANCIAL_REPORT_DB || csvDetail.type == EXPORT_CSV_TYPE.FINANCIAL_DB) {
          await getPlayerFinancialDbReportCsvData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.BET_REPORT || csvDetail.type == EXPORT_CSV_TYPE.SPORT_BETTING) {
          await getBetReportCsvData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.BET_REPORT_DB || csvDetail.type == EXPORT_CSV_TYPE.SPORT_BETTING_DB) {
          await getBetReportDbCsvData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.SPORT_TRANSACTION) {
          await getSportTransactionCsvData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.SPORT_TRANSACTION_DB) {
          await getSportTransactionDbCsvData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.UNIFIED_TRANSACTION_REPORT) {
          await getUnifiedTransactionReportData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.UNIFIED_TRANSACTION_REPORT_DB) {
          await getUnifiedTransactionDbReportData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.GAME_TRANSACTION_REPORT) {
          await getGameTransactionReportData(csvDetail)
        }else if (csvDetail.type == EXPORT_CSV_TYPE.GAME_TRANSACTION_REPORT_DB) {
          await getGameTransactionDbReportData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.POWERPLAY_SETTLEMENT_REPORT) {
          await getPowerplaySettlementReportCsvData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.PLAYER_NCB_REPORT) {
          await getPlayerNcbReportData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.PLAYER_NCB_DB_REPORT) {
          await getPlayerNcbDbReportData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.PLAYER_LIST || csvDetail.type == EXPORT_CSV_TYPE.SAMPLE_PLAYER) {
          await getPlayerListData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.PROMO_CODE_USER_LIST) {
          await getPromoCodeUserList(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.DEPOSIT_UNVERIFIED_REQUEST || csvDetail.type == EXPORT_CSV_TYPE.DEPOSIT_VERIFIED_REQUEST || csvDetail.type == EXPORT_CSV_TYPE.DEPOSIT_REJECTED_REQUEST) {
          await getDepositUserList(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.WITHDRAW_UNVERIFIED_REQUEST || csvDetail.type == EXPORT_CSV_TYPE.WITHDRAW_VERIFIED_REQUEST || csvDetail.type == EXPORT_CSV_TYPE.WITHDRAW_REJECTED_REQUEST) {
          await getWithdrawlList(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.SAMPLE_WITHDRAWAL) {
          await getSampleWithdrawalList(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.SAMPLE_DEPOSIT) {
          await getSampleDepositList(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.FIRST_TIME_DEPOSIT_PLAYER_REPORT) {
          await getFirstTimeDepositPlayerReportCsvData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.SAMPLE_LANGUAGE_KEYS) {
          await getLanguageStrings(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.BIGGEST_WINNER_REPORT) {
          await getBiggestWinnerReportData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.PLAYER_REVENUE_REPORT) {
          await getPlayerRevenueReportData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.AGENT_REVENUE_REPORT) {
          await getAgentRevenueReportData(csvDetail)
        }else if (csvDetail.type == EXPORT_CSV_TYPE.AGENT_REVENUE_REPORT_V3) {
          await getAgentRevenueReportV3Data(csvDetail)
        }else if (csvDetail.type == EXPORT_CSV_TYPE.PLAYER_FILTER_REPORT) {
          await getPlayerFilterReportData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.PLAYER_PERFORMANCE_AND_FINANCIAL_REPORT) {
          await getPlayerPerformanceAndFinancialReport(csvDetail)
        } else if(csvDetail.type == EXPORT_CSV_TYPE.RISK_EVALUATION_REPORT){
          await getRiskEvaluationReportCsvData(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.CASHIER_BET_REPORT) {
          await getCashiersBetWinReport(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.USER_LOGIN_HISTORY) {
          await getUserLoginHistory(csvDetail)
        } else if (csvDetail.type == EXPORT_CSV_TYPE.SAMPLE_USER_RFID_LIST) {
          await getSampleUserRfidList(csvDetail)
        } else {
          const sites = ['jeeto555', 'AceXBet365']
          const wyntaSites = WYNTA_SITES
          const tenants = config.get('env') === 'development' ? STAGE_TENANTS : PROD_TENANTS
          const siteIds = Object.entries(tenants).filter(tenant => sites.includes(tenant[1].name)).map(item => item[0])
          const wyntaSiteIds = Object.entries(tenants).filter(tenant => wyntaSites.includes(tenant[1].name)).map(item => item[0])

          const partnerMatrixTypes = siteIds.map(i => Object.values(CSV_TYPES).map(j => `pm_${i}_${j}_report`)).flat()
          const wyntaTypes = wyntaSiteIds.map(i => Object.values(CSV_TYPES).map(j => `wynta_${i}_${j}_report`)).flat()

          if (partnerMatrixTypes.includes(csvDetail.type)) {
            const { result, successful, errors } = await PartnerMatrix.execute({
              type: csvDetail?.payload?.type,
              tenantId: +csvDetail?.payload.tenantId,
              queueId: +csvDetail?.id,
              csvDetail : csvDetail
            }, null)

            if (!successful) {
              throw new Error(errors)
            }
          } else if (wyntaTypes.includes(csvDetail.type)) {
            const { result, successful, errors } = await Wynta.execute({
              type: csvDetail?.payload?.type,
              tenantId: +csvDetail?.payload.tenantId,
              queueId: +csvDetail?.id,
              csvDetail : csvDetail
            }, null)

            if (!successful) {
              throw new Error(errors)
            }
          } else {
            throw new Error('Csv type is wrong')
          }
        }
        if (csvDetail.payload?.send_email) {
          const queueLogObject = {
            type: EXPORT_CSV_TYPE.MAIL_REPORT_CSV,
            status: QUEUE_WORKER_CONSTANT.READY,
            ids: [parseInt(csvDetail.id)]
          }
          await db.QueueLog.create(queueLogObject)
        }

        return { message: 'Csv created successfully' }
      } else {
        throw new Error('No data found')
      }
    } catch (error) {
      await db.ExportCsvCenter.update({
        status: EXPORT_CSV_STATUS.FAILED
      },
      {
        where: { id: csvId }
      }
      )
      console.log('======Create csv worker error', error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await CreateCsvWorker.run({ job })
  return result
}
