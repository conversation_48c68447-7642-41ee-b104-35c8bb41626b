import { v4 as uuidv4 } from 'uuid'
import { JETFAIR_INTEGRATION_CONSTANT, QUEUE_WORKER_CONSTANT, SPORTS_PROVIDERS_STAGE, SPORTS_PROVIDER_PROD, SPORT_PROVIDER, paymentForCodes } from '../../common/constants'
import currencyConversion from '../../common/newCurrencyConversion'
import config from '../../configs/app.config'
import db, { sequelize } from '../../db/models'
import '../../libs/setUpGraceFullShutDown'
import WorkerBase from '../../libs/workerBase'

class SportsCasinoTransactionWorker extends WorkerBase {
  async run () {
    try{
      const betTransactionId = this.args.job.data.transactionId

      const betTransaction = await db.BetsTransaction.findOne({
        raw: true,
        where: {
          id: betTransactionId
        }
      })
      let betBets;
      if(betTransaction?.marketId) {
           betBets = await db.BetsBet.findOne({
              raw: true,
              where: {
                  marketId: betTransaction.marketId
              },
              attributes: ['market']
          })
      }
      const user = await db.User.findOne({
        where: {
          id: betTransaction.userId
        },
        include: [{
          model: db.Wallet
        }]
      })

      const providers = config.get('env') === 'production' ? SPORTS_PROVIDER_PROD: SPORTS_PROVIDERS_STAGE

      let status = 'success'
      let queueTxnIds = []
      const userWallet = user.Wallet
      const uuid = uuidv4().replace(/-/g, '')
      const transactionId = uuid.substr(uuid.length - 10)
      let nonCashTxn, cashTxn, sourceWalletId, sourceBeforeBalance, sourceAfterBalance, sourceCurrencyId, targetWalletId, targetBeforeBalance, targetAfterBalance, targetCurrencyId, transactionType, comment
      if (Math.sign(betTransaction.nonCashAmount)) {
        if (betTransaction.journalEntry === "DR") {
          sourceWalletId = betTransaction.sourceWalletId
          sourceCurrencyId = betTransaction.sourceCurrencyId
          sourceBeforeBalance = betTransaction.sourceNonCashBeforeBalance
          sourceAfterBalance = betTransaction.sourceNonCashAfterBalance
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.PLACE_BET_TXN_CODE) {
            transactionType = 20
            comment = 'Exchange place bet non cash debit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.CANCEL_BET_TXN_CODE) {
            transactionType = 23
            comment = 'Exchange refund cancel bet non cash debit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.MARKET_CANCEL_TXN_CODE) {
            transactionType = 27
            comment = 'Exchange refund market cancel non cash debit'
          }
        } else {
          targetWalletId = betTransaction.targetWalletId
          targetCurrencyId = betTransaction.targetCurrencyId
          targetBeforeBalance = betTransaction.targetNonCashBeforeBalance
          targetAfterBalance = betTransaction.targetNonCashAfterBalance
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.CANCEL_BET_TXN_CODE) {
            transactionType = 25
            comment = 'Exchange refund cancel bet non cash credit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.MARKET_CANCEL_TXN_CODE) {
            transactionType = 29
            comment = 'Exchange refund market cancel non cash credit'
          }
          if (betTransaction.paymentFor == paymentForCodes.DEPOSIT_BONUS_PENDING) {
            transactionType = 37
            comment = 'Exchange deposit bonus pending'
            status = 'pending'
          }
        }

        let nonCashTransactionObj = {
          sourceWalletId: sourceWalletId,
          sourceCurrencyId: sourceCurrencyId,
          targetWalletId: targetWalletId,
          targetCurrencyId: targetCurrencyId,
          amount: parseFloat(betTransaction.nonCashAmount),
          conversionRate: betTransaction.conversionRate,
          sourceBeforeBalance: sourceBeforeBalance,
          sourceAfterBalance: sourceAfterBalance,
          targetBeforeBalance: targetBeforeBalance,
          targetAfterBalance: targetAfterBalance,
          transactionId: transactionId,
          comments: comment,
          actioneeId: betTransaction.userId,
          actioneeType: 'User',
          tenantId: betTransaction.tenantId,
          transactionType: transactionType,
          status: status,
          success: true,
          errorCode: 0,
          //errorDescription: 'Completed Successfully',
          providerId: betTransaction.providerId,
          otherCurrencyAmount: betTransaction.otherCurrencyNonCashAmount,
          debitTransactionId: betTransaction.id,
          seatId: betBets?.market,
          gameId: betTransaction.providerId === providers.JETFAIR ? SPORT_PROVIDER.JETFAIR: SPORT_PROVIDER.POWERPLAY,
          createdAt: betTransaction.createdAt
        }
        const checkSqlNonCash = `SELECT id FROM "transactions" WHERE "debit_transaction_id" = '${betTransaction.id}' AND tenant_id = ${betTransaction.tenantId}  AND "transaction_type" = '${transactionType}'`
        const checkSqlCashNonResult = await sequelize.query(checkSqlNonCash)
        if(!checkSqlCashNonResult[0].length){
          nonCashTxn = await db.Transaction.create(nonCashTransactionObj)
          queueTxnIds.push(nonCashTxn.id)
        }
      }
      if (Math.sign(betTransaction.amount) || (betTransaction.amount==0 && (!betTransaction.nonCashAmount || betTransaction.nonCashAmount==0))) {
        if (betTransaction.journalEntry === "DR") {
          sourceWalletId = betTransaction.sourceWalletId
          sourceCurrencyId = betTransaction.sourceCurrencyId
          sourceBeforeBalance = betTransaction.sourceBeforeBalance
          sourceAfterBalance = betTransaction.sourceAfterBalance
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.PLACE_BET_TXN_CODE) {
            transactionType = 21
            comment = 'Exchange place bet cash debit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.CANCEL_BET_TXN_CODE) {
            transactionType = 24
            comment = 'Exchange refund cancel bet cash debit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.MARKET_CANCEL_TXN_CODE) {
            transactionType = 28
            comment = 'Exchange refund market cancel cash debit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.SETTLE_MARKET_TXN_CODE) {
            transactionType = 32
            comment = 'Exchange settle market cash debit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.RESETTLE_TXN_CODE) {
            transactionType = 34
            comment = 'Exchange Resettle market cash debit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.CANCEL_SETTLED_MARKET_TXN_CODE) {
            transactionType = 36
            comment = 'Exchange cancel settled market cash debit'
          }
        } else {
          targetWalletId = betTransaction.targetWalletId
          targetCurrencyId = betTransaction.targetCurrencyId
          targetBeforeBalance = betTransaction.targetBeforeBalance
          targetAfterBalance = betTransaction.targetAfterBalance
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.PLACE_BET_TXN_CODE) {
            transactionType = 22
            comment = 'Exchange place bet cash credit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.CANCEL_BET_TXN_CODE) {
            transactionType = 26
            comment = 'Exchange refund cancel bet cash credit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.MARKET_CANCEL_TXN_CODE) {
            transactionType = 30
            comment = 'Exchange refund market cancel cash credit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.SETTLE_MARKET_TXN_CODE) {
            transactionType = 31
            comment = 'Exchange settle market cash credit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.RESETTLE_TXN_CODE) {
            transactionType = 33
            comment = 'Exchange Resettle market cash credit'
          }
          if (betTransaction.transactionCode == JETFAIR_INTEGRATION_CONSTANT.CANCEL_SETTLED_MARKET_TXN_CODE) {
            transactionType = 35
            comment = 'Exchange cancel settled market cash credit'
          }
          if (betTransaction.paymentFor == paymentForCodes.DEPOSIT_BONUS_PENDING) {
            transactionType = 37
            comment = 'Exchange deposit bonus pending'
            status = 'pending'
          }
        }
        let cashTransactionObj = {
          sourceWalletId: sourceWalletId,
          sourceCurrencyId: sourceCurrencyId,
          targetWalletId: targetWalletId,
          targetCurrencyId: targetCurrencyId,
          amount: parseFloat(betTransaction.amount),
          conversionRate: betTransaction.conversionRate,
          sourceBeforeBalance: sourceBeforeBalance,
          sourceAfterBalance: sourceAfterBalance,
          targetBeforeBalance: targetBeforeBalance,
          targetAfterBalance: targetAfterBalance,
          transactionId: transactionId,
          comments: comment,
          actioneeId: betTransaction.userId,
          actioneeType: 'User',
          tenantId: betTransaction.tenantId,
          transactionType: transactionType,
          status: status,
          success: true,
          errorCode: 0,
          //errorDescription: 'Completed Successfully',
          providerId: betTransaction.providerId,
          debitTransactionId: betTransaction.id,
          seatId: betBets?.market,
          gameId: betTransaction.providerId === providers.JETFAIR ? SPORT_PROVIDER.JETFAIR: SPORT_PROVIDER.POWERPLAY,
          createdAt: betTransaction.createdAt,
          metaData: betTransaction.betslipId ? null : {
            commissionAmount: (parseFloat(betTransaction.commissionAmount) || 0),
            commissionPer: (parseFloat(betTransaction.commissionPer) || 0),
            otherCurrencyCommissionAmountData: betTransaction?.otherCurrencyCommissionAmount ? JSON.parse(betTransaction?.otherCurrencyCommissionAmount) : null
          }
        }
        cashTransactionObj = await currencyConversion(cashTransactionObj, user.Wallet, betTransaction.amount)
        const checkSqlCash = `SELECT id FROM "transactions" WHERE "debit_transaction_id" = '${betTransaction.id}' AND tenant_id = ${betTransaction.tenantId} AND "transaction_type" = '${transactionType}'`
        const checkSqlCashResult = await sequelize.query(checkSqlCash)
        if(!checkSqlCashResult[0].length){
          cashTxn = await db.Transaction.create(cashTransactionObj)
          queueTxnIds.push(cashTxn.id)
        }
      }
      if(queueTxnIds.length>0){
        const queueLogObject = {
          type: QUEUE_WORKER_CONSTANT.CASINO_TRANSACTION,
          status: QUEUE_WORKER_CONSTANT.READY,
          ids: queueTxnIds
        }
        const queueLog = await db.QueueLog.create(queueLogObject)
      }
      return { message: 'Sport casino transaction created successfully' }
    }catch(error){
      console.log("======Sport to casino transaction worker error", error)
      throw error
    }
  }
}

export default async (job) => {
  const result = await SportsCasinoTransactionWorker.run({ job })
  return result
}
